<html>
<head>
  <title>设置</title>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=500, user-scalable=no" />
  <link href="static/mdi_font/css/materialdesignicons.min.css" rel="stylesheet" />
  <link href="static/vuetify.min.css" rel="stylesheet" />
  <script src="static/vue.min.js"></script>
  <script src="static/vuetify.min.js"></script>
  <style>
    .container { max-width: 900px; margin: 10px auto; }
    .mono { font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; }
    .hint { color: #666; font-size: 12px; }
  </style>
</head>
<body>
  <div id="app">
    <v-app>
			<v-toolbar :elevation="2"
				style="position: sticky;top: 0;z-index: 1;min-width: 100vw;padding-right: 10px;">
				<b style="font-size: xx-large;padding-left: 20px;">设置</b>
				<v-spacer></v-spacer>
			
        <v-btn icon @click="reload()"><v-icon>mdi-reload</v-icon></v-btn>
      			</v-toolbar>

      <v-container class="container">
        <v-card class="mb-6">
          <v-card-title>远程 API</v-card-title>
          <v-card-text>
            <v-text-field
              v-model="server"
              :type="serverHidden ? 'password' : 'text'"
              label="API Endpoint (llm_server)"
              placeholder="https://host:port/completion"
              clearable
              :append-icon="serverHidden ? 'mdi-eye' : 'mdi-eye-off'"
              @click:append="serverHidden = !serverHidden"
            ></v-text-field>
            <div class="hint">未设置则默认使用 /completion（本地后端）。</div>
            <v-select :items="protocolItems" v-model="protocol" label="协议 (llm_protocol)" item-text="text" item-value="value"></v-select>
            <v-select :items="templates_enum" v-model="templates_type" label="对话模板 (templates_type)" item-text="text" item-value="value"></v-select>
            <v-textarea
              class="mono"
              :class="headersHidden ? 'masked' : ''"
              v-model="headers"
              rows="6"
              label="自定义请求头 JSON (llm_headers)"
              placeholder='{"Authorization":"Bearer xxx"}'
              clearable
              :append-icon="headersHidden ? 'mdi-eye' : 'mdi-eye-off'"
              @click:append="headersHidden = !headersHidden"
            ></v-textarea>
            <div class="hint">建议将密钥放在反向代理层；如必须前端直连，请仅在受信环境使用。</div>
            <v-text-field v-model="model" label="默认模型名 (llm_model)" placeholder="Qwen3-..." clearable></v-text-field>
            <v-divider class="my-4"></v-divider>
            <v-btn :color="color" @click="save()"><v-icon left>mdi-content-save</v-icon>保存</v-btn>
            <v-btn class="ml-2" @click="reset()"><v-icon left>mdi-backup-restore</v-icon>恢复默认</v-btn>
            <v-btn class="ml-2" @click="test()" :loading="testing"><v-icon left>mdi-connection</v-icon>连通性测试</v-btn>
            <div class="mt-4 mono" v-if="test_result">
              <div><b>测试结果:</b></div>
              <pre style="white-space: pre-wrap;">{{test_result}}</pre>
            </div>
          </v-card-text>
        </v-card>

        <v-card>
          <v-card-title>界面与生成参数（全局默认）</v-card-title>
          <v-card-text>
            <v-text-field v-model.number="temperature" label="temperature" type="number" step="0.1"></v-text-field>
            <v-text-field v-model.number="top_p" label="top_p" type="number" step="0.05"></v-text-field>
            <v-text-field v-model.number="max_length" label="max_tokens / n_predict" type="number"></v-text-field>
            <div class="hint">聊天页会以这些值作为默认值（可在聊天页再次调整）。</div>
            <v-divider class="my-4"></v-divider>
            <v-btn :color="color" @click="save_ui()"><v-icon left>mdi-content-save</v-icon>保存参数</v-btn>
          </v-card-text>
        </v-card>
      </v-container>

      <v-snackbar v-model="snackbar" :timeout="3000">{{snackbar_text}}</v-snackbar>
    </v-app>
  </div>

  <script src="wd_sdk.js"></script>
  <script>
    templates_enum=[]
    for(k in templates_dict)templates_enum.push({ text: k, value: k })
    new Vue({
      el: '#app',
      vuetify: new Vuetify(),
      data(){
        return {
          color: 'primary',
          server: localStorage.getItem('llm_server') || '/completion',
          serverHidden: true,
          templates_type: templates_type(),
          templates_enum: templates_enum,
          protocol: (localStorage.getItem('llm_protocol') || 'auto').toLowerCase(),
          protocolItems: [
            { text: '自动 (auto)', value: 'auto' },
            { text: 'OpenAI Completions', value: 'completion' },
            { text: 'OpenAI Chat Completions', value: 'chat' },
          ],
          headers: localStorage.getItem('llm_headers') || '',
          headersHidden: true,
          model: localStorage.getItem('llm_model') || '',
          temperature: Number(localStorage.getItem('default_temperature')|| '0.5'),
          top_p: Number(localStorage.getItem('default_top_p') || '0.5'),
          max_length: Number(localStorage.getItem('default_max_length') || '40960'),
          snackbar: false,
          snackbar_text: '',
          testing: false,
          test_result: ''
        }
      },
      methods:{
        toast(s){ this.snackbar_text = s; this.snackbar = true; },
        save(){
          if(this.headers){
            try{ JSON.parse(this.headers) } catch(e){ this.toast('请求头不是合法 JSON'); return }
          }
          if(this.server) localStorage.setItem('llm_server', this.server); else localStorage.removeItem('llm_server');
          if(this.protocol) localStorage.setItem('llm_protocol', this.protocol); else localStorage.removeItem('llm_protocol');
          if(this.headers) localStorage.setItem('llm_headers', this.headers); else localStorage.removeItem('llm_headers');
          if(this.model) localStorage.setItem('llm_model', this.model); else localStorage.removeItem('llm_model');
          if(this.templates_type) localStorage.setItem('templates_type', this.templates_type); else localStorage.removeItem('templates_type');
          this.toast('已保存');
        },
        save_ui(){
          localStorage.setItem('default_temperature', this.temperature);
          localStorage.setItem('default_top_p', this.top_p);
          localStorage.setItem('default_max_length', this.max_length);
          this.toast('全局参数已保存');
        },
        reset(){
          this.server = '/completion';
          this.headers = '';
          this.model = '';
          this.protocol = 'auto';
          this.save();
        },
        reload(){ location.reload() },
        async test(){
          this.testing = true; this.test_result='';
          try{
            const endpoint = this.server || '/completion';
            const headers = Object.assign({ 'Content-Type':'application/json' }, this.headers? JSON.parse(this.headers):{});
            let body;
            const model = this.model || 'test-model';
            const proto = (this.protocol || 'auto');
            const inferProto = () => {
              const p = proto.toLowerCase();
              if (p !== 'auto') return p;
              const path = (endpoint||'').toLowerCase();
              if (path.includes('/chat/completions') || path.includes('/v1/chat/completions')) return 'chat';
              return 'completion';
            };
            const useProto = inferProto();
            if (useProto === 'chat') {
              body = {
                model,
                messages: [{ role: 'user', content: 'ping' }],
                stream: true,
                max_tokens: 8,
                temperature: 0.1,
                top_p: 0.95
              };
            } else {
              body = {
                model,
                prompt: 'ping',
                stream: true,
                max_tokens: 8,
                n_predict: 8,
                temperature: 0.1,
                top_p: 0.95
              };
            }
            const res = await fetch(endpoint, { method:'POST', headers, body: JSON.stringify(body) });
            const txt = await res.text();
            this.test_result = 'HTTP ' + res.status + '\n' + txt;
            if(res.ok) this.toast('连接成功'); else this.toast('连接失败: ' + res.status);
          }catch(e){
            this.test_result = String(e);
            this.toast('请求失败');
          } finally { this.testing = false; }
        }
      }
    })
  </script>
  <style>
    .masked textarea, .masked .v-text-field__slot input {
      -webkit-text-security: disc;
      /* text-security is non-standard and removed to avoid lint warning */
    }
  </style>
</body>
</html>
