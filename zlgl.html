<!DOCTYPE html>
<html>

<head>
	<title>资料管理</title>
	<meta charset="utf-8">
	<meta content="yes" name="mobile-web-app-capable" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<meta name="viewport" content="width=650,  user-scalable=no">
	<link rel="shortcut icon" href="#" />
	<link href="static/mdi_font/css/materialdesignicons.min.css" rel="stylesheet">
	<link href="static/3/vuetify.min.css" rel="stylesheet">
	<script src="static/3/vue.global.prod.js"></script>
	<script src="static/3/vuetify.min.js"></script>
	<link href="common.css" rel="stylesheet" />

	<style>
		div {
			transition: all 0.3s;
		}

		.logo-left {
			left: 20px;
			width: 100%;
			font-size: xx-large;
			position: relative;
		}

		.v-application--wrap {
			display: unset;
		}

		.v-data-table tr>td:nth-child(1) {
			width: 5em;
		}

		.v-data-table tr>td:nth-child(2),
		.text-start input {
			width: 10em;
		}

		.v-data-table td>input {
			transition: outline 0.5s;
		}

		.v-data-table tr>td:nth-child(4) {
			width: 5em;
		}

		.v-data-table tr:hover {
			background-color: white !important;
		}

		.text-start input:hover,
		.text-start textarea:hover {
			outline-color: #000000;
		}

		.text-start input,
		.text-start textarea {
			outline: #00000000 solid 1px;
		}


		.text-start {
			padding: 0 4px !important;
		}

		.text-start textarea {
			width: 100%;
			height: 5em;
			padding: 0;
			scrollbar-width: thin;
			resize: none;
			transition: height 0.8s, outline 0.5s;
		}
	</style>
</head>

<body>
	<div id="app" v-cloak>
		<v-app>
			<v-app-bar :elevation="5" style="position: sticky;top: 0;z-index: 1;min-width: 100vw;padding-right: 10px;">
				<b style="font-size: xx-large;padding-left: 20px;">资料管理</b>
				<v-spacer></v-spacer>
				<v-btn size="large" @click="show_dialog=true" variant="tonal" :loading="loading">
					智能添加
				</v-btn>
				<v-btn size="large" @click="removeAllData" variant="tonal" color="red">
					清除所有数据
				</v-btn>
				<v-btn size="large" @click="removeAllDB()" variant="tonal" color="red">
					清除资料
				</v-btn>
				<v-btn size="large" @click="导出()" variant="tonal">
					备份
				</v-btn>
				<v-btn size="large" @click="导入()" variant="tonal" class="mr-5">
					恢复
				</v-btn>
			</v-app-bar>
			<v-tabs v-model="tab" background-color="white"  v-if="s输出.length>0">
				<v-tabs-slider color="primary"></v-tabs-slider>
				<v-tab>查看
				</v-tab>
				<v-tab>智能添加
				</v-tab>
			</v-tabs>
			
      <v-tabs-window v-model="tab">
        <v-tabs-window-item class="mx-10">
			
			<v-card elevation="5" class="my-10" >
				<v-data-table :headers=" [{ title: '默认', key: 'use', sortable: false, },{ title: '题目', key: 'title', sortable: false, },
					{ title: '内容', key: 'content', sortable: false, },
					{ title: '删除', key: '删除', sortable: false, },
					]" :items="articles" :items-per-page="2000" hide-default-footer>
					<template v-slot:item.use="{ item }">
						<v-checkbox v-model="item.use" label=""></v-checkbox>
					</template>
					<template v-slot:item.title="{ item }">
						<div class="input-container">
							<input v-model="item.title" placeholder="输入新添加内容的标题"></input>
						</div><br>
						<p class="data-table-content-counter" v-show="item.content.length" v-text="item.content.length">
						</p>

					</template>
					<template v-slot:item.content="{ item }">
						<div class="textarea-container">
							<textarea v-model="item.content"
								@change="item.content=item.content.replace(/^\s+/g,'').replace(/\n\s+/g,'\n').replace(/\n\n+/g,'\n\n');"
								placeholder="输入新添加内容的正文"></textarea>
						</div>
					</template>
					<template v-slot:item.删除="{ item }">
						<v-icon large text @click="删除(item)">mdi-delete</v-icon>
					</template>
				</v-data-table>
			</v-card>
		</v-tabs-window-item>
        <v-tabs-window-item class="mx-10">

				<template v-for="(item, index) in parsedOutput" :key="index">
					<template v-if="item.contentType === 'article'">
						<v-card elevation="5" class="my-3">
							<template v-slot:title>
								{{ item.title }}
							</template>
							<template v-slot:subtitle>
								自动整理资料
							</template>
							<template v-slot:text>
								{{ item.content }}
							</template>
							<v-card-actions>
								<v-btn class="ma-2" size="large"
									@click="item.isAdded=true;articles.unshift({content: item.content,title: item.title,use: false});"
									:disabled="item.isAdded">确认添加
								</v-btn>
							</v-card-actions>
						</v-card>
					</template>
					<think v-else-if="item.contentType === 'think'">
						{{ item.content }}
					</think>
					<div v-else class="md" v-html="item.content"></div>
				</template>
				<v-btn v-if="parsedOutput.length>0" class="ma-2" size="large" @click="f全部添加()">
					全部添加
				</v-btn>
		</v-tabs-window-item>
	  </v-tabs-window>
			<v-dialog v-model="show_dialog" persistent max-width="600px">
				<v-card class="ma-0 pa0">
					<v-card-title>
						<span class="text-h5">智能添加</span>
					</v-card-title>
					<v-card-text>
						<v-container>
							<v-textarea autofocus v-model="dialog_input" rows="5" hide-details="auto"
								@keypress.enter="show_dialog = false;智能添加()"></v-textarea>
						</v-container>
					</v-card-text>
					<v-card-actions>
						<v-spacer></v-spacer>
						<v-btn color="blue darken-1" text @click="show_dialog = false;">
							取消
						</v-btn>
						<v-btn color="blue darken-1" text @click="show_dialog = false;智能添加()">
							确认
						</v-btn>
					</v-card-actions>
				</v-card>
			</v-dialog>
			<v-snackbar v-model="b显示提示文本" :timeout="1300" style="white-space: pre-line">{{s提示文本}}</v-snackbar>
		</v-app>

	</div>
	<script src="wd_sdk.js"></script>
	<script>
		const vuetify = Vuetify.createVuetify({
			defaults: {
				VBtn: {
					color: 'primary',
					variant: 'tonal'
				},
				VCheckbox: {
					color: 'primary',
					density: 'compact'
				},
			}
		})
		init = async () => {
			articlesStorage = new clyDBStorage('articles');
			let articles = await articlesStorage["main"]
			articles = articles ? JSON.parse(articles) : init_articles
			let data = {
				articles: articles,
				b显示提示文本: false,
				s提示文本: "",
				show_dialog: false,
				dialog_input: "",
				s输出: "",
				loading: false,
				tab: 0,
			}
			app = await Vue.createApp({
				data() {
					return data
				},
				methods: {
					parseOutput(input) {
						if (!input) return [];
						const tempDiv = document.createElement('div');
						tempDiv.innerHTML = input;
						const nodes = Array.from(tempDiv.childNodes);
						const result = [];
						nodes.forEach(node => {
							if (node.nodeType === Node.TEXT_NODE && node.textContent.trim()) {
								// 纯文本节点
								result.push({
									contentType: 'text',
									content: node.textContent.trim()
								});
							} else if (node.nodeType === Node.ELEMENT_NODE) {
								if (node.tagName === 'article'.toUpperCase()) {
									const title = node.querySelector('title')?.textContent || '';
									const content = node.querySelector('content')?.textContent || '';

									result.push({
										contentType: 'article',
										title: title,
										content: content
									});
								} else if (node.tagName === 'THINK') {
									// think节点
									result.push({
										contentType: 'think',
										content: node.textContent || ''
									});
								} else {
									// 其他HTML标签作为纯文本处理
									result.push({
										contentType: 'text',
										content: node.outerHTML
									});
								}
							}
						});

						// 如果没有解析到任何有效节点，返回原始文本
						if (result.length === 0) {
							return [{
								contentType: 'text',
								content: input
							}];
						}

						return result;
					},
					async removeAllData() {
						if (confirm('确认清空')) {
							localStorage.clear()
							this.removeAllDB(true)
						}
					},
					async removeAllDB(noConfirm = false) {
						if (noConfirm || confirm('确认清空')) {
							this.articles = []
						}
					},
					删除: function (item) {
						if (confirm("确认删除[" + item.title + "]?")) {
							app.articles.splice(app.articles.indexOf(item), 1)
						}
					},
					removeMarkdownFormatting(text) {
						return text
							.replace(/^#+\s+/gm, '')
							.replace(/^[\*\-+]\s+/gm, '')
							// 去除粗体 (**bold** 或 __bold__)
							.replace(/(\*\*|__)(.*?)\1/g, '$2')
							// 去除斜体 (*italic* 或 _italic_)
							.replace(/(\*|_)(.*?)\1/g, '$2')
							// 去除引用 (> 引用内容)
							.replace(/^>\s+/gm, '')
							// 去除水平线 (---, ***, ___)
							.replace(/^[-*_]{3,}\s*$/gm, '')
							.replace(/<[^>]*>/g, '')
							// 去除多余的空行
							.replace(/[\n\s]+\n/g, '\n')
					},
					智能添加: async function () {
						let input_prompt = `你的工作是提取知识，可以多次以下使用命令，以XML标签调用资料增加工具：
<article><title>一级目录（应当为资料类型，如科学）/资料的标题（应当为单一独立知识点）</title><content>资料的内容（一般不超过1000字，过长时应当分割为多个资料）</content></article>
不要在<article>标签外进行任何输出，以下是需要被学习的文章：
${this.dialog_input}`
						this.dialog_input = ""
						let old = "<article><title>"
						let prompt = make_prompt(input_prompt, templates.skip_think_prompt + old)
						app.loading = true
						let stops = [templates.eos]
						app.tab=1
						await send_prompt(prompt, stops, (s) => {
							app.s输出 = old + s
						})
						app.loading = false
					},

					导出: async function () {
						const blob = new Blob([await articlesStorage["main"]], {
							type: "text/plain;charset=utf-8"
						})
						const objectURL = URL.createObjectURL(blob)
						const aTag = document.createElement('a')
						aTag.href = objectURL
						aTag.download = Date.now() + "-资料备份.json"
						aTag.click()
					},
					导入: async function () {
						let contents = ''
						await new Promise(resolve => {
							let input = document.createElement('input')
							input.type = 'file'
							input.accept = '.json'
							input.onchange = function () {
								var file = input.files[0];
								var reader = new FileReader();
								reader.onload = function (e) {
									contents = e.target.result;
									resolve()
								};
								reader.readAsText(file);
							}
							input.click()
						})

						await (articlesStorage["main"] = contents)
						app.articles = JSON.parse(contents)
					},
					f全部添加() {
						let items = app.parsedOutput

						let successCount = 0;
						items.forEach(item => {
							if (item.isAdded) {

							} else {
								successCount++;
								item.isAdded = true;
								this.articles = [{ content: item.content, title: item.title, use: false }, ...this.articles]
							}
						});

						let message = `已完成${successCount}个`;
						alert(message);
					},
				},
				computed: {
					parsedOutput() {
						return this.parseOutput(this.s输出)
					}
				},
				watch: {
					articles: {
						handler: throttle(async () => {
							let articles = app.articles
							if (articles.length == 0) {
								articles.push({ title: '', use: false, content: `` })
							} else if (articles[articles.length - 1].title != '') {
								articles.push({ title: '', use: false, content: `` })
							}
							await (articlesStorage["main"] = JSON.stringify(articles))
							on_articles_change_lock = true
							on_articles_change.postMessage('1');
							setTimeout(() => { on_articles_change_lock = false }, 500)
							alert('已保存')
						}, 1000),
						deep: true
					}
				}
			}).use(vuetify).mount('#app')
		}
		init()

		_alert = alert
		alert = (text) => {
			app.s提示文本 = text; //.replace(/\n/g,"<br>")
			app.b显示提示文本 = true;
		}
	</script>
</body>

</html>