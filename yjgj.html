<html>

<head>
	<title>研究工具</title>
	<meta charset="utf-8">
	<meta name="apple-mobile-web-app-capable" content="yes" />
	<meta name="mobile-web-app-capable" content="yes" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<meta name="viewport" content="width=650,  user-scalable=no">
	<link rel="shortcut icon" href="#" />

	<link href="static/mdi_font/css/materialdesignicons.min.css" rel="stylesheet">
	<link href="static/vuetify.min.css" rel="stylesheet">
	<script src="static/vue.js"></script>
	<script src="static/vuetify.min.js"></script>
	<script src="static/markdown-it.min.js"></script>
	<style>
		div {
			transition: all 0.3s;
		}

		.v-sheet.v-card {
			margin: 20px;
			padding: 10px;
		}

		input {
			width: 100%;
		}

		pre {
			white-space: break-spaces;
		}

		.logo-center {
			left: calc(50% - 140px);
			width: 260px;
			font-size: 4em;
			padding-top: 2em;
			padding-bottom: 0.8em;
			position: relative;
		}

		.logo-left {
			left: 20px;
			width: 100%;
			font-size: xx-large;
			position: relative;
		}

		[v-cloak] {
			display: none;
		}

		.v-application--wrap {
			display: unset;
		}

		tr th:first-child {
			width: 12em;
		}

		svg#markmap {
			background-color: white;
		}

		.v-menu__content.theme--light.v-menu__content--fixed {
			box-shadow: none;

			padding-bottom: 60px
		}

		.v-menu__content button {
			display: block;
			margin: 10px;
		}
	</style>
</head>

<body>
	<div id="app" v-cloak>
		<v-app>
			<v-menu open-on-hover transition="slide-y-transition">
				<template v-slot:activator="{ on, attrs }">
					<v-btn color="primary" dark v-bind="attrs" v-on="on" fab dark
						style="position:fixed;right:20px;bottom:20px;z-index: 10;">
						<v-icon>mdi-plus</v-icon>
					</v-btn>
				</template>

				<v-btn color="blue" dark size="x-large" @click="f复制正文()" :loading="loading" v-if="l分析步骤.length"
					class="ma-2">
					复制为文本
				</v-btn>

				<v-btn color="blue" dark size="x-large" @click="f打开研究结果()">
					打开
				</v-btn>
				<v-btn color="blue" dark size="x-large" @click="f保存导图()" v-if="l分析步骤.length">
					保存导图
				</v-btn>
				<v-btn color="blue" dark size="x-large" @click="f保存文本()" v-if="l分析步骤.length">
					{{shift_down?"保存MD":"保存文本"}}
				</v-btn>
				<v-btn color="blue" dark size="x-large" @click="window.mm.fit()" v-if="l分析步骤.length">
					导图视图
				</v-btn>
				<v-btn color="blue" dark size="x-large"
					@click="mm.svg._groups[0][0].requestFullscreen();setTimeout(()=>mm.fit(),500)" v-if="l分析步骤.length">
					全屏导图
				</v-btn>
			</v-menu>
			<div :class="(s文本.length!=0)?'logo-left':'logo-center'"><b style="color: blue;"></b><b>研究工具</b></div>
			<v-card :elevation="(s文本.length!=0)?2:0">
				<v-card-text>
					<v-row no-gutters>
						<v-col cols="11">
							<v-text-field autofocus v-model="s文本" label="输入文本" rows="1"
								hide-details="auto"></v-text-field>
							<v-textarea v-model="s分析结果" v-if="s分析结果.length" label="分析结果" rows="3" hide-details="auto">
							</v-textarea>
						</v-col>
						<v-col cols="1" style="text-align: right;">
							<v-btn color="blue" dark size="x-large" @click="f分析()" :loading="loading" class="ma-2">
								分析
							</v-btn>
							<v-btn color="blue" dark size="x-large" @click="f思考()" :loading="loading"
								v-if="l分析步骤.length" class="ma-2">
								重新思考
							</v-btn>
						</v-col>
					</v-row>
					<v-data-table :loading="loading" v-if="s分析结果.length" :headers=" [{ text: '步骤', value: 'title', sortable: false, },
					{ text: '内容', value: 'content', sortable: false, },
					]" :items="l分析步骤" :items-per-page="2000" hide-default-footer style="margin: 10px;">
						<template v-slot:item.title="{ item }">
							<input v-model="item.title"></input>
						</template>
						</template>
						<template v-slot:item.content="{ item }">
							<input v-model="item.content"></input>
						</template>
						</template>
					</v-data-table>
				</v-card-text>
			</v-card>

			<script src="static/<EMAIL>"></script>
			<script src="static/markmap-view.js"></script>
			<script src="static/markmap-lib.js"></script>

			<v-card elevation="2" v-if="l分析步骤.length">
				<svg id="markmap" style="width: 100%; height: 300px"></svg> </v-col>
			</v-card>

			<v-row no-gutters>
				<v-col cols="12" v-for="result in results">
					<v-card elevation="2">
						<v-row>
							<v-col cols="10">
								<p v-text="result.title"></p>
							</v-col>
							<v-col cols="2" style="text-align: right;">
								<v-icon text color="blue" @click="edit(result)">
									mdi-pencil
								</v-icon>
							</v-col>
						</v-row>
						<v-divider></v-divider>
						<v-card-text>
							<p v-text="result.content"></p>
							<pre v-text="result.result"></pre>

						</v-card-text>
					</v-card>
				</v-col>
			</v-row>
			<v-snackbar v-model="snackbar" :timeout="3000" style="white-space: pre-line">{{snackbar_text}}</v-snackbar>
			<v-dialog v-model="show_dialog" persistent max-width="600px">
				<v-card class="ma-0 pa0">
					<v-card-title>
						<span class="text-h5">{{dialog_title}}</span>
					</v-card-title>
					<v-card-text>
						<v-container>
							<v-textarea autofocus v-model="dialog_input" rows="5" hide-details="auto"
								@keypress.enter="show_dialog = false;window.dialog_input_resolver()"></v-textarea>
						</v-container>
					</v-card-text>
					<v-card-actions>
						<v-spacer></v-spacer>
						<v-btn color="blue darken-1" text
							@click="show_dialog = false;dialog_input='';window.dialog_input_resolver()">
							取消
						</v-btn>
						<v-btn color="blue darken-1" text @click="show_dialog = false;window.dialog_input_resolver()">
							确认
						</v-btn>
					</v-card-actions>
				</v-card>
			</v-dialog>
		</v-app>
	</div>
	<script>
		let md = new markdownit();
		md.disable(['link', 'image'])
		app = new Vue({
			el: '#app',
			vuetify: new Vuetify(),
			data: () => ({
				s文本: "",
				s分析结果: "",
				loading: false,
				temperature: 0.8,
				top_p: 0.2,
				max_length: 2000,
				l分析步骤: [],
				results: [],
				//显示对话框
				show_dialog: false,
				//对话框标题
				dialog_title: "",
				//对话框用户输入
				dialog_input: "",
				// 是否显示snackbar
				snackbar: false,
				// snackbar的文本
				snackbar_text: "",
				shift_down:false
			}),
			methods: {
			},
			watch: {
			}
		})

		f分析 = async (old = '[\n{"') => {
			app.s文本 = app.s文本.replace(/ +/g, ' ').replace(/[\r\n]+/g, '\n').replace(/^[\n\s\t]+/, '').replace(/[\n\s\t]+$/, '')
			let prompt = app.s文本
			app.loading = true
			await send_prompt(make_prompt(prompt,templates.skip_think_prompt +old, `针对用户问题一步一步地推理。对于每个步骤，提供一个标题，描述在该步骤中所做的工作以及内容。
以JSON格式响应，包含"title"、"content"键。
使用尽可能多的推理步骤。
响应示例:
[
{"title":"识别关键信息",
"content":"……"},
{"title":"综合分析",
"content":"……"},
{"title":"总结",
"content":"……"}
]`),
				[templates.eos], (s) => {
					app.s分析结果 = old + s

				})
			let rst_json = JSON.parse(app.s分析结果.replaceAll('"\n"', '",\n"',))
			app.l分析步骤 = rst_json
			f思考()
		}
		f思考 = async () => {
			const old = '1. '
			app.loading = true
			let rst_json = app.results = app.l分析步骤.map(i => ({ title: i.title, content: i.content, result: '' }))
			console.log(rst_json)
			let diedaijieguo = ''

			for (let i = 0; i < rst_json.length; i++) {
				let element = rst_json[i]
				if (i != 0) {
					diedaijieguo += '\n'
				}

				diedaijieguo = diedaijieguo + "步骤" + (i + 1).toString() + "：" + rst_json[i].title + "\n任务：" + rst_json[i].content

				let result = await send_prompt(make_prompt(diedaijieguo + '\n给出当前步骤结果',templates.skip_think_prompt+ old),
					[templates.eos], (s) => {
						element.result = old + s
						window.b需重新生成 = true
					})
				diedaijieguo = diedaijieguo + "\n结果：" + result;
			}
			app.loading = false
		}

		setInterval(() => {
			if (window.b需重新生成) {
				window.b需重新生成 = false
				f生成导图()
			}
		}, 500)
		const { Markmap, loadCSS, loadJS } = window.markmap
		f清除导图 = async (e) => {
			const svgEl = document.querySelector('#markmap');
			while (svgEl.children.length > 0) svgEl.removeChild(svgEl.children[0])
			delete window.mm
		}
		f生成导图 = async () => {
			const transformer = new markmap.Transformer()
			const { root, features } = transformer.transform('# 思考\n' + app.results.map(i => "## " + i.title + '\n' + i.result).join('\n'))
			if (window.mm) {
				window.mm.setData(root);
				window.mm.fit();
			} else {
				window.mm = Markmap.create('#markmap', null, root)
				mm.options.fitRatio = 0.99
				mm.options.duration = 15
				//toolbar = markmap.Toolbar.create(mm);
				//document.body.append(toolbar.el);
			}
		}
		const toChineseNum = (num) => {
			let changeNum = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
			let unit = ["", "十", "百", "千", "万"];
			num = parseInt(num);
			let getWan = (temp) => {
				let strArr = temp.toString().split("").reverse();
				let newNum = "";
				for (var i = 0; i < strArr.length; i++) {
					newNum = (i == 0 && strArr[i] == 0 ? "" : (i > 0 && strArr[i] == 0 && strArr[i - 1] == 0 ? "" : changeNum[strArr[i]] + (strArr[i] == 0 ? unit[0] : unit[i]))) + newNum;
				}
				return newNum;
			}
			let overWan = Math.floor(num / 10000);
			let noWan = num % 10000;
			if (noWan.toString().length < 4) noWan = "0" + noWan;
			let result = overWan ? getWan(overWan) + "万" + getWan(noWan) : getWan(num)
			return result.replace(/^一十/, '十')
		}
		f复制正文 = async () => {
			let r = app.results.map(e => `${e.title}\n${e.content}\n${e.result}`)
			let result = ''
			for (i in r) {
				result += toChineseNum(1 + 1 * i) + "、" + r[i] + '\n\n'

			}
			copy(result)
		}
		k=async function (e) {
			app.shift_down = e.shiftKey
			if (e.key.toLowerCase() == 's' && (navigator.platform.match("Mac") ? e.metaKey : e.ctrlKey)) {
				e.preventDefault();
				f保存导图()
			}
			if (e.key.toLowerCase() == 'l' && (navigator.platform.match("Mac") ? e.metaKey : e.ctrlKey)) {
				e.preventDefault();
				f打开研究结果()

			}
		}
		document.addEventListener('keydown', k);
		document.addEventListener('keyup', k);
		function string2XML(xmlString) {
			var parser = new DOMParser();
			var xmlObject = parser.parseFromString(xmlString, "text/xml");
			return xmlObject;
		}
		f打开研究结果 = async () => {
			let contents = ''
			await new Promise(resolve => {
				let input = document.createElement('input')
				input.type = 'file'
				input.accept = '.svg'
				input.onchange = function () {
					var file = input.files[0];
					var reader = new FileReader();
					reader.onload = function (e) {
						contents = e.target.result;
						resolve()
					};
					reader.readAsText(file);
				}
				input.click()
			})
			read_result_str(string2XML(contents).children[0].getAttribute('data'))
		}
		get_result_str = () => {
			return JSON.stringify({ 分析结果: app.s分析结果, 文本: app.s文本, results: app.results })
		}
		read_result_str = (s) => {
			result_json = JSON.parse(s)
			app.s分析结果 = result_json.分析结果
			app.s文本 = result_json.文本
			app.results = result_json.results

			let rst_json = JSON.parse(app.s分析结果.replaceAll('"\n"', '",\n"',))
			app.l分析步骤 = rst_json
			window.b需重新生成 = true
		}
		alert = (text) => {
			app.snackbar_text = text; //.replace(/\n/g,"<br>")
			app.snackbar = true;
		}

		//获取用户输入
		input = async (title = '请输入', input = '') => {
			app.dialog_title = title
			app.dialog_input = input
			app.show_dialog = true

			await new Promise(resolve => {
				window.dialog_input_resolver = resolve
			})
			return app.dialog_input
		}
		//编辑会话内容
		edit = async (current_conversation) => {
			let s修改后的内容 = await input('请输入修改后的内容', current_conversation.result)
			if (s修改后的内容) {
				current_conversation.result = s修改后的内容
				alert('修改成功')
			} else
				alert('取消修改')
		}
		f保存导图 = async () => {
			// 获取 SVG 根元素
			const svgElement = mm.svg._groups[0][0].cloneNode(1)

			// 设置 SVG 的宽度和高度为 100%
			svgElement.setAttribute('style', "width: 100%; height: 100%; -webkit-tap-highlight-color: rgba(0, 0, 0, 0);");
			svgElement.setAttribute('data', get_result_str());

			// 序列化 SVG 元素为字符串
			const svgContent = new XMLSerializer().serializeToString(svgElement);

			// 创建一个 Blob 对象，包含修改后的 SVG 内容
			const blob = new Blob([svgContent], {
				type: "image/svg+xml;charset=utf-8"
			});

			const objectURL = URL.createObjectURL(blob)
			const aTag = document.createElement('a')
			aTag.href = objectURL
			aTag.download = app.s文本 + ".svg"
			aTag.click()
			URL.revokeObjectURL(objectURL)
		}
		f保存文本 = async () => {

			let result = ''
			if (app.shift_down) {
				result = '# ' + app.s文本 + '\n' + app.results.map(i => "\n## " + i.title + '\n' + i.result).join('\n')
				const blob = new Blob([result], {
					type: "txt;charset=utf-8"
				});

				const objectURL = URL.createObjectURL(blob)
				const aTag = document.createElement('a')
				aTag.href = objectURL
				aTag.download = app.s文本 + ".md"
				aTag.click()
				URL.revokeObjectURL(objectURL)
			} else {
				result += app.s文本 + '\n'
				let r = app.results.map(e => `${e.title}\n${e.content}\n${e.result}`)
				for (i in r) {
					result += toChineseNum(1 + 1 * i) + "、" + r[i] + '\n\n'
				}
				const blob = new Blob([result], {
					type: "txt;charset=utf-8"
				});

				const objectURL = URL.createObjectURL(blob)
				const aTag = document.createElement('a')
				aTag.href = objectURL
				aTag.download = app.s文本 + ".txt"
				aTag.click()
				URL.revokeObjectURL(objectURL)
			}
		}
	</script>
	<script src="wd_sdk.js"></script>
</body>

</html>