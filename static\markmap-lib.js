/**
 * Minified by jsDelivr using Terser v5.19.2.
 * Original file: /npm/markmap-lib@0.17.2/dist/browser/index.iife.js
 *
 * Do NOT use SRI with dynamically generated files! More information: https://www.jsdelivr.com/using-sri-with-dynamic-files
 */
!function(e,t){"use strict";const n={jsdelivr:e=>`https://cdn.jsdelivr.net/npm/${e}`,unpkg:e=>`https://unpkg.com/${e}`};class s{constructor(){this.providers={...n},this.provider="jsdelivr"}async getFastestProvider(e=5e3,t="npm2url/dist/index.cjs"){const n=new AbortController;let s=0;try{return await new Promise(((r,i)=>{Promise.all(Object.entries(this.providers).map((async([e,s])=>{try{await async function(e,t){const n=await fetch(e,{signal:t});if(!n.ok)throw n;await n.text()}(s(t),n.signal),r(e)}catch{}}))).then((()=>i(new Error("All providers failed")))),s=setTimeout(i,e,new Error("Timed out"))}))}finally{n.abort(),clearTimeout(s)}}async findFastestProvider(e,t){return this.provider=await this.getFastestProvider(e,t),this.provider}setProvider(e,t){t?this.providers[e]=t:delete this.providers[e]}getFullUrl(e,t=this.provider){if(e.includes("://"))return e;const n=this.providers[t];if(!n)throw new Error(`Provider ${t} not found`);return n(e)}}class r{constructor(){this.listeners=[]}tap(e){return this.listeners.push(e),()=>this.revoke(e)}revoke(e){const t=this.listeners.indexOf(e);t>=0&&this.listeners.splice(t,1)}revokeAll(){this.listeners.splice(0)}call(...e){for(const t of this.listeners)t(...e)}}function i(){}Math.random().toString(36).slice(2,8);
/*! @gera2ld/jsx-dom v2.2.2 | ISC License */
const o=1,a=2,c="http://www.w3.org/2000/svg",l="http://www.w3.org/1999/xlink",u={show:l,actuate:l,href:l},h=e=>"string"==typeof e||"number"==typeof e,d=e=>(null==e?void 0:e.vtype)===o,p=e=>(null==e?void 0:e.vtype)===a;function f(e,t,...n){return function(e,t){let n;if("string"==typeof e)n=o;else{if("function"!=typeof e)throw new Error("Invalid VNode type");n=a}return{vtype:n,type:e,props:t}}(e,t=Object.assign({},t,{children:1===n.length?n[0]:n}))}function m(e){return e.children}const E={isSvg:!1};function T(e,t){Array.isArray(t)||(t=[t]),(t=t.filter(Boolean)).length&&e.append(...t)}const _={className:"class",labelFor:"for"};function A(e,t,n,s){if(t=_[t]||t,!0===n)e.setAttribute(t,"");else if(!1===n)e.removeAttribute(t);else{const r=s?u[t]:void 0;void 0!==r?e.setAttributeNS(r,t,n):e.setAttribute(t,n)}}function g(e,t){return Array.isArray(e)?e.map((e=>g(e,t))).reduce(((e,t)=>e.concat(t)),[]):C(e,t)}function C(e,t=E){if(null==e||"boolean"==typeof e)return null;if(e instanceof Node)return e;if(p(e)){const{type:n,props:s}=e;if(n===m){const e=document.createDocumentFragment();if(s.children){T(e,g(s.children,t))}return e}return C(n(s),t)}if(h(e))return document.createTextNode(`${e}`);if(d(e)){let n;const{type:s,props:r}=e;if(t.isSvg||"svg"!==s||(t=Object.assign({},t,{isSvg:!0})),n=t.isSvg?document.createElementNS(c,s):document.createElement(s),function(e,t,n){for(const s in t)if("key"!==s&&"children"!==s&&"ref"!==s)if("dangerouslySetInnerHTML"===s)e.innerHTML=t[s].__html;else if("innerHTML"===s||"textContent"===s||"innerText"===s||"value"===s&&["textarea","select"].includes(e.tagName)){const n=t[s];null!=n&&(e[s]=n)}else s.startsWith("on")?e[s.toLowerCase()]=t[s]:A(e,s,t[s],n.isSvg)}(n,r,t),r.children){let e=t;t.isSvg&&"foreignObject"===s&&(e=Object.assign({},e,{isSvg:!1}));const i=g(r.children,e);null!=i&&T(n,i)}const{ref:i}=r;return"function"==typeof i&&i(n),n}throw new Error("mount: Invalid Vnode!")}function b(...e){return C(f(...e))}const N=function(e){const t={};return function(...n){const s=`${n[0]}`;let r=t[s];return r||(r={value:e(...n)},t[s]=r),r.value}}((e=>{document.head.append(b("link",{rel:"preload",as:"script",href:e}))})),I={};async function k(e,t){var n;const s="script"===e.type&&(null==(n=e.data)?void 0:n.src)||"";if(e.loaded||(e.loaded=I[s]),!e.loaded){const n=function(){const e={};return e.promise=new Promise(((t,n)=>{e.resolve=t,e.reject=n})),e}();if(e.loaded=n.promise,"script"===e.type&&(document.head.append(b("script",{...e.data,onLoad:()=>n.resolve(),onError:n.reject})),s?I[s]=e.loaded:n.resolve()),"iife"===e.type){const{fn:s,getParams:r}=e.data;s(...(null==r?void 0:r(t))||[]),n.resolve()}}await e.loaded}async function S(e,t){e.forEach((e=>{var t;"script"===e.type&&(null==(t=e.data)?void 0:t.src)&&N(e.data.src)})),t={getMarkmap:()=>window.markmap,...t};for(const n of e)await k(n,t)}function D(e){return{type:"script",data:{src:e}}}function y(e){return{type:"stylesheet",data:{href:e}}}const O={xml:!1,decodeEntities:!0},L={_useHtmlParser2:!0,xmlMode:!0};function R(e){return(null==e?void 0:e.xml)?"boolean"==typeof e.xml?L:{...L,...e.xml}:null!=e?e:void 0}var M,v;(v=M||(M={})).Root="root",v.Text="text",v.Directive="directive",v.Comment="comment",v.Script="script",v.Style="style",v.Tag="tag",v.CDATA="cdata",v.Doctype="doctype";const x=M.Root,w=M.Text,P=M.Directive,F=M.Comment,B=M.Script,U=M.Style,H=M.Tag,G=M.CDATA,q=M.Doctype;let $=class{constructor(){this.parent=null,this.prev=null,this.next=null,this.startIndex=null,this.endIndex=null}get parentNode(){return this.parent}set parentNode(e){this.parent=e}get previousSibling(){return this.prev}set previousSibling(e){this.prev=e}get nextSibling(){return this.next}set nextSibling(e){this.next=e}cloneNode(e=!1){return ie(this,e)}};class Y extends ${constructor(e){super(),this.data=e}get nodeValue(){return this.data}set nodeValue(e){this.data=e}}class j extends Y{constructor(){super(...arguments),this.type=M.Text}get nodeType(){return 3}}class K extends Y{constructor(){super(...arguments),this.type=M.Comment}get nodeType(){return 8}}class V extends Y{constructor(e,t){super(t),this.name=e,this.type=M.Directive}get nodeType(){return 1}}class z extends ${constructor(e){super(),this.children=e}get firstChild(){var e;return null!==(e=this.children[0])&&void 0!==e?e:null}get lastChild(){return this.children.length>0?this.children[this.children.length-1]:null}get childNodes(){return this.children}set childNodes(e){this.children=e}}class Q extends z{constructor(){super(...arguments),this.type=M.CDATA}get nodeType(){return 4}}let W=class extends z{constructor(){super(...arguments),this.type=M.Root}get nodeType(){return 9}};class X extends z{constructor(e,t,n=[],s=("script"===e?M.Script:"style"===e?M.Style:M.Tag)){super(n),this.name=e,this.attribs=t,this.type=s}get nodeType(){return 1}get tagName(){return this.name}set tagName(e){this.name=e}get attributes(){return Object.keys(this.attribs).map((e=>{var t,n;return{name:e,value:this.attribs[e],namespace:null===(t=this["x-attribsNamespace"])||void 0===t?void 0:t[e],prefix:null===(n=this["x-attribsPrefix"])||void 0===n?void 0:n[e]}}))}}function Z(e){return(t=e).type===M.Tag||t.type===M.Script||t.type===M.Style;var t}function J(e){return e.type===M.CDATA}function ee(e){return e.type===M.Text}function te(e){return e.type===M.Comment}function ne(e){return e.type===M.Directive}function se(e){return e.type===M.Root}function re(e){return Object.prototype.hasOwnProperty.call(e,"children")}function ie(e,t=!1){let n;if(ee(e))n=new j(e.data);else if(te(e))n=new K(e.data);else if(Z(e)){const s=t?oe(e.children):[],r=new X(e.name,{...e.attribs},s);s.forEach((e=>e.parent=r)),null!=e.namespace&&(r.namespace=e.namespace),e["x-attribsNamespace"]&&(r["x-attribsNamespace"]={...e["x-attribsNamespace"]}),e["x-attribsPrefix"]&&(r["x-attribsPrefix"]={...e["x-attribsPrefix"]}),n=r}else if(J(e)){const s=t?oe(e.children):[],r=new Q(s);s.forEach((e=>e.parent=r)),n=r}else if(se(e)){const s=t?oe(e.children):[],r=new W(s);s.forEach((e=>e.parent=r)),e["x-mode"]&&(r["x-mode"]=e["x-mode"]),n=r}else{if(!ne(e))throw new Error(`Not implemented yet: ${e.type}`);{const t=new V(e.name,e.data);null!=e["x-name"]&&(t["x-name"]=e["x-name"],t["x-publicId"]=e["x-publicId"],t["x-systemId"]=e["x-systemId"]),n=t}}return n.startIndex=e.startIndex,n.endIndex=e.endIndex,null!=e.sourceCodeLocation&&(n.sourceCodeLocation=e.sourceCodeLocation),n}function oe(e){const t=e.map((e=>ie(e,!0)));for(let e=1;e<t.length;e++)t[e].prev=t[e-1],t[e-1].next=t[e];return t}const ae={withStartIndices:!1,withEndIndices:!1,xmlMode:!1};class ce{constructor(e,t,n){this.dom=[],this.root=new W(this.dom),this.done=!1,this.tagStack=[this.root],this.lastNode=null,this.parser=null,"function"==typeof t&&(n=t,t=ae),"object"==typeof e&&(t=e,e=void 0),this.callback=null!=e?e:null,this.options=null!=t?t:ae,this.elementCB=null!=n?n:null}onparserinit(e){this.parser=e}onreset(){this.dom=[],this.root=new W(this.dom),this.done=!1,this.tagStack=[this.root],this.lastNode=null,this.parser=null}onend(){this.done||(this.done=!0,this.parser=null,this.handleCallback(null))}onerror(e){this.handleCallback(e)}onclosetag(){this.lastNode=null;const e=this.tagStack.pop();this.options.withEndIndices&&(e.endIndex=this.parser.endIndex),this.elementCB&&this.elementCB(e)}onopentag(e,t){const n=this.options.xmlMode?M.Tag:void 0,s=new X(e,t,void 0,n);this.addNode(s),this.tagStack.push(s)}ontext(e){const{lastNode:t}=this;if(t&&t.type===M.Text)t.data+=e,this.options.withEndIndices&&(t.endIndex=this.parser.endIndex);else{const t=new j(e);this.addNode(t),this.lastNode=t}}oncomment(e){if(this.lastNode&&this.lastNode.type===M.Comment)return void(this.lastNode.data+=e);const t=new K(e);this.addNode(t),this.lastNode=t}oncommentend(){this.lastNode=null}oncdatastart(){const e=new j(""),t=new Q([e]);this.addNode(t),e.parent=t,this.lastNode=e}oncdataend(){this.lastNode=null}onprocessinginstruction(e,t){const n=new V(e,t);this.addNode(n)}handleCallback(e){if("function"==typeof this.callback)this.callback(e,this.dom);else if(e)throw e}addNode(e){const t=this.tagStack[this.tagStack.length-1],n=t.children[t.children.length-1];this.options.withStartIndices&&(e.startIndex=this.parser.startIndex),this.options.withEndIndices&&(e.endIndex=this.parser.endIndex),t.children.push(e),n&&(e.prev=n,n.next=e),e.parent=t,this.lastNode=null}}const le=new Uint16Array('ᵁ<Õıʊҝջאٵ۞ޢߖࠏ੊ઑඡ๭༉༦჊ረዡᐕᒝᓃᓟᔥ\0\0\0\0\0\0ᕫᛍᦍᰒᷝ὾⁠↰⊍⏀⏻⑂⠤⤒ⴈ⹈⿎〖㊺㘹㞬㣾㨨㩱㫠㬮ࠀEMabcfglmnoprstu\\bfms¦³¹ÈÏlig耻Æ䃆P耻&䀦cute耻Á䃁reve;䄂Āiyx}rc耻Â䃂;䐐r;쀀𝔄rave耻À䃀pha;䎑acr;䄀d;橓Āgp¡on;䄄f;쀀𝔸plyFunction;恡ing耻Å䃅Ācs¾Ãr;쀀𝒜ign;扔ilde耻Ã䃃ml耻Ä䃄ЀaceforsuåûþėĜĢħĪĀcrêòkslash;或Ŷöø;櫧ed;挆y;䐑ƀcrtąċĔause;戵noullis;愬a;䎒r;쀀𝔅pf;쀀𝔹eve;䋘còēmpeq;扎܀HOacdefhilorsuōőŖƀƞƢƵƷƺǜȕɳɸɾcy;䐧PY耻©䂩ƀcpyŝŢźute;䄆Ā;iŧŨ拒talDifferentialD;慅leys;愭ȀaeioƉƎƔƘron;䄌dil耻Ç䃇rc;䄈nint;戰ot;䄊ĀdnƧƭilla;䂸terDot;䂷òſi;䎧rcleȀDMPTǇǋǑǖot;抙inus;抖lus;投imes;抗oĀcsǢǸkwiseContourIntegral;戲eCurlyĀDQȃȏoubleQuote;思uote;怙ȀlnpuȞȨɇɕonĀ;eȥȦ户;橴ƀgitȯȶȺruent;扡nt;戯ourIntegral;戮ĀfrɌɎ;愂oduct;成nterClockwiseContourIntegral;戳oss;樯cr;쀀𝒞pĀ;Cʄʅ拓ap;才րDJSZacefiosʠʬʰʴʸˋ˗ˡ˦̳ҍĀ;oŹʥtrahd;椑cy;䐂cy;䐅cy;䐏ƀgrsʿ˄ˇger;怡r;憡hv;櫤Āayː˕ron;䄎;䐔lĀ;t˝˞戇a;䎔r;쀀𝔇Āaf˫̧Ācm˰̢riticalȀADGT̖̜̀̆cute;䂴oŴ̋̍;䋙bleAcute;䋝rave;䁠ilde;䋜ond;拄ferentialD;慆Ѱ̽\0\0\0͔͂\0Ѕf;쀀𝔻ƀ;DE͈͉͍䂨ot;惜qual;扐blèCDLRUVͣͲ΂ϏϢϸontourIntegraìȹoɴ͹\0\0ͻ»͉nArrow;懓Āeo·ΤftƀARTΐΖΡrrow;懐ightArrow;懔eåˊngĀLRΫτeftĀARγιrrow;柸ightArrow;柺ightArrow;柹ightĀATϘϞrrow;懒ee;抨pɁϩ\0\0ϯrrow;懑ownArrow;懕erticalBar;戥ǹABLRTaВЪаўѿͼrrowƀ;BUНОТ憓ar;椓pArrow;懵reve;䌑eft˒к\0ц\0ѐightVector;楐eeVector;楞ectorĀ;Bљњ憽ar;楖ightǔѧ\0ѱeeVector;楟ectorĀ;BѺѻ懁ar;楗eeĀ;A҆҇护rrow;憧ĀctҒҗr;쀀𝒟rok;䄐ࠀNTacdfglmopqstuxҽӀӄӋӞӢӧӮӵԡԯԶՒ՝ՠեG;䅊H耻Ð䃐cute耻É䃉ƀaiyӒӗӜron;䄚rc耻Ê䃊;䐭ot;䄖r;쀀𝔈rave耻È䃈ement;戈ĀapӺӾcr;䄒tyɓԆ\0\0ԒmallSquare;旻erySmallSquare;斫ĀgpԦԪon;䄘f;쀀𝔼silon;䎕uĀaiԼՉlĀ;TՂՃ橵ilde;扂librium;懌Āci՗՚r;愰m;橳a;䎗ml耻Ë䃋Āipժկsts;戃onentialE;慇ʀcfiosօֈ֍ֲ׌y;䐤r;쀀𝔉lledɓ֗\0\0֣mallSquare;旼erySmallSquare;斪Ͱֺ\0ֿ\0\0ׄf;쀀𝔽All;戀riertrf;愱cò׋؀JTabcdfgorstר׬ׯ׺؀ؒؖ؛؝أ٬ٲcy;䐃耻>䀾mmaĀ;d׷׸䎓;䏜reve;䄞ƀeiy؇،ؐdil;䄢rc;䄜;䐓ot;䄠r;쀀𝔊;拙pf;쀀𝔾eater̀EFGLSTصلَٖٛ٦qualĀ;Lؾؿ扥ess;招ullEqual;执reater;檢ess;扷lantEqual;橾ilde;扳cr;쀀𝒢;扫ЀAacfiosuڅڋږڛڞڪھۊRDcy;䐪Āctڐڔek;䋇;䁞irc;䄤r;愌lbertSpace;愋ǰگ\0ڲf;愍izontalLine;攀Āctۃۅòکrok;䄦mpńېۘownHumðįqual;扏܀EJOacdfgmnostuۺ۾܃܇܎ܚܞܡܨ݄ݸދޏޕcy;䐕lig;䄲cy;䐁cute耻Í䃍Āiyܓܘrc耻Î䃎;䐘ot;䄰r;愑rave耻Ì䃌ƀ;apܠܯܿĀcgܴܷr;䄪inaryI;慈lieóϝǴ݉\0ݢĀ;eݍݎ戬Āgrݓݘral;戫section;拂isibleĀCTݬݲomma;恣imes;恢ƀgptݿރވon;䄮f;쀀𝕀a;䎙cr;愐ilde;䄨ǫޚ\0ޞcy;䐆l耻Ï䃏ʀcfosuެ޷޼߂ߐĀiyޱ޵rc;䄴;䐙r;쀀𝔍pf;쀀𝕁ǣ߇\0ߌr;쀀𝒥rcy;䐈kcy;䐄΀HJacfosߤߨ߽߬߱ࠂࠈcy;䐥cy;䐌ppa;䎚Āey߶߻dil;䄶;䐚r;쀀𝔎pf;쀀𝕂cr;쀀𝒦րJTaceflmostࠥࠩࠬࡐࡣ঳সে্਷ੇcy;䐉耻<䀼ʀcmnpr࠷࠼ࡁࡄࡍute;䄹bda;䎛g;柪lacetrf;愒r;憞ƀaeyࡗ࡜ࡡron;䄽dil;䄻;䐛Āfsࡨ॰tԀACDFRTUVarࡾࢩࢱࣦ࣠ࣼयज़ΐ४Ānrࢃ࢏gleBracket;柨rowƀ;BR࢙࢚࢞憐ar;懤ightArrow;懆eiling;挈oǵࢷ\0ࣃbleBracket;柦nǔࣈ\0࣒eeVector;楡ectorĀ;Bࣛࣜ懃ar;楙loor;挊ightĀAV࣯ࣵrrow;憔ector;楎Āerँगeƀ;AVउऊऐ抣rrow;憤ector;楚iangleƀ;BEतथऩ抲ar;槏qual;抴pƀDTVषूौownVector;楑eeVector;楠ectorĀ;Bॖॗ憿ar;楘ectorĀ;B॥०憼ar;楒ightáΜs̀EFGLSTॾঋকঝঢভqualGreater;拚ullEqual;扦reater;扶ess;檡lantEqual;橽ilde;扲r;쀀𝔏Ā;eঽা拘ftarrow;懚idot;䄿ƀnpw৔ਖਛgȀLRlr৞৷ਂਐeftĀAR০৬rrow;柵ightArrow;柷ightArrow;柶eftĀarγਊightáοightáϊf;쀀𝕃erĀLRਢਬeftArrow;憙ightArrow;憘ƀchtਾੀੂòࡌ;憰rok;䅁;扪Ѐacefiosuਗ਼੝੠੷੼અઋ઎p;椅y;䐜Ādl੥੯iumSpace;恟lintrf;愳r;쀀𝔐nusPlus;戓pf;쀀𝕄cò੶;䎜ҀJacefostuણધભીଔଙඑ඗ඞcy;䐊cute;䅃ƀaey઴હાron;䅇dil;䅅;䐝ƀgswે૰଎ativeƀMTV૓૟૨ediumSpace;怋hiĀcn૦૘ë૙eryThiî૙tedĀGL૸ଆreaterGreateòٳessLesóੈLine;䀊r;쀀𝔑ȀBnptଢନଷ଺reak;恠BreakingSpace;䂠f;愕ڀ;CDEGHLNPRSTV୕ୖ୪୼஡௫ఄ౞಄ದ೘ൡඅ櫬Āou୛୤ngruent;扢pCap;扭oubleVerticalBar;戦ƀlqxஃஊ஛ement;戉ualĀ;Tஒஓ扠ilde;쀀≂̸ists;戄reater΀;EFGLSTஶஷ஽௉௓௘௥扯qual;扱ullEqual;쀀≧̸reater;쀀≫̸ess;批lantEqual;쀀⩾̸ilde;扵umpń௲௽ownHump;쀀≎̸qual;쀀≏̸eĀfsఊధtTriangleƀ;BEచఛడ拪ar;쀀⧏̸qual;括s̀;EGLSTవశ఼ౄోౘ扮qual;扰reater;扸ess;쀀≪̸lantEqual;쀀⩽̸ilde;扴estedĀGL౨౹reaterGreater;쀀⪢̸essLess;쀀⪡̸recedesƀ;ESಒಓಛ技qual;쀀⪯̸lantEqual;拠ĀeiಫಹverseElement;戌ghtTriangleƀ;BEೋೌ೒拫ar;쀀⧐̸qual;拭ĀquೝഌuareSuĀbp೨೹setĀ;E೰ೳ쀀⊏̸qual;拢ersetĀ;Eഃആ쀀⊐̸qual;拣ƀbcpഓതൎsetĀ;Eഛഞ쀀⊂⃒qual;抈ceedsȀ;ESTലള഻െ抁qual;쀀⪰̸lantEqual;拡ilde;쀀≿̸ersetĀ;E൘൛쀀⊃⃒qual;抉ildeȀ;EFT൮൯൵ൿ扁qual;扄ullEqual;扇ilde;扉erticalBar;戤cr;쀀𝒩ilde耻Ñ䃑;䎝܀Eacdfgmoprstuvලෂ෉෕ෛ෠෧෼ขภยา฿ไlig;䅒cute耻Ó䃓Āiy෎ීrc耻Ô䃔;䐞blac;䅐r;쀀𝔒rave耻Ò䃒ƀaei෮ෲ෶cr;䅌ga;䎩cron;䎟pf;쀀𝕆enCurlyĀDQฎบoubleQuote;怜uote;怘;橔Āclวฬr;쀀𝒪ash耻Ø䃘iŬื฼de耻Õ䃕es;樷ml耻Ö䃖erĀBP๋๠Āar๐๓r;怾acĀek๚๜;揞et;掴arenthesis;揜Ҁacfhilors๿ງຊຏຒດຝະ໼rtialD;戂y;䐟r;쀀𝔓i;䎦;䎠usMinus;䂱Āipຢອncareplanåڝf;愙Ȁ;eio຺ູ໠໤檻cedesȀ;EST່້໏໚扺qual;檯lantEqual;扼ilde;找me;怳Ādp໩໮uct;戏ortionĀ;aȥ໹l;戝Āci༁༆r;쀀𝒫;䎨ȀUfos༑༖༛༟OT耻"䀢r;쀀𝔔pf;愚cr;쀀𝒬؀BEacefhiorsu༾གྷཇའཱིྦྷྪྭ႖ႩႴႾarr;椐G耻®䂮ƀcnrཎནབute;䅔g;柫rĀ;tཛྷཝ憠l;椖ƀaeyཧཬཱron;䅘dil;䅖;䐠Ā;vླྀཹ愜erseĀEUྂྙĀlq྇ྎement;戋uilibrium;懋pEquilibrium;楯r»ཹo;䎡ghtЀACDFTUVa࿁࿫࿳ဢဨၛႇϘĀnr࿆࿒gleBracket;柩rowƀ;BL࿜࿝࿡憒ar;懥eftArrow;懄eiling;按oǵ࿹\0စbleBracket;柧nǔည\0နeeVector;楝ectorĀ;Bဝသ懂ar;楕loor;挋Āerိ၃eƀ;AVဵံြ抢rrow;憦ector;楛iangleƀ;BEၐၑၕ抳ar;槐qual;抵pƀDTVၣၮၸownVector;楏eeVector;楜ectorĀ;Bႂႃ憾ar;楔ectorĀ;B႑႒懀ar;楓Āpuႛ႞f;愝ndImplies;楰ightarrow;懛ĀchႹႼr;愛;憱leDelayed;槴ڀHOacfhimoqstuფჱჷჽᄙᄞᅑᅖᅡᅧᆵᆻᆿĀCcჩხHcy;䐩y;䐨FTcy;䐬cute;䅚ʀ;aeiyᄈᄉᄎᄓᄗ檼ron;䅠dil;䅞rc;䅜;䐡r;쀀𝔖ortȀDLRUᄪᄴᄾᅉownArrow»ОeftArrow»࢚ightArrow»࿝pArrow;憑gma;䎣allCircle;战pf;쀀𝕊ɲᅭ\0\0ᅰt;戚areȀ;ISUᅻᅼᆉᆯ斡ntersection;抓uĀbpᆏᆞsetĀ;Eᆗᆘ抏qual;抑ersetĀ;Eᆨᆩ抐qual;抒nion;抔cr;쀀𝒮ar;拆ȀbcmpᇈᇛሉላĀ;sᇍᇎ拐etĀ;Eᇍᇕqual;抆ĀchᇠህeedsȀ;ESTᇭᇮᇴᇿ扻qual;檰lantEqual;扽ilde;承Tháྌ;我ƀ;esሒሓሣ拑rsetĀ;Eሜም抃qual;抇et»ሓրHRSacfhiorsሾቄ቉ቕ቞ቱቶኟዂወዑORN耻Þ䃞ADE;愢ĀHc቎ቒcy;䐋y;䐦Ābuቚቜ;䀉;䎤ƀaeyብቪቯron;䅤dil;䅢;䐢r;쀀𝔗Āeiቻ኉ǲኀ\0ኇefore;戴a;䎘Ācn኎ኘkSpace;쀀  Space;怉ldeȀ;EFTካኬኲኼ戼qual;扃ullEqual;扅ilde;扈pf;쀀𝕋ipleDot;惛Āctዖዛr;쀀𝒯rok;䅦ૡዷጎጚጦ\0ጬጱ\0\0\0\0\0ጸጽ፷ᎅ\0᏿ᐄᐊᐐĀcrዻጁute耻Ú䃚rĀ;oጇገ憟cir;楉rǣጓ\0጖y;䐎ve;䅬Āiyጞጣrc耻Û䃛;䐣blac;䅰r;쀀𝔘rave耻Ù䃙acr;䅪Ādiፁ፩erĀBPፈ፝Āarፍፐr;䁟acĀekፗፙ;揟et;掵arenthesis;揝onĀ;P፰፱拃lus;抎Āgp፻፿on;䅲f;쀀𝕌ЀADETadps᎕ᎮᎸᏄϨᏒᏗᏳrrowƀ;BDᅐᎠᎤar;椒ownArrow;懅ownArrow;憕quilibrium;楮eeĀ;AᏋᏌ报rrow;憥ownáϳerĀLRᏞᏨeftArrow;憖ightArrow;憗iĀ;lᏹᏺ䏒on;䎥ing;䅮cr;쀀𝒰ilde;䅨ml耻Ü䃜ҀDbcdefosvᐧᐬᐰᐳᐾᒅᒊᒐᒖash;披ar;櫫y;䐒ashĀ;lᐻᐼ抩;櫦Āerᑃᑅ;拁ƀbtyᑌᑐᑺar;怖Ā;iᑏᑕcalȀBLSTᑡᑥᑪᑴar;戣ine;䁼eparator;杘ilde;所ThinSpace;怊r;쀀𝔙pf;쀀𝕍cr;쀀𝒱dash;抪ʀcefosᒧᒬᒱᒶᒼirc;䅴dge;拀r;쀀𝔚pf;쀀𝕎cr;쀀𝒲Ȁfiosᓋᓐᓒᓘr;쀀𝔛;䎞pf;쀀𝕏cr;쀀𝒳ҀAIUacfosuᓱᓵᓹᓽᔄᔏᔔᔚᔠcy;䐯cy;䐇cy;䐮cute耻Ý䃝Āiyᔉᔍrc;䅶;䐫r;쀀𝔜pf;쀀𝕐cr;쀀𝒴ml;䅸ЀHacdefosᔵᔹᔿᕋᕏᕝᕠᕤcy;䐖cute;䅹Āayᕄᕉron;䅽;䐗ot;䅻ǲᕔ\0ᕛoWidtè૙a;䎖r;愨pf;愤cr;쀀𝒵௡ᖃᖊᖐ\0ᖰᖶᖿ\0\0\0\0ᗆᗛᗫᙟ᙭\0ᚕ᚛ᚲᚹ\0ᚾcute耻á䃡reve;䄃̀;Ediuyᖜᖝᖡᖣᖨᖭ戾;쀀∾̳;房rc耻â䃢te肻´̆;䐰lig耻æ䃦Ā;r²ᖺ;쀀𝔞rave耻à䃠ĀepᗊᗖĀfpᗏᗔsym;愵èᗓha;䎱ĀapᗟcĀclᗤᗧr;䄁g;樿ɤᗰ\0\0ᘊʀ;adsvᗺᗻᗿᘁᘇ戧nd;橕;橜lope;橘;橚΀;elmrszᘘᘙᘛᘞᘿᙏᙙ戠;榤e»ᘙsdĀ;aᘥᘦ戡ѡᘰᘲᘴᘶᘸᘺᘼᘾ;榨;榩;榪;榫;榬;榭;榮;榯tĀ;vᙅᙆ戟bĀ;dᙌᙍ抾;榝Āptᙔᙗh;戢»¹arr;捼Āgpᙣᙧon;䄅f;쀀𝕒΀;Eaeiop዁ᙻᙽᚂᚄᚇᚊ;橰cir;橯;扊d;手s;䀧roxĀ;e዁ᚒñᚃing耻å䃥ƀctyᚡᚦᚨr;쀀𝒶;䀪mpĀ;e዁ᚯñʈilde耻ã䃣ml耻ä䃤Āciᛂᛈoninôɲnt;樑ࠀNabcdefiklnoprsu᛭ᛱᜰ᜼ᝃᝈ᝸᝽០៦ᠹᡐᜍ᤽᥈ᥰot;櫭Ācrᛶ᜞kȀcepsᜀᜅᜍᜓong;扌psilon;䏶rime;怵imĀ;e᜚᜛戽q;拍Ŷᜢᜦee;抽edĀ;gᜬᜭ挅e»ᜭrkĀ;t፜᜷brk;掶Āoyᜁᝁ;䐱quo;怞ʀcmprtᝓ᝛ᝡᝤᝨausĀ;eĊĉptyv;榰séᜌnoõēƀahwᝯ᝱ᝳ;䎲;愶een;扬r;쀀𝔟g΀costuvwឍឝឳេ៕៛៞ƀaiuបពរðݠrc;旯p»፱ƀdptឤឨឭot;樀lus;樁imes;樂ɱឹ\0\0ើcup;樆ar;昅riangleĀdu៍្own;施p;斳plus;樄eåᑄåᒭarow;植ƀako៭ᠦᠵĀcn៲ᠣkƀlst៺֫᠂ozenge;槫riangleȀ;dlr᠒᠓᠘᠝斴own;斾eft;旂ight;斸k;搣Ʊᠫ\0ᠳƲᠯ\0ᠱ;斒;斑4;斓ck;斈ĀeoᠾᡍĀ;qᡃᡆ쀀=⃥uiv;쀀≡⃥t;挐Ȁptwxᡙᡞᡧᡬf;쀀𝕓Ā;tᏋᡣom»Ꮜtie;拈؀DHUVbdhmptuvᢅᢖᢪᢻᣗᣛᣬ᣿ᤅᤊᤐᤡȀLRlrᢎᢐᢒᢔ;敗;敔;敖;敓ʀ;DUduᢡᢢᢤᢦᢨ敐;敦;敩;敤;敧ȀLRlrᢳᢵᢷᢹ;敝;敚;敜;教΀;HLRhlrᣊᣋᣍᣏᣑᣓᣕ救;敬;散;敠;敫;敢;敟ox;槉ȀLRlrᣤᣦᣨᣪ;敕;敒;攐;攌ʀ;DUduڽ᣷᣹᣻᣽;敥;敨;攬;攴inus;抟lus;択imes;抠ȀLRlrᤙᤛᤝ᤟;敛;敘;攘;攔΀;HLRhlrᤰᤱᤳᤵᤷ᤻᤹攂;敪;敡;敞;攼;攤;攜Āevģ᥂bar耻¦䂦Ȁceioᥑᥖᥚᥠr;쀀𝒷mi;恏mĀ;e᜚᜜lƀ;bhᥨᥩᥫ䁜;槅sub;柈Ŭᥴ᥾lĀ;e᥹᥺怢t»᥺pƀ;Eeįᦅᦇ;檮Ā;qۜۛೡᦧ\0᧨ᨑᨕᨲ\0ᨷᩐ\0\0᪴\0\0᫁\0\0ᬡᬮ᭍᭒\0᯽\0ᰌƀcpr᦭ᦲ᧝ute;䄇̀;abcdsᦿᧀᧄ᧊᧕᧙戩nd;橄rcup;橉Āau᧏᧒p;橋p;橇ot;橀;쀀∩︀Āeo᧢᧥t;恁îړȀaeiu᧰᧻ᨁᨅǰ᧵\0᧸s;橍on;䄍dil耻ç䃧rc;䄉psĀ;sᨌᨍ橌m;橐ot;䄋ƀdmnᨛᨠᨦil肻¸ƭptyv;榲t脀¢;eᨭᨮ䂢räƲr;쀀𝔠ƀceiᨽᩀᩍy;䑇ckĀ;mᩇᩈ朓ark»ᩈ;䏇r΀;Ecefms᩟᩠ᩢᩫ᪤᪪᪮旋;槃ƀ;elᩩᩪᩭ䋆q;扗eɡᩴ\0\0᪈rrowĀlr᩼᪁eft;憺ight;憻ʀRSacd᪒᪔᪖᪚᪟»ཇ;擈st;抛irc;抚ash;抝nint;樐id;櫯cir;槂ubsĀ;u᪻᪼晣it»᪼ˬ᫇᫔᫺\0ᬊonĀ;eᫍᫎ䀺Ā;qÇÆɭ᫙\0\0᫢aĀ;t᫞᫟䀬;䁀ƀ;fl᫨᫩᫫戁îᅠeĀmx᫱᫶ent»᫩eóɍǧ᫾\0ᬇĀ;dኻᬂot;橭nôɆƀfryᬐᬔᬗ;쀀𝕔oäɔ脀©;sŕᬝr;愗Āaoᬥᬩrr;憵ss;朗Ācuᬲᬷr;쀀𝒸Ābpᬼ᭄Ā;eᭁᭂ櫏;櫑Ā;eᭉᭊ櫐;櫒dot;拯΀delprvw᭠᭬᭷ᮂᮬᯔ᯹arrĀlr᭨᭪;椸;椵ɰ᭲\0\0᭵r;拞c;拟arrĀ;p᭿ᮀ憶;椽̀;bcdosᮏᮐᮖᮡᮥᮨ截rcap;橈Āauᮛᮞp;橆p;橊ot;抍r;橅;쀀∪︀Ȁalrv᮵ᮿᯞᯣrrĀ;mᮼᮽ憷;椼yƀevwᯇᯔᯘqɰᯎ\0\0ᯒreã᭳uã᭵ee;拎edge;拏en耻¤䂤earrowĀlrᯮ᯳eft»ᮀight»ᮽeäᯝĀciᰁᰇoninôǷnt;戱lcty;挭ঀAHabcdefhijlorstuwz᰸᰻᰿ᱝᱩᱵᲊᲞᲬᲷ᳻᳿ᴍᵻᶑᶫᶻ᷆᷍rò΁ar;楥Ȁglrs᱈ᱍ᱒᱔ger;怠eth;愸òᄳhĀ;vᱚᱛ怐»ऊūᱡᱧarow;椏aã̕Āayᱮᱳron;䄏;䐴ƀ;ao̲ᱼᲄĀgrʿᲁr;懊tseq;橷ƀglmᲑᲔᲘ耻°䂰ta;䎴ptyv;榱ĀirᲣᲨsht;楿;쀀𝔡arĀlrᲳᲵ»ࣜ»သʀaegsv᳂͸᳖᳜᳠mƀ;oș᳊᳔ndĀ;ș᳑uit;晦amma;䏝in;拲ƀ;io᳧᳨᳸䃷de脀÷;o᳧ᳰntimes;拇nø᳷cy;䑒cɯᴆ\0\0ᴊrn;挞op;挍ʀlptuwᴘᴝᴢᵉᵕlar;䀤f;쀀𝕕ʀ;emps̋ᴭᴷᴽᵂqĀ;d͒ᴳot;扑inus;戸lus;戔quare;抡blebarwedgåúnƀadhᄮᵝᵧownarrowóᲃarpoonĀlrᵲᵶefôᲴighôᲶŢᵿᶅkaro÷གɯᶊ\0\0ᶎrn;挟op;挌ƀcotᶘᶣᶦĀryᶝᶡ;쀀𝒹;䑕l;槶rok;䄑Ādrᶰᶴot;拱iĀ;fᶺ᠖斿Āah᷀᷃ròЩaòྦangle;榦Āci᷒ᷕy;䑟grarr;柿ऀDacdefglmnopqrstuxḁḉḙḸոḼṉṡṾấắẽỡἪἷὄ὎὚ĀDoḆᴴoôᲉĀcsḎḔute耻é䃩ter;橮ȀaioyḢḧḱḶron;䄛rĀ;cḭḮ扖耻ê䃪lon;払;䑍ot;䄗ĀDrṁṅot;扒;쀀𝔢ƀ;rsṐṑṗ檚ave耻è䃨Ā;dṜṝ檖ot;檘Ȁ;ilsṪṫṲṴ檙nters;揧;愓Ā;dṹṺ檕ot;檗ƀapsẅẉẗcr;䄓tyƀ;svẒẓẕ戅et»ẓpĀ1;ẝẤĳạả;怄;怅怃ĀgsẪẬ;䅋p;怂ĀgpẴẸon;䄙f;쀀𝕖ƀalsỄỎỒrĀ;sỊị拕l;槣us;橱iƀ;lvỚớở䎵on»ớ;䏵ȀcsuvỪỳἋἣĀioữḱrc»Ḯɩỹ\0\0ỻíՈantĀglἂἆtr»ṝess»Ṻƀaeiἒ἖Ἒls;䀽st;扟vĀ;DȵἠD;橸parsl;槥ĀDaἯἳot;打rr;楱ƀcdiἾὁỸr;愯oô͒ĀahὉὋ;䎷耻ð䃰Āmrὓὗl耻ë䃫o;悬ƀcipὡὤὧl;䀡sôծĀeoὬὴctatioîՙnentialåչৡᾒ\0ᾞ\0ᾡᾧ\0\0ῆῌ\0ΐ\0ῦῪ \0 ⁚llingdotseñṄy;䑄male;晀ƀilrᾭᾳ῁lig;耀ﬃɩᾹ\0\0᾽g;耀ﬀig;耀ﬄ;쀀𝔣lig;耀ﬁlig;쀀fjƀaltῙ῜ῡt;晭ig;耀ﬂns;斱of;䆒ǰ΅\0ῳf;쀀𝕗ĀakֿῷĀ;vῼ´拔;櫙artint;樍Āao‌⁕Ācs‑⁒α‚‰‸⁅⁈\0⁐β•‥‧‪‬\0‮耻½䂽;慓耻¼䂼;慕;慙;慛Ƴ‴\0‶;慔;慖ʴ‾⁁\0\0⁃耻¾䂾;慗;慜5;慘ƶ⁌\0⁎;慚;慝8;慞l;恄wn;挢cr;쀀𝒻ࢀEabcdefgijlnorstv₂₉₟₥₰₴⃰⃵⃺⃿℃ℒℸ̗ℾ⅒↞Ā;lٍ₇;檌ƀcmpₐₕ₝ute;䇵maĀ;dₜ᳚䎳;檆reve;䄟Āiy₪₮rc;䄝;䐳ot;䄡Ȁ;lqsؾق₽⃉ƀ;qsؾٌ⃄lanô٥Ȁ;cdl٥⃒⃥⃕c;檩otĀ;o⃜⃝檀Ā;l⃢⃣檂;檄Ā;e⃪⃭쀀⋛︀s;檔r;쀀𝔤Ā;gٳ؛mel;愷cy;䑓Ȁ;Eajٚℌℎℐ;檒;檥;檤ȀEaesℛℝ℩ℴ;扩pĀ;p℣ℤ檊rox»ℤĀ;q℮ℯ檈Ā;q℮ℛim;拧pf;쀀𝕘Āci⅃ⅆr;愊mƀ;el٫ⅎ⅐;檎;檐茀>;cdlqr׮ⅠⅪⅮⅳⅹĀciⅥⅧ;檧r;橺ot;拗Par;榕uest;橼ʀadelsↄⅪ←ٖ↛ǰ↉\0↎proø₞r;楸qĀlqؿ↖lesó₈ií٫Āen↣↭rtneqq;쀀≩︀Å↪ԀAabcefkosy⇄⇇⇱⇵⇺∘∝∯≨≽ròΠȀilmr⇐⇔⇗⇛rsðᒄf»․ilôکĀdr⇠⇤cy;䑊ƀ;cwࣴ⇫⇯ir;楈;憭ar;意irc;䄥ƀalr∁∎∓rtsĀ;u∉∊晥it»∊lip;怦con;抹r;쀀𝔥sĀew∣∩arow;椥arow;椦ʀamopr∺∾≃≞≣rr;懿tht;戻kĀlr≉≓eftarrow;憩ightarrow;憪f;쀀𝕙bar;怕ƀclt≯≴≸r;쀀𝒽asè⇴rok;䄧Ābp⊂⊇ull;恃hen»ᱛૡ⊣\0⊪\0⊸⋅⋎\0⋕⋳\0\0⋸⌢⍧⍢⍿\0⎆⎪⎴cute耻í䃭ƀ;iyݱ⊰⊵rc耻î䃮;䐸Ācx⊼⊿y;䐵cl耻¡䂡ĀfrΟ⋉;쀀𝔦rave耻ì䃬Ȁ;inoܾ⋝⋩⋮Āin⋢⋦nt;樌t;戭fin;槜ta;愩lig;䄳ƀaop⋾⌚⌝ƀcgt⌅⌈⌗r;䄫ƀelpܟ⌏⌓inåގarôܠh;䄱f;抷ed;䆵ʀ;cfotӴ⌬⌱⌽⍁are;愅inĀ;t⌸⌹戞ie;槝doô⌙ʀ;celpݗ⍌⍐⍛⍡al;抺Āgr⍕⍙eróᕣã⍍arhk;樗rod;樼Ȁcgpt⍯⍲⍶⍻y;䑑on;䄯f;쀀𝕚a;䎹uest耻¿䂿Āci⎊⎏r;쀀𝒾nʀ;EdsvӴ⎛⎝⎡ӳ;拹ot;拵Ā;v⎦⎧拴;拳Ā;iݷ⎮lde;䄩ǫ⎸\0⎼cy;䑖l耻ï䃯̀cfmosu⏌⏗⏜⏡⏧⏵Āiy⏑⏕rc;䄵;䐹r;쀀𝔧ath;䈷pf;쀀𝕛ǣ⏬\0⏱r;쀀𝒿rcy;䑘kcy;䑔Ѐacfghjos␋␖␢␧␭␱␵␻ppaĀ;v␓␔䎺;䏰Āey␛␠dil;䄷;䐺r;쀀𝔨reen;䄸cy;䑅cy;䑜pf;쀀𝕜cr;쀀𝓀஀ABEHabcdefghjlmnoprstuv⑰⒁⒆⒍⒑┎┽╚▀♎♞♥♹♽⚚⚲⛘❝❨➋⟀⠁⠒ƀart⑷⑺⑼rò৆òΕail;椛arr;椎Ā;gঔ⒋;檋ar;楢ॣ⒥\0⒪\0⒱\0\0\0\0\0⒵Ⓔ\0ⓆⓈⓍ\0⓹ute;䄺mptyv;榴raîࡌbda;䎻gƀ;dlࢎⓁⓃ;榑åࢎ;檅uo耻«䂫rЀ;bfhlpst࢙ⓞⓦⓩ⓫⓮⓱⓵Ā;f࢝ⓣs;椟s;椝ë≒p;憫l;椹im;楳l;憢ƀ;ae⓿─┄檫il;椙Ā;s┉┊檭;쀀⪭︀ƀabr┕┙┝rr;椌rk;杲Āak┢┬cĀek┨┪;䁻;䁛Āes┱┳;榋lĀdu┹┻;榏;榍Ȁaeuy╆╋╖╘ron;䄾Ādi═╔il;䄼ìࢰâ┩;䐻Ȁcqrs╣╦╭╽a;椶uoĀ;rนᝆĀdu╲╷har;楧shar;楋h;憲ʀ;fgqs▋▌উ◳◿扤tʀahlrt▘▤▷◂◨rrowĀ;t࢙□aé⓶arpoonĀdu▯▴own»њp»०eftarrows;懇ightƀahs◍◖◞rrowĀ;sࣴࢧarpoonó྘quigarro÷⇰hreetimes;拋ƀ;qs▋ও◺lanôবʀ;cdgsব☊☍☝☨c;檨otĀ;o☔☕橿Ā;r☚☛檁;檃Ā;e☢☥쀀⋚︀s;檓ʀadegs☳☹☽♉♋pproøⓆot;拖qĀgq♃♅ôউgtò⒌ôছiíলƀilr♕࣡♚sht;楼;쀀𝔩Ā;Eজ♣;檑š♩♶rĀdu▲♮Ā;l॥♳;楪lk;斄cy;䑙ʀ;achtੈ⚈⚋⚑⚖rò◁orneòᴈard;楫ri;旺Āio⚟⚤dot;䅀ustĀ;a⚬⚭掰che»⚭ȀEaes⚻⚽⛉⛔;扨pĀ;p⛃⛄檉rox»⛄Ā;q⛎⛏檇Ā;q⛎⚻im;拦Ѐabnoptwz⛩⛴⛷✚✯❁❇❐Ānr⛮⛱g;柬r;懽rëࣁgƀlmr⛿✍✔eftĀar০✇ightá৲apsto;柼ightá৽parrowĀlr✥✩efô⓭ight;憬ƀafl✶✹✽r;榅;쀀𝕝us;樭imes;樴š❋❏st;戗áፎƀ;ef❗❘᠀旊nge»❘arĀ;l❤❥䀨t;榓ʀachmt❳❶❼➅➇ròࢨorneòᶌarĀ;d྘➃;業;怎ri;抿̀achiqt➘➝ੀ➢➮➻quo;怹r;쀀𝓁mƀ;egল➪➬;檍;檏Ābu┪➳oĀ;rฟ➹;怚rok;䅂萀<;cdhilqrࠫ⟒☹⟜⟠⟥⟪⟰Āci⟗⟙;檦r;橹reå◲mes;拉arr;楶uest;橻ĀPi⟵⟹ar;榖ƀ;ef⠀भ᠛旃rĀdu⠇⠍shar;楊har;楦Āen⠗⠡rtneqq;쀀≨︀Å⠞܀Dacdefhilnopsu⡀⡅⢂⢎⢓⢠⢥⢨⣚⣢⣤ઃ⣳⤂Dot;戺Ȁclpr⡎⡒⡣⡽r耻¯䂯Āet⡗⡙;時Ā;e⡞⡟朠se»⡟Ā;sျ⡨toȀ;dluျ⡳⡷⡻owîҌefôएðᏑker;斮Āoy⢇⢌mma;権;䐼ash;怔asuredangle»ᘦr;쀀𝔪o;愧ƀcdn⢯⢴⣉ro耻µ䂵Ȁ;acdᑤ⢽⣀⣄sôᚧir;櫰ot肻·Ƶusƀ;bd⣒ᤃ⣓戒Ā;uᴼ⣘;横ţ⣞⣡p;櫛ò−ðઁĀdp⣩⣮els;抧f;쀀𝕞Āct⣸⣽r;쀀𝓂pos»ᖝƀ;lm⤉⤊⤍䎼timap;抸ఀGLRVabcdefghijlmoprstuvw⥂⥓⥾⦉⦘⧚⧩⨕⨚⩘⩝⪃⪕⪤⪨⬄⬇⭄⭿⮮ⰴⱧⱼ⳩Āgt⥇⥋;쀀⋙̸Ā;v⥐௏쀀≫⃒ƀelt⥚⥲⥶ftĀar⥡⥧rrow;懍ightarrow;懎;쀀⋘̸Ā;v⥻ే쀀≪⃒ightarrow;懏ĀDd⦎⦓ash;抯ash;抮ʀbcnpt⦣⦧⦬⦱⧌la»˞ute;䅄g;쀀∠⃒ʀ;Eiop඄⦼⧀⧅⧈;쀀⩰̸d;쀀≋̸s;䅉roø඄urĀ;a⧓⧔普lĀ;s⧓ସǳ⧟\0⧣p肻 ଷmpĀ;e௹ఀʀaeouy⧴⧾⨃⨐⨓ǰ⧹\0⧻;橃on;䅈dil;䅆ngĀ;dൾ⨊ot;쀀⩭̸p;橂;䐽ash;怓΀;Aadqsxஒ⨩⨭⨻⩁⩅⩐rr;懗rĀhr⨳⨶k;椤Ā;oᏲᏰot;쀀≐̸uiöୣĀei⩊⩎ar;椨í஘istĀ;s஠டr;쀀𝔫ȀEest௅⩦⩹⩼ƀ;qs஼⩭௡ƀ;qs஼௅⩴lanô௢ií௪Ā;rஶ⪁»ஷƀAap⪊⪍⪑rò⥱rr;憮ar;櫲ƀ;svྍ⪜ྌĀ;d⪡⪢拼;拺cy;䑚΀AEadest⪷⪺⪾⫂⫅⫶⫹rò⥦;쀀≦̸rr;憚r;急Ȁ;fqs఻⫎⫣⫯tĀar⫔⫙rro÷⫁ightarro÷⪐ƀ;qs఻⪺⫪lanôౕĀ;sౕ⫴»శiíౝĀ;rవ⫾iĀ;eచథiäඐĀpt⬌⬑f;쀀𝕟膀¬;in⬙⬚⬶䂬nȀ;Edvஉ⬤⬨⬮;쀀⋹̸ot;쀀⋵̸ǡஉ⬳⬵;拷;拶iĀ;vಸ⬼ǡಸ⭁⭃;拾;拽ƀaor⭋⭣⭩rȀ;ast୻⭕⭚⭟lleì୻l;쀀⫽⃥;쀀∂̸lint;樔ƀ;ceಒ⭰⭳uåಥĀ;cಘ⭸Ā;eಒ⭽ñಘȀAait⮈⮋⮝⮧rò⦈rrƀ;cw⮔⮕⮙憛;쀀⤳̸;쀀↝̸ghtarrow»⮕riĀ;eೋೖ΀chimpqu⮽⯍⯙⬄୸⯤⯯Ȁ;cerല⯆ഷ⯉uå൅;쀀𝓃ortɭ⬅\0\0⯖ará⭖mĀ;e൮⯟Ā;q൴൳suĀbp⯫⯭å೸åഋƀbcp⯶ⰑⰙȀ;Ees⯿ⰀഢⰄ抄;쀀⫅̸etĀ;eഛⰋqĀ;qണⰀcĀ;eലⰗñസȀ;EesⰢⰣൟⰧ抅;쀀⫆̸etĀ;e൘ⰮqĀ;qൠⰣȀgilrⰽⰿⱅⱇìௗlde耻ñ䃱çృiangleĀlrⱒⱜeftĀ;eచⱚñదightĀ;eೋⱥñ೗Ā;mⱬⱭ䎽ƀ;esⱴⱵⱹ䀣ro;愖p;怇ҀDHadgilrsⲏⲔⲙⲞⲣⲰⲶⳓⳣash;抭arr;椄p;쀀≍⃒ash;抬ĀetⲨⲬ;쀀≥⃒;쀀>⃒nfin;槞ƀAetⲽⳁⳅrr;椂;쀀≤⃒Ā;rⳊⳍ쀀<⃒ie;쀀⊴⃒ĀAtⳘⳜrr;椃rie;쀀⊵⃒im;쀀∼⃒ƀAan⳰⳴ⴂrr;懖rĀhr⳺⳽k;椣Ā;oᏧᏥear;椧ቓ᪕\0\0\0\0\0\0\0\0\0\0\0\0\0ⴭ\0ⴸⵈⵠⵥ⵲ⶄᬇ\0\0ⶍⶫ\0ⷈⷎ\0ⷜ⸙⸫⸾⹃Ācsⴱ᪗ute耻ó䃳ĀiyⴼⵅrĀ;c᪞ⵂ耻ô䃴;䐾ʀabios᪠ⵒⵗǈⵚlac;䅑v;樸old;榼lig;䅓Ācr⵩⵭ir;榿;쀀𝔬ͯ⵹\0\0⵼\0ⶂn;䋛ave耻ò䃲;槁Ābmⶈ෴ar;榵Ȁacitⶕ⶘ⶥⶨrò᪀Āir⶝ⶠr;榾oss;榻nå๒;槀ƀaeiⶱⶵⶹcr;䅍ga;䏉ƀcdnⷀⷅǍron;䎿;榶pf;쀀𝕠ƀaelⷔ⷗ǒr;榷rp;榹΀;adiosvⷪⷫⷮ⸈⸍⸐⸖戨rò᪆Ȁ;efmⷷⷸ⸂⸅橝rĀ;oⷾⷿ愴f»ⷿ耻ª䂪耻º䂺gof;抶r;橖lope;橗;橛ƀclo⸟⸡⸧ò⸁ash耻ø䃸l;折iŬⸯ⸴de耻õ䃵esĀ;aǛ⸺s;樶ml耻ö䃶bar;挽ૡ⹞\0⹽\0⺀⺝\0⺢⺹\0\0⻋ຜ\0⼓\0\0⼫⾼\0⿈rȀ;astЃ⹧⹲຅脀¶;l⹭⹮䂶leìЃɩ⹸\0\0⹻m;櫳;櫽y;䐿rʀcimpt⺋⺏⺓ᡥ⺗nt;䀥od;䀮il;怰enk;怱r;쀀𝔭ƀimo⺨⺰⺴Ā;v⺭⺮䏆;䏕maô੶ne;明ƀ;tv⺿⻀⻈䏀chfork»´;䏖Āau⻏⻟nĀck⻕⻝kĀ;h⇴⻛;愎ö⇴sҀ;abcdemst⻳⻴ᤈ⻹⻽⼄⼆⼊⼎䀫cir;樣ir;樢Āouᵀ⼂;樥;橲n肻±ຝim;樦wo;樧ƀipu⼙⼠⼥ntint;樕f;쀀𝕡nd耻£䂣Ԁ;Eaceinosu່⼿⽁⽄⽇⾁⾉⾒⽾⾶;檳p;檷uå໙Ā;c໎⽌̀;acens່⽙⽟⽦⽨⽾pproø⽃urlyeñ໙ñ໎ƀaes⽯⽶⽺pprox;檹qq;檵im;拨iíໟmeĀ;s⾈ຮ怲ƀEas⽸⾐⽺ð⽵ƀdfp໬⾙⾯ƀals⾠⾥⾪lar;挮ine;挒urf;挓Ā;t໻⾴ï໻rel;抰Āci⿀⿅r;쀀𝓅;䏈ncsp;怈̀fiopsu⿚⋢⿟⿥⿫⿱r;쀀𝔮pf;쀀𝕢rime;恗cr;쀀𝓆ƀaeo⿸〉〓tĀei⿾々rnionóڰnt;樖stĀ;e【】䀿ñἙô༔઀ABHabcdefhilmnoprstux぀けさすムㄎㄫㅇㅢㅲㆎ㈆㈕㈤㈩㉘㉮㉲㊐㊰㊷ƀartぇおがròႳòϝail;検aròᱥar;楤΀cdenqrtとふへみわゔヌĀeuねぱ;쀀∽̱te;䅕iãᅮmptyv;榳gȀ;del࿑らるろ;榒;榥å࿑uo耻»䂻rր;abcfhlpstw࿜ガクシスゼゾダッデナp;極Ā;f࿠ゴs;椠;椳s;椞ë≝ð✮l;楅im;楴l;憣;憝Āaiパフil;椚oĀ;nホボ戶aló༞ƀabrョリヮrò៥rk;杳ĀakンヽcĀekヹ・;䁽;䁝Āes㄂㄄;榌lĀduㄊㄌ;榎;榐Ȁaeuyㄗㄜㄧㄩron;䅙Ādiㄡㄥil;䅗ì࿲âヺ;䑀Ȁclqsㄴㄷㄽㅄa;椷dhar;楩uoĀ;rȎȍh;憳ƀacgㅎㅟངlȀ;ipsླྀㅘㅛႜnåႻarôྩt;断ƀilrㅩဣㅮsht;楽;쀀𝔯ĀaoㅷㆆrĀduㅽㅿ»ѻĀ;l႑ㆄ;楬Ā;vㆋㆌ䏁;䏱ƀgns㆕ㇹㇼht̀ahlrstㆤㆰ㇂㇘㇤㇮rrowĀ;t࿜ㆭaéトarpoonĀduㆻㆿowîㅾp»႒eftĀah㇊㇐rrowó࿪arpoonóՑightarrows;應quigarro÷ニhreetimes;拌g;䋚ingdotseñἲƀahm㈍㈐㈓rò࿪aòՑ;怏oustĀ;a㈞㈟掱che»㈟mid;櫮Ȁabpt㈲㈽㉀㉒Ānr㈷㈺g;柭r;懾rëဃƀafl㉇㉊㉎r;榆;쀀𝕣us;樮imes;樵Āap㉝㉧rĀ;g㉣㉤䀩t;榔olint;樒arò㇣Ȁachq㉻㊀Ⴜ㊅quo;怺r;쀀𝓇Ābu・㊊oĀ;rȔȓƀhir㊗㊛㊠reåㇸmes;拊iȀ;efl㊪ၙᠡ㊫方tri;槎luhar;楨;愞ൡ㋕㋛㋟㌬㌸㍱\0㍺㎤\0\0㏬㏰\0㐨㑈㑚㒭㒱㓊㓱\0㘖\0\0㘳cute;䅛quï➺Ԁ;Eaceinpsyᇭ㋳㋵㋿㌂㌋㌏㌟㌦㌩;檴ǰ㋺\0㋼;檸on;䅡uåᇾĀ;dᇳ㌇il;䅟rc;䅝ƀEas㌖㌘㌛;檶p;檺im;择olint;樓iíሄ;䑁otƀ;be㌴ᵇ㌵担;橦΀Aacmstx㍆㍊㍗㍛㍞㍣㍭rr;懘rĀhr㍐㍒ë∨Ā;oਸ਼਴t耻§䂧i;䀻war;椩mĀin㍩ðnuóñt;朶rĀ;o㍶⁕쀀𝔰Ȁacoy㎂㎆㎑㎠rp;景Āhy㎋㎏cy;䑉;䑈rtɭ㎙\0\0㎜iäᑤaraì⹯耻­䂭Āgm㎨㎴maƀ;fv㎱㎲㎲䏃;䏂Ѐ;deglnprካ㏅㏉㏎㏖㏞㏡㏦ot;橪Ā;q኱ኰĀ;E㏓㏔檞;檠Ā;E㏛㏜檝;檟e;扆lus;樤arr;楲aròᄽȀaeit㏸㐈㐏㐗Āls㏽㐄lsetmé㍪hp;樳parsl;槤Ādlᑣ㐔e;挣Ā;e㐜㐝檪Ā;s㐢㐣檬;쀀⪬︀ƀflp㐮㐳㑂tcy;䑌Ā;b㐸㐹䀯Ā;a㐾㐿槄r;挿f;쀀𝕤aĀdr㑍ЂesĀ;u㑔㑕晠it»㑕ƀcsu㑠㑹㒟Āau㑥㑯pĀ;sᆈ㑫;쀀⊓︀pĀ;sᆴ㑵;쀀⊔︀uĀbp㑿㒏ƀ;esᆗᆜ㒆etĀ;eᆗ㒍ñᆝƀ;esᆨᆭ㒖etĀ;eᆨ㒝ñᆮƀ;afᅻ㒦ְrť㒫ֱ»ᅼaròᅈȀcemt㒹㒾㓂㓅r;쀀𝓈tmîñiì㐕aræᆾĀar㓎㓕rĀ;f㓔ឿ昆Āan㓚㓭ightĀep㓣㓪psiloîỠhé⺯s»⡒ʀbcmnp㓻㕞ሉ㖋㖎Ҁ;Edemnprs㔎㔏㔑㔕㔞㔣㔬㔱㔶抂;櫅ot;檽Ā;dᇚ㔚ot;櫃ult;櫁ĀEe㔨㔪;櫋;把lus;檿arr;楹ƀeiu㔽㕒㕕tƀ;en㔎㕅㕋qĀ;qᇚ㔏eqĀ;q㔫㔨m;櫇Ābp㕚㕜;櫕;櫓c̀;acensᇭ㕬㕲㕹㕻㌦pproø㋺urlyeñᇾñᇳƀaes㖂㖈㌛pproø㌚qñ㌗g;晪ڀ123;Edehlmnps㖩㖬㖯ሜ㖲㖴㗀㗉㗕㗚㗟㗨㗭耻¹䂹耻²䂲耻³䂳;櫆Āos㖹㖼t;檾ub;櫘Ā;dሢ㗅ot;櫄sĀou㗏㗒l;柉b;櫗arr;楻ult;櫂ĀEe㗤㗦;櫌;抋lus;櫀ƀeiu㗴㘉㘌tƀ;enሜ㗼㘂qĀ;qሢ㖲eqĀ;q㗧㗤m;櫈Ābp㘑㘓;櫔;櫖ƀAan㘜㘠㘭rr;懙rĀhr㘦㘨ë∮Ā;oਫ਩war;椪lig耻ß䃟௡㙑㙝㙠ዎ㙳㙹\0㙾㛂\0\0\0\0\0㛛㜃\0㜉㝬\0\0\0㞇ɲ㙖\0\0㙛get;挖;䏄rë๟ƀaey㙦㙫㙰ron;䅥dil;䅣;䑂lrec;挕r;쀀𝔱Ȁeiko㚆㚝㚵㚼ǲ㚋\0㚑eĀ4fኄኁaƀ;sv㚘㚙㚛䎸ym;䏑Ācn㚢㚲kĀas㚨㚮pproø዁im»ኬsðኞĀas㚺㚮ð዁rn耻þ䃾Ǭ̟㛆⋧es膀×;bd㛏㛐㛘䃗Ā;aᤏ㛕r;樱;樰ƀeps㛡㛣㜀á⩍Ȁ;bcf҆㛬㛰㛴ot;挶ir;櫱Ā;o㛹㛼쀀𝕥rk;櫚á㍢rime;怴ƀaip㜏㜒㝤dåቈ΀adempst㜡㝍㝀㝑㝗㝜㝟ngleʀ;dlqr㜰㜱㜶㝀㝂斵own»ᶻeftĀ;e⠀㜾ñम;扜ightĀ;e㊪㝋ñၚot;旬inus;樺lus;樹b;槍ime;樻ezium;揢ƀcht㝲㝽㞁Āry㝷㝻;쀀𝓉;䑆cy;䑛rok;䅧Āio㞋㞎xô᝷headĀlr㞗㞠eftarro÷ࡏightarrow»ཝऀAHabcdfghlmoprstuw㟐㟓㟗㟤㟰㟼㠎㠜㠣㠴㡑㡝㡫㢩㣌㣒㣪㣶ròϭar;楣Ācr㟜㟢ute耻ú䃺òᅐrǣ㟪\0㟭y;䑞ve;䅭Āiy㟵㟺rc耻û䃻;䑃ƀabh㠃㠆㠋ròᎭlac;䅱aòᏃĀir㠓㠘sht;楾;쀀𝔲rave耻ù䃹š㠧㠱rĀlr㠬㠮»ॗ»ႃlk;斀Āct㠹㡍ɯ㠿\0\0㡊rnĀ;e㡅㡆挜r»㡆op;挏ri;旸Āal㡖㡚cr;䅫肻¨͉Āgp㡢㡦on;䅳f;쀀𝕦̀adhlsuᅋ㡸㡽፲㢑㢠ownáᎳarpoonĀlr㢈㢌efô㠭ighô㠯iƀ;hl㢙㢚㢜䏅»ᏺon»㢚parrows;懈ƀcit㢰㣄㣈ɯ㢶\0\0㣁rnĀ;e㢼㢽挝r»㢽op;挎ng;䅯ri;旹cr;쀀𝓊ƀdir㣙㣝㣢ot;拰lde;䅩iĀ;f㜰㣨»᠓Āam㣯㣲rò㢨l耻ü䃼angle;榧ހABDacdeflnoprsz㤜㤟㤩㤭㦵㦸㦽㧟㧤㧨㧳㧹㧽㨁㨠ròϷarĀ;v㤦㤧櫨;櫩asèϡĀnr㤲㤷grt;榜΀eknprst㓣㥆㥋㥒㥝㥤㦖appá␕othinçẖƀhir㓫⻈㥙opô⾵Ā;hᎷ㥢ïㆍĀiu㥩㥭gmá㎳Ābp㥲㦄setneqĀ;q㥽㦀쀀⊊︀;쀀⫋︀setneqĀ;q㦏㦒쀀⊋︀;쀀⫌︀Āhr㦛㦟etá㚜iangleĀlr㦪㦯eft»थight»ၑy;䐲ash»ံƀelr㧄㧒㧗ƀ;beⷪ㧋㧏ar;抻q;扚lip;拮Ābt㧜ᑨaòᑩr;쀀𝔳tré㦮suĀbp㧯㧱»ജ»൙pf;쀀𝕧roð໻tré㦴Ācu㨆㨋r;쀀𝓋Ābp㨐㨘nĀEe㦀㨖»㥾nĀEe㦒㨞»㦐igzag;榚΀cefoprs㨶㨻㩖㩛㩔㩡㩪irc;䅵Ādi㩀㩑Ābg㩅㩉ar;機eĀ;qᗺ㩏;扙erp;愘r;쀀𝔴pf;쀀𝕨Ā;eᑹ㩦atèᑹcr;쀀𝓌ૣណ㪇\0㪋\0㪐㪛\0\0㪝㪨㪫㪯\0\0㫃㫎\0㫘ៜ៟tré៑r;쀀𝔵ĀAa㪔㪗ròσrò৶;䎾ĀAa㪡㪤ròθrò৫að✓is;拻ƀdptឤ㪵㪾Āfl㪺ឩ;쀀𝕩imåឲĀAa㫇㫊ròώròਁĀcq㫒ីr;쀀𝓍Āpt៖㫜ré។Ѐacefiosu㫰㫽㬈㬌㬑㬕㬛㬡cĀuy㫶㫻te耻ý䃽;䑏Āiy㬂㬆rc;䅷;䑋n耻¥䂥r;쀀𝔶cy;䑗pf;쀀𝕪cr;쀀𝓎Ācm㬦㬩y;䑎l耻ÿ䃿Ԁacdefhiosw㭂㭈㭔㭘㭤㭩㭭㭴㭺㮀cute;䅺Āay㭍㭒ron;䅾;䐷ot;䅼Āet㭝㭡træᕟa;䎶r;쀀𝔷cy;䐶grarr;懝pf;쀀𝕫cr;쀀𝓏Ājn㮅㮇;怍j;怌'.split("").map((e=>e.charCodeAt(0)))),ue=new Uint16Array("Ȁaglq\tɭ\0\0p;䀦os;䀧t;䀾t;䀼uot;䀢".split("").map((e=>e.charCodeAt(0))));var he;const de=new Map([[0,65533],[128,8364],[130,8218],[131,402],[132,8222],[133,8230],[134,8224],[135,8225],[136,710],[137,8240],[138,352],[139,8249],[140,338],[142,381],[145,8216],[146,8217],[147,8220],[148,8221],[149,8226],[150,8211],[151,8212],[152,732],[153,8482],[154,353],[155,8250],[156,339],[158,382],[159,376]]),pe=null!==(he=String.fromCodePoint)&&void 0!==he?he:function(e){let t="";return e>65535&&(e-=65536,t+=String.fromCharCode(e>>>10&1023|55296),e=56320|1023&e),t+=String.fromCharCode(e),t};function fe(e){var t;return e>=55296&&e<=57343||e>1114111?65533:null!==(t=de.get(e))&&void 0!==t?t:e}var me,Ee;(Ee=me||(me={}))[Ee.NUM=35]="NUM",Ee[Ee.SEMI=59]="SEMI",Ee[Ee.EQUALS=61]="EQUALS",Ee[Ee.ZERO=48]="ZERO",Ee[Ee.NINE=57]="NINE",Ee[Ee.LOWER_A=97]="LOWER_A",Ee[Ee.LOWER_F=102]="LOWER_F",Ee[Ee.LOWER_X=120]="LOWER_X",Ee[Ee.LOWER_Z=122]="LOWER_Z",Ee[Ee.UPPER_A=65]="UPPER_A",Ee[Ee.UPPER_F=70]="UPPER_F",Ee[Ee.UPPER_Z=90]="UPPER_Z";var Te,_e,Ae,ge,Ce,be;function Ne(e){return e>=me.ZERO&&e<=me.NINE}function Ie(e){return e===me.EQUALS||function(e){return e>=me.UPPER_A&&e<=me.UPPER_Z||e>=me.LOWER_A&&e<=me.LOWER_Z||Ne(e)}(e)}(_e=Te||(Te={}))[_e.VALUE_LENGTH=49152]="VALUE_LENGTH",_e[_e.BRANCH_LENGTH=16256]="BRANCH_LENGTH",_e[_e.JUMP_TABLE=127]="JUMP_TABLE",(ge=Ae||(Ae={}))[ge.EntityStart=0]="EntityStart",ge[ge.NumericStart=1]="NumericStart",ge[ge.NumericDecimal=2]="NumericDecimal",ge[ge.NumericHex=3]="NumericHex",ge[ge.NamedEntity=4]="NamedEntity",(be=Ce||(Ce={}))[be.Legacy=0]="Legacy",be[be.Strict=1]="Strict",be[be.Attribute=2]="Attribute";class ke{constructor(e,t,n){this.decodeTree=e,this.emitCodePoint=t,this.errors=n,this.state=Ae.EntityStart,this.consumed=1,this.result=0,this.treeIndex=0,this.excess=1,this.decodeMode=Ce.Strict}startEntity(e){this.decodeMode=e,this.state=Ae.EntityStart,this.result=0,this.treeIndex=0,this.excess=1,this.consumed=1}write(e,t){switch(this.state){case Ae.EntityStart:return e.charCodeAt(t)===me.NUM?(this.state=Ae.NumericStart,this.consumed+=1,this.stateNumericStart(e,t+1)):(this.state=Ae.NamedEntity,this.stateNamedEntity(e,t));case Ae.NumericStart:return this.stateNumericStart(e,t);case Ae.NumericDecimal:return this.stateNumericDecimal(e,t);case Ae.NumericHex:return this.stateNumericHex(e,t);case Ae.NamedEntity:return this.stateNamedEntity(e,t)}}stateNumericStart(e,t){return t>=e.length?-1:(32|e.charCodeAt(t))===me.LOWER_X?(this.state=Ae.NumericHex,this.consumed+=1,this.stateNumericHex(e,t+1)):(this.state=Ae.NumericDecimal,this.stateNumericDecimal(e,t))}addToNumericResult(e,t,n,s){if(t!==n){const r=n-t;this.result=this.result*Math.pow(s,r)+parseInt(e.substr(t,r),s),this.consumed+=r}}stateNumericHex(e,t){const n=t;for(;t<e.length;){const r=e.charCodeAt(t);if(!(Ne(r)||(s=r,s>=me.UPPER_A&&s<=me.UPPER_F||s>=me.LOWER_A&&s<=me.LOWER_F)))return this.addToNumericResult(e,n,t,16),this.emitNumericEntity(r,3);t+=1}var s;return this.addToNumericResult(e,n,t,16),-1}stateNumericDecimal(e,t){const n=t;for(;t<e.length;){const s=e.charCodeAt(t);if(!Ne(s))return this.addToNumericResult(e,n,t,10),this.emitNumericEntity(s,2);t+=1}return this.addToNumericResult(e,n,t,10),-1}emitNumericEntity(e,t){var n;if(this.consumed<=t)return null===(n=this.errors)||void 0===n||n.absenceOfDigitsInNumericCharacterReference(this.consumed),0;if(e===me.SEMI)this.consumed+=1;else if(this.decodeMode===Ce.Strict)return 0;return this.emitCodePoint(fe(this.result),this.consumed),this.errors&&(e!==me.SEMI&&this.errors.missingSemicolonAfterCharacterReference(),this.errors.validateNumericCharacterReference(this.result)),this.consumed}stateNamedEntity(e,t){const{decodeTree:n}=this;let s=n[this.treeIndex],r=(s&Te.VALUE_LENGTH)>>14;for(;t<e.length;t++,this.excess++){const i=e.charCodeAt(t);if(this.treeIndex=De(n,s,this.treeIndex+Math.max(1,r),i),this.treeIndex<0)return 0===this.result||this.decodeMode===Ce.Attribute&&(0===r||Ie(i))?0:this.emitNotTerminatedNamedEntity();if(s=n[this.treeIndex],r=(s&Te.VALUE_LENGTH)>>14,0!==r){if(i===me.SEMI)return this.emitNamedEntityData(this.treeIndex,r,this.consumed+this.excess);this.decodeMode!==Ce.Strict&&(this.result=this.treeIndex,this.consumed+=this.excess,this.excess=0)}}return-1}emitNotTerminatedNamedEntity(){var e;const{result:t,decodeTree:n}=this,s=(n[t]&Te.VALUE_LENGTH)>>14;return this.emitNamedEntityData(t,s,this.consumed),null===(e=this.errors)||void 0===e||e.missingSemicolonAfterCharacterReference(),this.consumed}emitNamedEntityData(e,t,n){const{decodeTree:s}=this;return this.emitCodePoint(1===t?s[e]&~Te.VALUE_LENGTH:s[e+1],n),3===t&&this.emitCodePoint(s[e+2],n),n}end(){var e;switch(this.state){case Ae.NamedEntity:return 0===this.result||this.decodeMode===Ce.Attribute&&this.result!==this.treeIndex?0:this.emitNotTerminatedNamedEntity();case Ae.NumericDecimal:return this.emitNumericEntity(0,2);case Ae.NumericHex:return this.emitNumericEntity(0,3);case Ae.NumericStart:return null===(e=this.errors)||void 0===e||e.absenceOfDigitsInNumericCharacterReference(this.consumed),0;case Ae.EntityStart:return 0}}}function Se(e){let t="";const n=new ke(e,(e=>t+=pe(e)));return function(e,s){let r=0,i=0;for(;(i=e.indexOf("&",i))>=0;){t+=e.slice(r,i),n.startEntity(s);const o=n.write(e,i+1);if(o<0){r=i+n.end();break}r=i+o,i=0===o?r+1:r}const o=t+e.slice(r);return t="",o}}function De(e,t,n,s){const r=(t&Te.BRANCH_LENGTH)>>7,i=t&Te.JUMP_TABLE;if(0===r)return 0!==i&&s===i?n:-1;if(i){const t=s-i;return t<0||t>=r?-1:e[n+t]-1}let o=n,a=o+r-1;for(;o<=a;){const t=o+a>>>1,n=e[t];if(n<s)o=t+1;else{if(!(n>s))return e[t+r];a=t-1}}return-1}const ye=Se(le);function Oe(e,t=Ce.Legacy){return ye(e,t)}Se(ue);const Le=/["&'<>$\x80-\uFFFF]/g,Re=new Map([[34,"&quot;"],[38,"&amp;"],[39,"&apos;"],[60,"&lt;"],[62,"&gt;"]]),Me=null!=String.prototype.codePointAt?(e,t)=>e.codePointAt(t):(e,t)=>55296==(64512&e.charCodeAt(t))?1024*(e.charCodeAt(t)-55296)+e.charCodeAt(t+1)-56320+65536:e.charCodeAt(t);function ve(e){let t,n="",s=0;for(;null!==(t=Le.exec(e));){const r=t.index,i=e.charCodeAt(r),o=Re.get(i);void 0!==o?(n+=e.substring(s,r)+o,s=r+1):(n+=`${e.substring(s,r)}&#x${Me(e,r).toString(16)};`,s=Le.lastIndex+=Number(55296==(64512&i)))}return n+e.substr(s)}function xe(e,t){return function(n){let s,r=0,i="";for(;s=e.exec(n);)r!==s.index&&(i+=n.substring(r,s.index)),i+=t.get(s[0].charCodeAt(0)),r=s.index+1;return i+n.substring(r)}}const we=xe(/["&\u00A0]/g,new Map([[34,"&quot;"],[38,"&amp;"],[160,"&nbsp;"]])),Pe=xe(/[&<>\u00A0]/g,new Map([[38,"&amp;"],[60,"&lt;"],[62,"&gt;"],[160,"&nbsp;"]])),Fe=new Map(["altGlyph","altGlyphDef","altGlyphItem","animateColor","animateMotion","animateTransform","clipPath","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","foreignObject","glyphRef","linearGradient","radialGradient","textPath"].map((e=>[e.toLowerCase(),e]))),Be=new Map(["definitionURL","attributeName","attributeType","baseFrequency","baseProfile","calcMode","clipPathUnits","diffuseConstant","edgeMode","filterUnits","glyphRef","gradientTransform","gradientUnits","kernelMatrix","kernelUnitLength","keyPoints","keySplines","keyTimes","lengthAdjust","limitingConeAngle","markerHeight","markerUnits","markerWidth","maskContentUnits","maskUnits","numOctaves","pathLength","patternContentUnits","patternTransform","patternUnits","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","refX","refY","repeatCount","repeatDur","requiredExtensions","requiredFeatures","specularConstant","specularExponent","spreadMethod","startOffset","stdDeviation","stitchTiles","surfaceScale","systemLanguage","tableValues","targetX","targetY","textLength","viewBox","viewTarget","xChannelSelector","yChannelSelector","zoomAndPan"].map((e=>[e.toLowerCase(),e]))),Ue=new Set(["style","script","xmp","iframe","noembed","noframes","plaintext","noscript"]);function He(e){return e.replace(/"/g,"&quot;")}const Ge=new Set(["area","base","basefont","br","col","command","embed","frame","hr","img","input","isindex","keygen","link","meta","param","source","track","wbr"]);function qe(e,t={}){const n="length"in e?e:[e];let s="";for(let e=0;e<n.length;e++)s+=$e(n[e],t);return s}function $e(e,t){switch(e.type){case x:return qe(e.children,t);case q:case P:return`<${e.data}>`;case F:return function(e){return`\x3c!--${e.data}--\x3e`}(e);case G:return function(e){return`<![CDATA[${e.children[0].data}]]>`}(e);case B:case U:case H:return function(e,t){var n;"foreign"===t.xmlMode&&(e.name=null!==(n=Fe.get(e.name))&&void 0!==n?n:e.name,e.parent&&Ye.has(e.parent.name)&&(t={...t,xmlMode:!1}));!t.xmlMode&&je.has(e.name)&&(t={...t,xmlMode:"foreign"});let s=`<${e.name}`;const r=function(e,t){var n;if(!e)return;const s=!1===(null!==(n=t.encodeEntities)&&void 0!==n?n:t.decodeEntities)?He:t.xmlMode||"utf8"!==t.encodeEntities?ve:we;return Object.keys(e).map((n=>{var r,i;const o=null!==(r=e[n])&&void 0!==r?r:"";return"foreign"===t.xmlMode&&(n=null!==(i=Be.get(n))&&void 0!==i?i:n),t.emptyAttrs||t.xmlMode||""!==o?`${n}="${s(o)}"`:n})).join(" ")}(e.attribs,t);r&&(s+=` ${r}`);0===e.children.length&&(t.xmlMode?!1!==t.selfClosingTags:t.selfClosingTags&&Ge.has(e.name))?(t.xmlMode||(s+=" "),s+="/>"):(s+=">",e.children.length>0&&(s+=qe(e.children,t)),!t.xmlMode&&Ge.has(e.name)||(s+=`</${e.name}>`));return s}(e,t);case w:return function(e,t){var n;let s=e.data||"";!1===(null!==(n=t.encodeEntities)&&void 0!==n?n:t.decodeEntities)||!t.xmlMode&&e.parent&&Ue.has(e.parent.name)||(s=t.xmlMode||"utf8"!==t.encodeEntities?ve(s):Pe(s));return s}(e,t)}}const Ye=new Set(["mi","mo","mn","ms","mtext","annotation-xml","foreignObject","desc","title"]),je=new Set(["svg","math"]);function Ke(e,t){return qe(e,t)}function Ve(e){return Array.isArray(e)?e.map(Ve).join(""):re(e)&&!te(e)?Ve(e.children):ee(e)?e.data:""}function ze(e){return Array.isArray(e)?e.map(ze).join(""):re(e)&&(e.type===M.Tag||J(e))?ze(e.children):ee(e)?e.data:""}function Qe(e){return re(e)?e.children:[]}function We(e){return e.parent||null}function Xe(e){const t=We(e);if(null!=t)return Qe(t);const n=[e];let{prev:s,next:r}=e;for(;null!=s;)n.unshift(s),({prev:s}=s);for(;null!=r;)n.push(r),({next:r}=r);return n}function Ze(e){let{next:t}=e;for(;null!==t&&!Z(t);)({next:t}=t);return t}function Je(e){let{prev:t}=e;for(;null!==t&&!Z(t);)({prev:t}=t);return t}function et(e){if(e.prev&&(e.prev.next=e.next),e.next&&(e.next.prev=e.prev),e.parent){const t=e.parent.children,n=t.lastIndexOf(e);n>=0&&t.splice(n,1)}e.next=null,e.prev=null,e.parent=null}function tt(e,t,n=!0,s=1/0){return nt(e,Array.isArray(t)?t:[t],n,s)}function nt(e,t,n,s){const r=[],i=[t],o=[0];for(;;){if(o[0]>=i[0].length){if(1===o.length)return r;i.shift(),o.shift();continue}const t=i[0][o[0]++];if(e(t)&&(r.push(t),--s<=0))return r;n&&re(t)&&t.children.length>0&&(o.unshift(0),i.unshift(t.children))}}function st(e,t,n=!0){let s=null;for(let r=0;r<t.length&&!s;r++){const i=t[r];Z(i)&&(e(i)?s=i:n&&i.children.length>0&&(s=st(e,i.children,!0)))}return s}const rt={tag_name:e=>"function"==typeof e?t=>Z(t)&&e(t.name):"*"===e?Z:t=>Z(t)&&t.name===e,tag_type:e=>"function"==typeof e?t=>e(t.type):t=>t.type===e,tag_contains:e=>"function"==typeof e?t=>ee(t)&&e(t.data):t=>ee(t)&&t.data===e};function it(e,t){return"function"==typeof t?n=>Z(n)&&t(n.attribs[e]):n=>Z(n)&&n.attribs[e]===t}function ot(e,t){return n=>e(n)||t(n)}function at(e){const t=Object.keys(e).map((t=>{const n=e[t];return Object.prototype.hasOwnProperty.call(rt,t)?rt[t](n):it(t,n)}));return 0===t.length?null:t.reduce(ot)}function ct(e,t,n=!0,s=1/0){return tt(rt.tag_name(e),t,n,s)}var lt,ut;function ht(e,t){const n=[],s=[];if(e===t)return 0;let r=re(e)?e:e.parent;for(;r;)n.unshift(r),r=r.parent;for(r=re(t)?t:t.parent;r;)s.unshift(r),r=r.parent;const i=Math.min(n.length,s.length);let o=0;for(;o<i&&n[o]===s[o];)o++;if(0===o)return lt.DISCONNECTED;const a=n[o-1],c=a.children,l=n[o],u=s[o];return c.indexOf(l)>c.indexOf(u)?a===t?lt.FOLLOWING|lt.CONTAINED_BY:lt.FOLLOWING:a===e?lt.PRECEDING|lt.CONTAINS:lt.PRECEDING}function dt(e){return(e=e.filter(((e,t,n)=>!n.includes(e,t+1)))).sort(((e,t)=>{const n=ht(e,t);return n&lt.PRECEDING?-1:n&lt.FOLLOWING?1:0})),e}(ut=lt||(lt={}))[ut.DISCONNECTED=1]="DISCONNECTED",ut[ut.PRECEDING=2]="PRECEDING",ut[ut.FOLLOWING=4]="FOLLOWING",ut[ut.CONTAINS=8]="CONTAINS",ut[ut.CONTAINED_BY=16]="CONTAINED_BY";const pt=["url","type","lang"],ft=["fileSize","bitrate","framerate","samplingrate","channels","duration","height","width"];function mt(e){return ct("media:content",e).map((e=>{const{attribs:t}=e,n={medium:t.medium,isDefault:!!t.isDefault};for(const e of pt)t[e]&&(n[e]=t[e]);for(const e of ft)t[e]&&(n[e]=parseInt(t[e],10));return t.expression&&(n.expression=t.expression),n}))}function Et(e,t){return ct(e,t,!0,1)[0]}function Tt(e,t,n=!1){return Ve(ct(e,t,n,1)).trim()}function _t(e,t,n,s,r=!1){const i=Tt(n,s,r);i&&(e[t]=i)}function At(e){return"rss"===e||"feed"===e||"rdf:RDF"===e}const gt=Object.freeze(Object.defineProperty({__proto__:null,get DocumentPosition(){return lt},append:function(e,t){et(t);const{parent:n}=e,s=e.next;if(t.next=s,t.prev=e,e.next=t,t.parent=n,s){if(s.prev=t,n){const e=n.children;e.splice(e.lastIndexOf(s),0,t)}}else n&&n.children.push(t)},appendChild:function(e,t){if(et(t),t.next=null,t.parent=e,e.children.push(t)>1){const n=e.children[e.children.length-2];n.next=t,t.prev=n}else t.prev=null},compareDocumentPosition:ht,existsOne:function e(t,n){return n.some((n=>Z(n)&&(t(n)||e(t,n.children))))},filter:tt,find:nt,findAll:function(e,t){const n=[],s=[t],r=[0];for(;;){if(r[0]>=s[0].length){if(1===s.length)return n;s.shift(),r.shift();continue}const t=s[0][r[0]++];Z(t)&&(e(t)&&n.push(t),t.children.length>0&&(r.unshift(0),s.unshift(t.children)))}},findOne:st,findOneChild:function(e,t){return t.find(e)},getAttributeValue:function(e,t){var n;return null===(n=e.attribs)||void 0===n?void 0:n[t]},getChildren:Qe,getElementById:function(e,t,n=!0){return Array.isArray(t)||(t=[t]),st(it("id",e),t,n)},getElements:function(e,t,n,s=1/0){const r=at(e);return r?tt(r,t,n,s):[]},getElementsByTagName:ct,getElementsByTagType:function(e,t,n=!0,s=1/0){return tt(rt.tag_type(e),t,n,s)},getFeed:function(e){const t=Et(At,e);return t?"feed"===t.name?function(e){var t;const n=e.children,s={type:"atom",items:ct("entry",n).map((e=>{var t;const{children:n}=e,s={media:mt(n)};_t(s,"id","id",n),_t(s,"title","title",n);const r=null===(t=Et("link",n))||void 0===t?void 0:t.attribs.href;r&&(s.link=r);const i=Tt("summary",n)||Tt("content",n);i&&(s.description=i);const o=Tt("updated",n);return o&&(s.pubDate=new Date(o)),s}))};_t(s,"id","id",n),_t(s,"title","title",n);const r=null===(t=Et("link",n))||void 0===t?void 0:t.attribs.href;r&&(s.link=r);_t(s,"description","subtitle",n);const i=Tt("updated",n);i&&(s.updated=new Date(i));return _t(s,"author","email",n,!0),s}(t):function(e){var t,n;const s=null!==(n=null===(t=Et("channel",e.children))||void 0===t?void 0:t.children)&&void 0!==n?n:[],r={type:e.name.substr(0,3),id:"",items:ct("item",e.children).map((e=>{const{children:t}=e,n={media:mt(t)};_t(n,"id","guid",t),_t(n,"title","title",t),_t(n,"link","link",t),_t(n,"description","description",t);const s=Tt("pubDate",t)||Tt("dc:date",t);return s&&(n.pubDate=new Date(s)),n}))};_t(r,"title","title",s),_t(r,"link","link",s),_t(r,"description","description",s);const i=Tt("lastBuildDate",s);i&&(r.updated=new Date(i));return _t(r,"author","managingEditor",s,!0),r}(t):null},getInnerHTML:function(e,t){return re(e)?e.children.map((e=>Ke(e,t))).join(""):""},getName:function(e){return e.name},getOuterHTML:Ke,getParent:We,getSiblings:Xe,getText:function e(t){return Array.isArray(t)?t.map(e).join(""):Z(t)?"br"===t.name?"\n":e(t.children):J(t)?e(t.children):ee(t)?t.data:""},hasAttrib:function(e,t){return null!=e.attribs&&Object.prototype.hasOwnProperty.call(e.attribs,t)&&null!=e.attribs[t]},hasChildren:re,innerText:ze,isCDATA:J,isComment:te,isDocument:se,isTag:Z,isText:ee,nextElementSibling:Ze,prepend:function(e,t){et(t);const{parent:n}=e;if(n){const s=n.children;s.splice(s.indexOf(e),0,t)}e.prev&&(e.prev.next=t),t.parent=n,t.prev=e.prev,t.next=e,e.prev=t},prependChild:function(e,t){if(et(t),t.parent=e,t.prev=null,1!==e.children.unshift(t)){const n=e.children[1];n.prev=t,t.next=n}else t.next=null},prevElementSibling:Je,removeElement:et,removeSubsets:function(e){let t=e.length;for(;--t>=0;){const n=e[t];if(t>0&&e.lastIndexOf(n,t-1)>=0)e.splice(t,1);else for(let s=n.parent;s;s=s.parent)if(e.includes(s)){e.splice(t,1);break}}return e},replaceElement:function(e,t){const n=t.prev=e.prev;n&&(n.next=t);const s=t.next=e.next;s&&(s.prev=t);const r=t.parent=e.parent;if(r){const n=r.children;n[n.lastIndexOf(e)]=t,e.parent=null}},testElement:function(e,t){const n=at(e);return!n||n(t)},textContent:Ve,uniqueSort:dt},Symbol.toStringTag,{value:"Module"}));function Ct(e,t,n){return e?e(null!=t?t:e._root.children,null,void 0,n).toString():""}function bt(e){const t=e||(this?this.root():[]);let n="";for(let e=0;e<t.length;e++)n+=Ve(t[e]);return n}function Nt(e,t){if(t===e)return!1;let n=t;for(;n&&n!==n.parent;)if(n=n.parent,n===e)return!0;return!1}function It(e){if(Array.isArray(e))return!0;if("object"!=typeof e||!Object.prototype.hasOwnProperty.call(e,"length")||"number"!=typeof e.length||e.length<0)return!1;for(let t=0;t<e.length;t++)if(!(t in e))return!1;return!0}const kt=Object.freeze(Object.defineProperty({__proto__:null,contains:Nt,html:function(e,t){return Ct(this,function(e,t){return!t&&"object"==typeof e&&null!=e&&!("length"in e)&&!("type"in e)}(e)?void(t=e):e,{...O,...null==this?void 0:this._options,...R(null!=t?t:{})})},merge:function(e,t){if(!It(e)||!It(t))return;let n=e.length;const s=+t.length;for(let r=0;r<s;r++)e[n++]=t[r];return e.length=n,e},parseHTML:function(e,t,n="boolean"==typeof t&&t){if(!e||"string"!=typeof e)return null;"boolean"==typeof t&&(n=t);const s=this.load(e,O,!1);return n||s("script").remove(),s.root()[0].children.slice()},root:function(){return this(this._root)},text:bt,xml:function(e){return Ct(this,e,{...this._options,xmlMode:!0})}},Symbol.toStringTag,{value:"Module"}));function St(e){return null!=e.cheerio}function Dt(e,t){const n=e.length;for(let s=0;s<n;s++)t(e[s],s);return e}function yt(e){const t="length"in e?Array.prototype.map.call(e,(e=>ie(e,!0))):[ie(e,!0)],n=new W(t);return t.forEach((e=>{e.parent=n})),t}var Ot,Lt;function Rt(e){const t=e.indexOf("<");if(t<0||t>e.length-3)return!1;const n=e.charCodeAt(t+1);return(n>=Ot.LowerA&&n<=Ot.LowerZ||n>=Ot.UpperA&&n<=Ot.UpperZ||n===Ot.Exclamation)&&e.includes(">",t+2)}(Lt=Ot||(Ot={}))[Lt.LowerA=97]="LowerA",Lt[Lt.LowerZ=122]="LowerZ",Lt[Lt.UpperA=65]="UpperA",Lt[Lt.UpperZ=90]="UpperZ",Lt[Lt.Exclamation=33]="Exclamation";const Mt=Object.prototype.hasOwnProperty,vt=/\s+/,xt="data-",wt={null:null,true:!0,false:!1},Pt=/^(?:autofocus|autoplay|async|checked|controls|defer|disabled|hidden|loop|multiple|open|readonly|required|scoped|selected)$/i,Ft=/^{[^]*}$|^\[[^]*]$/;function Bt(e,t,n){var s;if(e&&Z(e))return null!==(s=e.attribs)&&void 0!==s||(e.attribs={}),t?Mt.call(e.attribs,t)?!n&&Pt.test(t)?t:e.attribs[t]:"option"===e.name&&"value"===t?bt(e.children):"input"!==e.name||"radio"!==e.attribs.type&&"checkbox"!==e.attribs.type||"value"!==t?void 0:"on":e.attribs}function Ut(e,t,n){null===n?Yt(e,t):e.attribs[t]=`${n}`}function Ht(e,t,n){return t in e?e[t]:!n&&Pt.test(t)?void 0!==Bt(e,t,!1):Bt(e,t,n)}function Gt(e,t,n,s){t in e?e[t]=n:Ut(e,t,!s&&Pt.test(t)?n?"":null:`${n}`)}function qt(e,t,n){var s;const r=e;null!==(s=r.data)&&void 0!==s||(r.data={}),"object"==typeof t?Object.assign(r.data,t):"string"==typeof t&&void 0!==n&&(r.data[t]=n)}function $t(e,t){let n,s,r;var i;null==t?(n=Object.keys(e.attribs).filter((e=>e.startsWith(xt))),s=n.map((e=>e.slice(xt.length).replace(/[_.-](\w|$)/g,((e,t)=>t.toUpperCase()))))):(n=[xt+(i=t,i.replace(/[A-Z]/g,"-$&").toLowerCase())],s=[t]);for(let t=0;t<n.length;++t){const i=n[t],o=s[t];if(Mt.call(e.attribs,i)&&!Mt.call(e.data,o)){if(r=e.attribs[i],Mt.call(wt,r))r=wt[r];else if(r===String(Number(r)))r=Number(r);else if(Ft.test(r))try{r=JSON.parse(r)}catch(e){}e.data[o]=r}}return null==t?e.data:r}function Yt(e,t){e.attribs&&Mt.call(e.attribs,t)&&delete e.attribs[t]}function jt(e){return e?e.trim().split(vt):[]}const Kt=Object.freeze(Object.defineProperty({__proto__:null,addClass:function e(t){if("function"==typeof t)return Dt(this,((n,s)=>{if(Z(n)){const r=n.attribs.class||"";e.call([n],t.call(n,s,r))}}));if(!t||"string"!=typeof t)return this;const n=t.split(vt),s=this.length;for(let e=0;e<s;e++){const t=this[e];if(!Z(t))continue;const s=Bt(t,"class",!1);if(s){let e=` ${s} `;for(let t=0;t<n.length;t++){const s=`${n[t]} `;e.includes(` ${s}`)||(e+=s)}Ut(t,"class",e.trim())}else Ut(t,"class",n.join(" ").trim())}return this},attr:function(e,t){if("object"==typeof e||void 0!==t){if("function"==typeof t){if("string"!=typeof e)throw new Error("Bad combination of arguments.");return Dt(this,((n,s)=>{Z(n)&&Ut(n,e,t.call(n,s,n.attribs[e]))}))}return Dt(this,(n=>{Z(n)&&("object"==typeof e?Object.keys(e).forEach((t=>{const s=e[t];Ut(n,t,s)})):Ut(n,e,t))}))}return arguments.length>1?this:Bt(this[0],e,this.options.xmlMode)},data:function(e,t){var n;const s=this[0];if(!s||!Z(s))return;const r=s;return null!==(n=r.data)&&void 0!==n||(r.data={}),e?"object"==typeof e||void 0!==t?(Dt(this,(n=>{Z(n)&&("object"==typeof e?qt(n,e):qt(n,e,t))})),this):Mt.call(r.data,e)?r.data[e]:$t(r,e):$t(r)},hasClass:function(e){return this.toArray().some((t=>{const n=Z(t)&&t.attribs.class;let s=-1;if(n&&e.length)for(;(s=n.indexOf(e,s+1))>-1;){const t=s+e.length;if((0===s||vt.test(n[s-1]))&&(t===n.length||vt.test(n[t])))return!0}return!1}))},prop:function(e,t){var n;if("string"==typeof e&&void 0===t){const t=this[0];if(!t||!Z(t))return;switch(e){case"style":{const e=this.css(),t=Object.keys(e);return t.forEach(((t,n)=>{e[n]=t})),e.length=t.length,e}case"tagName":case"nodeName":return t.name.toUpperCase();case"href":case"src":{const s=null===(n=t.attribs)||void 0===n?void 0:n[e];return"undefined"==typeof URL||("href"!==e||"a"!==t.tagName&&"link"!==t.name)&&("src"!==e||"img"!==t.tagName&&"iframe"!==t.tagName&&"audio"!==t.tagName&&"video"!==t.tagName&&"source"!==t.tagName)||void 0===s||!this.options.baseURI?s:new URL(s,this.options.baseURI).href}case"innerText":return ze(t);case"textContent":return Ve(t);case"outerHTML":return this.clone().wrap("<container />").parent().html();case"innerHTML":return this.html();default:return Ht(t,e,this.options.xmlMode)}}if("object"==typeof e||void 0!==t){if("function"==typeof t){if("object"==typeof e)throw new Error("Bad combination of arguments.");return Dt(this,((n,s)=>{Z(n)&&Gt(n,e,t.call(n,s,Ht(n,e,this.options.xmlMode)),this.options.xmlMode)}))}return Dt(this,(n=>{Z(n)&&("object"==typeof e?Object.keys(e).forEach((t=>{const s=e[t];Gt(n,t,s,this.options.xmlMode)})):Gt(n,e,t,this.options.xmlMode))}))}},removeAttr:function(e){const t=jt(e);for(let e=0;e<t.length;e++)Dt(this,(n=>{Z(n)&&Yt(n,t[e])}));return this},removeClass:function e(t){if("function"==typeof t)return Dt(this,((n,s)=>{Z(n)&&e.call([n],t.call(n,s,n.attribs.class||""))}));const n=jt(t),s=n.length,r=0===arguments.length;return Dt(this,(e=>{if(Z(e))if(r)e.attribs.class="";else{const t=jt(e.attribs.class);let r=!1;for(let e=0;e<s;e++){const s=t.indexOf(n[e]);s>=0&&(t.splice(s,1),r=!0,e--)}r&&(e.attribs.class=t.join(" "))}}))},toggleClass:function e(t,n){if("function"==typeof t)return Dt(this,((s,r)=>{Z(s)&&e.call([s],t.call(s,r,s.attribs.class||"",n),n)}));if(!t||"string"!=typeof t)return this;const s=t.split(vt),r=s.length,i="boolean"==typeof n?n?1:-1:0,o=this.length;for(let e=0;e<o;e++){const t=this[e];if(!Z(t))continue;const n=jt(t.attribs.class);for(let e=0;e<r;e++){const t=n.indexOf(s[e]);i>=0&&t<0?n.push(s[e]):i<=0&&t>=0&&n.splice(t,1)}t.attribs.class=n.join(" ")}return this},val:function(e){const t=0===arguments.length,n=this[0];if(!n||!Z(n))return t?void 0:this;switch(n.name){case"textarea":return this.text(e);case"select":{const n=this.find("option:selected");if(!t){if(null==this.attr("multiple")&&"object"==typeof e)return this;this.find("option").removeAttr("selected");const t="object"!=typeof e?[e]:e;for(let e=0;e<t.length;e++)this.find(`option[value="${t[e]}"]`).attr("selected","");return this}return this.attr("multiple")?n.toArray().map((e=>bt(e.children))):n.attr("value")}case"input":case"option":return t?this.attr("value"):this.attr("value",e)}}},Symbol.toStringTag,{value:"Module"}));var Vt,zt,Qt,Wt;(zt=Vt||(Vt={})).Attribute="attribute",zt.Pseudo="pseudo",zt.PseudoElement="pseudo-element",zt.Tag="tag",zt.Universal="universal",zt.Adjacent="adjacent",zt.Child="child",zt.Descendant="descendant",zt.Parent="parent",zt.Sibling="sibling",zt.ColumnCombinator="column-combinator",(Wt=Qt||(Qt={})).Any="any",Wt.Element="element",Wt.End="end",Wt.Equals="equals",Wt.Exists="exists",Wt.Hyphen="hyphen",Wt.Not="not",Wt.Start="start";const Xt=/^[^\\#]?(?:\\(?:[\da-f]{1,6}\s?|.)|[\w\-\u00b0-\uFFFF])+/,Zt=/\\([\da-f]{1,6}\s?|(\s)|.)/gi,Jt=new Map([[126,Qt.Element],[94,Qt.Start],[36,Qt.End],[42,Qt.Any],[33,Qt.Not],[124,Qt.Hyphen]]),en=new Set(["has","not","matches","is","where","host","host-context"]);function tn(e){switch(e.type){case Vt.Adjacent:case Vt.Child:case Vt.Descendant:case Vt.Parent:case Vt.Sibling:case Vt.ColumnCombinator:return!0;default:return!1}}const nn=new Set(["contains","icontains"]);function sn(e,t,n){const s=parseInt(t,16)-65536;return s!=s||n?t:s<0?String.fromCharCode(s+65536):String.fromCharCode(s>>10|55296,1023&s|56320)}function rn(e){return e.replace(Zt,sn)}function on(e){return 39===e||34===e}function an(e){return 32===e||9===e||10===e||12===e||13===e}function cn(e){const t=[],n=ln(t,`${e}`,0);if(n<e.length)throw new Error(`Unmatched selector: ${e.slice(n)}`);return t}function ln(e,t,n){let s=[];function r(e){const s=t.slice(n+e).match(Xt);if(!s)throw new Error(`Expected name, found ${t.slice(n)}`);const[r]=s;return n+=e+r.length,rn(r)}function i(e){for(n+=e;n<t.length&&an(t.charCodeAt(n));)n++}function o(){const e=n+=1;let s=1;for(;s>0&&n<t.length;n++)40!==t.charCodeAt(n)||a(n)?41!==t.charCodeAt(n)||a(n)||s--:s++;if(s)throw new Error("Parenthesis not matched");return rn(t.slice(e,n-1))}function a(e){let n=0;for(;92===t.charCodeAt(--e);)n++;return 1==(1&n)}function c(){if(s.length>0&&tn(s[s.length-1]))throw new Error("Did not expect successive traversals.")}function l(e){s.length>0&&s[s.length-1].type===Vt.Descendant?s[s.length-1].type=e:(c(),s.push({type:e}))}function u(e,t){s.push({type:Vt.Attribute,name:e,action:t,value:r(1),namespace:null,ignoreCase:"quirks"})}function h(){if(s.length&&s[s.length-1].type===Vt.Descendant&&s.pop(),0===s.length)throw new Error("Empty sub-selector");e.push(s)}if(i(0),t.length===n)return n;e:for(;n<t.length;){const e=t.charCodeAt(n);switch(e){case 32:case 9:case 10:case 12:case 13:0!==s.length&&s[0].type===Vt.Descendant||(c(),s.push({type:Vt.Descendant})),i(1);break;case 62:l(Vt.Child),i(1);break;case 60:l(Vt.Parent),i(1);break;case 126:l(Vt.Sibling),i(1);break;case 43:l(Vt.Adjacent),i(1);break;case 46:u("class",Qt.Element);break;case 35:u("id",Qt.Equals);break;case 91:{let e;i(1);let o=null;124===t.charCodeAt(n)?e=r(1):t.startsWith("*|",n)?(o="*",e=r(2)):(e=r(0),124===t.charCodeAt(n)&&61!==t.charCodeAt(n+1)&&(o=e,e=r(1))),i(0);let c=Qt.Exists;const l=Jt.get(t.charCodeAt(n));if(l){if(c=l,61!==t.charCodeAt(n+1))throw new Error("Expected `=`");i(2)}else 61===t.charCodeAt(n)&&(c=Qt.Equals,i(1));let u="",h=null;if("exists"!==c){if(on(t.charCodeAt(n))){const e=t.charCodeAt(n);let s=n+1;for(;s<t.length&&(t.charCodeAt(s)!==e||a(s));)s+=1;if(t.charCodeAt(s)!==e)throw new Error("Attribute value didn't end");u=rn(t.slice(n+1,s)),n=s+1}else{const e=n;for(;n<t.length&&(!an(t.charCodeAt(n))&&93!==t.charCodeAt(n)||a(n));)n+=1;u=rn(t.slice(e,n))}i(0);const e=32|t.charCodeAt(n);115===e?(h=!1,i(1)):105===e&&(h=!0,i(1))}if(93!==t.charCodeAt(n))throw new Error("Attribute selector didn't terminate");n+=1;const d={type:Vt.Attribute,name:e,action:c,value:u,namespace:o,ignoreCase:h};s.push(d);break}case 58:{if(58===t.charCodeAt(n+1)){s.push({type:Vt.PseudoElement,name:r(2).toLowerCase(),data:40===t.charCodeAt(n)?o():null});continue}const e=r(1).toLowerCase();let i=null;if(40===t.charCodeAt(n))if(en.has(e)){if(on(t.charCodeAt(n+1)))throw new Error(`Pseudo-selector ${e} cannot be quoted`);if(i=[],n=ln(i,t,n+1),41!==t.charCodeAt(n))throw new Error(`Missing closing parenthesis in :${e} (${t})`);n+=1}else{if(i=o(),nn.has(e)){const e=i.charCodeAt(0);e===i.charCodeAt(i.length-1)&&on(e)&&(i=i.slice(1,-1))}i=rn(i)}s.push({type:Vt.Pseudo,name:e,data:i});break}case 44:h(),s=[],i(1);break;default:{if(t.startsWith("/*",n)){const e=t.indexOf("*/",n+2);if(e<0)throw new Error("Comment was not terminated");n=e+2,0===s.length&&i(0);break}let o,a=null;if(42===e)n+=1,o="*";else if(124===e){if(o="",124===t.charCodeAt(n+1)){l(Vt.ColumnCombinator),i(2);break}}else{if(!Xt.test(t.slice(n)))break e;o=r(0)}124===t.charCodeAt(n)&&124!==t.charCodeAt(n+1)&&(a=o,42===t.charCodeAt(n+1)?(o="*",n+=2):o=r(1)),s.push("*"===o?{type:Vt.Universal,namespace:a}:{type:Vt.Tag,name:o,namespace:a})}}}return h(),n}function un(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var hn={trueFunc:function(){return!0},falseFunc:function(){return!1}};const dn=un(hn),pn=new Map([[Vt.Universal,50],[Vt.Tag,30],[Vt.Attribute,1],[Vt.Pseudo,0]]);function fn(e){return!pn.has(e.type)}const mn=new Map([[Qt.Exists,10],[Qt.Equals,8],[Qt.Not,7],[Qt.Start,6],[Qt.End,6],[Qt.Any,5]]);function En(e){const t=e.map(Tn);for(let n=1;n<e.length;n++){const s=t[n];if(!(s<0))for(let r=n-1;r>=0&&s<t[r];r--){const n=e[r+1];e[r+1]=e[r],e[r]=n,t[r+1]=t[r],t[r]=s}}}function Tn(e){var t,n;let s=null!==(t=pn.get(e.type))&&void 0!==t?t:-1;return e.type===Vt.Attribute?(s=null!==(n=mn.get(e.action))&&void 0!==n?n:4,e.action===Qt.Equals&&"id"===e.name&&(s=9),e.ignoreCase&&(s>>=1)):e.type===Vt.Pseudo&&(e.data?"has"===e.name||"contains"===e.name?s=0:Array.isArray(e.data)?(s=Math.min(...e.data.map((e=>Math.min(...e.map(Tn))))),s<0&&(s=0)):s=2:s=3),s}const _n=/[-[\]{}()*+?.,\\^$|#\s]/g;function An(e){return e.replace(_n,"\\$&")}const gn=new Set(["accept","accept-charset","align","alink","axis","bgcolor","charset","checked","clear","codetype","color","compact","declare","defer","dir","direction","disabled","enctype","face","frame","hreflang","http-equiv","lang","language","link","media","method","multiple","nohref","noresize","noshade","nowrap","readonly","rel","rev","rules","scope","scrolling","selected","shape","target","text","type","valign","valuetype","vlink"]);function Cn(e,t){return"boolean"==typeof e.ignoreCase?e.ignoreCase:"quirks"===e.ignoreCase?!!t.quirksMode:!t.xmlMode&&gn.has(e.name)}const bn={equals(e,t,n){const{adapter:s}=n,{name:r}=t;let{value:i}=t;return Cn(t,n)?(i=i.toLowerCase(),t=>{const n=s.getAttributeValue(t,r);return null!=n&&n.length===i.length&&n.toLowerCase()===i&&e(t)}):t=>s.getAttributeValue(t,r)===i&&e(t)},hyphen(e,t,n){const{adapter:s}=n,{name:r}=t;let{value:i}=t;const o=i.length;return Cn(t,n)?(i=i.toLowerCase(),function(t){const n=s.getAttributeValue(t,r);return null!=n&&(n.length===o||"-"===n.charAt(o))&&n.substr(0,o).toLowerCase()===i&&e(t)}):function(t){const n=s.getAttributeValue(t,r);return null!=n&&(n.length===o||"-"===n.charAt(o))&&n.substr(0,o)===i&&e(t)}},element(e,t,n){const{adapter:s}=n,{name:r,value:i}=t;if(/\s/.test(i))return dn.falseFunc;const o=new RegExp(`(?:^|\\s)${An(i)}(?:$|\\s)`,Cn(t,n)?"i":"");return function(t){const n=s.getAttributeValue(t,r);return null!=n&&n.length>=i.length&&o.test(n)&&e(t)}},exists:(e,{name:t},{adapter:n})=>s=>n.hasAttrib(s,t)&&e(s),start(e,t,n){const{adapter:s}=n,{name:r}=t;let{value:i}=t;const o=i.length;return 0===o?dn.falseFunc:Cn(t,n)?(i=i.toLowerCase(),t=>{const n=s.getAttributeValue(t,r);return null!=n&&n.length>=o&&n.substr(0,o).toLowerCase()===i&&e(t)}):t=>{var n;return!!(null===(n=s.getAttributeValue(t,r))||void 0===n?void 0:n.startsWith(i))&&e(t)}},end(e,t,n){const{adapter:s}=n,{name:r}=t;let{value:i}=t;const o=-i.length;return 0===o?dn.falseFunc:Cn(t,n)?(i=i.toLowerCase(),t=>{var n;return(null===(n=s.getAttributeValue(t,r))||void 0===n?void 0:n.substr(o).toLowerCase())===i&&e(t)}):t=>{var n;return!!(null===(n=s.getAttributeValue(t,r))||void 0===n?void 0:n.endsWith(i))&&e(t)}},any(e,t,n){const{adapter:s}=n,{name:r,value:i}=t;if(""===i)return dn.falseFunc;if(Cn(t,n)){const t=new RegExp(An(i),"i");return function(n){const o=s.getAttributeValue(n,r);return null!=o&&o.length>=i.length&&t.test(o)&&e(n)}}return t=>{var n;return!!(null===(n=s.getAttributeValue(t,r))||void 0===n?void 0:n.includes(i))&&e(t)}},not(e,t,n){const{adapter:s}=n,{name:r}=t;let{value:i}=t;return""===i?t=>!!s.getAttributeValue(t,r)&&e(t):Cn(t,n)?(i=i.toLowerCase(),t=>{const n=s.getAttributeValue(t,r);return(null==n||n.length!==i.length||n.toLowerCase()!==i)&&e(t)}):t=>s.getAttributeValue(t,r)!==i&&e(t)}},Nn=new Set([9,10,12,13,32]),In="0".charCodeAt(0),kn="9".charCodeAt(0);function Sn(e){return function(e){const t=e[0],n=e[1]-1;if(n<0&&t<=0)return dn.falseFunc;if(-1===t)return e=>e<=n;if(0===t)return e=>e===n;if(1===t)return n<0?dn.trueFunc:e=>e>=n;const s=Math.abs(t),r=(n%s+s)%s;return t>1?e=>e>=n&&e%s===r:e=>e<=n&&e%s===r}(function(e){if("even"===(e=e.trim().toLowerCase()))return[2,0];if("odd"===e)return[2,1];let t=0,n=0,s=i(),r=o();if(t<e.length&&"n"===e.charAt(t)&&(t++,n=s*(null!=r?r:1),a(),t<e.length?(s=i(),a(),r=o()):s=r=0),null===r||t<e.length)throw new Error(`n-th rule couldn't be parsed ('${e}')`);return[n,s*r];function i(){return"-"===e.charAt(t)?(t++,-1):("+"===e.charAt(t)&&t++,1)}function o(){const n=t;let s=0;for(;t<e.length&&e.charCodeAt(t)>=In&&e.charCodeAt(t)<=kn;)s=10*s+(e.charCodeAt(t)-In),t++;return t===n?null:s}function a(){for(;t<e.length&&Nn.has(e.charCodeAt(t));)t++}}(e))}function Dn(e,t){return n=>{const s=t.getParent(n);return null!=s&&t.isTag(s)&&e(n)}}const yn={contains:(e,t,{adapter:n})=>function(s){return e(s)&&n.getText(s).includes(t)},icontains(e,t,{adapter:n}){const s=t.toLowerCase();return function(t){return e(t)&&n.getText(t).toLowerCase().includes(s)}},"nth-child"(e,t,{adapter:n,equals:s}){const r=Sn(t);return r===dn.falseFunc?dn.falseFunc:r===dn.trueFunc?Dn(e,n):function(t){const i=n.getSiblings(t);let o=0;for(let e=0;e<i.length&&!s(t,i[e]);e++)n.isTag(i[e])&&o++;return r(o)&&e(t)}},"nth-last-child"(e,t,{adapter:n,equals:s}){const r=Sn(t);return r===dn.falseFunc?dn.falseFunc:r===dn.trueFunc?Dn(e,n):function(t){const i=n.getSiblings(t);let o=0;for(let e=i.length-1;e>=0&&!s(t,i[e]);e--)n.isTag(i[e])&&o++;return r(o)&&e(t)}},"nth-of-type"(e,t,{adapter:n,equals:s}){const r=Sn(t);return r===dn.falseFunc?dn.falseFunc:r===dn.trueFunc?Dn(e,n):function(t){const i=n.getSiblings(t);let o=0;for(let e=0;e<i.length;e++){const r=i[e];if(s(t,r))break;n.isTag(r)&&n.getName(r)===n.getName(t)&&o++}return r(o)&&e(t)}},"nth-last-of-type"(e,t,{adapter:n,equals:s}){const r=Sn(t);return r===dn.falseFunc?dn.falseFunc:r===dn.trueFunc?Dn(e,n):function(t){const i=n.getSiblings(t);let o=0;for(let e=i.length-1;e>=0;e--){const r=i[e];if(s(t,r))break;n.isTag(r)&&n.getName(r)===n.getName(t)&&o++}return r(o)&&e(t)}},root:(e,t,{adapter:n})=>t=>{const s=n.getParent(t);return(null==s||!n.isTag(s))&&e(t)},scope(e,t,n,s){const{equals:r}=n;return s&&0!==s.length?1===s.length?t=>r(s[0],t)&&e(t):t=>s.includes(t)&&e(t):yn.root(e,t,n)},hover:On("isHovered"),visited:On("isVisited"),active:On("isActive")};function On(e){return function(t,n,{adapter:s}){const r=s[e];return"function"!=typeof r?dn.falseFunc:function(e){return r(e)&&t(e)}}}const Ln={empty:(e,{adapter:t})=>!t.getChildren(e).some((e=>t.isTag(e)||""!==t.getText(e))),"first-child"(e,{adapter:t,equals:n}){if(t.prevElementSibling)return null==t.prevElementSibling(e);const s=t.getSiblings(e).find((e=>t.isTag(e)));return null!=s&&n(e,s)},"last-child"(e,{adapter:t,equals:n}){const s=t.getSiblings(e);for(let r=s.length-1;r>=0;r--){if(n(e,s[r]))return!0;if(t.isTag(s[r]))break}return!1},"first-of-type"(e,{adapter:t,equals:n}){const s=t.getSiblings(e),r=t.getName(e);for(let i=0;i<s.length;i++){const o=s[i];if(n(e,o))return!0;if(t.isTag(o)&&t.getName(o)===r)break}return!1},"last-of-type"(e,{adapter:t,equals:n}){const s=t.getSiblings(e),r=t.getName(e);for(let i=s.length-1;i>=0;i--){const o=s[i];if(n(e,o))return!0;if(t.isTag(o)&&t.getName(o)===r)break}return!1},"only-of-type"(e,{adapter:t,equals:n}){const s=t.getName(e);return t.getSiblings(e).every((r=>n(e,r)||!t.isTag(r)||t.getName(r)!==s))},"only-child":(e,{adapter:t,equals:n})=>t.getSiblings(e).every((s=>n(e,s)||!t.isTag(s)))};function Rn(e,t,n,s){if(null===n){if(e.length>s)throw new Error(`Pseudo-class :${t} requires an argument`)}else if(e.length===s)throw new Error(`Pseudo-class :${t} doesn't have any arguments`)}const Mn={"any-link":":is(a, area, link)[href]",link:":any-link:not(:visited)",disabled:":is(\n        :is(button, input, select, textarea, optgroup, option)[disabled],\n        optgroup[disabled] > option,\n        fieldset[disabled]:not(fieldset[disabled] legend:first-of-type *)\n    )",enabled:":not(:disabled)",checked:":is(:is(input[type=radio], input[type=checkbox])[checked], option:selected)",required:":is(input, select, textarea)[required]",optional:":is(input, select, textarea):not([required])",selected:"option:is([selected], select:not([multiple]):not(:has(> option[selected])) > :first-of-type)",checkbox:"[type=checkbox]",file:"[type=file]",password:"[type=password]",radio:"[type=radio]",reset:"[type=reset]",image:"[type=image]",submit:"[type=submit]",parent:":not(:empty)",header:":is(h1, h2, h3, h4, h5, h6)",button:":is(button, input[type=button])",input:":is(input, textarea, select, button)",text:"input:is(:not([type!='']), [type=text])"},vn={};function xn(e,t){const n=t.getSiblings(e);if(n.length<=1)return[];const s=n.indexOf(e);return s<0||s===n.length-1?[]:n.slice(s+1).filter(t.isTag)}function wn(e){return{xmlMode:!!e.xmlMode,lowerCaseAttributeNames:!!e.lowerCaseAttributeNames,lowerCaseTags:!!e.lowerCaseTags,quirksMode:!!e.quirksMode,cacheResults:!!e.cacheResults,pseudos:e.pseudos,adapter:e.adapter,equals:e.equals}}const Pn=(e,t,n,s,r)=>{const i=r(t,wn(n),s);return i===dn.trueFunc?e:i===dn.falseFunc?dn.falseFunc:t=>i(t)&&e(t)},Fn={is:Pn,matches:Pn,where:Pn,not(e,t,n,s,r){const i=r(t,wn(n),s);return i===dn.falseFunc?e:i===dn.trueFunc?dn.falseFunc:t=>!i(t)&&e(t)},has(e,t,n,s,r){const{adapter:i}=n,o=wn(n);o.relativeSelector=!0;const a=t.some((e=>e.some(fn)))?[vn]:void 0,c=r(t,o,a);if(c===dn.falseFunc)return dn.falseFunc;const l=function(e,t){return e===dn.falseFunc?dn.falseFunc:n=>t.isTag(n)&&e(n)}(c,i);if(a&&c!==dn.trueFunc){const{shouldTestNextSiblings:t=!1}=c;return n=>{if(!e(n))return!1;a[0]=n;const s=i.getChildren(n),r=t?[...s,...xn(n,i)]:s;return i.existsOne(l,r)}}return t=>e(t)&&i.existsOne(l,i.getChildren(t))}};function Bn(e,t){const n=t.getParent(e);return n&&t.isTag(n)?n:null}function Un(e,t,n,s,r){const{adapter:i,equals:o}=n;switch(t.type){case Vt.PseudoElement:throw new Error("Pseudo-elements are not supported by css-select");case Vt.ColumnCombinator:throw new Error("Column combinators are not yet supported by css-select");case Vt.Attribute:if(null!=t.namespace)throw new Error("Namespaced attributes are not yet supported by css-select");return n.xmlMode&&!n.lowerCaseAttributeNames||(t.name=t.name.toLowerCase()),bn[t.action](e,t,n);case Vt.Pseudo:return function(e,t,n,s,r){var i;const{name:o,data:a}=t;if(Array.isArray(a)){if(!(o in Fn))throw new Error(`Unknown pseudo-class :${o}(${a})`);return Fn[o](e,a,n,s,r)}const c=null===(i=n.pseudos)||void 0===i?void 0:i[o],l="string"==typeof c?c:Mn[o];if("string"==typeof l){if(null!=a)throw new Error(`Pseudo ${o} doesn't have any arguments`);const t=cn(l);return Fn.is(e,t,n,s,r)}if("function"==typeof c)return Rn(c,o,a,1),t=>c(t,a)&&e(t);if(o in yn)return yn[o](e,a,n,s);if(o in Ln){const t=Ln[o];return Rn(t,o,a,2),s=>t(s,n,a)&&e(s)}throw new Error(`Unknown pseudo-class :${o}`)}(e,t,n,s,r);case Vt.Tag:{if(null!=t.namespace)throw new Error("Namespaced tag names are not yet supported by css-select");let{name:s}=t;return n.xmlMode&&!n.lowerCaseTags||(s=s.toLowerCase()),function(t){return i.getName(t)===s&&e(t)}}case Vt.Descendant:{if(!1===n.cacheResults||"undefined"==typeof WeakSet)return function(t){let n=t;for(;n=Bn(n,i);)if(e(n))return!0;return!1};const t=new WeakSet;return function(n){let s=n;for(;s=Bn(s,i);)if(!t.has(s)){if(i.isTag(s)&&e(s))return!0;t.add(s)}return!1}}case"_flexibleDescendant":return function(t){let n=t;do{if(e(n))return!0}while(n=Bn(n,i));return!1};case Vt.Parent:return function(t){return i.getChildren(t).some((t=>i.isTag(t)&&e(t)))};case Vt.Child:return function(t){const n=i.getParent(t);return null!=n&&i.isTag(n)&&e(n)};case Vt.Sibling:return function(t){const n=i.getSiblings(t);for(let s=0;s<n.length;s++){const r=n[s];if(o(t,r))break;if(i.isTag(r)&&e(r))return!0}return!1};case Vt.Adjacent:return i.prevElementSibling?function(t){const n=i.prevElementSibling(t);return null!=n&&e(n)}:function(t){const n=i.getSiblings(t);let s;for(let e=0;e<n.length;e++){const r=n[e];if(o(t,r))break;i.isTag(r)&&(s=r)}return!!s&&e(s)};case Vt.Universal:if(null!=t.namespace&&"*"!==t.namespace)throw new Error("Namespaced universal selectors are not yet supported by css-select");return e}}function Hn(e){return e.type===Vt.Pseudo&&("scope"===e.name||Array.isArray(e.data)&&e.data.some((e=>e.some(Hn))))}const Gn={type:Vt.Descendant},qn={type:"_flexibleDescendant"},$n={type:Vt.Pseudo,name:"scope",data:null};function Yn(e,t,n){var s;e.forEach(En),n=null!==(s=t.context)&&void 0!==s?s:n;const r=Array.isArray(n),i=n&&(Array.isArray(n)?n:[n]);if(!1!==t.relativeSelector)!function(e,{adapter:t},n){const s=!!(null==n?void 0:n.every((e=>{const n=t.isTag(e)&&t.getParent(e);return e===vn||n&&t.isTag(n)})));for(const t of e){if(t.length>0&&fn(t[0])&&t[0].type!==Vt.Descendant);else{if(!s||t.some(Hn))continue;t.unshift(Gn)}t.unshift($n)}}(e,t,i);else if(e.some((e=>e.length>0&&fn(e[0]))))throw new Error("Relative selectors are not allowed when the `relativeSelector` option is disabled");let o=!1;const a=e.map((e=>{if(e.length>=2){const[t,n]=e;t.type!==Vt.Pseudo||"scope"!==t.name||(r&&n.type===Vt.Descendant?e[1]=qn:n.type!==Vt.Adjacent&&n.type!==Vt.Sibling||(o=!0))}return function(e,t,n){var s;return e.reduce(((e,s)=>e===dn.falseFunc?dn.falseFunc:Un(e,s,t,n,Yn)),null!==(s=t.rootFunc)&&void 0!==s?s:dn.trueFunc)}(e,t,i)})).reduce(jn,dn.falseFunc);return a.shouldTestNextSiblings=o,a}function jn(e,t){return t===dn.falseFunc||e===dn.trueFunc?e:e===dn.falseFunc||t===dn.trueFunc?t:function(n){return e(n)||t(n)}}const Kn=(e,t)=>e===t,Vn={adapter:gt,equals:Kn};const zn=(Qn=Yn,function(e,t,n){const s=function(e){var t,n,s,r;const i=null!=e?e:Vn;return null!==(t=i.adapter)&&void 0!==t||(i.adapter=gt),null!==(n=i.equals)&&void 0!==n||(i.equals=null!==(r=null===(s=i.adapter)||void 0===s?void 0:s.equals)&&void 0!==r?r:Kn),i}(t);return Qn(e,s,n)});var Qn;function Wn(e,t,n=!1){return n&&(e=function(e,t){const n=Array.isArray(e)?e.slice(0):[e],s=n.length;for(let e=0;e<s;e++){const s=xn(n[e],t);n.push(...s)}return n}(e,t)),Array.isArray(e)?t.removeSubsets(e):t.getChildren(e)}const Xn=new Set(["first","last","eq","gt","nth","lt","even","odd"]);function Zn(e){return"pseudo"===e.type&&(!!Xn.has(e.name)||!("not"!==e.name||!Array.isArray(e.data))&&e.data.some((e=>e.some(Zn))))}function Jn(e){const t=[],n=[];for(const s of e)s.some(Zn)?t.push(s):n.push(s);return[n,t]}const es={type:Vt.Universal,namespace:null},ts={type:Vt.Pseudo,name:"scope",data:null};function ns(e,t,n={}){return ss([e],t,n)}function ss(e,t,n={}){if("function"==typeof t)return e.some(t);const[s,r]=Jn(cn(t));return s.length>0&&e.some(zn(s,n))||r.some((t=>os(t,e,n).length>0))}function rs(e,t,n={}){return is(cn(e),t,n)}function is(e,t,n){if(0===t.length)return[];const[s,r]=Jn(e);let i;if(s.length){const e=us(t,s,n);if(0===r.length)return e;e.length&&(i=new Set(e))}for(let e=0;e<r.length&&(null==i?void 0:i.size)!==t.length;e++){const s=r[e];if(0===(i?t.filter((e=>Z(e)&&!i.has(e))):t).length)break;const o=os(s,t,n);if(o.length)if(i)o.forEach((e=>i.add(e)));else{if(e===r.length-1)return o;i=new Set(o)}}return void 0!==i?i.size===t.length?t:t.filter((e=>i.has(e))):[]}function os(e,t,n){var s;if(e.some(tn)){const r=null!==(s=n.root)&&void 0!==s?s:function(e){for(;e.parent;)e=e.parent;return e}(t[0]),i={...n,context:t,relativeSelector:!1};return e.push(ts),as(r,e,i,!0,t.length)}return as(t,e,n,!1,t.length)}function as(e,t,n,s,r){const i=t.findIndex(Zn),o=t.slice(0,i),a=t[i],c=t.length-1===i?r:1/0,l=function(e,t,n){const s=null!=t?parseInt(t,10):NaN;switch(e){case"first":return 1;case"nth":case"eq":return isFinite(s)?s>=0?s+1:1/0:0;case"lt":return isFinite(s)?s>=0?Math.min(s,n):1/0:0;case"gt":return isFinite(s)?1/0:0;case"odd":return 2*n;case"even":return 2*n-1;case"last":case"not":return 1/0}}(a.name,a.data,c);if(0===l)return[];const u=(0!==o.length||Array.isArray(e)?0===o.length?(Array.isArray(e)?e:[e]).filter(Z):s||o.some(tn)?cs(e,[o],n,l):us(e,[o],n):Qe(e).filter(Z)).slice(0,l);let h=function(e,t,n,s){const r="string"==typeof n?parseInt(n,10):NaN;switch(e){case"first":case"lt":return t;case"last":return t.length>0?[t[t.length-1]]:t;case"nth":case"eq":return isFinite(r)&&Math.abs(r)<t.length?[r<0?t[t.length+r]:t[r]]:[];case"gt":return isFinite(r)?t.slice(r+1):[];case"even":return t.filter(((e,t)=>t%2==0));case"odd":return t.filter(((e,t)=>t%2==1));case"not":{const e=new Set(is(n,t,s));return t.filter((t=>!e.has(t)))}}}(a.name,u,a.data,n);if(0===h.length||t.length===i+1)return h;const d=t.slice(i+1),p=d.some(tn);if(p){if(tn(d[0])){const{type:e}=d[0];e!==Vt.Sibling&&e!==Vt.Adjacent||(h=Wn(h,gt,!0)),d.unshift(es)}n={...n,relativeSelector:!1,rootFunc:e=>h.includes(e)}}else n.rootFunc&&n.rootFunc!==hn.trueFunc&&(n={...n,rootFunc:hn.trueFunc});return d.some(Zn)?as(h,d,n,!1,r):p?cs(h,[d],n,r):us(h,[d],n)}function cs(e,t,n,s){return ls(e,zn(t,n,e),s)}function ls(e,t,n=1/0){return nt((e=>Z(e)&&t(e)),Wn(e,gt,t.shouldTestNextSiblings),!0,n)}function us(e,t,n){const s=(Array.isArray(e)?e:[e]).filter(Z);if(0===s.length)return s;const r=zn(t,n);return r===hn.trueFunc?s:s.filter(r)}const hs=/^\s*[~+]/;function ds(e){return function(t,...n){return function(s){var r;let i=e(t,this);return s&&(i=Os(i,s,this.options.xmlMode,null===(r=this._root)||void 0===r?void 0:r[0])),this._make(this.length>1&&i.length>1?n.reduce(((e,t)=>t(e)),i):i)}}}const ps=ds(((e,t)=>{const n=[];for(let s=0;s<t.length;s++){const r=e(t[s]);n.push(r)}return(new Array).concat(...n)})),fs=ds(((e,t)=>{const n=[];for(let s=0;s<t.length;s++){const r=e(t[s]);null!==r&&n.push(r)}return n}));function ms(e,...t){let n=null;const s=ds(((e,t)=>{const s=[];return Dt(t,(t=>{for(let r;(r=e(t))&&!(null==n?void 0:n(r,s.length));t=r)s.push(r)})),s}))(e,...t);return function(e,t){n="string"==typeof e?t=>ns(t,e,this.options):e?ys(e):null;const r=s.call(this,t);return n=null,r}}function Es(e){return Array.from(new Set(e))}const Ts=fs((({parent:e})=>e&&!se(e)?e:null),Es),_s=ps((e=>{const t=[];for(;e.parent&&!se(e.parent);)t.push(e.parent),e=e.parent;return t}),dt,(e=>e.reverse())),As=ms((({parent:e})=>e&&!se(e)?e:null),dt,(e=>e.reverse()));const gs=fs((e=>Ze(e))),Cs=ps((e=>{const t=[];for(;e.next;)Z(e=e.next)&&t.push(e);return t}),Es),bs=ms((e=>Ze(e)),Es),Ns=fs((e=>Je(e))),Is=ps((e=>{const t=[];for(;e.prev;)Z(e=e.prev)&&t.push(e);return t}),Es),ks=ms((e=>Je(e)),Es),Ss=ps((e=>Xe(e).filter((t=>Z(t)&&t!==e))),dt),Ds=ps((e=>Qe(e).filter(Z)),Es);function ys(e){return"function"==typeof e?(t,n)=>e.call(t,n,t):St(e)?t=>Array.prototype.includes.call(e,t):function(t){return e===t}}function Os(e,t,n,s){return"string"==typeof t?rs(t,e,{xmlMode:n,root:s}):e.filter(ys(t))}const Ls=Object.freeze(Object.defineProperty({__proto__:null,add:function(e,t){const n=this._make(e,t),s=dt([...this.get(),...n.get()]);return this._make(s)},addBack:function(e){return this.prevObject?this.add(e?this.prevObject.filter(e):this.prevObject):this},children:Ds,closest:function(e){var t;const n=[];if(!e)return this._make(n);const s={xmlMode:this.options.xmlMode,root:null===(t=this._root)||void 0===t?void 0:t[0]},r="string"==typeof e?t=>ns(t,e,s):ys(e);return Dt(this,(e=>{for(;e&&Z(e);){if(r(e,0)){n.includes(e)||n.push(e);break}e=e.parent}})),this._make(n)},contents:function(){const e=this.toArray().reduce(((e,t)=>re(t)?e.concat(t.children):e),[]);return this._make(e)},each:function(e){let t=0;const n=this.length;for(;t<n&&!1!==e.call(this[t],t,this[t]);)++t;return this},end:function(){var e;return null!==(e=this.prevObject)&&void 0!==e?e:this._make([])},eq:function(e){var t;return 0===(e=+e)&&this.length<=1?this:(e<0&&(e=this.length+e),this._make(null!==(t=this[e])&&void 0!==t?t:[]))},filter:function(e){var t;return this._make(Os(this.toArray(),e,this.options.xmlMode,null===(t=this._root)||void 0===t?void 0:t[0]))},filterArray:Os,find:function(e){var t;if(!e)return this._make([]);const n=this.toArray();if("string"!=typeof e){const t=St(e)?e.toArray():[e];return this._make(t.filter((e=>n.some((t=>Nt(t,e))))))}const s=hs.test(e)?n:this.children().toArray(),r={context:n,root:null===(t=this._root)||void 0===t?void 0:t[0],xmlMode:this.options.xmlMode,lowerCaseTags:this.options.lowerCaseTags,lowerCaseAttributeNames:this.options.lowerCaseAttributeNames,pseudos:this.options.pseudos,quirksMode:this.options.quirksMode};return this._make(function(e,t,n={},s=1/0){if("function"==typeof e)return ls(t,e);const[r,i]=Jn(cn(e)),o=i.map((e=>as(t,e,n,!0,s)));return r.length&&o.push(cs(t,r,n,s)),0===o.length?[]:1===o.length?o[0]:dt(o.reduce(((e,t)=>[...e,...t])))}(e,s,r))},first:function(){return this.length>1?this._make(this[0]):this},get:function(e){return null==e?this.toArray():this[e<0?this.length+e:e]},has:function(e){return this.filter("string"==typeof e?`:has(${e})`:(t,n)=>this._make(n).find(e).length>0)},index:function(e){let t,n;return null==e?(t=this.parent().children(),n=this[0]):"string"==typeof e?(t=this._make(e),n=this[0]):(t=this,n=St(e)?e[0]:e),Array.prototype.indexOf.call(t,n)},is:function(e){const t=this.toArray();return"string"==typeof e?ss(t.filter(Z),e,this.options):!!e&&t.some(ys(e))},last:function(){return this.length>0?this._make(this[this.length-1]):this},map:function(e){let t=[];for(let n=0;n<this.length;n++){const s=this[n],r=e.call(s,n,s);null!=r&&(t=t.concat(r))}return this._make(t)},next:gs,nextAll:Cs,nextUntil:bs,not:function(e){let t=this.toArray();if("string"==typeof e){const n=new Set(rs(e,t,this.options));t=t.filter((e=>!n.has(e)))}else{const n=ys(e);t=t.filter(((e,t)=>!n(e,t)))}return this._make(t)},parent:Ts,parents:_s,parentsUntil:As,prev:Ns,prevAll:Is,prevUntil:ks,siblings:Ss,slice:function(e,t){return this._make(Array.prototype.slice.call(this,e,t))},toArray:function(){return Array.prototype.slice.call(this)}},Symbol.toStringTag,{value:"Module"}));function Rs(e,t){const n=Array.isArray(e)?e:[e];t?t.children=n:t=null;for(let e=0;e<n.length;e++){const s=n[e];s.parent&&s.parent.children!==n&&et(s),t?(s.prev=n[e-1]||null,s.next=n[e+1]||null):s.prev=s.next=null,s.parent=t}return t}function Ms(e){return function(...t){const n=this.length-1;return Dt(this,((s,r)=>{if(!re(s))return;const i="function"==typeof t[0]?t[0].call(s,r,this._render(s.children)):t,o=this._makeDomArray(i,r<n);e(o,s.children,s)}))}}function vs(e,t,n,s,r){var i,o;const a=[t,n,...s],c=0===t?null:e[t-1],l=t+n>=e.length?null:e[t+n];for(let e=0;e<s.length;++e){const n=s[e],u=n.parent;if(u){const e=u.children.indexOf(n);e>-1&&(u.children.splice(e,1),r===u&&t>e&&a[0]--)}n.parent=r,n.prev&&(n.prev.next=null!==(i=n.next)&&void 0!==i?i:null),n.next&&(n.next.prev=null!==(o=n.prev)&&void 0!==o?o:null),n.prev=0===e?c:s[e-1],n.next=e===s.length-1?l:s[e+1]}return c&&(c.next=s[0]),l&&(l.prev=s[s.length-1]),e.splice(...a)}const xs=Ms(((e,t,n)=>{vs(t,t.length,0,e,n)})),ws=Ms(((e,t,n)=>{vs(t,0,0,e,n)}));function Ps(e){return function(t){const n=this.length-1,s=this.parents().last();for(let r=0;r<this.length;r++){const i=this[r],o="function"==typeof t?t.call(i,r,i):"string"!=typeof t||Rt(t)?t:s.find(t).clone(),[a]=this._makeDomArray(o,r<n);if(!a||!re(a))continue;let c=a,l=0;for(;l<c.children.length;){const e=c.children[l];Z(e)?(c=e,l=0):l++}e(i,c,[a])}return this}}const Fs=Ps(((e,t,n)=>{const{parent:s}=e;if(!s)return;const r=s.children,i=r.indexOf(e);Rs([e],t),vs(r,i,0,n,s)})),Bs=Ps(((e,t,n)=>{re(e)&&(Rs(e.children,t),Rs(n,e))}));const Us=Object.freeze(Object.defineProperty({__proto__:null,_makeDomArray:function(e,t){return null==e?[]:St(e)?t?yt(e.get()):e.get():Array.isArray(e)?e.reduce(((e,n)=>e.concat(this._makeDomArray(n,t))),[]):"string"==typeof e?this._parse(e,this.options,!1,null).children:t?yt([e]):[e]},after:function(...e){const t=this.length-1;return Dt(this,((n,s)=>{const{parent:r}=n;if(!re(n)||!r)return;const i=r.children,o=i.indexOf(n);if(o<0)return;const a="function"==typeof e[0]?e[0].call(n,s,this._render(n.children)):e;vs(i,o+1,0,this._makeDomArray(a,s<t),r)}))},append:xs,appendTo:function(e){return(St(e)?e:this._make(e)).append(this),this},before:function(...e){const t=this.length-1;return Dt(this,((n,s)=>{const{parent:r}=n;if(!re(n)||!r)return;const i=r.children,o=i.indexOf(n);if(o<0)return;const a="function"==typeof e[0]?e[0].call(n,s,this._render(n.children)):e;vs(i,o,0,this._makeDomArray(a,s<t),r)}))},clone:function(){return this._make(yt(this.get()))},empty:function(){return Dt(this,(e=>{re(e)&&(e.children.forEach((e=>{e.next=e.prev=e.parent=null})),e.children.length=0)}))},html:function(e){if(void 0===e){const e=this[0];return e&&re(e)?this._render(e.children):null}return Dt(this,(t=>{if(!re(t))return;t.children.forEach((e=>{e.next=e.prev=e.parent=null}));Rs(St(e)?e.toArray():this._parse(`${e}`,this.options,!1,t).children,t)}))},insertAfter:function(e){"string"==typeof e&&(e=this._make(e)),this.remove();const t=[];return this._makeDomArray(e).forEach((e=>{const n=this.clone().toArray(),{parent:s}=e;if(!s)return;const r=s.children,i=r.indexOf(e);i<0||(vs(r,i+1,0,n,s),t.push(...n))})),this._make(t)},insertBefore:function(e){const t=this._make(e);this.remove();const n=[];return Dt(t,(e=>{const t=this.clone().toArray(),{parent:s}=e;if(!s)return;const r=s.children,i=r.indexOf(e);i<0||(vs(r,i,0,t,s),n.push(...t))})),this._make(n)},prepend:ws,prependTo:function(e){return(St(e)?e:this._make(e)).prepend(this),this},remove:function(e){return Dt(e?this.filter(e):this,(e=>{et(e),e.prev=e.next=e.parent=null})),this},replaceWith:function(e){return Dt(this,((t,n)=>{const{parent:s}=t;if(!s)return;const r=s.children,i="function"==typeof e?e.call(t,n,t):e,o=this._makeDomArray(i);Rs(o,null);const a=r.indexOf(t);vs(r,a,1,o,s),o.includes(t)||(t.parent=t.prev=t.next=null)}))},text:function(e){return void 0===e?bt(this):Dt(this,"function"==typeof e?(t,n)=>this._make(t).text(e.call(t,n,bt([t]))):t=>{if(!re(t))return;t.children.forEach((e=>{e.next=e.prev=e.parent=null}));Rs(new j(`${e}`),t)})},toString:function(){return this._render(this)},unwrap:function(e){return this.parent(e).not("body").each(((e,t)=>{this._make(t).replaceWith(t.children)})),this},wrap:Fs,wrapAll:function(e){const t=this[0];if(t){const n=this._make("function"==typeof e?e.call(t,0,t):e).insertBefore(t);let s;for(let e=0;e<n.length;e++)"tag"===n[e].type&&(s=n[e]);let r=0;for(;s&&r<s.children.length;){const e=s.children[r];"tag"===e.type?(s=e,r=0):r++}s&&this._make(s).append(this)}return this},wrapInner:Bs},Symbol.toStringTag,{value:"Module"}));function Hs(e,t,n,s){if("string"==typeof t){const i=Gs(e),o="function"==typeof n?n.call(e,s,i[t]):n;""===o?delete i[t]:null!=o&&(i[t]=o),e.attribs.style=(r=i,Object.keys(r).reduce(((e,t)=>`${e}${e?" ":""}${t}: ${r[t]};`),""))}else"object"==typeof t&&Object.keys(t).forEach(((n,s)=>{Hs(e,n,t[n],s)}));var r}function Gs(e,t){if(!e||!Z(e))return;const n=function(e){if(e=(e||"").trim(),!e)return{};const t={};let n;for(const s of e.split(";")){const e=s.indexOf(":");if(e<1||e===s.length-1){const e=s.trimEnd();e.length>0&&void 0!==n&&(t[n]+=`;${e}`)}else n=s.slice(0,e).trim(),t[n]=s.slice(e+1).trim()}return t}(e.attribs.style);if("string"==typeof t)return n[t];if(Array.isArray(t)){const e={};return t.forEach((t=>{null!=n[t]&&(e[t]=n[t])})),e}return n}const qs=Object.freeze(Object.defineProperty({__proto__:null,css:function(e,t){return null!=e&&null!=t||"object"==typeof e&&!Array.isArray(e)?Dt(this,((n,s)=>{Z(n)&&Hs(n,e,t,s)})):0!==this.length?Gs(this[0],e):void 0}},Symbol.toStringTag,{value:"Module"})),$s="input,select,textarea,keygen",Ys=/%20/g,js=/\r?\n/g;const Ks=Object.freeze(Object.defineProperty({__proto__:null,serialize:function(){return this.serializeArray().map((e=>`${encodeURIComponent(e.name)}=${encodeURIComponent(e.value)}`)).join("&").replace(Ys,"+")},serializeArray:function(){return this.map(((e,t)=>{const n=this._make(t);return Z(t)&&"form"===t.name?n.find($s).toArray():n.filter($s).toArray()})).filter('[name!=""]:enabled:not(:submit, :button, :image, :reset, :file):matches([checked], :not(:checkbox, :radio))').map(((e,t)=>{var n;const s=this._make(t),r=s.attr("name"),i=null!==(n=s.val())&&void 0!==n?n:"";return Array.isArray(i)?i.map((e=>({name:r,value:e.replace(js,"\r\n")}))):{name:r,value:i.replace(js,"\r\n")}})).toArray()}},Symbol.toStringTag,{value:"Module"}));class Vs{constructor(e,t,n){if(this.length=0,this.options=n,this._root=t,e){for(let t=0;t<e.length;t++)this[t]=e[t];this.length=e.length}}}Vs.prototype.cheerio="[cheerio object]",Vs.prototype.splice=Array.prototype.splice,Vs.prototype[Symbol.iterator]=Array.prototype[Symbol.iterator],Object.assign(Vs.prototype,Kt,Ls,Us,qs,Ks);const zs=new Set([65534,65535,131070,131071,196606,196607,262142,262143,327678,327679,393214,393215,458750,458751,524286,524287,589822,589823,655358,655359,720894,720895,786430,786431,851966,851967,917502,917503,983038,983039,1048574,1048575,1114110,1114111]),Qs="�";var Ws,Xs;(Xs=Ws=Ws||(Ws={}))[Xs.EOF=-1]="EOF",Xs[Xs.NULL=0]="NULL",Xs[Xs.TABULATION=9]="TABULATION",Xs[Xs.CARRIAGE_RETURN=13]="CARRIAGE_RETURN",Xs[Xs.LINE_FEED=10]="LINE_FEED",Xs[Xs.FORM_FEED=12]="FORM_FEED",Xs[Xs.SPACE=32]="SPACE",Xs[Xs.EXCLAMATION_MARK=33]="EXCLAMATION_MARK",Xs[Xs.QUOTATION_MARK=34]="QUOTATION_MARK",Xs[Xs.NUMBER_SIGN=35]="NUMBER_SIGN",Xs[Xs.AMPERSAND=38]="AMPERSAND",Xs[Xs.APOSTROPHE=39]="APOSTROPHE",Xs[Xs.HYPHEN_MINUS=45]="HYPHEN_MINUS",Xs[Xs.SOLIDUS=47]="SOLIDUS",Xs[Xs.DIGIT_0=48]="DIGIT_0",Xs[Xs.DIGIT_9=57]="DIGIT_9",Xs[Xs.SEMICOLON=59]="SEMICOLON",Xs[Xs.LESS_THAN_SIGN=60]="LESS_THAN_SIGN",Xs[Xs.EQUALS_SIGN=61]="EQUALS_SIGN",Xs[Xs.GREATER_THAN_SIGN=62]="GREATER_THAN_SIGN",Xs[Xs.QUESTION_MARK=63]="QUESTION_MARK",Xs[Xs.LATIN_CAPITAL_A=65]="LATIN_CAPITAL_A",Xs[Xs.LATIN_CAPITAL_F=70]="LATIN_CAPITAL_F",Xs[Xs.LATIN_CAPITAL_X=88]="LATIN_CAPITAL_X",Xs[Xs.LATIN_CAPITAL_Z=90]="LATIN_CAPITAL_Z",Xs[Xs.RIGHT_SQUARE_BRACKET=93]="RIGHT_SQUARE_BRACKET",Xs[Xs.GRAVE_ACCENT=96]="GRAVE_ACCENT",Xs[Xs.LATIN_SMALL_A=97]="LATIN_SMALL_A",Xs[Xs.LATIN_SMALL_F=102]="LATIN_SMALL_F",Xs[Xs.LATIN_SMALL_X=120]="LATIN_SMALL_X",Xs[Xs.LATIN_SMALL_Z=122]="LATIN_SMALL_Z",Xs[Xs.REPLACEMENT_CHARACTER=65533]="REPLACEMENT_CHARACTER";const Zs="--",Js="[CDATA[",er="doctype",tr="script",nr="public",sr="system";function rr(e){return e>=55296&&e<=57343}function ir(e){return 32!==e&&10!==e&&13!==e&&9!==e&&12!==e&&e>=1&&e<=31||e>=127&&e<=159}function or(e){return e>=64976&&e<=65007||zs.has(e)}var ar,cr;(cr=ar=ar||(ar={})).controlCharacterInInputStream="control-character-in-input-stream",cr.noncharacterInInputStream="noncharacter-in-input-stream",cr.surrogateInInputStream="surrogate-in-input-stream",cr.nonVoidHtmlElementStartTagWithTrailingSolidus="non-void-html-element-start-tag-with-trailing-solidus",cr.endTagWithAttributes="end-tag-with-attributes",cr.endTagWithTrailingSolidus="end-tag-with-trailing-solidus",cr.unexpectedSolidusInTag="unexpected-solidus-in-tag",cr.unexpectedNullCharacter="unexpected-null-character",cr.unexpectedQuestionMarkInsteadOfTagName="unexpected-question-mark-instead-of-tag-name",cr.invalidFirstCharacterOfTagName="invalid-first-character-of-tag-name",cr.unexpectedEqualsSignBeforeAttributeName="unexpected-equals-sign-before-attribute-name",cr.missingEndTagName="missing-end-tag-name",cr.unexpectedCharacterInAttributeName="unexpected-character-in-attribute-name",cr.unknownNamedCharacterReference="unknown-named-character-reference",cr.missingSemicolonAfterCharacterReference="missing-semicolon-after-character-reference",cr.unexpectedCharacterAfterDoctypeSystemIdentifier="unexpected-character-after-doctype-system-identifier",cr.unexpectedCharacterInUnquotedAttributeValue="unexpected-character-in-unquoted-attribute-value",cr.eofBeforeTagName="eof-before-tag-name",cr.eofInTag="eof-in-tag",cr.missingAttributeValue="missing-attribute-value",cr.missingWhitespaceBetweenAttributes="missing-whitespace-between-attributes",cr.missingWhitespaceAfterDoctypePublicKeyword="missing-whitespace-after-doctype-public-keyword",cr.missingWhitespaceBetweenDoctypePublicAndSystemIdentifiers="missing-whitespace-between-doctype-public-and-system-identifiers",cr.missingWhitespaceAfterDoctypeSystemKeyword="missing-whitespace-after-doctype-system-keyword",cr.missingQuoteBeforeDoctypePublicIdentifier="missing-quote-before-doctype-public-identifier",cr.missingQuoteBeforeDoctypeSystemIdentifier="missing-quote-before-doctype-system-identifier",cr.missingDoctypePublicIdentifier="missing-doctype-public-identifier",cr.missingDoctypeSystemIdentifier="missing-doctype-system-identifier",cr.abruptDoctypePublicIdentifier="abrupt-doctype-public-identifier",cr.abruptDoctypeSystemIdentifier="abrupt-doctype-system-identifier",cr.cdataInHtmlContent="cdata-in-html-content",cr.incorrectlyOpenedComment="incorrectly-opened-comment",cr.eofInScriptHtmlCommentLikeText="eof-in-script-html-comment-like-text",cr.eofInDoctype="eof-in-doctype",cr.nestedComment="nested-comment",cr.abruptClosingOfEmptyComment="abrupt-closing-of-empty-comment",cr.eofInComment="eof-in-comment",cr.incorrectlyClosedComment="incorrectly-closed-comment",cr.eofInCdata="eof-in-cdata",cr.absenceOfDigitsInNumericCharacterReference="absence-of-digits-in-numeric-character-reference",cr.nullCharacterReference="null-character-reference",cr.surrogateCharacterReference="surrogate-character-reference",cr.characterReferenceOutsideUnicodeRange="character-reference-outside-unicode-range",cr.controlCharacterReference="control-character-reference",cr.noncharacterCharacterReference="noncharacter-character-reference",cr.missingWhitespaceBeforeDoctypeName="missing-whitespace-before-doctype-name",cr.missingDoctypeName="missing-doctype-name",cr.invalidCharacterSequenceAfterDoctypeName="invalid-character-sequence-after-doctype-name",cr.duplicateAttribute="duplicate-attribute",cr.nonConformingDoctype="non-conforming-doctype",cr.missingDoctype="missing-doctype",cr.misplacedDoctype="misplaced-doctype",cr.endTagWithoutMatchingOpenElement="end-tag-without-matching-open-element",cr.closingOfElementWithOpenChildElements="closing-of-element-with-open-child-elements",cr.disallowedContentInNoscriptInHead="disallowed-content-in-noscript-in-head",cr.openElementsLeftAfterEof="open-elements-left-after-eof",cr.abandonedHeadElementChild="abandoned-head-element-child",cr.misplacedStartTagForHeadElement="misplaced-start-tag-for-head-element",cr.nestedNoscriptInHead="nested-noscript-in-head",cr.eofInElementThatCanContainOnlyText="eof-in-element-that-can-contain-only-text";class lr{constructor(e){this.handler=e,this.html="",this.pos=-1,this.lastGapPos=-2,this.gapStack=[],this.skipNextNewLine=!1,this.lastChunkWritten=!1,this.endOfChunkHit=!1,this.bufferWaterline=65536,this.isEol=!1,this.lineStartPos=0,this.droppedBufferSize=0,this.line=1,this.lastErrOffset=-1}get col(){return this.pos-this.lineStartPos+Number(this.lastGapPos!==this.pos)}get offset(){return this.droppedBufferSize+this.pos}getError(e){const{line:t,col:n,offset:s}=this;return{code:e,startLine:t,endLine:t,startCol:n,endCol:n,startOffset:s,endOffset:s}}_err(e){this.handler.onParseError&&this.lastErrOffset!==this.offset&&(this.lastErrOffset=this.offset,this.handler.onParseError(this.getError(e)))}_addGap(){this.gapStack.push(this.lastGapPos),this.lastGapPos=this.pos}_processSurrogate(e){if(this.pos!==this.html.length-1){const t=this.html.charCodeAt(this.pos+1);if(function(e){return e>=56320&&e<=57343}(t))return this.pos++,this._addGap(),1024*(e-55296)+9216+t}else if(!this.lastChunkWritten)return this.endOfChunkHit=!0,Ws.EOF;return this._err(ar.surrogateInInputStream),e}willDropParsedChunk(){return this.pos>this.bufferWaterline}dropParsedChunk(){this.willDropParsedChunk()&&(this.html=this.html.substring(this.pos),this.lineStartPos-=this.pos,this.droppedBufferSize+=this.pos,this.pos=0,this.lastGapPos=-2,this.gapStack.length=0)}write(e,t){this.html.length>0?this.html+=e:this.html=e,this.endOfChunkHit=!1,this.lastChunkWritten=t}insertHtmlAtCurrentPos(e){this.html=this.html.substring(0,this.pos+1)+e+this.html.substring(this.pos+1),this.endOfChunkHit=!1}startsWith(e,t){if(this.pos+e.length>this.html.length)return this.endOfChunkHit=!this.lastChunkWritten,!1;if(t)return this.html.startsWith(e,this.pos);for(let t=0;t<e.length;t++){if((32|this.html.charCodeAt(this.pos+t))!==e.charCodeAt(t))return!1}return!0}peek(e){const t=this.pos+e;if(t>=this.html.length)return this.endOfChunkHit=!this.lastChunkWritten,Ws.EOF;const n=this.html.charCodeAt(t);return n===Ws.CARRIAGE_RETURN?Ws.LINE_FEED:n}advance(){if(this.pos++,this.isEol&&(this.isEol=!1,this.line++,this.lineStartPos=this.pos),this.pos>=this.html.length)return this.endOfChunkHit=!this.lastChunkWritten,Ws.EOF;let e=this.html.charCodeAt(this.pos);if(e===Ws.CARRIAGE_RETURN)return this.isEol=!0,this.skipNextNewLine=!0,Ws.LINE_FEED;if(e===Ws.LINE_FEED&&(this.isEol=!0,this.skipNextNewLine))return this.line--,this.skipNextNewLine=!1,this._addGap(),this.advance();this.skipNextNewLine=!1,rr(e)&&(e=this._processSurrogate(e));return null===this.handler.onParseError||e>31&&e<127||e===Ws.LINE_FEED||e===Ws.CARRIAGE_RETURN||e>159&&e<64976||this._checkForProblematicCharacters(e),e}_checkForProblematicCharacters(e){ir(e)?this._err(ar.controlCharacterInInputStream):or(e)&&this._err(ar.noncharacterInInputStream)}retreat(e){for(this.pos-=e;this.pos<this.lastGapPos;)this.lastGapPos=this.gapStack.pop(),this.pos--;this.isEol=!1}}var ur,hr,dr,pr,fr,mr,Er,Tr,_r,Ar,gr,Cr;function br(e,t){for(let n=e.attrs.length-1;n>=0;n--)if(e.attrs[n].name===t)return e.attrs[n].value;return null}(hr=ur=ur||(ur={}))[hr.CHARACTER=0]="CHARACTER",hr[hr.NULL_CHARACTER=1]="NULL_CHARACTER",hr[hr.WHITESPACE_CHARACTER=2]="WHITESPACE_CHARACTER",hr[hr.START_TAG=3]="START_TAG",hr[hr.END_TAG=4]="END_TAG",hr[hr.COMMENT=5]="COMMENT",hr[hr.DOCTYPE=6]="DOCTYPE",hr[hr.EOF=7]="EOF",hr[hr.HIBERNATION=8]="HIBERNATION",(pr=dr=dr||(dr={})).HTML="http://www.w3.org/1999/xhtml",pr.MATHML="http://www.w3.org/1998/Math/MathML",pr.SVG="http://www.w3.org/2000/svg",pr.XLINK="http://www.w3.org/1999/xlink",pr.XML="http://www.w3.org/XML/1998/namespace",pr.XMLNS="http://www.w3.org/2000/xmlns/",(mr=fr=fr||(fr={})).TYPE="type",mr.ACTION="action",mr.ENCODING="encoding",mr.PROMPT="prompt",mr.NAME="name",mr.COLOR="color",mr.FACE="face",mr.SIZE="size",(Tr=Er=Er||(Er={})).NO_QUIRKS="no-quirks",Tr.QUIRKS="quirks",Tr.LIMITED_QUIRKS="limited-quirks",(Ar=_r=_r||(_r={})).A="a",Ar.ADDRESS="address",Ar.ANNOTATION_XML="annotation-xml",Ar.APPLET="applet",Ar.AREA="area",Ar.ARTICLE="article",Ar.ASIDE="aside",Ar.B="b",Ar.BASE="base",Ar.BASEFONT="basefont",Ar.BGSOUND="bgsound",Ar.BIG="big",Ar.BLOCKQUOTE="blockquote",Ar.BODY="body",Ar.BR="br",Ar.BUTTON="button",Ar.CAPTION="caption",Ar.CENTER="center",Ar.CODE="code",Ar.COL="col",Ar.COLGROUP="colgroup",Ar.DD="dd",Ar.DESC="desc",Ar.DETAILS="details",Ar.DIALOG="dialog",Ar.DIR="dir",Ar.DIV="div",Ar.DL="dl",Ar.DT="dt",Ar.EM="em",Ar.EMBED="embed",Ar.FIELDSET="fieldset",Ar.FIGCAPTION="figcaption",Ar.FIGURE="figure",Ar.FONT="font",Ar.FOOTER="footer",Ar.FOREIGN_OBJECT="foreignObject",Ar.FORM="form",Ar.FRAME="frame",Ar.FRAMESET="frameset",Ar.H1="h1",Ar.H2="h2",Ar.H3="h3",Ar.H4="h4",Ar.H5="h5",Ar.H6="h6",Ar.HEAD="head",Ar.HEADER="header",Ar.HGROUP="hgroup",Ar.HR="hr",Ar.HTML="html",Ar.I="i",Ar.IMG="img",Ar.IMAGE="image",Ar.INPUT="input",Ar.IFRAME="iframe",Ar.KEYGEN="keygen",Ar.LABEL="label",Ar.LI="li",Ar.LINK="link",Ar.LISTING="listing",Ar.MAIN="main",Ar.MALIGNMARK="malignmark",Ar.MARQUEE="marquee",Ar.MATH="math",Ar.MENU="menu",Ar.META="meta",Ar.MGLYPH="mglyph",Ar.MI="mi",Ar.MO="mo",Ar.MN="mn",Ar.MS="ms",Ar.MTEXT="mtext",Ar.NAV="nav",Ar.NOBR="nobr",Ar.NOFRAMES="noframes",Ar.NOEMBED="noembed",Ar.NOSCRIPT="noscript",Ar.OBJECT="object",Ar.OL="ol",Ar.OPTGROUP="optgroup",Ar.OPTION="option",Ar.P="p",Ar.PARAM="param",Ar.PLAINTEXT="plaintext",Ar.PRE="pre",Ar.RB="rb",Ar.RP="rp",Ar.RT="rt",Ar.RTC="rtc",Ar.RUBY="ruby",Ar.S="s",Ar.SCRIPT="script",Ar.SECTION="section",Ar.SELECT="select",Ar.SOURCE="source",Ar.SMALL="small",Ar.SPAN="span",Ar.STRIKE="strike",Ar.STRONG="strong",Ar.STYLE="style",Ar.SUB="sub",Ar.SUMMARY="summary",Ar.SUP="sup",Ar.TABLE="table",Ar.TBODY="tbody",Ar.TEMPLATE="template",Ar.TEXTAREA="textarea",Ar.TFOOT="tfoot",Ar.TD="td",Ar.TH="th",Ar.THEAD="thead",Ar.TITLE="title",Ar.TR="tr",Ar.TRACK="track",Ar.TT="tt",Ar.U="u",Ar.UL="ul",Ar.SVG="svg",Ar.VAR="var",Ar.WBR="wbr",Ar.XMP="xmp",(Cr=gr=gr||(gr={}))[Cr.UNKNOWN=0]="UNKNOWN",Cr[Cr.A=1]="A",Cr[Cr.ADDRESS=2]="ADDRESS",Cr[Cr.ANNOTATION_XML=3]="ANNOTATION_XML",Cr[Cr.APPLET=4]="APPLET",Cr[Cr.AREA=5]="AREA",Cr[Cr.ARTICLE=6]="ARTICLE",Cr[Cr.ASIDE=7]="ASIDE",Cr[Cr.B=8]="B",Cr[Cr.BASE=9]="BASE",Cr[Cr.BASEFONT=10]="BASEFONT",Cr[Cr.BGSOUND=11]="BGSOUND",Cr[Cr.BIG=12]="BIG",Cr[Cr.BLOCKQUOTE=13]="BLOCKQUOTE",Cr[Cr.BODY=14]="BODY",Cr[Cr.BR=15]="BR",Cr[Cr.BUTTON=16]="BUTTON",Cr[Cr.CAPTION=17]="CAPTION",Cr[Cr.CENTER=18]="CENTER",Cr[Cr.CODE=19]="CODE",Cr[Cr.COL=20]="COL",Cr[Cr.COLGROUP=21]="COLGROUP",Cr[Cr.DD=22]="DD",Cr[Cr.DESC=23]="DESC",Cr[Cr.DETAILS=24]="DETAILS",Cr[Cr.DIALOG=25]="DIALOG",Cr[Cr.DIR=26]="DIR",Cr[Cr.DIV=27]="DIV",Cr[Cr.DL=28]="DL",Cr[Cr.DT=29]="DT",Cr[Cr.EM=30]="EM",Cr[Cr.EMBED=31]="EMBED",Cr[Cr.FIELDSET=32]="FIELDSET",Cr[Cr.FIGCAPTION=33]="FIGCAPTION",Cr[Cr.FIGURE=34]="FIGURE",Cr[Cr.FONT=35]="FONT",Cr[Cr.FOOTER=36]="FOOTER",Cr[Cr.FOREIGN_OBJECT=37]="FOREIGN_OBJECT",Cr[Cr.FORM=38]="FORM",Cr[Cr.FRAME=39]="FRAME",Cr[Cr.FRAMESET=40]="FRAMESET",Cr[Cr.H1=41]="H1",Cr[Cr.H2=42]="H2",Cr[Cr.H3=43]="H3",Cr[Cr.H4=44]="H4",Cr[Cr.H5=45]="H5",Cr[Cr.H6=46]="H6",Cr[Cr.HEAD=47]="HEAD",Cr[Cr.HEADER=48]="HEADER",Cr[Cr.HGROUP=49]="HGROUP",Cr[Cr.HR=50]="HR",Cr[Cr.HTML=51]="HTML",Cr[Cr.I=52]="I",Cr[Cr.IMG=53]="IMG",Cr[Cr.IMAGE=54]="IMAGE",Cr[Cr.INPUT=55]="INPUT",Cr[Cr.IFRAME=56]="IFRAME",Cr[Cr.KEYGEN=57]="KEYGEN",Cr[Cr.LABEL=58]="LABEL",Cr[Cr.LI=59]="LI",Cr[Cr.LINK=60]="LINK",Cr[Cr.LISTING=61]="LISTING",Cr[Cr.MAIN=62]="MAIN",Cr[Cr.MALIGNMARK=63]="MALIGNMARK",Cr[Cr.MARQUEE=64]="MARQUEE",Cr[Cr.MATH=65]="MATH",Cr[Cr.MENU=66]="MENU",Cr[Cr.META=67]="META",Cr[Cr.MGLYPH=68]="MGLYPH",Cr[Cr.MI=69]="MI",Cr[Cr.MO=70]="MO",Cr[Cr.MN=71]="MN",Cr[Cr.MS=72]="MS",Cr[Cr.MTEXT=73]="MTEXT",Cr[Cr.NAV=74]="NAV",Cr[Cr.NOBR=75]="NOBR",Cr[Cr.NOFRAMES=76]="NOFRAMES",Cr[Cr.NOEMBED=77]="NOEMBED",Cr[Cr.NOSCRIPT=78]="NOSCRIPT",Cr[Cr.OBJECT=79]="OBJECT",Cr[Cr.OL=80]="OL",Cr[Cr.OPTGROUP=81]="OPTGROUP",Cr[Cr.OPTION=82]="OPTION",Cr[Cr.P=83]="P",Cr[Cr.PARAM=84]="PARAM",Cr[Cr.PLAINTEXT=85]="PLAINTEXT",Cr[Cr.PRE=86]="PRE",Cr[Cr.RB=87]="RB",Cr[Cr.RP=88]="RP",Cr[Cr.RT=89]="RT",Cr[Cr.RTC=90]="RTC",Cr[Cr.RUBY=91]="RUBY",Cr[Cr.S=92]="S",Cr[Cr.SCRIPT=93]="SCRIPT",Cr[Cr.SECTION=94]="SECTION",Cr[Cr.SELECT=95]="SELECT",Cr[Cr.SOURCE=96]="SOURCE",Cr[Cr.SMALL=97]="SMALL",Cr[Cr.SPAN=98]="SPAN",Cr[Cr.STRIKE=99]="STRIKE",Cr[Cr.STRONG=100]="STRONG",Cr[Cr.STYLE=101]="STYLE",Cr[Cr.SUB=102]="SUB",Cr[Cr.SUMMARY=103]="SUMMARY",Cr[Cr.SUP=104]="SUP",Cr[Cr.TABLE=105]="TABLE",Cr[Cr.TBODY=106]="TBODY",Cr[Cr.TEMPLATE=107]="TEMPLATE",Cr[Cr.TEXTAREA=108]="TEXTAREA",Cr[Cr.TFOOT=109]="TFOOT",Cr[Cr.TD=110]="TD",Cr[Cr.TH=111]="TH",Cr[Cr.THEAD=112]="THEAD",Cr[Cr.TITLE=113]="TITLE",Cr[Cr.TR=114]="TR",Cr[Cr.TRACK=115]="TRACK",Cr[Cr.TT=116]="TT",Cr[Cr.U=117]="U",Cr[Cr.UL=118]="UL",Cr[Cr.SVG=119]="SVG",Cr[Cr.VAR=120]="VAR",Cr[Cr.WBR=121]="WBR",Cr[Cr.XMP=122]="XMP";const Nr=new Map([[_r.A,gr.A],[_r.ADDRESS,gr.ADDRESS],[_r.ANNOTATION_XML,gr.ANNOTATION_XML],[_r.APPLET,gr.APPLET],[_r.AREA,gr.AREA],[_r.ARTICLE,gr.ARTICLE],[_r.ASIDE,gr.ASIDE],[_r.B,gr.B],[_r.BASE,gr.BASE],[_r.BASEFONT,gr.BASEFONT],[_r.BGSOUND,gr.BGSOUND],[_r.BIG,gr.BIG],[_r.BLOCKQUOTE,gr.BLOCKQUOTE],[_r.BODY,gr.BODY],[_r.BR,gr.BR],[_r.BUTTON,gr.BUTTON],[_r.CAPTION,gr.CAPTION],[_r.CENTER,gr.CENTER],[_r.CODE,gr.CODE],[_r.COL,gr.COL],[_r.COLGROUP,gr.COLGROUP],[_r.DD,gr.DD],[_r.DESC,gr.DESC],[_r.DETAILS,gr.DETAILS],[_r.DIALOG,gr.DIALOG],[_r.DIR,gr.DIR],[_r.DIV,gr.DIV],[_r.DL,gr.DL],[_r.DT,gr.DT],[_r.EM,gr.EM],[_r.EMBED,gr.EMBED],[_r.FIELDSET,gr.FIELDSET],[_r.FIGCAPTION,gr.FIGCAPTION],[_r.FIGURE,gr.FIGURE],[_r.FONT,gr.FONT],[_r.FOOTER,gr.FOOTER],[_r.FOREIGN_OBJECT,gr.FOREIGN_OBJECT],[_r.FORM,gr.FORM],[_r.FRAME,gr.FRAME],[_r.FRAMESET,gr.FRAMESET],[_r.H1,gr.H1],[_r.H2,gr.H2],[_r.H3,gr.H3],[_r.H4,gr.H4],[_r.H5,gr.H5],[_r.H6,gr.H6],[_r.HEAD,gr.HEAD],[_r.HEADER,gr.HEADER],[_r.HGROUP,gr.HGROUP],[_r.HR,gr.HR],[_r.HTML,gr.HTML],[_r.I,gr.I],[_r.IMG,gr.IMG],[_r.IMAGE,gr.IMAGE],[_r.INPUT,gr.INPUT],[_r.IFRAME,gr.IFRAME],[_r.KEYGEN,gr.KEYGEN],[_r.LABEL,gr.LABEL],[_r.LI,gr.LI],[_r.LINK,gr.LINK],[_r.LISTING,gr.LISTING],[_r.MAIN,gr.MAIN],[_r.MALIGNMARK,gr.MALIGNMARK],[_r.MARQUEE,gr.MARQUEE],[_r.MATH,gr.MATH],[_r.MENU,gr.MENU],[_r.META,gr.META],[_r.MGLYPH,gr.MGLYPH],[_r.MI,gr.MI],[_r.MO,gr.MO],[_r.MN,gr.MN],[_r.MS,gr.MS],[_r.MTEXT,gr.MTEXT],[_r.NAV,gr.NAV],[_r.NOBR,gr.NOBR],[_r.NOFRAMES,gr.NOFRAMES],[_r.NOEMBED,gr.NOEMBED],[_r.NOSCRIPT,gr.NOSCRIPT],[_r.OBJECT,gr.OBJECT],[_r.OL,gr.OL],[_r.OPTGROUP,gr.OPTGROUP],[_r.OPTION,gr.OPTION],[_r.P,gr.P],[_r.PARAM,gr.PARAM],[_r.PLAINTEXT,gr.PLAINTEXT],[_r.PRE,gr.PRE],[_r.RB,gr.RB],[_r.RP,gr.RP],[_r.RT,gr.RT],[_r.RTC,gr.RTC],[_r.RUBY,gr.RUBY],[_r.S,gr.S],[_r.SCRIPT,gr.SCRIPT],[_r.SECTION,gr.SECTION],[_r.SELECT,gr.SELECT],[_r.SOURCE,gr.SOURCE],[_r.SMALL,gr.SMALL],[_r.SPAN,gr.SPAN],[_r.STRIKE,gr.STRIKE],[_r.STRONG,gr.STRONG],[_r.STYLE,gr.STYLE],[_r.SUB,gr.SUB],[_r.SUMMARY,gr.SUMMARY],[_r.SUP,gr.SUP],[_r.TABLE,gr.TABLE],[_r.TBODY,gr.TBODY],[_r.TEMPLATE,gr.TEMPLATE],[_r.TEXTAREA,gr.TEXTAREA],[_r.TFOOT,gr.TFOOT],[_r.TD,gr.TD],[_r.TH,gr.TH],[_r.THEAD,gr.THEAD],[_r.TITLE,gr.TITLE],[_r.TR,gr.TR],[_r.TRACK,gr.TRACK],[_r.TT,gr.TT],[_r.U,gr.U],[_r.UL,gr.UL],[_r.SVG,gr.SVG],[_r.VAR,gr.VAR],[_r.WBR,gr.WBR],[_r.XMP,gr.XMP]]);function Ir(e){var t;return null!==(t=Nr.get(e))&&void 0!==t?t:gr.UNKNOWN}const kr=gr,Sr={[dr.HTML]:new Set([kr.ADDRESS,kr.APPLET,kr.AREA,kr.ARTICLE,kr.ASIDE,kr.BASE,kr.BASEFONT,kr.BGSOUND,kr.BLOCKQUOTE,kr.BODY,kr.BR,kr.BUTTON,kr.CAPTION,kr.CENTER,kr.COL,kr.COLGROUP,kr.DD,kr.DETAILS,kr.DIR,kr.DIV,kr.DL,kr.DT,kr.EMBED,kr.FIELDSET,kr.FIGCAPTION,kr.FIGURE,kr.FOOTER,kr.FORM,kr.FRAME,kr.FRAMESET,kr.H1,kr.H2,kr.H3,kr.H4,kr.H5,kr.H6,kr.HEAD,kr.HEADER,kr.HGROUP,kr.HR,kr.HTML,kr.IFRAME,kr.IMG,kr.INPUT,kr.LI,kr.LINK,kr.LISTING,kr.MAIN,kr.MARQUEE,kr.MENU,kr.META,kr.NAV,kr.NOEMBED,kr.NOFRAMES,kr.NOSCRIPT,kr.OBJECT,kr.OL,kr.P,kr.PARAM,kr.PLAINTEXT,kr.PRE,kr.SCRIPT,kr.SECTION,kr.SELECT,kr.SOURCE,kr.STYLE,kr.SUMMARY,kr.TABLE,kr.TBODY,kr.TD,kr.TEMPLATE,kr.TEXTAREA,kr.TFOOT,kr.TH,kr.THEAD,kr.TITLE,kr.TR,kr.TRACK,kr.UL,kr.WBR,kr.XMP]),[dr.MATHML]:new Set([kr.MI,kr.MO,kr.MN,kr.MS,kr.MTEXT,kr.ANNOTATION_XML]),[dr.SVG]:new Set([kr.TITLE,kr.FOREIGN_OBJECT,kr.DESC]),[dr.XLINK]:new Set,[dr.XML]:new Set,[dr.XMLNS]:new Set};function Dr(e){return e===kr.H1||e===kr.H2||e===kr.H3||e===kr.H4||e===kr.H5||e===kr.H6}const yr=new Set([_r.STYLE,_r.SCRIPT,_r.XMP,_r.IFRAME,_r.NOEMBED,_r.NOFRAMES,_r.PLAINTEXT]);const Or=new Map([[128,8364],[130,8218],[131,402],[132,8222],[133,8230],[134,8224],[135,8225],[136,710],[137,8240],[138,352],[139,8249],[140,338],[142,381],[145,8216],[146,8217],[147,8220],[148,8221],[149,8226],[150,8211],[151,8212],[152,732],[153,8482],[154,353],[155,8250],[156,339],[158,382],[159,376]]);var Lr,Rr;(Rr=Lr||(Lr={}))[Rr.DATA=0]="DATA",Rr[Rr.RCDATA=1]="RCDATA",Rr[Rr.RAWTEXT=2]="RAWTEXT",Rr[Rr.SCRIPT_DATA=3]="SCRIPT_DATA",Rr[Rr.PLAINTEXT=4]="PLAINTEXT",Rr[Rr.TAG_OPEN=5]="TAG_OPEN",Rr[Rr.END_TAG_OPEN=6]="END_TAG_OPEN",Rr[Rr.TAG_NAME=7]="TAG_NAME",Rr[Rr.RCDATA_LESS_THAN_SIGN=8]="RCDATA_LESS_THAN_SIGN",Rr[Rr.RCDATA_END_TAG_OPEN=9]="RCDATA_END_TAG_OPEN",Rr[Rr.RCDATA_END_TAG_NAME=10]="RCDATA_END_TAG_NAME",Rr[Rr.RAWTEXT_LESS_THAN_SIGN=11]="RAWTEXT_LESS_THAN_SIGN",Rr[Rr.RAWTEXT_END_TAG_OPEN=12]="RAWTEXT_END_TAG_OPEN",Rr[Rr.RAWTEXT_END_TAG_NAME=13]="RAWTEXT_END_TAG_NAME",Rr[Rr.SCRIPT_DATA_LESS_THAN_SIGN=14]="SCRIPT_DATA_LESS_THAN_SIGN",Rr[Rr.SCRIPT_DATA_END_TAG_OPEN=15]="SCRIPT_DATA_END_TAG_OPEN",Rr[Rr.SCRIPT_DATA_END_TAG_NAME=16]="SCRIPT_DATA_END_TAG_NAME",Rr[Rr.SCRIPT_DATA_ESCAPE_START=17]="SCRIPT_DATA_ESCAPE_START",Rr[Rr.SCRIPT_DATA_ESCAPE_START_DASH=18]="SCRIPT_DATA_ESCAPE_START_DASH",Rr[Rr.SCRIPT_DATA_ESCAPED=19]="SCRIPT_DATA_ESCAPED",Rr[Rr.SCRIPT_DATA_ESCAPED_DASH=20]="SCRIPT_DATA_ESCAPED_DASH",Rr[Rr.SCRIPT_DATA_ESCAPED_DASH_DASH=21]="SCRIPT_DATA_ESCAPED_DASH_DASH",Rr[Rr.SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN=22]="SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN",Rr[Rr.SCRIPT_DATA_ESCAPED_END_TAG_OPEN=23]="SCRIPT_DATA_ESCAPED_END_TAG_OPEN",Rr[Rr.SCRIPT_DATA_ESCAPED_END_TAG_NAME=24]="SCRIPT_DATA_ESCAPED_END_TAG_NAME",Rr[Rr.SCRIPT_DATA_DOUBLE_ESCAPE_START=25]="SCRIPT_DATA_DOUBLE_ESCAPE_START",Rr[Rr.SCRIPT_DATA_DOUBLE_ESCAPED=26]="SCRIPT_DATA_DOUBLE_ESCAPED",Rr[Rr.SCRIPT_DATA_DOUBLE_ESCAPED_DASH=27]="SCRIPT_DATA_DOUBLE_ESCAPED_DASH",Rr[Rr.SCRIPT_DATA_DOUBLE_ESCAPED_DASH_DASH=28]="SCRIPT_DATA_DOUBLE_ESCAPED_DASH_DASH",Rr[Rr.SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN=29]="SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN",Rr[Rr.SCRIPT_DATA_DOUBLE_ESCAPE_END=30]="SCRIPT_DATA_DOUBLE_ESCAPE_END",Rr[Rr.BEFORE_ATTRIBUTE_NAME=31]="BEFORE_ATTRIBUTE_NAME",Rr[Rr.ATTRIBUTE_NAME=32]="ATTRIBUTE_NAME",Rr[Rr.AFTER_ATTRIBUTE_NAME=33]="AFTER_ATTRIBUTE_NAME",Rr[Rr.BEFORE_ATTRIBUTE_VALUE=34]="BEFORE_ATTRIBUTE_VALUE",Rr[Rr.ATTRIBUTE_VALUE_DOUBLE_QUOTED=35]="ATTRIBUTE_VALUE_DOUBLE_QUOTED",Rr[Rr.ATTRIBUTE_VALUE_SINGLE_QUOTED=36]="ATTRIBUTE_VALUE_SINGLE_QUOTED",Rr[Rr.ATTRIBUTE_VALUE_UNQUOTED=37]="ATTRIBUTE_VALUE_UNQUOTED",Rr[Rr.AFTER_ATTRIBUTE_VALUE_QUOTED=38]="AFTER_ATTRIBUTE_VALUE_QUOTED",Rr[Rr.SELF_CLOSING_START_TAG=39]="SELF_CLOSING_START_TAG",Rr[Rr.BOGUS_COMMENT=40]="BOGUS_COMMENT",Rr[Rr.MARKUP_DECLARATION_OPEN=41]="MARKUP_DECLARATION_OPEN",Rr[Rr.COMMENT_START=42]="COMMENT_START",Rr[Rr.COMMENT_START_DASH=43]="COMMENT_START_DASH",Rr[Rr.COMMENT=44]="COMMENT",Rr[Rr.COMMENT_LESS_THAN_SIGN=45]="COMMENT_LESS_THAN_SIGN",Rr[Rr.COMMENT_LESS_THAN_SIGN_BANG=46]="COMMENT_LESS_THAN_SIGN_BANG",Rr[Rr.COMMENT_LESS_THAN_SIGN_BANG_DASH=47]="COMMENT_LESS_THAN_SIGN_BANG_DASH",Rr[Rr.COMMENT_LESS_THAN_SIGN_BANG_DASH_DASH=48]="COMMENT_LESS_THAN_SIGN_BANG_DASH_DASH",Rr[Rr.COMMENT_END_DASH=49]="COMMENT_END_DASH",Rr[Rr.COMMENT_END=50]="COMMENT_END",Rr[Rr.COMMENT_END_BANG=51]="COMMENT_END_BANG",Rr[Rr.DOCTYPE=52]="DOCTYPE",Rr[Rr.BEFORE_DOCTYPE_NAME=53]="BEFORE_DOCTYPE_NAME",Rr[Rr.DOCTYPE_NAME=54]="DOCTYPE_NAME",Rr[Rr.AFTER_DOCTYPE_NAME=55]="AFTER_DOCTYPE_NAME",Rr[Rr.AFTER_DOCTYPE_PUBLIC_KEYWORD=56]="AFTER_DOCTYPE_PUBLIC_KEYWORD",Rr[Rr.BEFORE_DOCTYPE_PUBLIC_IDENTIFIER=57]="BEFORE_DOCTYPE_PUBLIC_IDENTIFIER",Rr[Rr.DOCTYPE_PUBLIC_IDENTIFIER_DOUBLE_QUOTED=58]="DOCTYPE_PUBLIC_IDENTIFIER_DOUBLE_QUOTED",Rr[Rr.DOCTYPE_PUBLIC_IDENTIFIER_SINGLE_QUOTED=59]="DOCTYPE_PUBLIC_IDENTIFIER_SINGLE_QUOTED",Rr[Rr.AFTER_DOCTYPE_PUBLIC_IDENTIFIER=60]="AFTER_DOCTYPE_PUBLIC_IDENTIFIER",Rr[Rr.BETWEEN_DOCTYPE_PUBLIC_AND_SYSTEM_IDENTIFIERS=61]="BETWEEN_DOCTYPE_PUBLIC_AND_SYSTEM_IDENTIFIERS",Rr[Rr.AFTER_DOCTYPE_SYSTEM_KEYWORD=62]="AFTER_DOCTYPE_SYSTEM_KEYWORD",Rr[Rr.BEFORE_DOCTYPE_SYSTEM_IDENTIFIER=63]="BEFORE_DOCTYPE_SYSTEM_IDENTIFIER",Rr[Rr.DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED=64]="DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED",Rr[Rr.DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED=65]="DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED",Rr[Rr.AFTER_DOCTYPE_SYSTEM_IDENTIFIER=66]="AFTER_DOCTYPE_SYSTEM_IDENTIFIER",Rr[Rr.BOGUS_DOCTYPE=67]="BOGUS_DOCTYPE",Rr[Rr.CDATA_SECTION=68]="CDATA_SECTION",Rr[Rr.CDATA_SECTION_BRACKET=69]="CDATA_SECTION_BRACKET",Rr[Rr.CDATA_SECTION_END=70]="CDATA_SECTION_END",Rr[Rr.CHARACTER_REFERENCE=71]="CHARACTER_REFERENCE",Rr[Rr.NAMED_CHARACTER_REFERENCE=72]="NAMED_CHARACTER_REFERENCE",Rr[Rr.AMBIGUOUS_AMPERSAND=73]="AMBIGUOUS_AMPERSAND",Rr[Rr.NUMERIC_CHARACTER_REFERENCE=74]="NUMERIC_CHARACTER_REFERENCE",Rr[Rr.HEXADEMICAL_CHARACTER_REFERENCE_START=75]="HEXADEMICAL_CHARACTER_REFERENCE_START",Rr[Rr.HEXADEMICAL_CHARACTER_REFERENCE=76]="HEXADEMICAL_CHARACTER_REFERENCE",Rr[Rr.DECIMAL_CHARACTER_REFERENCE=77]="DECIMAL_CHARACTER_REFERENCE",Rr[Rr.NUMERIC_CHARACTER_REFERENCE_END=78]="NUMERIC_CHARACTER_REFERENCE_END";const Mr={DATA:Lr.DATA,RCDATA:Lr.RCDATA,RAWTEXT:Lr.RAWTEXT,SCRIPT_DATA:Lr.SCRIPT_DATA,PLAINTEXT:Lr.PLAINTEXT,CDATA_SECTION:Lr.CDATA_SECTION};function vr(e){return e>=Ws.DIGIT_0&&e<=Ws.DIGIT_9}function xr(e){return e>=Ws.LATIN_CAPITAL_A&&e<=Ws.LATIN_CAPITAL_Z}function wr(e){return function(e){return e>=Ws.LATIN_SMALL_A&&e<=Ws.LATIN_SMALL_Z}(e)||xr(e)}function Pr(e){return wr(e)||vr(e)}function Fr(e){return e>=Ws.LATIN_CAPITAL_A&&e<=Ws.LATIN_CAPITAL_F}function Br(e){return e>=Ws.LATIN_SMALL_A&&e<=Ws.LATIN_SMALL_F}function Ur(e){return e+32}function Hr(e){return e===Ws.SPACE||e===Ws.LINE_FEED||e===Ws.TABULATION||e===Ws.FORM_FEED}function Gr(e){return Hr(e)||e===Ws.SOLIDUS||e===Ws.GREATER_THAN_SIGN}const qr=new Set([gr.DD,gr.DT,gr.LI,gr.OPTGROUP,gr.OPTION,gr.P,gr.RB,gr.RP,gr.RT,gr.RTC]),$r=new Set([...qr,gr.CAPTION,gr.COLGROUP,gr.TBODY,gr.TD,gr.TFOOT,gr.TH,gr.THEAD,gr.TR]),Yr=new Map([[gr.APPLET,dr.HTML],[gr.CAPTION,dr.HTML],[gr.HTML,dr.HTML],[gr.MARQUEE,dr.HTML],[gr.OBJECT,dr.HTML],[gr.TABLE,dr.HTML],[gr.TD,dr.HTML],[gr.TEMPLATE,dr.HTML],[gr.TH,dr.HTML],[gr.ANNOTATION_XML,dr.MATHML],[gr.MI,dr.MATHML],[gr.MN,dr.MATHML],[gr.MO,dr.MATHML],[gr.MS,dr.MATHML],[gr.MTEXT,dr.MATHML],[gr.DESC,dr.SVG],[gr.FOREIGN_OBJECT,dr.SVG],[gr.TITLE,dr.SVG]]),jr=[gr.H1,gr.H2,gr.H3,gr.H4,gr.H5,gr.H6],Kr=[gr.TR,gr.TEMPLATE,gr.HTML],Vr=[gr.TBODY,gr.TFOOT,gr.THEAD,gr.TEMPLATE,gr.HTML],zr=[gr.TABLE,gr.TEMPLATE,gr.HTML],Qr=[gr.TD,gr.TH];class Wr{get currentTmplContentOrNode(){return this._isInTemplate()?this.treeAdapter.getTemplateContent(this.current):this.current}constructor(e,t,n){this.treeAdapter=t,this.handler=n,this.items=[],this.tagIDs=[],this.stackTop=-1,this.tmplCount=0,this.currentTagId=gr.UNKNOWN,this.current=e}_indexOf(e){return this.items.lastIndexOf(e,this.stackTop)}_isInTemplate(){return this.currentTagId===gr.TEMPLATE&&this.treeAdapter.getNamespaceURI(this.current)===dr.HTML}_updateCurrentElement(){this.current=this.items[this.stackTop],this.currentTagId=this.tagIDs[this.stackTop]}push(e,t){this.stackTop++,this.items[this.stackTop]=e,this.current=e,this.tagIDs[this.stackTop]=t,this.currentTagId=t,this._isInTemplate()&&this.tmplCount++,this.handler.onItemPush(e,t,!0)}pop(){const e=this.current;this.tmplCount>0&&this._isInTemplate()&&this.tmplCount--,this.stackTop--,this._updateCurrentElement(),this.handler.onItemPop(e,!0)}replace(e,t){const n=this._indexOf(e);this.items[n]=t,n===this.stackTop&&(this.current=t)}insertAfter(e,t,n){const s=this._indexOf(e)+1;this.items.splice(s,0,t),this.tagIDs.splice(s,0,n),this.stackTop++,s===this.stackTop&&this._updateCurrentElement(),this.handler.onItemPush(this.current,this.currentTagId,s===this.stackTop)}popUntilTagNamePopped(e){let t=this.stackTop+1;do{t=this.tagIDs.lastIndexOf(e,t-1)}while(t>0&&this.treeAdapter.getNamespaceURI(this.items[t])!==dr.HTML);this.shortenToLength(t<0?0:t)}shortenToLength(e){for(;this.stackTop>=e;){const t=this.current;this.tmplCount>0&&this._isInTemplate()&&(this.tmplCount-=1),this.stackTop--,this._updateCurrentElement(),this.handler.onItemPop(t,this.stackTop<e)}}popUntilElementPopped(e){const t=this._indexOf(e);this.shortenToLength(t<0?0:t)}popUntilPopped(e,t){const n=this._indexOfTagNames(e,t);this.shortenToLength(n<0?0:n)}popUntilNumberedHeaderPopped(){this.popUntilPopped(jr,dr.HTML)}popUntilTableCellPopped(){this.popUntilPopped(Qr,dr.HTML)}popAllUpToHtmlElement(){this.tmplCount=0,this.shortenToLength(1)}_indexOfTagNames(e,t){for(let n=this.stackTop;n>=0;n--)if(e.includes(this.tagIDs[n])&&this.treeAdapter.getNamespaceURI(this.items[n])===t)return n;return-1}clearBackTo(e,t){const n=this._indexOfTagNames(e,t);this.shortenToLength(n+1)}clearBackToTableContext(){this.clearBackTo(zr,dr.HTML)}clearBackToTableBodyContext(){this.clearBackTo(Vr,dr.HTML)}clearBackToTableRowContext(){this.clearBackTo(Kr,dr.HTML)}remove(e){const t=this._indexOf(e);t>=0&&(t===this.stackTop?this.pop():(this.items.splice(t,1),this.tagIDs.splice(t,1),this.stackTop--,this._updateCurrentElement(),this.handler.onItemPop(e,!1)))}tryPeekProperlyNestedBodyElement(){return this.stackTop>=1&&this.tagIDs[1]===gr.BODY?this.items[1]:null}contains(e){return this._indexOf(e)>-1}getCommonAncestor(e){const t=this._indexOf(e)-1;return t>=0?this.items[t]:null}isRootHtmlElementCurrent(){return 0===this.stackTop&&this.tagIDs[0]===gr.HTML}hasInScope(e){for(let t=this.stackTop;t>=0;t--){const n=this.tagIDs[t],s=this.treeAdapter.getNamespaceURI(this.items[t]);if(n===e&&s===dr.HTML)return!0;if(Yr.get(n)===s)return!1}return!0}hasNumberedHeaderInScope(){for(let e=this.stackTop;e>=0;e--){const t=this.tagIDs[e],n=this.treeAdapter.getNamespaceURI(this.items[e]);if(Dr(t)&&n===dr.HTML)return!0;if(Yr.get(t)===n)return!1}return!0}hasInListItemScope(e){for(let t=this.stackTop;t>=0;t--){const n=this.tagIDs[t],s=this.treeAdapter.getNamespaceURI(this.items[t]);if(n===e&&s===dr.HTML)return!0;if((n===gr.UL||n===gr.OL)&&s===dr.HTML||Yr.get(n)===s)return!1}return!0}hasInButtonScope(e){for(let t=this.stackTop;t>=0;t--){const n=this.tagIDs[t],s=this.treeAdapter.getNamespaceURI(this.items[t]);if(n===e&&s===dr.HTML)return!0;if(n===gr.BUTTON&&s===dr.HTML||Yr.get(n)===s)return!1}return!0}hasInTableScope(e){for(let t=this.stackTop;t>=0;t--){const n=this.tagIDs[t];if(this.treeAdapter.getNamespaceURI(this.items[t])===dr.HTML){if(n===e)return!0;if(n===gr.TABLE||n===gr.TEMPLATE||n===gr.HTML)return!1}}return!0}hasTableBodyContextInTableScope(){for(let e=this.stackTop;e>=0;e--){const t=this.tagIDs[e];if(this.treeAdapter.getNamespaceURI(this.items[e])===dr.HTML){if(t===gr.TBODY||t===gr.THEAD||t===gr.TFOOT)return!0;if(t===gr.TABLE||t===gr.HTML)return!1}}return!0}hasInSelectScope(e){for(let t=this.stackTop;t>=0;t--){const n=this.tagIDs[t];if(this.treeAdapter.getNamespaceURI(this.items[t])===dr.HTML){if(n===e)return!0;if(n!==gr.OPTION&&n!==gr.OPTGROUP)return!1}}return!0}generateImpliedEndTags(){for(;qr.has(this.currentTagId);)this.pop()}generateImpliedEndTagsThoroughly(){for(;$r.has(this.currentTagId);)this.pop()}generateImpliedEndTagsWithExclusion(e){for(;this.currentTagId!==e&&$r.has(this.currentTagId);)this.pop()}}var Xr,Zr;(Zr=Xr=Xr||(Xr={}))[Zr.Marker=0]="Marker",Zr[Zr.Element=1]="Element";const Jr={type:Xr.Marker};class ei{constructor(e){this.treeAdapter=e,this.entries=[],this.bookmark=null}_getNoahArkConditionCandidates(e,t){const n=[],s=t.length,r=this.treeAdapter.getTagName(e),i=this.treeAdapter.getNamespaceURI(e);for(let e=0;e<this.entries.length;e++){const t=this.entries[e];if(t.type===Xr.Marker)break;const{element:o}=t;if(this.treeAdapter.getTagName(o)===r&&this.treeAdapter.getNamespaceURI(o)===i){const t=this.treeAdapter.getAttrList(o);t.length===s&&n.push({idx:e,attrs:t})}}return n}_ensureNoahArkCondition(e){if(this.entries.length<3)return;const t=this.treeAdapter.getAttrList(e),n=this._getNoahArkConditionCandidates(e,t);if(n.length<3)return;const s=new Map(t.map((e=>[e.name,e.value])));let r=0;for(let e=0;e<n.length;e++){const t=n[e];t.attrs.every((e=>s.get(e.name)===e.value))&&(r+=1,r>=3&&this.entries.splice(t.idx,1))}}insertMarker(){this.entries.unshift(Jr)}pushElement(e,t){this._ensureNoahArkCondition(e),this.entries.unshift({type:Xr.Element,element:e,token:t})}insertElementAfterBookmark(e,t){const n=this.entries.indexOf(this.bookmark);this.entries.splice(n,0,{type:Xr.Element,element:e,token:t})}removeEntry(e){const t=this.entries.indexOf(e);t>=0&&this.entries.splice(t,1)}clearToLastMarker(){const e=this.entries.indexOf(Jr);e>=0?this.entries.splice(0,e+1):this.entries.length=0}getElementEntryInScopeWithTagName(e){const t=this.entries.find((t=>t.type===Xr.Marker||this.treeAdapter.getTagName(t.element)===e));return t&&t.type===Xr.Element?t:null}getElementEntry(e){return this.entries.find((t=>t.type===Xr.Element&&t.element===e))}}function ti(e){return{nodeName:"#text",value:e,parentNode:null}}const ni={createDocument:()=>({nodeName:"#document",mode:Er.NO_QUIRKS,childNodes:[]}),createDocumentFragment:()=>({nodeName:"#document-fragment",childNodes:[]}),createElement:(e,t,n)=>({nodeName:e,tagName:e,attrs:n,namespaceURI:t,childNodes:[],parentNode:null}),createCommentNode:e=>({nodeName:"#comment",data:e,parentNode:null}),appendChild(e,t){e.childNodes.push(t),t.parentNode=e},insertBefore(e,t,n){const s=e.childNodes.indexOf(n);e.childNodes.splice(s,0,t),t.parentNode=e},setTemplateContent(e,t){e.content=t},getTemplateContent:e=>e.content,setDocumentType(e,t,n,s){const r=e.childNodes.find((e=>"#documentType"===e.nodeName));if(r)r.name=t,r.publicId=n,r.systemId=s;else{const r={nodeName:"#documentType",name:t,publicId:n,systemId:s,parentNode:null};ni.appendChild(e,r)}},setDocumentMode(e,t){e.mode=t},getDocumentMode:e=>e.mode,detachNode(e){if(e.parentNode){const t=e.parentNode.childNodes.indexOf(e);e.parentNode.childNodes.splice(t,1),e.parentNode=null}},insertText(e,t){if(e.childNodes.length>0){const n=e.childNodes[e.childNodes.length-1];if(ni.isTextNode(n))return void(n.value+=t)}ni.appendChild(e,ti(t))},insertTextBefore(e,t,n){const s=e.childNodes[e.childNodes.indexOf(n)-1];s&&ni.isTextNode(s)?s.value+=t:ni.insertBefore(e,ti(t),n)},adoptAttributes(e,t){const n=new Set(e.attrs.map((e=>e.name)));for(let s=0;s<t.length;s++)n.has(t[s].name)||e.attrs.push(t[s])},getFirstChild:e=>e.childNodes[0],getChildNodes:e=>e.childNodes,getParentNode:e=>e.parentNode,getAttrList:e=>e.attrs,getTagName:e=>e.tagName,getNamespaceURI:e=>e.namespaceURI,getTextNodeContent:e=>e.value,getCommentNodeContent:e=>e.data,getDocumentTypeNodeName:e=>e.name,getDocumentTypeNodePublicId:e=>e.publicId,getDocumentTypeNodeSystemId:e=>e.systemId,isTextNode:e=>"#text"===e.nodeName,isCommentNode:e=>"#comment"===e.nodeName,isDocumentTypeNode:e=>"#documentType"===e.nodeName,isElementNode:e=>Object.prototype.hasOwnProperty.call(e,"tagName"),setNodeSourceCodeLocation(e,t){e.sourceCodeLocation=t},getNodeSourceCodeLocation:e=>e.sourceCodeLocation,updateNodeSourceCodeLocation(e,t){e.sourceCodeLocation={...e.sourceCodeLocation,...t}}},si="html",ri="about:legacy-compat",ii="http://www.ibm.com/data/dtd/v11/ibmxhtml1-transitional.dtd",oi=["+//silmaril//dtd html pro v0r11 19970101//","-//as//dtd html 3.0 aswedit + extensions//","-//advasoft ltd//dtd html 3.0 aswedit + extensions//","-//ietf//dtd html 2.0 level 1//","-//ietf//dtd html 2.0 level 2//","-//ietf//dtd html 2.0 strict level 1//","-//ietf//dtd html 2.0 strict level 2//","-//ietf//dtd html 2.0 strict//","-//ietf//dtd html 2.0//","-//ietf//dtd html 2.1e//","-//ietf//dtd html 3.0//","-//ietf//dtd html 3.2 final//","-//ietf//dtd html 3.2//","-//ietf//dtd html 3//","-//ietf//dtd html level 0//","-//ietf//dtd html level 1//","-//ietf//dtd html level 2//","-//ietf//dtd html level 3//","-//ietf//dtd html strict level 0//","-//ietf//dtd html strict level 1//","-//ietf//dtd html strict level 2//","-//ietf//dtd html strict level 3//","-//ietf//dtd html strict//","-//ietf//dtd html//","-//metrius//dtd metrius presentational//","-//microsoft//dtd internet explorer 2.0 html strict//","-//microsoft//dtd internet explorer 2.0 html//","-//microsoft//dtd internet explorer 2.0 tables//","-//microsoft//dtd internet explorer 3.0 html strict//","-//microsoft//dtd internet explorer 3.0 html//","-//microsoft//dtd internet explorer 3.0 tables//","-//netscape comm. corp.//dtd html//","-//netscape comm. corp.//dtd strict html//","-//o'reilly and associates//dtd html 2.0//","-//o'reilly and associates//dtd html extended 1.0//","-//o'reilly and associates//dtd html extended relaxed 1.0//","-//sq//dtd html 2.0 hotmetal + extensions//","-//softquad software//dtd hotmetal pro 6.0::19990601::extensions to html 4.0//","-//softquad//dtd hotmetal pro 4.0::19971010::extensions to html 4.0//","-//spyglass//dtd html 2.0 extended//","-//sun microsystems corp.//dtd hotjava html//","-//sun microsystems corp.//dtd hotjava strict html//","-//w3c//dtd html 3 1995-03-24//","-//w3c//dtd html 3.2 draft//","-//w3c//dtd html 3.2 final//","-//w3c//dtd html 3.2//","-//w3c//dtd html 3.2s draft//","-//w3c//dtd html 4.0 frameset//","-//w3c//dtd html 4.0 transitional//","-//w3c//dtd html experimental 19960712//","-//w3c//dtd html experimental 970421//","-//w3c//dtd w3 html//","-//w3o//dtd w3 html 3.0//","-//webtechs//dtd mozilla html 2.0//","-//webtechs//dtd mozilla html//"],ai=[...oi,"-//w3c//dtd html 4.01 frameset//","-//w3c//dtd html 4.01 transitional//"],ci=new Set(["-//w3o//dtd w3 html strict 3.0//en//","-/w3c/dtd html 4.0 transitional/en","html"]),li=["-//w3c//dtd xhtml 1.0 frameset//","-//w3c//dtd xhtml 1.0 transitional//"],ui=[...li,"-//w3c//dtd html 4.01 frameset//","-//w3c//dtd html 4.01 transitional//"];function hi(e,t){return t.some((t=>e.startsWith(t)))}const di={TEXT_HTML:"text/html",APPLICATION_XML:"application/xhtml+xml"},pi="definitionurl",fi="definitionURL",mi=new Map(["attributeName","attributeType","baseFrequency","baseProfile","calcMode","clipPathUnits","diffuseConstant","edgeMode","filterUnits","glyphRef","gradientTransform","gradientUnits","kernelMatrix","kernelUnitLength","keyPoints","keySplines","keyTimes","lengthAdjust","limitingConeAngle","markerHeight","markerUnits","markerWidth","maskContentUnits","maskUnits","numOctaves","pathLength","patternContentUnits","patternTransform","patternUnits","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","refX","refY","repeatCount","repeatDur","requiredExtensions","requiredFeatures","specularConstant","specularExponent","spreadMethod","startOffset","stdDeviation","stitchTiles","surfaceScale","systemLanguage","tableValues","targetX","targetY","textLength","viewBox","viewTarget","xChannelSelector","yChannelSelector","zoomAndPan"].map((e=>[e.toLowerCase(),e]))),Ei=new Map([["xlink:actuate",{prefix:"xlink",name:"actuate",namespace:dr.XLINK}],["xlink:arcrole",{prefix:"xlink",name:"arcrole",namespace:dr.XLINK}],["xlink:href",{prefix:"xlink",name:"href",namespace:dr.XLINK}],["xlink:role",{prefix:"xlink",name:"role",namespace:dr.XLINK}],["xlink:show",{prefix:"xlink",name:"show",namespace:dr.XLINK}],["xlink:title",{prefix:"xlink",name:"title",namespace:dr.XLINK}],["xlink:type",{prefix:"xlink",name:"type",namespace:dr.XLINK}],["xml:base",{prefix:"xml",name:"base",namespace:dr.XML}],["xml:lang",{prefix:"xml",name:"lang",namespace:dr.XML}],["xml:space",{prefix:"xml",name:"space",namespace:dr.XML}],["xmlns",{prefix:"",name:"xmlns",namespace:dr.XMLNS}],["xmlns:xlink",{prefix:"xmlns",name:"xlink",namespace:dr.XMLNS}]]),Ti=new Map(["altGlyph","altGlyphDef","altGlyphItem","animateColor","animateMotion","animateTransform","clipPath","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","foreignObject","glyphRef","linearGradient","radialGradient","textPath"].map((e=>[e.toLowerCase(),e]))),_i=new Set([gr.B,gr.BIG,gr.BLOCKQUOTE,gr.BODY,gr.BR,gr.CENTER,gr.CODE,gr.DD,gr.DIV,gr.DL,gr.DT,gr.EM,gr.EMBED,gr.H1,gr.H2,gr.H3,gr.H4,gr.H5,gr.H6,gr.HEAD,gr.HR,gr.I,gr.IMG,gr.LI,gr.LISTING,gr.MENU,gr.META,gr.NOBR,gr.OL,gr.P,gr.PRE,gr.RUBY,gr.S,gr.SMALL,gr.SPAN,gr.STRONG,gr.STRIKE,gr.SUB,gr.SUP,gr.TABLE,gr.TT,gr.U,gr.UL,gr.VAR]);function Ai(e){for(let t=0;t<e.attrs.length;t++)if(e.attrs[t].name===pi){e.attrs[t].name=fi;break}}function gi(e){for(let t=0;t<e.attrs.length;t++){const n=mi.get(e.attrs[t].name);null!=n&&(e.attrs[t].name=n)}}function Ci(e){for(let t=0;t<e.attrs.length;t++){const n=Ei.get(e.attrs[t].name);n&&(e.attrs[t].prefix=n.prefix,e.attrs[t].name=n.name,e.attrs[t].namespace=n.namespace)}}function bi(e,t,n,s){return(!s||s===dr.HTML)&&function(e,t,n){if(t===dr.MATHML&&e===gr.ANNOTATION_XML)for(let e=0;e<n.length;e++)if(n[e].name===fr.ENCODING){const t=n[e].value.toLowerCase();return t===di.TEXT_HTML||t===di.APPLICATION_XML}return t===dr.SVG&&(e===gr.FOREIGN_OBJECT||e===gr.DESC||e===gr.TITLE)}(e,t,n)||(!s||s===dr.MATHML)&&function(e,t){return t===dr.MATHML&&(e===gr.MI||e===gr.MO||e===gr.MN||e===gr.MS||e===gr.MTEXT)}(e,t)}const Ni="hidden",Ii=8,ki=3;var Si,Di;(Di=Si||(Si={}))[Di.INITIAL=0]="INITIAL",Di[Di.BEFORE_HTML=1]="BEFORE_HTML",Di[Di.BEFORE_HEAD=2]="BEFORE_HEAD",Di[Di.IN_HEAD=3]="IN_HEAD",Di[Di.IN_HEAD_NO_SCRIPT=4]="IN_HEAD_NO_SCRIPT",Di[Di.AFTER_HEAD=5]="AFTER_HEAD",Di[Di.IN_BODY=6]="IN_BODY",Di[Di.TEXT=7]="TEXT",Di[Di.IN_TABLE=8]="IN_TABLE",Di[Di.IN_TABLE_TEXT=9]="IN_TABLE_TEXT",Di[Di.IN_CAPTION=10]="IN_CAPTION",Di[Di.IN_COLUMN_GROUP=11]="IN_COLUMN_GROUP",Di[Di.IN_TABLE_BODY=12]="IN_TABLE_BODY",Di[Di.IN_ROW=13]="IN_ROW",Di[Di.IN_CELL=14]="IN_CELL",Di[Di.IN_SELECT=15]="IN_SELECT",Di[Di.IN_SELECT_IN_TABLE=16]="IN_SELECT_IN_TABLE",Di[Di.IN_TEMPLATE=17]="IN_TEMPLATE",Di[Di.AFTER_BODY=18]="AFTER_BODY",Di[Di.IN_FRAMESET=19]="IN_FRAMESET",Di[Di.AFTER_FRAMESET=20]="AFTER_FRAMESET",Di[Di.AFTER_AFTER_BODY=21]="AFTER_AFTER_BODY",Di[Di.AFTER_AFTER_FRAMESET=22]="AFTER_AFTER_FRAMESET";const yi={startLine:-1,startCol:-1,startOffset:-1,endLine:-1,endCol:-1,endOffset:-1},Oi=new Set([gr.TABLE,gr.TBODY,gr.TFOOT,gr.THEAD,gr.TR]),Li={scriptingEnabled:!0,sourceCodeLocationInfo:!1,treeAdapter:ni,onParseError:null};let Ri=class{constructor(e,t,n=null,s=null){this.fragmentContext=n,this.scriptHandler=s,this.currentToken=null,this.stopped=!1,this.insertionMode=Si.INITIAL,this.originalInsertionMode=Si.INITIAL,this.headElement=null,this.formElement=null,this.currentNotInHTML=!1,this.tmplInsertionModeStack=[],this.pendingCharacterTokens=[],this.hasNonWhitespacePendingCharacterToken=!1,this.framesetOk=!0,this.skipNextNewLine=!1,this.fosterParentingEnabled=!1,this.options={...Li,...e},this.treeAdapter=this.options.treeAdapter,this.onParseError=this.options.onParseError,this.onParseError&&(this.options.sourceCodeLocationInfo=!0),this.document=null!=t?t:this.treeAdapter.createDocument(),this.tokenizer=new class{constructor(e,t){this.options=e,this.handler=t,this.paused=!1,this.inLoop=!1,this.inForeignNode=!1,this.lastStartTagName="",this.active=!1,this.state=Lr.DATA,this.returnState=Lr.DATA,this.charRefCode=-1,this.consumedAfterSnapshot=-1,this.currentCharacterToken=null,this.currentToken=null,this.currentAttr={name:"",value:""},this.preprocessor=new lr(t),this.currentLocation=this.getCurrentLocation(-1)}_err(e){var t,n;null===(n=(t=this.handler).onParseError)||void 0===n||n.call(t,this.preprocessor.getError(e))}getCurrentLocation(e){return this.options.sourceCodeLocationInfo?{startLine:this.preprocessor.line,startCol:this.preprocessor.col-e,startOffset:this.preprocessor.offset-e,endLine:-1,endCol:-1,endOffset:-1}:null}_runParsingLoop(){if(!this.inLoop){for(this.inLoop=!0;this.active&&!this.paused;){this.consumedAfterSnapshot=0;const e=this._consume();this._ensureHibernation()||this._callState(e)}this.inLoop=!1}}pause(){this.paused=!0}resume(e){if(!this.paused)throw new Error("Parser was already resumed");this.paused=!1,this.inLoop||(this._runParsingLoop(),this.paused||null==e||e())}write(e,t,n){this.active=!0,this.preprocessor.write(e,t),this._runParsingLoop(),this.paused||null==n||n()}insertHtmlAtCurrentPos(e){this.active=!0,this.preprocessor.insertHtmlAtCurrentPos(e),this._runParsingLoop()}_ensureHibernation(){return!!this.preprocessor.endOfChunkHit&&(this._unconsume(this.consumedAfterSnapshot),this.active=!1,!0)}_consume(){return this.consumedAfterSnapshot++,this.preprocessor.advance()}_unconsume(e){this.consumedAfterSnapshot-=e,this.preprocessor.retreat(e)}_reconsumeInState(e,t){this.state=e,this._callState(t)}_advanceBy(e){this.consumedAfterSnapshot+=e;for(let t=0;t<e;t++)this.preprocessor.advance()}_consumeSequenceIfMatch(e,t){return!!this.preprocessor.startsWith(e,t)&&(this._advanceBy(e.length-1),!0)}_createStartTagToken(){this.currentToken={type:ur.START_TAG,tagName:"",tagID:gr.UNKNOWN,selfClosing:!1,ackSelfClosing:!1,attrs:[],location:this.getCurrentLocation(1)}}_createEndTagToken(){this.currentToken={type:ur.END_TAG,tagName:"",tagID:gr.UNKNOWN,selfClosing:!1,ackSelfClosing:!1,attrs:[],location:this.getCurrentLocation(2)}}_createCommentToken(e){this.currentToken={type:ur.COMMENT,data:"",location:this.getCurrentLocation(e)}}_createDoctypeToken(e){this.currentToken={type:ur.DOCTYPE,name:e,forceQuirks:!1,publicId:null,systemId:null,location:this.currentLocation}}_createCharacterToken(e,t){this.currentCharacterToken={type:e,chars:t,location:this.currentLocation}}_createAttr(e){this.currentAttr={name:e,value:""},this.currentLocation=this.getCurrentLocation(0)}_leaveAttrName(){var e,t;const n=this.currentToken;null===br(n,this.currentAttr.name)?(n.attrs.push(this.currentAttr),n.location&&this.currentLocation&&((null!==(e=(t=n.location).attrs)&&void 0!==e?e:t.attrs=Object.create(null))[this.currentAttr.name]=this.currentLocation,this._leaveAttrValue())):this._err(ar.duplicateAttribute)}_leaveAttrValue(){this.currentLocation&&(this.currentLocation.endLine=this.preprocessor.line,this.currentLocation.endCol=this.preprocessor.col,this.currentLocation.endOffset=this.preprocessor.offset)}prepareToken(e){this._emitCurrentCharacterToken(e.location),this.currentToken=null,e.location&&(e.location.endLine=this.preprocessor.line,e.location.endCol=this.preprocessor.col+1,e.location.endOffset=this.preprocessor.offset+1),this.currentLocation=this.getCurrentLocation(-1)}emitCurrentTagToken(){const e=this.currentToken;this.prepareToken(e),e.tagID=Ir(e.tagName),e.type===ur.START_TAG?(this.lastStartTagName=e.tagName,this.handler.onStartTag(e)):(e.attrs.length>0&&this._err(ar.endTagWithAttributes),e.selfClosing&&this._err(ar.endTagWithTrailingSolidus),this.handler.onEndTag(e)),this.preprocessor.dropParsedChunk()}emitCurrentComment(e){this.prepareToken(e),this.handler.onComment(e),this.preprocessor.dropParsedChunk()}emitCurrentDoctype(e){this.prepareToken(e),this.handler.onDoctype(e),this.preprocessor.dropParsedChunk()}_emitCurrentCharacterToken(e){if(this.currentCharacterToken){switch(e&&this.currentCharacterToken.location&&(this.currentCharacterToken.location.endLine=e.startLine,this.currentCharacterToken.location.endCol=e.startCol,this.currentCharacterToken.location.endOffset=e.startOffset),this.currentCharacterToken.type){case ur.CHARACTER:this.handler.onCharacter(this.currentCharacterToken);break;case ur.NULL_CHARACTER:this.handler.onNullCharacter(this.currentCharacterToken);break;case ur.WHITESPACE_CHARACTER:this.handler.onWhitespaceCharacter(this.currentCharacterToken)}this.currentCharacterToken=null}}_emitEOFToken(){const e=this.getCurrentLocation(0);e&&(e.endLine=e.startLine,e.endCol=e.startCol,e.endOffset=e.startOffset),this._emitCurrentCharacterToken(e),this.handler.onEof({type:ur.EOF,location:e}),this.active=!1}_appendCharToCurrentCharacterToken(e,t){if(this.currentCharacterToken){if(this.currentCharacterToken.type===e)return void(this.currentCharacterToken.chars+=t);this.currentLocation=this.getCurrentLocation(0),this._emitCurrentCharacterToken(this.currentLocation),this.preprocessor.dropParsedChunk()}this._createCharacterToken(e,t)}_emitCodePoint(e){const t=Hr(e)?ur.WHITESPACE_CHARACTER:e===Ws.NULL?ur.NULL_CHARACTER:ur.CHARACTER;this._appendCharToCurrentCharacterToken(t,String.fromCodePoint(e))}_emitChars(e){this._appendCharToCurrentCharacterToken(ur.CHARACTER,e)}_matchNamedCharacterReference(e){let t=null,n=0,s=!1;for(let i=0,o=le[0];i>=0&&(i=De(le,o,i+1,e),!(i<0));e=this._consume()){n+=1,o=le[i];const a=o&Te.VALUE_LENGTH;if(a){const o=(a>>14)-1;if(e!==Ws.SEMICOLON&&this._isCharacterReferenceInAttribute()&&((r=this.preprocessor.peek(1))===Ws.EQUALS_SIGN||Pr(r))?(t=[Ws.AMPERSAND],i+=o):(t=0===o?[le[i]&~Te.VALUE_LENGTH]:1===o?[le[++i]]:[le[++i],le[++i]],n=0,s=e!==Ws.SEMICOLON),0===o){this._consume();break}}}var r;return this._unconsume(n),s&&!this.preprocessor.endOfChunkHit&&this._err(ar.missingSemicolonAfterCharacterReference),this._unconsume(1),t}_isCharacterReferenceInAttribute(){return this.returnState===Lr.ATTRIBUTE_VALUE_DOUBLE_QUOTED||this.returnState===Lr.ATTRIBUTE_VALUE_SINGLE_QUOTED||this.returnState===Lr.ATTRIBUTE_VALUE_UNQUOTED}_flushCodePointConsumedAsCharacterReference(e){this._isCharacterReferenceInAttribute()?this.currentAttr.value+=String.fromCodePoint(e):this._emitCodePoint(e)}_callState(e){switch(this.state){case Lr.DATA:this._stateData(e);break;case Lr.RCDATA:this._stateRcdata(e);break;case Lr.RAWTEXT:this._stateRawtext(e);break;case Lr.SCRIPT_DATA:this._stateScriptData(e);break;case Lr.PLAINTEXT:this._statePlaintext(e);break;case Lr.TAG_OPEN:this._stateTagOpen(e);break;case Lr.END_TAG_OPEN:this._stateEndTagOpen(e);break;case Lr.TAG_NAME:this._stateTagName(e);break;case Lr.RCDATA_LESS_THAN_SIGN:this._stateRcdataLessThanSign(e);break;case Lr.RCDATA_END_TAG_OPEN:this._stateRcdataEndTagOpen(e);break;case Lr.RCDATA_END_TAG_NAME:this._stateRcdataEndTagName(e);break;case Lr.RAWTEXT_LESS_THAN_SIGN:this._stateRawtextLessThanSign(e);break;case Lr.RAWTEXT_END_TAG_OPEN:this._stateRawtextEndTagOpen(e);break;case Lr.RAWTEXT_END_TAG_NAME:this._stateRawtextEndTagName(e);break;case Lr.SCRIPT_DATA_LESS_THAN_SIGN:this._stateScriptDataLessThanSign(e);break;case Lr.SCRIPT_DATA_END_TAG_OPEN:this._stateScriptDataEndTagOpen(e);break;case Lr.SCRIPT_DATA_END_TAG_NAME:this._stateScriptDataEndTagName(e);break;case Lr.SCRIPT_DATA_ESCAPE_START:this._stateScriptDataEscapeStart(e);break;case Lr.SCRIPT_DATA_ESCAPE_START_DASH:this._stateScriptDataEscapeStartDash(e);break;case Lr.SCRIPT_DATA_ESCAPED:this._stateScriptDataEscaped(e);break;case Lr.SCRIPT_DATA_ESCAPED_DASH:this._stateScriptDataEscapedDash(e);break;case Lr.SCRIPT_DATA_ESCAPED_DASH_DASH:this._stateScriptDataEscapedDashDash(e);break;case Lr.SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN:this._stateScriptDataEscapedLessThanSign(e);break;case Lr.SCRIPT_DATA_ESCAPED_END_TAG_OPEN:this._stateScriptDataEscapedEndTagOpen(e);break;case Lr.SCRIPT_DATA_ESCAPED_END_TAG_NAME:this._stateScriptDataEscapedEndTagName(e);break;case Lr.SCRIPT_DATA_DOUBLE_ESCAPE_START:this._stateScriptDataDoubleEscapeStart(e);break;case Lr.SCRIPT_DATA_DOUBLE_ESCAPED:this._stateScriptDataDoubleEscaped(e);break;case Lr.SCRIPT_DATA_DOUBLE_ESCAPED_DASH:this._stateScriptDataDoubleEscapedDash(e);break;case Lr.SCRIPT_DATA_DOUBLE_ESCAPED_DASH_DASH:this._stateScriptDataDoubleEscapedDashDash(e);break;case Lr.SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN:this._stateScriptDataDoubleEscapedLessThanSign(e);break;case Lr.SCRIPT_DATA_DOUBLE_ESCAPE_END:this._stateScriptDataDoubleEscapeEnd(e);break;case Lr.BEFORE_ATTRIBUTE_NAME:this._stateBeforeAttributeName(e);break;case Lr.ATTRIBUTE_NAME:this._stateAttributeName(e);break;case Lr.AFTER_ATTRIBUTE_NAME:this._stateAfterAttributeName(e);break;case Lr.BEFORE_ATTRIBUTE_VALUE:this._stateBeforeAttributeValue(e);break;case Lr.ATTRIBUTE_VALUE_DOUBLE_QUOTED:this._stateAttributeValueDoubleQuoted(e);break;case Lr.ATTRIBUTE_VALUE_SINGLE_QUOTED:this._stateAttributeValueSingleQuoted(e);break;case Lr.ATTRIBUTE_VALUE_UNQUOTED:this._stateAttributeValueUnquoted(e);break;case Lr.AFTER_ATTRIBUTE_VALUE_QUOTED:this._stateAfterAttributeValueQuoted(e);break;case Lr.SELF_CLOSING_START_TAG:this._stateSelfClosingStartTag(e);break;case Lr.BOGUS_COMMENT:this._stateBogusComment(e);break;case Lr.MARKUP_DECLARATION_OPEN:this._stateMarkupDeclarationOpen(e);break;case Lr.COMMENT_START:this._stateCommentStart(e);break;case Lr.COMMENT_START_DASH:this._stateCommentStartDash(e);break;case Lr.COMMENT:this._stateComment(e);break;case Lr.COMMENT_LESS_THAN_SIGN:this._stateCommentLessThanSign(e);break;case Lr.COMMENT_LESS_THAN_SIGN_BANG:this._stateCommentLessThanSignBang(e);break;case Lr.COMMENT_LESS_THAN_SIGN_BANG_DASH:this._stateCommentLessThanSignBangDash(e);break;case Lr.COMMENT_LESS_THAN_SIGN_BANG_DASH_DASH:this._stateCommentLessThanSignBangDashDash(e);break;case Lr.COMMENT_END_DASH:this._stateCommentEndDash(e);break;case Lr.COMMENT_END:this._stateCommentEnd(e);break;case Lr.COMMENT_END_BANG:this._stateCommentEndBang(e);break;case Lr.DOCTYPE:this._stateDoctype(e);break;case Lr.BEFORE_DOCTYPE_NAME:this._stateBeforeDoctypeName(e);break;case Lr.DOCTYPE_NAME:this._stateDoctypeName(e);break;case Lr.AFTER_DOCTYPE_NAME:this._stateAfterDoctypeName(e);break;case Lr.AFTER_DOCTYPE_PUBLIC_KEYWORD:this._stateAfterDoctypePublicKeyword(e);break;case Lr.BEFORE_DOCTYPE_PUBLIC_IDENTIFIER:this._stateBeforeDoctypePublicIdentifier(e);break;case Lr.DOCTYPE_PUBLIC_IDENTIFIER_DOUBLE_QUOTED:this._stateDoctypePublicIdentifierDoubleQuoted(e);break;case Lr.DOCTYPE_PUBLIC_IDENTIFIER_SINGLE_QUOTED:this._stateDoctypePublicIdentifierSingleQuoted(e);break;case Lr.AFTER_DOCTYPE_PUBLIC_IDENTIFIER:this._stateAfterDoctypePublicIdentifier(e);break;case Lr.BETWEEN_DOCTYPE_PUBLIC_AND_SYSTEM_IDENTIFIERS:this._stateBetweenDoctypePublicAndSystemIdentifiers(e);break;case Lr.AFTER_DOCTYPE_SYSTEM_KEYWORD:this._stateAfterDoctypeSystemKeyword(e);break;case Lr.BEFORE_DOCTYPE_SYSTEM_IDENTIFIER:this._stateBeforeDoctypeSystemIdentifier(e);break;case Lr.DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED:this._stateDoctypeSystemIdentifierDoubleQuoted(e);break;case Lr.DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED:this._stateDoctypeSystemIdentifierSingleQuoted(e);break;case Lr.AFTER_DOCTYPE_SYSTEM_IDENTIFIER:this._stateAfterDoctypeSystemIdentifier(e);break;case Lr.BOGUS_DOCTYPE:this._stateBogusDoctype(e);break;case Lr.CDATA_SECTION:this._stateCdataSection(e);break;case Lr.CDATA_SECTION_BRACKET:this._stateCdataSectionBracket(e);break;case Lr.CDATA_SECTION_END:this._stateCdataSectionEnd(e);break;case Lr.CHARACTER_REFERENCE:this._stateCharacterReference(e);break;case Lr.NAMED_CHARACTER_REFERENCE:this._stateNamedCharacterReference(e);break;case Lr.AMBIGUOUS_AMPERSAND:this._stateAmbiguousAmpersand(e);break;case Lr.NUMERIC_CHARACTER_REFERENCE:this._stateNumericCharacterReference(e);break;case Lr.HEXADEMICAL_CHARACTER_REFERENCE_START:this._stateHexademicalCharacterReferenceStart(e);break;case Lr.HEXADEMICAL_CHARACTER_REFERENCE:this._stateHexademicalCharacterReference(e);break;case Lr.DECIMAL_CHARACTER_REFERENCE:this._stateDecimalCharacterReference(e);break;case Lr.NUMERIC_CHARACTER_REFERENCE_END:this._stateNumericCharacterReferenceEnd(e);break;default:throw new Error("Unknown state")}}_stateData(e){switch(e){case Ws.LESS_THAN_SIGN:this.state=Lr.TAG_OPEN;break;case Ws.AMPERSAND:this.returnState=Lr.DATA,this.state=Lr.CHARACTER_REFERENCE;break;case Ws.NULL:this._err(ar.unexpectedNullCharacter),this._emitCodePoint(e);break;case Ws.EOF:this._emitEOFToken();break;default:this._emitCodePoint(e)}}_stateRcdata(e){switch(e){case Ws.AMPERSAND:this.returnState=Lr.RCDATA,this.state=Lr.CHARACTER_REFERENCE;break;case Ws.LESS_THAN_SIGN:this.state=Lr.RCDATA_LESS_THAN_SIGN;break;case Ws.NULL:this._err(ar.unexpectedNullCharacter),this._emitChars(Qs);break;case Ws.EOF:this._emitEOFToken();break;default:this._emitCodePoint(e)}}_stateRawtext(e){switch(e){case Ws.LESS_THAN_SIGN:this.state=Lr.RAWTEXT_LESS_THAN_SIGN;break;case Ws.NULL:this._err(ar.unexpectedNullCharacter),this._emitChars(Qs);break;case Ws.EOF:this._emitEOFToken();break;default:this._emitCodePoint(e)}}_stateScriptData(e){switch(e){case Ws.LESS_THAN_SIGN:this.state=Lr.SCRIPT_DATA_LESS_THAN_SIGN;break;case Ws.NULL:this._err(ar.unexpectedNullCharacter),this._emitChars(Qs);break;case Ws.EOF:this._emitEOFToken();break;default:this._emitCodePoint(e)}}_statePlaintext(e){switch(e){case Ws.NULL:this._err(ar.unexpectedNullCharacter),this._emitChars(Qs);break;case Ws.EOF:this._emitEOFToken();break;default:this._emitCodePoint(e)}}_stateTagOpen(e){if(wr(e))this._createStartTagToken(),this.state=Lr.TAG_NAME,this._stateTagName(e);else switch(e){case Ws.EXCLAMATION_MARK:this.state=Lr.MARKUP_DECLARATION_OPEN;break;case Ws.SOLIDUS:this.state=Lr.END_TAG_OPEN;break;case Ws.QUESTION_MARK:this._err(ar.unexpectedQuestionMarkInsteadOfTagName),this._createCommentToken(1),this.state=Lr.BOGUS_COMMENT,this._stateBogusComment(e);break;case Ws.EOF:this._err(ar.eofBeforeTagName),this._emitChars("<"),this._emitEOFToken();break;default:this._err(ar.invalidFirstCharacterOfTagName),this._emitChars("<"),this.state=Lr.DATA,this._stateData(e)}}_stateEndTagOpen(e){if(wr(e))this._createEndTagToken(),this.state=Lr.TAG_NAME,this._stateTagName(e);else switch(e){case Ws.GREATER_THAN_SIGN:this._err(ar.missingEndTagName),this.state=Lr.DATA;break;case Ws.EOF:this._err(ar.eofBeforeTagName),this._emitChars("</"),this._emitEOFToken();break;default:this._err(ar.invalidFirstCharacterOfTagName),this._createCommentToken(2),this.state=Lr.BOGUS_COMMENT,this._stateBogusComment(e)}}_stateTagName(e){const t=this.currentToken;switch(e){case Ws.SPACE:case Ws.LINE_FEED:case Ws.TABULATION:case Ws.FORM_FEED:this.state=Lr.BEFORE_ATTRIBUTE_NAME;break;case Ws.SOLIDUS:this.state=Lr.SELF_CLOSING_START_TAG;break;case Ws.GREATER_THAN_SIGN:this.state=Lr.DATA,this.emitCurrentTagToken();break;case Ws.NULL:this._err(ar.unexpectedNullCharacter),t.tagName+=Qs;break;case Ws.EOF:this._err(ar.eofInTag),this._emitEOFToken();break;default:t.tagName+=String.fromCodePoint(xr(e)?Ur(e):e)}}_stateRcdataLessThanSign(e){e===Ws.SOLIDUS?this.state=Lr.RCDATA_END_TAG_OPEN:(this._emitChars("<"),this.state=Lr.RCDATA,this._stateRcdata(e))}_stateRcdataEndTagOpen(e){wr(e)?(this.state=Lr.RCDATA_END_TAG_NAME,this._stateRcdataEndTagName(e)):(this._emitChars("</"),this.state=Lr.RCDATA,this._stateRcdata(e))}handleSpecialEndTag(e){if(!this.preprocessor.startsWith(this.lastStartTagName,!1))return!this._ensureHibernation();switch(this._createEndTagToken(),this.currentToken.tagName=this.lastStartTagName,this.preprocessor.peek(this.lastStartTagName.length)){case Ws.SPACE:case Ws.LINE_FEED:case Ws.TABULATION:case Ws.FORM_FEED:return this._advanceBy(this.lastStartTagName.length),this.state=Lr.BEFORE_ATTRIBUTE_NAME,!1;case Ws.SOLIDUS:return this._advanceBy(this.lastStartTagName.length),this.state=Lr.SELF_CLOSING_START_TAG,!1;case Ws.GREATER_THAN_SIGN:return this._advanceBy(this.lastStartTagName.length),this.emitCurrentTagToken(),this.state=Lr.DATA,!1;default:return!this._ensureHibernation()}}_stateRcdataEndTagName(e){this.handleSpecialEndTag(e)&&(this._emitChars("</"),this.state=Lr.RCDATA,this._stateRcdata(e))}_stateRawtextLessThanSign(e){e===Ws.SOLIDUS?this.state=Lr.RAWTEXT_END_TAG_OPEN:(this._emitChars("<"),this.state=Lr.RAWTEXT,this._stateRawtext(e))}_stateRawtextEndTagOpen(e){wr(e)?(this.state=Lr.RAWTEXT_END_TAG_NAME,this._stateRawtextEndTagName(e)):(this._emitChars("</"),this.state=Lr.RAWTEXT,this._stateRawtext(e))}_stateRawtextEndTagName(e){this.handleSpecialEndTag(e)&&(this._emitChars("</"),this.state=Lr.RAWTEXT,this._stateRawtext(e))}_stateScriptDataLessThanSign(e){switch(e){case Ws.SOLIDUS:this.state=Lr.SCRIPT_DATA_END_TAG_OPEN;break;case Ws.EXCLAMATION_MARK:this.state=Lr.SCRIPT_DATA_ESCAPE_START,this._emitChars("<!");break;default:this._emitChars("<"),this.state=Lr.SCRIPT_DATA,this._stateScriptData(e)}}_stateScriptDataEndTagOpen(e){wr(e)?(this.state=Lr.SCRIPT_DATA_END_TAG_NAME,this._stateScriptDataEndTagName(e)):(this._emitChars("</"),this.state=Lr.SCRIPT_DATA,this._stateScriptData(e))}_stateScriptDataEndTagName(e){this.handleSpecialEndTag(e)&&(this._emitChars("</"),this.state=Lr.SCRIPT_DATA,this._stateScriptData(e))}_stateScriptDataEscapeStart(e){e===Ws.HYPHEN_MINUS?(this.state=Lr.SCRIPT_DATA_ESCAPE_START_DASH,this._emitChars("-")):(this.state=Lr.SCRIPT_DATA,this._stateScriptData(e))}_stateScriptDataEscapeStartDash(e){e===Ws.HYPHEN_MINUS?(this.state=Lr.SCRIPT_DATA_ESCAPED_DASH_DASH,this._emitChars("-")):(this.state=Lr.SCRIPT_DATA,this._stateScriptData(e))}_stateScriptDataEscaped(e){switch(e){case Ws.HYPHEN_MINUS:this.state=Lr.SCRIPT_DATA_ESCAPED_DASH,this._emitChars("-");break;case Ws.LESS_THAN_SIGN:this.state=Lr.SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN;break;case Ws.NULL:this._err(ar.unexpectedNullCharacter),this._emitChars(Qs);break;case Ws.EOF:this._err(ar.eofInScriptHtmlCommentLikeText),this._emitEOFToken();break;default:this._emitCodePoint(e)}}_stateScriptDataEscapedDash(e){switch(e){case Ws.HYPHEN_MINUS:this.state=Lr.SCRIPT_DATA_ESCAPED_DASH_DASH,this._emitChars("-");break;case Ws.LESS_THAN_SIGN:this.state=Lr.SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN;break;case Ws.NULL:this._err(ar.unexpectedNullCharacter),this.state=Lr.SCRIPT_DATA_ESCAPED,this._emitChars(Qs);break;case Ws.EOF:this._err(ar.eofInScriptHtmlCommentLikeText),this._emitEOFToken();break;default:this.state=Lr.SCRIPT_DATA_ESCAPED,this._emitCodePoint(e)}}_stateScriptDataEscapedDashDash(e){switch(e){case Ws.HYPHEN_MINUS:this._emitChars("-");break;case Ws.LESS_THAN_SIGN:this.state=Lr.SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN;break;case Ws.GREATER_THAN_SIGN:this.state=Lr.SCRIPT_DATA,this._emitChars(">");break;case Ws.NULL:this._err(ar.unexpectedNullCharacter),this.state=Lr.SCRIPT_DATA_ESCAPED,this._emitChars(Qs);break;case Ws.EOF:this._err(ar.eofInScriptHtmlCommentLikeText),this._emitEOFToken();break;default:this.state=Lr.SCRIPT_DATA_ESCAPED,this._emitCodePoint(e)}}_stateScriptDataEscapedLessThanSign(e){e===Ws.SOLIDUS?this.state=Lr.SCRIPT_DATA_ESCAPED_END_TAG_OPEN:wr(e)?(this._emitChars("<"),this.state=Lr.SCRIPT_DATA_DOUBLE_ESCAPE_START,this._stateScriptDataDoubleEscapeStart(e)):(this._emitChars("<"),this.state=Lr.SCRIPT_DATA_ESCAPED,this._stateScriptDataEscaped(e))}_stateScriptDataEscapedEndTagOpen(e){wr(e)?(this.state=Lr.SCRIPT_DATA_ESCAPED_END_TAG_NAME,this._stateScriptDataEscapedEndTagName(e)):(this._emitChars("</"),this.state=Lr.SCRIPT_DATA_ESCAPED,this._stateScriptDataEscaped(e))}_stateScriptDataEscapedEndTagName(e){this.handleSpecialEndTag(e)&&(this._emitChars("</"),this.state=Lr.SCRIPT_DATA_ESCAPED,this._stateScriptDataEscaped(e))}_stateScriptDataDoubleEscapeStart(e){if(this.preprocessor.startsWith(tr,!1)&&Gr(this.preprocessor.peek(tr.length))){this._emitCodePoint(e);for(let e=0;e<tr.length;e++)this._emitCodePoint(this._consume());this.state=Lr.SCRIPT_DATA_DOUBLE_ESCAPED}else this._ensureHibernation()||(this.state=Lr.SCRIPT_DATA_ESCAPED,this._stateScriptDataEscaped(e))}_stateScriptDataDoubleEscaped(e){switch(e){case Ws.HYPHEN_MINUS:this.state=Lr.SCRIPT_DATA_DOUBLE_ESCAPED_DASH,this._emitChars("-");break;case Ws.LESS_THAN_SIGN:this.state=Lr.SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN,this._emitChars("<");break;case Ws.NULL:this._err(ar.unexpectedNullCharacter),this._emitChars(Qs);break;case Ws.EOF:this._err(ar.eofInScriptHtmlCommentLikeText),this._emitEOFToken();break;default:this._emitCodePoint(e)}}_stateScriptDataDoubleEscapedDash(e){switch(e){case Ws.HYPHEN_MINUS:this.state=Lr.SCRIPT_DATA_DOUBLE_ESCAPED_DASH_DASH,this._emitChars("-");break;case Ws.LESS_THAN_SIGN:this.state=Lr.SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN,this._emitChars("<");break;case Ws.NULL:this._err(ar.unexpectedNullCharacter),this.state=Lr.SCRIPT_DATA_DOUBLE_ESCAPED,this._emitChars(Qs);break;case Ws.EOF:this._err(ar.eofInScriptHtmlCommentLikeText),this._emitEOFToken();break;default:this.state=Lr.SCRIPT_DATA_DOUBLE_ESCAPED,this._emitCodePoint(e)}}_stateScriptDataDoubleEscapedDashDash(e){switch(e){case Ws.HYPHEN_MINUS:this._emitChars("-");break;case Ws.LESS_THAN_SIGN:this.state=Lr.SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN,this._emitChars("<");break;case Ws.GREATER_THAN_SIGN:this.state=Lr.SCRIPT_DATA,this._emitChars(">");break;case Ws.NULL:this._err(ar.unexpectedNullCharacter),this.state=Lr.SCRIPT_DATA_DOUBLE_ESCAPED,this._emitChars(Qs);break;case Ws.EOF:this._err(ar.eofInScriptHtmlCommentLikeText),this._emitEOFToken();break;default:this.state=Lr.SCRIPT_DATA_DOUBLE_ESCAPED,this._emitCodePoint(e)}}_stateScriptDataDoubleEscapedLessThanSign(e){e===Ws.SOLIDUS?(this.state=Lr.SCRIPT_DATA_DOUBLE_ESCAPE_END,this._emitChars("/")):(this.state=Lr.SCRIPT_DATA_DOUBLE_ESCAPED,this._stateScriptDataDoubleEscaped(e))}_stateScriptDataDoubleEscapeEnd(e){if(this.preprocessor.startsWith(tr,!1)&&Gr(this.preprocessor.peek(tr.length))){this._emitCodePoint(e);for(let e=0;e<tr.length;e++)this._emitCodePoint(this._consume());this.state=Lr.SCRIPT_DATA_ESCAPED}else this._ensureHibernation()||(this.state=Lr.SCRIPT_DATA_DOUBLE_ESCAPED,this._stateScriptDataDoubleEscaped(e))}_stateBeforeAttributeName(e){switch(e){case Ws.SPACE:case Ws.LINE_FEED:case Ws.TABULATION:case Ws.FORM_FEED:break;case Ws.SOLIDUS:case Ws.GREATER_THAN_SIGN:case Ws.EOF:this.state=Lr.AFTER_ATTRIBUTE_NAME,this._stateAfterAttributeName(e);break;case Ws.EQUALS_SIGN:this._err(ar.unexpectedEqualsSignBeforeAttributeName),this._createAttr("="),this.state=Lr.ATTRIBUTE_NAME;break;default:this._createAttr(""),this.state=Lr.ATTRIBUTE_NAME,this._stateAttributeName(e)}}_stateAttributeName(e){switch(e){case Ws.SPACE:case Ws.LINE_FEED:case Ws.TABULATION:case Ws.FORM_FEED:case Ws.SOLIDUS:case Ws.GREATER_THAN_SIGN:case Ws.EOF:this._leaveAttrName(),this.state=Lr.AFTER_ATTRIBUTE_NAME,this._stateAfterAttributeName(e);break;case Ws.EQUALS_SIGN:this._leaveAttrName(),this.state=Lr.BEFORE_ATTRIBUTE_VALUE;break;case Ws.QUOTATION_MARK:case Ws.APOSTROPHE:case Ws.LESS_THAN_SIGN:this._err(ar.unexpectedCharacterInAttributeName),this.currentAttr.name+=String.fromCodePoint(e);break;case Ws.NULL:this._err(ar.unexpectedNullCharacter),this.currentAttr.name+=Qs;break;default:this.currentAttr.name+=String.fromCodePoint(xr(e)?Ur(e):e)}}_stateAfterAttributeName(e){switch(e){case Ws.SPACE:case Ws.LINE_FEED:case Ws.TABULATION:case Ws.FORM_FEED:break;case Ws.SOLIDUS:this.state=Lr.SELF_CLOSING_START_TAG;break;case Ws.EQUALS_SIGN:this.state=Lr.BEFORE_ATTRIBUTE_VALUE;break;case Ws.GREATER_THAN_SIGN:this.state=Lr.DATA,this.emitCurrentTagToken();break;case Ws.EOF:this._err(ar.eofInTag),this._emitEOFToken();break;default:this._createAttr(""),this.state=Lr.ATTRIBUTE_NAME,this._stateAttributeName(e)}}_stateBeforeAttributeValue(e){switch(e){case Ws.SPACE:case Ws.LINE_FEED:case Ws.TABULATION:case Ws.FORM_FEED:break;case Ws.QUOTATION_MARK:this.state=Lr.ATTRIBUTE_VALUE_DOUBLE_QUOTED;break;case Ws.APOSTROPHE:this.state=Lr.ATTRIBUTE_VALUE_SINGLE_QUOTED;break;case Ws.GREATER_THAN_SIGN:this._err(ar.missingAttributeValue),this.state=Lr.DATA,this.emitCurrentTagToken();break;default:this.state=Lr.ATTRIBUTE_VALUE_UNQUOTED,this._stateAttributeValueUnquoted(e)}}_stateAttributeValueDoubleQuoted(e){switch(e){case Ws.QUOTATION_MARK:this.state=Lr.AFTER_ATTRIBUTE_VALUE_QUOTED;break;case Ws.AMPERSAND:this.returnState=Lr.ATTRIBUTE_VALUE_DOUBLE_QUOTED,this.state=Lr.CHARACTER_REFERENCE;break;case Ws.NULL:this._err(ar.unexpectedNullCharacter),this.currentAttr.value+=Qs;break;case Ws.EOF:this._err(ar.eofInTag),this._emitEOFToken();break;default:this.currentAttr.value+=String.fromCodePoint(e)}}_stateAttributeValueSingleQuoted(e){switch(e){case Ws.APOSTROPHE:this.state=Lr.AFTER_ATTRIBUTE_VALUE_QUOTED;break;case Ws.AMPERSAND:this.returnState=Lr.ATTRIBUTE_VALUE_SINGLE_QUOTED,this.state=Lr.CHARACTER_REFERENCE;break;case Ws.NULL:this._err(ar.unexpectedNullCharacter),this.currentAttr.value+=Qs;break;case Ws.EOF:this._err(ar.eofInTag),this._emitEOFToken();break;default:this.currentAttr.value+=String.fromCodePoint(e)}}_stateAttributeValueUnquoted(e){switch(e){case Ws.SPACE:case Ws.LINE_FEED:case Ws.TABULATION:case Ws.FORM_FEED:this._leaveAttrValue(),this.state=Lr.BEFORE_ATTRIBUTE_NAME;break;case Ws.AMPERSAND:this.returnState=Lr.ATTRIBUTE_VALUE_UNQUOTED,this.state=Lr.CHARACTER_REFERENCE;break;case Ws.GREATER_THAN_SIGN:this._leaveAttrValue(),this.state=Lr.DATA,this.emitCurrentTagToken();break;case Ws.NULL:this._err(ar.unexpectedNullCharacter),this.currentAttr.value+=Qs;break;case Ws.QUOTATION_MARK:case Ws.APOSTROPHE:case Ws.LESS_THAN_SIGN:case Ws.EQUALS_SIGN:case Ws.GRAVE_ACCENT:this._err(ar.unexpectedCharacterInUnquotedAttributeValue),this.currentAttr.value+=String.fromCodePoint(e);break;case Ws.EOF:this._err(ar.eofInTag),this._emitEOFToken();break;default:this.currentAttr.value+=String.fromCodePoint(e)}}_stateAfterAttributeValueQuoted(e){switch(e){case Ws.SPACE:case Ws.LINE_FEED:case Ws.TABULATION:case Ws.FORM_FEED:this._leaveAttrValue(),this.state=Lr.BEFORE_ATTRIBUTE_NAME;break;case Ws.SOLIDUS:this._leaveAttrValue(),this.state=Lr.SELF_CLOSING_START_TAG;break;case Ws.GREATER_THAN_SIGN:this._leaveAttrValue(),this.state=Lr.DATA,this.emitCurrentTagToken();break;case Ws.EOF:this._err(ar.eofInTag),this._emitEOFToken();break;default:this._err(ar.missingWhitespaceBetweenAttributes),this.state=Lr.BEFORE_ATTRIBUTE_NAME,this._stateBeforeAttributeName(e)}}_stateSelfClosingStartTag(e){switch(e){case Ws.GREATER_THAN_SIGN:this.currentToken.selfClosing=!0,this.state=Lr.DATA,this.emitCurrentTagToken();break;case Ws.EOF:this._err(ar.eofInTag),this._emitEOFToken();break;default:this._err(ar.unexpectedSolidusInTag),this.state=Lr.BEFORE_ATTRIBUTE_NAME,this._stateBeforeAttributeName(e)}}_stateBogusComment(e){const t=this.currentToken;switch(e){case Ws.GREATER_THAN_SIGN:this.state=Lr.DATA,this.emitCurrentComment(t);break;case Ws.EOF:this.emitCurrentComment(t),this._emitEOFToken();break;case Ws.NULL:this._err(ar.unexpectedNullCharacter),t.data+=Qs;break;default:t.data+=String.fromCodePoint(e)}}_stateMarkupDeclarationOpen(e){this._consumeSequenceIfMatch(Zs,!0)?(this._createCommentToken(Zs.length+1),this.state=Lr.COMMENT_START):this._consumeSequenceIfMatch(er,!1)?(this.currentLocation=this.getCurrentLocation(er.length+1),this.state=Lr.DOCTYPE):this._consumeSequenceIfMatch(Js,!0)?this.inForeignNode?this.state=Lr.CDATA_SECTION:(this._err(ar.cdataInHtmlContent),this._createCommentToken(Js.length+1),this.currentToken.data="[CDATA[",this.state=Lr.BOGUS_COMMENT):this._ensureHibernation()||(this._err(ar.incorrectlyOpenedComment),this._createCommentToken(2),this.state=Lr.BOGUS_COMMENT,this._stateBogusComment(e))}_stateCommentStart(e){switch(e){case Ws.HYPHEN_MINUS:this.state=Lr.COMMENT_START_DASH;break;case Ws.GREATER_THAN_SIGN:{this._err(ar.abruptClosingOfEmptyComment),this.state=Lr.DATA;const e=this.currentToken;this.emitCurrentComment(e);break}default:this.state=Lr.COMMENT,this._stateComment(e)}}_stateCommentStartDash(e){const t=this.currentToken;switch(e){case Ws.HYPHEN_MINUS:this.state=Lr.COMMENT_END;break;case Ws.GREATER_THAN_SIGN:this._err(ar.abruptClosingOfEmptyComment),this.state=Lr.DATA,this.emitCurrentComment(t);break;case Ws.EOF:this._err(ar.eofInComment),this.emitCurrentComment(t),this._emitEOFToken();break;default:t.data+="-",this.state=Lr.COMMENT,this._stateComment(e)}}_stateComment(e){const t=this.currentToken;switch(e){case Ws.HYPHEN_MINUS:this.state=Lr.COMMENT_END_DASH;break;case Ws.LESS_THAN_SIGN:t.data+="<",this.state=Lr.COMMENT_LESS_THAN_SIGN;break;case Ws.NULL:this._err(ar.unexpectedNullCharacter),t.data+=Qs;break;case Ws.EOF:this._err(ar.eofInComment),this.emitCurrentComment(t),this._emitEOFToken();break;default:t.data+=String.fromCodePoint(e)}}_stateCommentLessThanSign(e){const t=this.currentToken;switch(e){case Ws.EXCLAMATION_MARK:t.data+="!",this.state=Lr.COMMENT_LESS_THAN_SIGN_BANG;break;case Ws.LESS_THAN_SIGN:t.data+="<";break;default:this.state=Lr.COMMENT,this._stateComment(e)}}_stateCommentLessThanSignBang(e){e===Ws.HYPHEN_MINUS?this.state=Lr.COMMENT_LESS_THAN_SIGN_BANG_DASH:(this.state=Lr.COMMENT,this._stateComment(e))}_stateCommentLessThanSignBangDash(e){e===Ws.HYPHEN_MINUS?this.state=Lr.COMMENT_LESS_THAN_SIGN_BANG_DASH_DASH:(this.state=Lr.COMMENT_END_DASH,this._stateCommentEndDash(e))}_stateCommentLessThanSignBangDashDash(e){e!==Ws.GREATER_THAN_SIGN&&e!==Ws.EOF&&this._err(ar.nestedComment),this.state=Lr.COMMENT_END,this._stateCommentEnd(e)}_stateCommentEndDash(e){const t=this.currentToken;switch(e){case Ws.HYPHEN_MINUS:this.state=Lr.COMMENT_END;break;case Ws.EOF:this._err(ar.eofInComment),this.emitCurrentComment(t),this._emitEOFToken();break;default:t.data+="-",this.state=Lr.COMMENT,this._stateComment(e)}}_stateCommentEnd(e){const t=this.currentToken;switch(e){case Ws.GREATER_THAN_SIGN:this.state=Lr.DATA,this.emitCurrentComment(t);break;case Ws.EXCLAMATION_MARK:this.state=Lr.COMMENT_END_BANG;break;case Ws.HYPHEN_MINUS:t.data+="-";break;case Ws.EOF:this._err(ar.eofInComment),this.emitCurrentComment(t),this._emitEOFToken();break;default:t.data+="--",this.state=Lr.COMMENT,this._stateComment(e)}}_stateCommentEndBang(e){const t=this.currentToken;switch(e){case Ws.HYPHEN_MINUS:t.data+="--!",this.state=Lr.COMMENT_END_DASH;break;case Ws.GREATER_THAN_SIGN:this._err(ar.incorrectlyClosedComment),this.state=Lr.DATA,this.emitCurrentComment(t);break;case Ws.EOF:this._err(ar.eofInComment),this.emitCurrentComment(t),this._emitEOFToken();break;default:t.data+="--!",this.state=Lr.COMMENT,this._stateComment(e)}}_stateDoctype(e){switch(e){case Ws.SPACE:case Ws.LINE_FEED:case Ws.TABULATION:case Ws.FORM_FEED:this.state=Lr.BEFORE_DOCTYPE_NAME;break;case Ws.GREATER_THAN_SIGN:this.state=Lr.BEFORE_DOCTYPE_NAME,this._stateBeforeDoctypeName(e);break;case Ws.EOF:{this._err(ar.eofInDoctype),this._createDoctypeToken(null);const e=this.currentToken;e.forceQuirks=!0,this.emitCurrentDoctype(e),this._emitEOFToken();break}default:this._err(ar.missingWhitespaceBeforeDoctypeName),this.state=Lr.BEFORE_DOCTYPE_NAME,this._stateBeforeDoctypeName(e)}}_stateBeforeDoctypeName(e){if(xr(e))this._createDoctypeToken(String.fromCharCode(Ur(e))),this.state=Lr.DOCTYPE_NAME;else switch(e){case Ws.SPACE:case Ws.LINE_FEED:case Ws.TABULATION:case Ws.FORM_FEED:break;case Ws.NULL:this._err(ar.unexpectedNullCharacter),this._createDoctypeToken(Qs),this.state=Lr.DOCTYPE_NAME;break;case Ws.GREATER_THAN_SIGN:{this._err(ar.missingDoctypeName),this._createDoctypeToken(null);const e=this.currentToken;e.forceQuirks=!0,this.emitCurrentDoctype(e),this.state=Lr.DATA;break}case Ws.EOF:{this._err(ar.eofInDoctype),this._createDoctypeToken(null);const e=this.currentToken;e.forceQuirks=!0,this.emitCurrentDoctype(e),this._emitEOFToken();break}default:this._createDoctypeToken(String.fromCodePoint(e)),this.state=Lr.DOCTYPE_NAME}}_stateDoctypeName(e){const t=this.currentToken;switch(e){case Ws.SPACE:case Ws.LINE_FEED:case Ws.TABULATION:case Ws.FORM_FEED:this.state=Lr.AFTER_DOCTYPE_NAME;break;case Ws.GREATER_THAN_SIGN:this.state=Lr.DATA,this.emitCurrentDoctype(t);break;case Ws.NULL:this._err(ar.unexpectedNullCharacter),t.name+=Qs;break;case Ws.EOF:this._err(ar.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:t.name+=String.fromCodePoint(xr(e)?Ur(e):e)}}_stateAfterDoctypeName(e){const t=this.currentToken;switch(e){case Ws.SPACE:case Ws.LINE_FEED:case Ws.TABULATION:case Ws.FORM_FEED:break;case Ws.GREATER_THAN_SIGN:this.state=Lr.DATA,this.emitCurrentDoctype(t);break;case Ws.EOF:this._err(ar.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:this._consumeSequenceIfMatch(nr,!1)?this.state=Lr.AFTER_DOCTYPE_PUBLIC_KEYWORD:this._consumeSequenceIfMatch(sr,!1)?this.state=Lr.AFTER_DOCTYPE_SYSTEM_KEYWORD:this._ensureHibernation()||(this._err(ar.invalidCharacterSequenceAfterDoctypeName),t.forceQuirks=!0,this.state=Lr.BOGUS_DOCTYPE,this._stateBogusDoctype(e))}}_stateAfterDoctypePublicKeyword(e){const t=this.currentToken;switch(e){case Ws.SPACE:case Ws.LINE_FEED:case Ws.TABULATION:case Ws.FORM_FEED:this.state=Lr.BEFORE_DOCTYPE_PUBLIC_IDENTIFIER;break;case Ws.QUOTATION_MARK:this._err(ar.missingWhitespaceAfterDoctypePublicKeyword),t.publicId="",this.state=Lr.DOCTYPE_PUBLIC_IDENTIFIER_DOUBLE_QUOTED;break;case Ws.APOSTROPHE:this._err(ar.missingWhitespaceAfterDoctypePublicKeyword),t.publicId="",this.state=Lr.DOCTYPE_PUBLIC_IDENTIFIER_SINGLE_QUOTED;break;case Ws.GREATER_THAN_SIGN:this._err(ar.missingDoctypePublicIdentifier),t.forceQuirks=!0,this.state=Lr.DATA,this.emitCurrentDoctype(t);break;case Ws.EOF:this._err(ar.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:this._err(ar.missingQuoteBeforeDoctypePublicIdentifier),t.forceQuirks=!0,this.state=Lr.BOGUS_DOCTYPE,this._stateBogusDoctype(e)}}_stateBeforeDoctypePublicIdentifier(e){const t=this.currentToken;switch(e){case Ws.SPACE:case Ws.LINE_FEED:case Ws.TABULATION:case Ws.FORM_FEED:break;case Ws.QUOTATION_MARK:t.publicId="",this.state=Lr.DOCTYPE_PUBLIC_IDENTIFIER_DOUBLE_QUOTED;break;case Ws.APOSTROPHE:t.publicId="",this.state=Lr.DOCTYPE_PUBLIC_IDENTIFIER_SINGLE_QUOTED;break;case Ws.GREATER_THAN_SIGN:this._err(ar.missingDoctypePublicIdentifier),t.forceQuirks=!0,this.state=Lr.DATA,this.emitCurrentDoctype(t);break;case Ws.EOF:this._err(ar.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:this._err(ar.missingQuoteBeforeDoctypePublicIdentifier),t.forceQuirks=!0,this.state=Lr.BOGUS_DOCTYPE,this._stateBogusDoctype(e)}}_stateDoctypePublicIdentifierDoubleQuoted(e){const t=this.currentToken;switch(e){case Ws.QUOTATION_MARK:this.state=Lr.AFTER_DOCTYPE_PUBLIC_IDENTIFIER;break;case Ws.NULL:this._err(ar.unexpectedNullCharacter),t.publicId+=Qs;break;case Ws.GREATER_THAN_SIGN:this._err(ar.abruptDoctypePublicIdentifier),t.forceQuirks=!0,this.emitCurrentDoctype(t),this.state=Lr.DATA;break;case Ws.EOF:this._err(ar.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:t.publicId+=String.fromCodePoint(e)}}_stateDoctypePublicIdentifierSingleQuoted(e){const t=this.currentToken;switch(e){case Ws.APOSTROPHE:this.state=Lr.AFTER_DOCTYPE_PUBLIC_IDENTIFIER;break;case Ws.NULL:this._err(ar.unexpectedNullCharacter),t.publicId+=Qs;break;case Ws.GREATER_THAN_SIGN:this._err(ar.abruptDoctypePublicIdentifier),t.forceQuirks=!0,this.emitCurrentDoctype(t),this.state=Lr.DATA;break;case Ws.EOF:this._err(ar.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:t.publicId+=String.fromCodePoint(e)}}_stateAfterDoctypePublicIdentifier(e){const t=this.currentToken;switch(e){case Ws.SPACE:case Ws.LINE_FEED:case Ws.TABULATION:case Ws.FORM_FEED:this.state=Lr.BETWEEN_DOCTYPE_PUBLIC_AND_SYSTEM_IDENTIFIERS;break;case Ws.GREATER_THAN_SIGN:this.state=Lr.DATA,this.emitCurrentDoctype(t);break;case Ws.QUOTATION_MARK:this._err(ar.missingWhitespaceBetweenDoctypePublicAndSystemIdentifiers),t.systemId="",this.state=Lr.DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED;break;case Ws.APOSTROPHE:this._err(ar.missingWhitespaceBetweenDoctypePublicAndSystemIdentifiers),t.systemId="",this.state=Lr.DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED;break;case Ws.EOF:this._err(ar.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:this._err(ar.missingQuoteBeforeDoctypeSystemIdentifier),t.forceQuirks=!0,this.state=Lr.BOGUS_DOCTYPE,this._stateBogusDoctype(e)}}_stateBetweenDoctypePublicAndSystemIdentifiers(e){const t=this.currentToken;switch(e){case Ws.SPACE:case Ws.LINE_FEED:case Ws.TABULATION:case Ws.FORM_FEED:break;case Ws.GREATER_THAN_SIGN:this.emitCurrentDoctype(t),this.state=Lr.DATA;break;case Ws.QUOTATION_MARK:t.systemId="",this.state=Lr.DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED;break;case Ws.APOSTROPHE:t.systemId="",this.state=Lr.DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED;break;case Ws.EOF:this._err(ar.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:this._err(ar.missingQuoteBeforeDoctypeSystemIdentifier),t.forceQuirks=!0,this.state=Lr.BOGUS_DOCTYPE,this._stateBogusDoctype(e)}}_stateAfterDoctypeSystemKeyword(e){const t=this.currentToken;switch(e){case Ws.SPACE:case Ws.LINE_FEED:case Ws.TABULATION:case Ws.FORM_FEED:this.state=Lr.BEFORE_DOCTYPE_SYSTEM_IDENTIFIER;break;case Ws.QUOTATION_MARK:this._err(ar.missingWhitespaceAfterDoctypeSystemKeyword),t.systemId="",this.state=Lr.DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED;break;case Ws.APOSTROPHE:this._err(ar.missingWhitespaceAfterDoctypeSystemKeyword),t.systemId="",this.state=Lr.DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED;break;case Ws.GREATER_THAN_SIGN:this._err(ar.missingDoctypeSystemIdentifier),t.forceQuirks=!0,this.state=Lr.DATA,this.emitCurrentDoctype(t);break;case Ws.EOF:this._err(ar.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:this._err(ar.missingQuoteBeforeDoctypeSystemIdentifier),t.forceQuirks=!0,this.state=Lr.BOGUS_DOCTYPE,this._stateBogusDoctype(e)}}_stateBeforeDoctypeSystemIdentifier(e){const t=this.currentToken;switch(e){case Ws.SPACE:case Ws.LINE_FEED:case Ws.TABULATION:case Ws.FORM_FEED:break;case Ws.QUOTATION_MARK:t.systemId="",this.state=Lr.DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED;break;case Ws.APOSTROPHE:t.systemId="",this.state=Lr.DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED;break;case Ws.GREATER_THAN_SIGN:this._err(ar.missingDoctypeSystemIdentifier),t.forceQuirks=!0,this.state=Lr.DATA,this.emitCurrentDoctype(t);break;case Ws.EOF:this._err(ar.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:this._err(ar.missingQuoteBeforeDoctypeSystemIdentifier),t.forceQuirks=!0,this.state=Lr.BOGUS_DOCTYPE,this._stateBogusDoctype(e)}}_stateDoctypeSystemIdentifierDoubleQuoted(e){const t=this.currentToken;switch(e){case Ws.QUOTATION_MARK:this.state=Lr.AFTER_DOCTYPE_SYSTEM_IDENTIFIER;break;case Ws.NULL:this._err(ar.unexpectedNullCharacter),t.systemId+=Qs;break;case Ws.GREATER_THAN_SIGN:this._err(ar.abruptDoctypeSystemIdentifier),t.forceQuirks=!0,this.emitCurrentDoctype(t),this.state=Lr.DATA;break;case Ws.EOF:this._err(ar.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:t.systemId+=String.fromCodePoint(e)}}_stateDoctypeSystemIdentifierSingleQuoted(e){const t=this.currentToken;switch(e){case Ws.APOSTROPHE:this.state=Lr.AFTER_DOCTYPE_SYSTEM_IDENTIFIER;break;case Ws.NULL:this._err(ar.unexpectedNullCharacter),t.systemId+=Qs;break;case Ws.GREATER_THAN_SIGN:this._err(ar.abruptDoctypeSystemIdentifier),t.forceQuirks=!0,this.emitCurrentDoctype(t),this.state=Lr.DATA;break;case Ws.EOF:this._err(ar.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:t.systemId+=String.fromCodePoint(e)}}_stateAfterDoctypeSystemIdentifier(e){const t=this.currentToken;switch(e){case Ws.SPACE:case Ws.LINE_FEED:case Ws.TABULATION:case Ws.FORM_FEED:break;case Ws.GREATER_THAN_SIGN:this.emitCurrentDoctype(t),this.state=Lr.DATA;break;case Ws.EOF:this._err(ar.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:this._err(ar.unexpectedCharacterAfterDoctypeSystemIdentifier),this.state=Lr.BOGUS_DOCTYPE,this._stateBogusDoctype(e)}}_stateBogusDoctype(e){const t=this.currentToken;switch(e){case Ws.GREATER_THAN_SIGN:this.emitCurrentDoctype(t),this.state=Lr.DATA;break;case Ws.NULL:this._err(ar.unexpectedNullCharacter);break;case Ws.EOF:this.emitCurrentDoctype(t),this._emitEOFToken()}}_stateCdataSection(e){switch(e){case Ws.RIGHT_SQUARE_BRACKET:this.state=Lr.CDATA_SECTION_BRACKET;break;case Ws.EOF:this._err(ar.eofInCdata),this._emitEOFToken();break;default:this._emitCodePoint(e)}}_stateCdataSectionBracket(e){e===Ws.RIGHT_SQUARE_BRACKET?this.state=Lr.CDATA_SECTION_END:(this._emitChars("]"),this.state=Lr.CDATA_SECTION,this._stateCdataSection(e))}_stateCdataSectionEnd(e){switch(e){case Ws.GREATER_THAN_SIGN:this.state=Lr.DATA;break;case Ws.RIGHT_SQUARE_BRACKET:this._emitChars("]");break;default:this._emitChars("]]"),this.state=Lr.CDATA_SECTION,this._stateCdataSection(e)}}_stateCharacterReference(e){e===Ws.NUMBER_SIGN?this.state=Lr.NUMERIC_CHARACTER_REFERENCE:Pr(e)?(this.state=Lr.NAMED_CHARACTER_REFERENCE,this._stateNamedCharacterReference(e)):(this._flushCodePointConsumedAsCharacterReference(Ws.AMPERSAND),this._reconsumeInState(this.returnState,e))}_stateNamedCharacterReference(e){const t=this._matchNamedCharacterReference(e);if(this._ensureHibernation());else if(t){for(let e=0;e<t.length;e++)this._flushCodePointConsumedAsCharacterReference(t[e]);this.state=this.returnState}else this._flushCodePointConsumedAsCharacterReference(Ws.AMPERSAND),this.state=Lr.AMBIGUOUS_AMPERSAND}_stateAmbiguousAmpersand(e){Pr(e)?this._flushCodePointConsumedAsCharacterReference(e):(e===Ws.SEMICOLON&&this._err(ar.unknownNamedCharacterReference),this._reconsumeInState(this.returnState,e))}_stateNumericCharacterReference(e){this.charRefCode=0,e===Ws.LATIN_SMALL_X||e===Ws.LATIN_CAPITAL_X?this.state=Lr.HEXADEMICAL_CHARACTER_REFERENCE_START:vr(e)?(this.state=Lr.DECIMAL_CHARACTER_REFERENCE,this._stateDecimalCharacterReference(e)):(this._err(ar.absenceOfDigitsInNumericCharacterReference),this._flushCodePointConsumedAsCharacterReference(Ws.AMPERSAND),this._flushCodePointConsumedAsCharacterReference(Ws.NUMBER_SIGN),this._reconsumeInState(this.returnState,e))}_stateHexademicalCharacterReferenceStart(e){!function(e){return vr(e)||Fr(e)||Br(e)}(e)?(this._err(ar.absenceOfDigitsInNumericCharacterReference),this._flushCodePointConsumedAsCharacterReference(Ws.AMPERSAND),this._flushCodePointConsumedAsCharacterReference(Ws.NUMBER_SIGN),this._unconsume(2),this.state=this.returnState):(this.state=Lr.HEXADEMICAL_CHARACTER_REFERENCE,this._stateHexademicalCharacterReference(e))}_stateHexademicalCharacterReference(e){Fr(e)?this.charRefCode=16*this.charRefCode+e-55:Br(e)?this.charRefCode=16*this.charRefCode+e-87:vr(e)?this.charRefCode=16*this.charRefCode+e-48:e===Ws.SEMICOLON?this.state=Lr.NUMERIC_CHARACTER_REFERENCE_END:(this._err(ar.missingSemicolonAfterCharacterReference),this.state=Lr.NUMERIC_CHARACTER_REFERENCE_END,this._stateNumericCharacterReferenceEnd(e))}_stateDecimalCharacterReference(e){vr(e)?this.charRefCode=10*this.charRefCode+e-48:e===Ws.SEMICOLON?this.state=Lr.NUMERIC_CHARACTER_REFERENCE_END:(this._err(ar.missingSemicolonAfterCharacterReference),this.state=Lr.NUMERIC_CHARACTER_REFERENCE_END,this._stateNumericCharacterReferenceEnd(e))}_stateNumericCharacterReferenceEnd(e){if(this.charRefCode===Ws.NULL)this._err(ar.nullCharacterReference),this.charRefCode=Ws.REPLACEMENT_CHARACTER;else if(this.charRefCode>1114111)this._err(ar.characterReferenceOutsideUnicodeRange),this.charRefCode=Ws.REPLACEMENT_CHARACTER;else if(rr(this.charRefCode))this._err(ar.surrogateCharacterReference),this.charRefCode=Ws.REPLACEMENT_CHARACTER;else if(or(this.charRefCode))this._err(ar.noncharacterCharacterReference);else if(ir(this.charRefCode)||this.charRefCode===Ws.CARRIAGE_RETURN){this._err(ar.controlCharacterReference);const e=Or.get(this.charRefCode);void 0!==e&&(this.charRefCode=e)}this._flushCodePointConsumedAsCharacterReference(this.charRefCode),this._reconsumeInState(this.returnState,e)}}(this.options,this),this.activeFormattingElements=new ei(this.treeAdapter),this.fragmentContextID=n?Ir(this.treeAdapter.getTagName(n)):gr.UNKNOWN,this._setContextModes(null!=n?n:this.document,this.fragmentContextID),this.openElements=new Wr(this.document,this.treeAdapter,this)}static parse(e,t){const n=new this(t);return n.tokenizer.write(e,!0),n.document}static getFragmentParser(e,t){const n={...Li,...t};null!=e||(e=n.treeAdapter.createElement(_r.TEMPLATE,dr.HTML,[]));const s=n.treeAdapter.createElement("documentmock",dr.HTML,[]),r=new this(n,s,e);return r.fragmentContextID===gr.TEMPLATE&&r.tmplInsertionModeStack.unshift(Si.IN_TEMPLATE),r._initTokenizerForFragmentParsing(),r._insertFakeRootElement(),r._resetInsertionMode(),r._findFormInFragmentContext(),r}getFragment(){const e=this.treeAdapter.getFirstChild(this.document),t=this.treeAdapter.createDocumentFragment();return this._adoptNodes(e,t),t}_err(e,t,n){var s;if(!this.onParseError)return;const r=null!==(s=e.location)&&void 0!==s?s:yi,i={code:t,startLine:r.startLine,startCol:r.startCol,startOffset:r.startOffset,endLine:n?r.startLine:r.endLine,endCol:n?r.startCol:r.endCol,endOffset:n?r.startOffset:r.endOffset};this.onParseError(i)}onItemPush(e,t,n){var s,r;null===(r=(s=this.treeAdapter).onItemPush)||void 0===r||r.call(s,e),n&&this.openElements.stackTop>0&&this._setContextModes(e,t)}onItemPop(e,t){var n,s;if(this.options.sourceCodeLocationInfo&&this._setEndLocation(e,this.currentToken),null===(s=(n=this.treeAdapter).onItemPop)||void 0===s||s.call(n,e,this.openElements.current),t){let e,t;0===this.openElements.stackTop&&this.fragmentContext?(e=this.fragmentContext,t=this.fragmentContextID):({current:e,currentTagId:t}=this.openElements),this._setContextModes(e,t)}}_setContextModes(e,t){const n=e===this.document||this.treeAdapter.getNamespaceURI(e)===dr.HTML;this.currentNotInHTML=!n,this.tokenizer.inForeignNode=!n&&!this._isIntegrationPoint(t,e)}_switchToTextParsing(e,t){this._insertElement(e,dr.HTML),this.tokenizer.state=t,this.originalInsertionMode=this.insertionMode,this.insertionMode=Si.TEXT}switchToPlaintextParsing(){this.insertionMode=Si.TEXT,this.originalInsertionMode=Si.IN_BODY,this.tokenizer.state=Mr.PLAINTEXT}_getAdjustedCurrentElement(){return 0===this.openElements.stackTop&&this.fragmentContext?this.fragmentContext:this.openElements.current}_findFormInFragmentContext(){let e=this.fragmentContext;for(;e;){if(this.treeAdapter.getTagName(e)===_r.FORM){this.formElement=e;break}e=this.treeAdapter.getParentNode(e)}}_initTokenizerForFragmentParsing(){if(this.fragmentContext&&this.treeAdapter.getNamespaceURI(this.fragmentContext)===dr.HTML)switch(this.fragmentContextID){case gr.TITLE:case gr.TEXTAREA:this.tokenizer.state=Mr.RCDATA;break;case gr.STYLE:case gr.XMP:case gr.IFRAME:case gr.NOEMBED:case gr.NOFRAMES:case gr.NOSCRIPT:this.tokenizer.state=Mr.RAWTEXT;break;case gr.SCRIPT:this.tokenizer.state=Mr.SCRIPT_DATA;break;case gr.PLAINTEXT:this.tokenizer.state=Mr.PLAINTEXT}}_setDocumentType(e){const t=e.name||"",n=e.publicId||"",s=e.systemId||"";if(this.treeAdapter.setDocumentType(this.document,t,n,s),e.location){const t=this.treeAdapter.getChildNodes(this.document).find((e=>this.treeAdapter.isDocumentTypeNode(e)));t&&this.treeAdapter.setNodeSourceCodeLocation(t,e.location)}}_attachElementToTree(e,t){if(this.options.sourceCodeLocationInfo){const n=t&&{...t,startTag:t};this.treeAdapter.setNodeSourceCodeLocation(e,n)}if(this._shouldFosterParentOnInsertion())this._fosterParentElement(e);else{const t=this.openElements.currentTmplContentOrNode;this.treeAdapter.appendChild(t,e)}}_appendElement(e,t){const n=this.treeAdapter.createElement(e.tagName,t,e.attrs);this._attachElementToTree(n,e.location)}_insertElement(e,t){const n=this.treeAdapter.createElement(e.tagName,t,e.attrs);this._attachElementToTree(n,e.location),this.openElements.push(n,e.tagID)}_insertFakeElement(e,t){const n=this.treeAdapter.createElement(e,dr.HTML,[]);this._attachElementToTree(n,null),this.openElements.push(n,t)}_insertTemplate(e){const t=this.treeAdapter.createElement(e.tagName,dr.HTML,e.attrs),n=this.treeAdapter.createDocumentFragment();this.treeAdapter.setTemplateContent(t,n),this._attachElementToTree(t,e.location),this.openElements.push(t,e.tagID),this.options.sourceCodeLocationInfo&&this.treeAdapter.setNodeSourceCodeLocation(n,null)}_insertFakeRootElement(){const e=this.treeAdapter.createElement(_r.HTML,dr.HTML,[]);this.options.sourceCodeLocationInfo&&this.treeAdapter.setNodeSourceCodeLocation(e,null),this.treeAdapter.appendChild(this.openElements.current,e),this.openElements.push(e,gr.HTML)}_appendCommentNode(e,t){const n=this.treeAdapter.createCommentNode(e.data);this.treeAdapter.appendChild(t,n),this.options.sourceCodeLocationInfo&&this.treeAdapter.setNodeSourceCodeLocation(n,e.location)}_insertCharacters(e){let t,n;if(this._shouldFosterParentOnInsertion()?(({parent:t,beforeElement:n}=this._findFosterParentingLocation()),n?this.treeAdapter.insertTextBefore(t,e.chars,n):this.treeAdapter.insertText(t,e.chars)):(t=this.openElements.currentTmplContentOrNode,this.treeAdapter.insertText(t,e.chars)),!e.location)return;const s=this.treeAdapter.getChildNodes(t),r=n?s.lastIndexOf(n):s.length,i=s[r-1];if(this.treeAdapter.getNodeSourceCodeLocation(i)){const{endLine:t,endCol:n,endOffset:s}=e.location;this.treeAdapter.updateNodeSourceCodeLocation(i,{endLine:t,endCol:n,endOffset:s})}else this.options.sourceCodeLocationInfo&&this.treeAdapter.setNodeSourceCodeLocation(i,e.location)}_adoptNodes(e,t){for(let n=this.treeAdapter.getFirstChild(e);n;n=this.treeAdapter.getFirstChild(e))this.treeAdapter.detachNode(n),this.treeAdapter.appendChild(t,n)}_setEndLocation(e,t){if(this.treeAdapter.getNodeSourceCodeLocation(e)&&t.location){const n=t.location,s=this.treeAdapter.getTagName(e),r=t.type===ur.END_TAG&&s===t.tagName?{endTag:{...n},endLine:n.endLine,endCol:n.endCol,endOffset:n.endOffset}:{endLine:n.startLine,endCol:n.startCol,endOffset:n.startOffset};this.treeAdapter.updateNodeSourceCodeLocation(e,r)}}shouldProcessStartTagTokenInForeignContent(e){if(!this.currentNotInHTML)return!1;let t,n;return 0===this.openElements.stackTop&&this.fragmentContext?(t=this.fragmentContext,n=this.fragmentContextID):({current:t,currentTagId:n}=this.openElements),(e.tagID!==gr.SVG||this.treeAdapter.getTagName(t)!==_r.ANNOTATION_XML||this.treeAdapter.getNamespaceURI(t)!==dr.MATHML)&&(this.tokenizer.inForeignNode||(e.tagID===gr.MGLYPH||e.tagID===gr.MALIGNMARK)&&!this._isIntegrationPoint(n,t,dr.HTML))}_processToken(e){switch(e.type){case ur.CHARACTER:this.onCharacter(e);break;case ur.NULL_CHARACTER:this.onNullCharacter(e);break;case ur.COMMENT:this.onComment(e);break;case ur.DOCTYPE:this.onDoctype(e);break;case ur.START_TAG:this._processStartTag(e);break;case ur.END_TAG:this.onEndTag(e);break;case ur.EOF:this.onEof(e);break;case ur.WHITESPACE_CHARACTER:this.onWhitespaceCharacter(e)}}_isIntegrationPoint(e,t,n){return bi(e,this.treeAdapter.getNamespaceURI(t),this.treeAdapter.getAttrList(t),n)}_reconstructActiveFormattingElements(){const e=this.activeFormattingElements.entries.length;if(e){const t=this.activeFormattingElements.entries.findIndex((e=>e.type===Xr.Marker||this.openElements.contains(e.element)));for(let n=t<0?e-1:t-1;n>=0;n--){const e=this.activeFormattingElements.entries[n];this._insertElement(e.token,this.treeAdapter.getNamespaceURI(e.element)),e.element=this.openElements.current}}}_closeTableCell(){this.openElements.generateImpliedEndTags(),this.openElements.popUntilTableCellPopped(),this.activeFormattingElements.clearToLastMarker(),this.insertionMode=Si.IN_ROW}_closePElement(){this.openElements.generateImpliedEndTagsWithExclusion(gr.P),this.openElements.popUntilTagNamePopped(gr.P)}_resetInsertionMode(){for(let e=this.openElements.stackTop;e>=0;e--)switch(0===e&&this.fragmentContext?this.fragmentContextID:this.openElements.tagIDs[e]){case gr.TR:return void(this.insertionMode=Si.IN_ROW);case gr.TBODY:case gr.THEAD:case gr.TFOOT:return void(this.insertionMode=Si.IN_TABLE_BODY);case gr.CAPTION:return void(this.insertionMode=Si.IN_CAPTION);case gr.COLGROUP:return void(this.insertionMode=Si.IN_COLUMN_GROUP);case gr.TABLE:return void(this.insertionMode=Si.IN_TABLE);case gr.BODY:return void(this.insertionMode=Si.IN_BODY);case gr.FRAMESET:return void(this.insertionMode=Si.IN_FRAMESET);case gr.SELECT:return void this._resetInsertionModeForSelect(e);case gr.TEMPLATE:return void(this.insertionMode=this.tmplInsertionModeStack[0]);case gr.HTML:return void(this.insertionMode=this.headElement?Si.AFTER_HEAD:Si.BEFORE_HEAD);case gr.TD:case gr.TH:if(e>0)return void(this.insertionMode=Si.IN_CELL);break;case gr.HEAD:if(e>0)return void(this.insertionMode=Si.IN_HEAD)}this.insertionMode=Si.IN_BODY}_resetInsertionModeForSelect(e){if(e>0)for(let t=e-1;t>0;t--){const e=this.openElements.tagIDs[t];if(e===gr.TEMPLATE)break;if(e===gr.TABLE)return void(this.insertionMode=Si.IN_SELECT_IN_TABLE)}this.insertionMode=Si.IN_SELECT}_isElementCausesFosterParenting(e){return Oi.has(e)}_shouldFosterParentOnInsertion(){return this.fosterParentingEnabled&&this._isElementCausesFosterParenting(this.openElements.currentTagId)}_findFosterParentingLocation(){for(let e=this.openElements.stackTop;e>=0;e--){const t=this.openElements.items[e];switch(this.openElements.tagIDs[e]){case gr.TEMPLATE:if(this.treeAdapter.getNamespaceURI(t)===dr.HTML)return{parent:this.treeAdapter.getTemplateContent(t),beforeElement:null};break;case gr.TABLE:{const n=this.treeAdapter.getParentNode(t);return n?{parent:n,beforeElement:t}:{parent:this.openElements.items[e-1],beforeElement:null}}}}return{parent:this.openElements.items[0],beforeElement:null}}_fosterParentElement(e){const t=this._findFosterParentingLocation();t.beforeElement?this.treeAdapter.insertBefore(t.parent,e,t.beforeElement):this.treeAdapter.appendChild(t.parent,e)}_isSpecialElement(e,t){const n=this.treeAdapter.getNamespaceURI(e);return Sr[n].has(t)}onCharacter(e){if(this.skipNextNewLine=!1,this.tokenizer.inForeignNode)!function(e,t){e._insertCharacters(t),e.framesetOk=!1}(this,e);else switch(this.insertionMode){case Si.INITIAL:Gi(this,e);break;case Si.BEFORE_HTML:qi(this,e);break;case Si.BEFORE_HEAD:$i(this,e);break;case Si.IN_HEAD:Ki(this,e);break;case Si.IN_HEAD_NO_SCRIPT:Vi(this,e);break;case Si.AFTER_HEAD:zi(this,e);break;case Si.IN_BODY:case Si.IN_CAPTION:case Si.IN_CELL:case Si.IN_TEMPLATE:Xi(this,e);break;case Si.TEXT:case Si.IN_SELECT:case Si.IN_SELECT_IN_TABLE:this._insertCharacters(e);break;case Si.IN_TABLE:case Si.IN_TABLE_BODY:case Si.IN_ROW:oo(this,e);break;case Si.IN_TABLE_TEXT:ho(this,e);break;case Si.IN_COLUMN_GROUP:Eo(this,e);break;case Si.AFTER_BODY:ko(this,e);break;case Si.AFTER_AFTER_BODY:So(this,e)}}onNullCharacter(e){if(this.skipNextNewLine=!1,this.tokenizer.inForeignNode)!function(e,t){t.chars=Qs,e._insertCharacters(t)}(this,e);else switch(this.insertionMode){case Si.INITIAL:Gi(this,e);break;case Si.BEFORE_HTML:qi(this,e);break;case Si.BEFORE_HEAD:$i(this,e);break;case Si.IN_HEAD:Ki(this,e);break;case Si.IN_HEAD_NO_SCRIPT:Vi(this,e);break;case Si.AFTER_HEAD:zi(this,e);break;case Si.TEXT:this._insertCharacters(e);break;case Si.IN_TABLE:case Si.IN_TABLE_BODY:case Si.IN_ROW:oo(this,e);break;case Si.IN_COLUMN_GROUP:Eo(this,e);break;case Si.AFTER_BODY:ko(this,e);break;case Si.AFTER_AFTER_BODY:So(this,e)}}onComment(e){if(this.skipNextNewLine=!1,this.currentNotInHTML)Ui(this,e);else switch(this.insertionMode){case Si.INITIAL:case Si.BEFORE_HTML:case Si.BEFORE_HEAD:case Si.IN_HEAD:case Si.IN_HEAD_NO_SCRIPT:case Si.AFTER_HEAD:case Si.IN_BODY:case Si.IN_TABLE:case Si.IN_CAPTION:case Si.IN_COLUMN_GROUP:case Si.IN_TABLE_BODY:case Si.IN_ROW:case Si.IN_CELL:case Si.IN_SELECT:case Si.IN_SELECT_IN_TABLE:case Si.IN_TEMPLATE:case Si.IN_FRAMESET:case Si.AFTER_FRAMESET:Ui(this,e);break;case Si.IN_TABLE_TEXT:po(this,e);break;case Si.AFTER_BODY:!function(e,t){e._appendCommentNode(t,e.openElements.items[0])}(this,e);break;case Si.AFTER_AFTER_BODY:case Si.AFTER_AFTER_FRAMESET:!function(e,t){e._appendCommentNode(t,e.document)}(this,e)}}onDoctype(e){switch(this.skipNextNewLine=!1,this.insertionMode){case Si.INITIAL:!function(e,t){e._setDocumentType(t);const n=t.forceQuirks?Er.QUIRKS:function(e){if(e.name!==si)return Er.QUIRKS;const{systemId:t}=e;if(t&&t.toLowerCase()===ii)return Er.QUIRKS;let{publicId:n}=e;if(null!==n){if(n=n.toLowerCase(),ci.has(n))return Er.QUIRKS;let e=null===t?ai:oi;if(hi(n,e))return Er.QUIRKS;if(e=null===t?li:ui,hi(n,e))return Er.LIMITED_QUIRKS}return Er.NO_QUIRKS}(t);(function(e){return e.name===si&&null===e.publicId&&(null===e.systemId||e.systemId===ri)})(t)||e._err(t,ar.nonConformingDoctype);e.treeAdapter.setDocumentMode(e.document,n),e.insertionMode=Si.BEFORE_HTML}(this,e);break;case Si.BEFORE_HEAD:case Si.IN_HEAD:case Si.IN_HEAD_NO_SCRIPT:case Si.AFTER_HEAD:this._err(e,ar.misplacedDoctype);break;case Si.IN_TABLE_TEXT:po(this,e)}}onStartTag(e){this.skipNextNewLine=!1,this.currentToken=e,this._processStartTag(e),e.selfClosing&&!e.ackSelfClosing&&this._err(e,ar.nonVoidHtmlElementStartTagWithTrailingSolidus)}_processStartTag(e){this.shouldProcessStartTagTokenInForeignContent(e)?function(e,t){if(function(e){const t=e.tagID;return t===gr.FONT&&e.attrs.some((({name:e})=>e===fr.COLOR||e===fr.SIZE||e===fr.FACE))||_i.has(t)}(t))Do(e),e._startTagOutsideForeignContent(t);else{const n=e._getAdjustedCurrentElement(),s=e.treeAdapter.getNamespaceURI(n);s===dr.MATHML?Ai(t):s===dr.SVG&&(!function(e){const t=Ti.get(e.tagName);null!=t&&(e.tagName=t,e.tagID=Ir(e.tagName))}(t),gi(t)),Ci(t),t.selfClosing?e._appendElement(t,s):e._insertElement(t,s),t.ackSelfClosing=!0}}(this,e):this._startTagOutsideForeignContent(e)}_startTagOutsideForeignContent(e){switch(this.insertionMode){case Si.INITIAL:Gi(this,e);break;case Si.BEFORE_HTML:!function(e,t){t.tagID===gr.HTML?(e._insertElement(t,dr.HTML),e.insertionMode=Si.BEFORE_HEAD):qi(e,t)}(this,e);break;case Si.BEFORE_HEAD:!function(e,t){switch(t.tagID){case gr.HTML:no(e,t);break;case gr.HEAD:e._insertElement(t,dr.HTML),e.headElement=e.openElements.current,e.insertionMode=Si.IN_HEAD;break;default:$i(e,t)}}(this,e);break;case Si.IN_HEAD:Yi(this,e);break;case Si.IN_HEAD_NO_SCRIPT:!function(e,t){switch(t.tagID){case gr.HTML:no(e,t);break;case gr.BASEFONT:case gr.BGSOUND:case gr.HEAD:case gr.LINK:case gr.META:case gr.NOFRAMES:case gr.STYLE:Yi(e,t);break;case gr.NOSCRIPT:e._err(t,ar.nestedNoscriptInHead);break;default:Vi(e,t)}}(this,e);break;case Si.AFTER_HEAD:!function(e,t){switch(t.tagID){case gr.HTML:no(e,t);break;case gr.BODY:e._insertElement(t,dr.HTML),e.framesetOk=!1,e.insertionMode=Si.IN_BODY;break;case gr.FRAMESET:e._insertElement(t,dr.HTML),e.insertionMode=Si.IN_FRAMESET;break;case gr.BASE:case gr.BASEFONT:case gr.BGSOUND:case gr.LINK:case gr.META:case gr.NOFRAMES:case gr.SCRIPT:case gr.STYLE:case gr.TEMPLATE:case gr.TITLE:e._err(t,ar.abandonedHeadElementChild),e.openElements.push(e.headElement,gr.HEAD),Yi(e,t),e.openElements.remove(e.headElement);break;case gr.HEAD:e._err(t,ar.misplacedStartTagForHeadElement);break;default:zi(e,t)}}(this,e);break;case Si.IN_BODY:no(this,e);break;case Si.IN_TABLE:ao(this,e);break;case Si.IN_TABLE_TEXT:po(this,e);break;case Si.IN_CAPTION:!function(e,t){const n=t.tagID;fo.has(n)?e.openElements.hasInTableScope(gr.CAPTION)&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilTagNamePopped(gr.CAPTION),e.activeFormattingElements.clearToLastMarker(),e.insertionMode=Si.IN_TABLE,ao(e,t)):no(e,t)}(this,e);break;case Si.IN_COLUMN_GROUP:mo(this,e);break;case Si.IN_TABLE_BODY:To(this,e);break;case Si.IN_ROW:Ao(this,e);break;case Si.IN_CELL:!function(e,t){const n=t.tagID;fo.has(n)?(e.openElements.hasInTableScope(gr.TD)||e.openElements.hasInTableScope(gr.TH))&&(e._closeTableCell(),Ao(e,t)):no(e,t)}(this,e);break;case Si.IN_SELECT:Co(this,e);break;case Si.IN_SELECT_IN_TABLE:!function(e,t){const n=t.tagID;n===gr.CAPTION||n===gr.TABLE||n===gr.TBODY||n===gr.TFOOT||n===gr.THEAD||n===gr.TR||n===gr.TD||n===gr.TH?(e.openElements.popUntilTagNamePopped(gr.SELECT),e._resetInsertionMode(),e._processStartTag(t)):Co(e,t)}(this,e);break;case Si.IN_TEMPLATE:!function(e,t){switch(t.tagID){case gr.BASE:case gr.BASEFONT:case gr.BGSOUND:case gr.LINK:case gr.META:case gr.NOFRAMES:case gr.SCRIPT:case gr.STYLE:case gr.TEMPLATE:case gr.TITLE:Yi(e,t);break;case gr.CAPTION:case gr.COLGROUP:case gr.TBODY:case gr.TFOOT:case gr.THEAD:e.tmplInsertionModeStack[0]=Si.IN_TABLE,e.insertionMode=Si.IN_TABLE,ao(e,t);break;case gr.COL:e.tmplInsertionModeStack[0]=Si.IN_COLUMN_GROUP,e.insertionMode=Si.IN_COLUMN_GROUP,mo(e,t);break;case gr.TR:e.tmplInsertionModeStack[0]=Si.IN_TABLE_BODY,e.insertionMode=Si.IN_TABLE_BODY,To(e,t);break;case gr.TD:case gr.TH:e.tmplInsertionModeStack[0]=Si.IN_ROW,e.insertionMode=Si.IN_ROW,Ao(e,t);break;default:e.tmplInsertionModeStack[0]=Si.IN_BODY,e.insertionMode=Si.IN_BODY,no(e,t)}}(this,e);break;case Si.AFTER_BODY:!function(e,t){t.tagID===gr.HTML?no(e,t):ko(e,t)}(this,e);break;case Si.IN_FRAMESET:!function(e,t){switch(t.tagID){case gr.HTML:no(e,t);break;case gr.FRAMESET:e._insertElement(t,dr.HTML);break;case gr.FRAME:e._appendElement(t,dr.HTML),t.ackSelfClosing=!0;break;case gr.NOFRAMES:Yi(e,t)}}(this,e);break;case Si.AFTER_FRAMESET:!function(e,t){switch(t.tagID){case gr.HTML:no(e,t);break;case gr.NOFRAMES:Yi(e,t)}}(this,e);break;case Si.AFTER_AFTER_BODY:!function(e,t){t.tagID===gr.HTML?no(e,t):So(e,t)}(this,e);break;case Si.AFTER_AFTER_FRAMESET:!function(e,t){switch(t.tagID){case gr.HTML:no(e,t);break;case gr.NOFRAMES:Yi(e,t)}}(this,e)}}onEndTag(e){this.skipNextNewLine=!1,this.currentToken=e,this.currentNotInHTML?function(e,t){if(t.tagID===gr.P||t.tagID===gr.BR)return Do(e),void e._endTagOutsideForeignContent(t);for(let n=e.openElements.stackTop;n>0;n--){const s=e.openElements.items[n];if(e.treeAdapter.getNamespaceURI(s)===dr.HTML){e._endTagOutsideForeignContent(t);break}const r=e.treeAdapter.getTagName(s);if(r.toLowerCase()===t.tagName){t.tagName=r,e.openElements.shortenToLength(n);break}}}(this,e):this._endTagOutsideForeignContent(e)}_endTagOutsideForeignContent(e){switch(this.insertionMode){case Si.INITIAL:Gi(this,e);break;case Si.BEFORE_HTML:!function(e,t){const n=t.tagID;n!==gr.HTML&&n!==gr.HEAD&&n!==gr.BODY&&n!==gr.BR||qi(e,t)}(this,e);break;case Si.BEFORE_HEAD:!function(e,t){const n=t.tagID;n===gr.HEAD||n===gr.BODY||n===gr.HTML||n===gr.BR?$i(e,t):e._err(t,ar.endTagWithoutMatchingOpenElement)}(this,e);break;case Si.IN_HEAD:!function(e,t){switch(t.tagID){case gr.HEAD:e.openElements.pop(),e.insertionMode=Si.AFTER_HEAD;break;case gr.BODY:case gr.BR:case gr.HTML:Ki(e,t);break;case gr.TEMPLATE:ji(e,t);break;default:e._err(t,ar.endTagWithoutMatchingOpenElement)}}(this,e);break;case Si.IN_HEAD_NO_SCRIPT:!function(e,t){switch(t.tagID){case gr.NOSCRIPT:e.openElements.pop(),e.insertionMode=Si.IN_HEAD;break;case gr.BR:Vi(e,t);break;default:e._err(t,ar.endTagWithoutMatchingOpenElement)}}(this,e);break;case Si.AFTER_HEAD:!function(e,t){switch(t.tagID){case gr.BODY:case gr.HTML:case gr.BR:zi(e,t);break;case gr.TEMPLATE:ji(e,t);break;default:e._err(t,ar.endTagWithoutMatchingOpenElement)}}(this,e);break;case Si.IN_BODY:ro(this,e);break;case Si.TEXT:!function(e,t){var n;t.tagID===gr.SCRIPT&&(null===(n=e.scriptHandler)||void 0===n||n.call(e,e.openElements.current));e.openElements.pop(),e.insertionMode=e.originalInsertionMode}(this,e);break;case Si.IN_TABLE:co(this,e);break;case Si.IN_TABLE_TEXT:po(this,e);break;case Si.IN_CAPTION:!function(e,t){const n=t.tagID;switch(n){case gr.CAPTION:case gr.TABLE:e.openElements.hasInTableScope(gr.CAPTION)&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilTagNamePopped(gr.CAPTION),e.activeFormattingElements.clearToLastMarker(),e.insertionMode=Si.IN_TABLE,n===gr.TABLE&&co(e,t));break;case gr.BODY:case gr.COL:case gr.COLGROUP:case gr.HTML:case gr.TBODY:case gr.TD:case gr.TFOOT:case gr.TH:case gr.THEAD:case gr.TR:break;default:ro(e,t)}}(this,e);break;case Si.IN_COLUMN_GROUP:!function(e,t){switch(t.tagID){case gr.COLGROUP:e.openElements.currentTagId===gr.COLGROUP&&(e.openElements.pop(),e.insertionMode=Si.IN_TABLE);break;case gr.TEMPLATE:ji(e,t);break;case gr.COL:break;default:Eo(e,t)}}(this,e);break;case Si.IN_TABLE_BODY:_o(this,e);break;case Si.IN_ROW:go(this,e);break;case Si.IN_CELL:!function(e,t){const n=t.tagID;switch(n){case gr.TD:case gr.TH:e.openElements.hasInTableScope(n)&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilTagNamePopped(n),e.activeFormattingElements.clearToLastMarker(),e.insertionMode=Si.IN_ROW);break;case gr.TABLE:case gr.TBODY:case gr.TFOOT:case gr.THEAD:case gr.TR:e.openElements.hasInTableScope(n)&&(e._closeTableCell(),go(e,t));break;case gr.BODY:case gr.CAPTION:case gr.COL:case gr.COLGROUP:case gr.HTML:break;default:ro(e,t)}}(this,e);break;case Si.IN_SELECT:bo(this,e);break;case Si.IN_SELECT_IN_TABLE:!function(e,t){const n=t.tagID;n===gr.CAPTION||n===gr.TABLE||n===gr.TBODY||n===gr.TFOOT||n===gr.THEAD||n===gr.TR||n===gr.TD||n===gr.TH?e.openElements.hasInTableScope(n)&&(e.openElements.popUntilTagNamePopped(gr.SELECT),e._resetInsertionMode(),e.onEndTag(t)):bo(e,t)}(this,e);break;case Si.IN_TEMPLATE:!function(e,t){t.tagID===gr.TEMPLATE&&ji(e,t)}(this,e);break;case Si.AFTER_BODY:Io(this,e);break;case Si.IN_FRAMESET:!function(e,t){t.tagID!==gr.FRAMESET||e.openElements.isRootHtmlElementCurrent()||(e.openElements.pop(),e.fragmentContext||e.openElements.currentTagId===gr.FRAMESET||(e.insertionMode=Si.AFTER_FRAMESET))}(this,e);break;case Si.AFTER_FRAMESET:!function(e,t){t.tagID===gr.HTML&&(e.insertionMode=Si.AFTER_AFTER_FRAMESET)}(this,e);break;case Si.AFTER_AFTER_BODY:So(this,e)}}onEof(e){switch(this.insertionMode){case Si.INITIAL:Gi(this,e);break;case Si.BEFORE_HTML:qi(this,e);break;case Si.BEFORE_HEAD:$i(this,e);break;case Si.IN_HEAD:Ki(this,e);break;case Si.IN_HEAD_NO_SCRIPT:Vi(this,e);break;case Si.AFTER_HEAD:zi(this,e);break;case Si.IN_BODY:case Si.IN_TABLE:case Si.IN_CAPTION:case Si.IN_COLUMN_GROUP:case Si.IN_TABLE_BODY:case Si.IN_ROW:case Si.IN_CELL:case Si.IN_SELECT:case Si.IN_SELECT_IN_TABLE:io(this,e);break;case Si.TEXT:!function(e,t){e._err(t,ar.eofInElementThatCanContainOnlyText),e.openElements.pop(),e.insertionMode=e.originalInsertionMode,e.onEof(t)}(this,e);break;case Si.IN_TABLE_TEXT:po(this,e);break;case Si.IN_TEMPLATE:No(this,e);break;case Si.AFTER_BODY:case Si.IN_FRAMESET:case Si.AFTER_FRAMESET:case Si.AFTER_AFTER_BODY:case Si.AFTER_AFTER_FRAMESET:Hi(this,e)}}onWhitespaceCharacter(e){if(this.skipNextNewLine&&(this.skipNextNewLine=!1,e.chars.charCodeAt(0)===Ws.LINE_FEED)){if(1===e.chars.length)return;e.chars=e.chars.substr(1)}if(this.tokenizer.inForeignNode)this._insertCharacters(e);else switch(this.insertionMode){case Si.IN_HEAD:case Si.IN_HEAD_NO_SCRIPT:case Si.AFTER_HEAD:case Si.TEXT:case Si.IN_COLUMN_GROUP:case Si.IN_SELECT:case Si.IN_SELECT_IN_TABLE:case Si.IN_FRAMESET:case Si.AFTER_FRAMESET:this._insertCharacters(e);break;case Si.IN_BODY:case Si.IN_CAPTION:case Si.IN_CELL:case Si.IN_TEMPLATE:case Si.AFTER_BODY:case Si.AFTER_AFTER_BODY:case Si.AFTER_AFTER_FRAMESET:Wi(this,e);break;case Si.IN_TABLE:case Si.IN_TABLE_BODY:case Si.IN_ROW:oo(this,e);break;case Si.IN_TABLE_TEXT:uo(this,e)}}};function Mi(e,t){let n=e.activeFormattingElements.getElementEntryInScopeWithTagName(t.tagName);return n?e.openElements.contains(n.element)?e.openElements.hasInScope(t.tagID)||(n=null):(e.activeFormattingElements.removeEntry(n),n=null):so(e,t),n}function vi(e,t){let n=null,s=e.openElements.stackTop;for(;s>=0;s--){const r=e.openElements.items[s];if(r===t.element)break;e._isSpecialElement(r,e.openElements.tagIDs[s])&&(n=r)}return n||(e.openElements.shortenToLength(s<0?0:s),e.activeFormattingElements.removeEntry(t)),n}function xi(e,t,n){let s=t,r=e.openElements.getCommonAncestor(t);for(let i=0,o=r;o!==n;i++,o=r){r=e.openElements.getCommonAncestor(o);const n=e.activeFormattingElements.getElementEntry(o),a=n&&i>=ki;!n||a?(a&&e.activeFormattingElements.removeEntry(n),e.openElements.remove(o)):(o=wi(e,n),s===t&&(e.activeFormattingElements.bookmark=n),e.treeAdapter.detachNode(s),e.treeAdapter.appendChild(o,s),s=o)}return s}function wi(e,t){const n=e.treeAdapter.getNamespaceURI(t.element),s=e.treeAdapter.createElement(t.token.tagName,n,t.token.attrs);return e.openElements.replace(t.element,s),t.element=s,s}function Pi(e,t,n){const s=Ir(e.treeAdapter.getTagName(t));if(e._isElementCausesFosterParenting(s))e._fosterParentElement(n);else{const r=e.treeAdapter.getNamespaceURI(t);s===gr.TEMPLATE&&r===dr.HTML&&(t=e.treeAdapter.getTemplateContent(t)),e.treeAdapter.appendChild(t,n)}}function Fi(e,t,n){const s=e.treeAdapter.getNamespaceURI(n.element),{token:r}=n,i=e.treeAdapter.createElement(r.tagName,s,r.attrs);e._adoptNodes(t,i),e.treeAdapter.appendChild(t,i),e.activeFormattingElements.insertElementAfterBookmark(i,r),e.activeFormattingElements.removeEntry(n),e.openElements.remove(n.element),e.openElements.insertAfter(t,i,r.tagID)}function Bi(e,t){for(let n=0;n<Ii;n++){const n=Mi(e,t);if(!n)break;const s=vi(e,n);if(!s)break;e.activeFormattingElements.bookmark=n;const r=xi(e,s,n.element),i=e.openElements.getCommonAncestor(n.element);e.treeAdapter.detachNode(r),i&&Pi(e,i,r),Fi(e,s,n)}}function Ui(e,t){e._appendCommentNode(t,e.openElements.currentTmplContentOrNode)}function Hi(e,t){if(e.stopped=!0,t.location){const n=e.fragmentContext?0:2;for(let s=e.openElements.stackTop;s>=n;s--)e._setEndLocation(e.openElements.items[s],t);if(!e.fragmentContext&&e.openElements.stackTop>=0){const n=e.openElements.items[0],s=e.treeAdapter.getNodeSourceCodeLocation(n);if(s&&!s.endTag&&(e._setEndLocation(n,t),e.openElements.stackTop>=1)){const n=e.openElements.items[1],s=e.treeAdapter.getNodeSourceCodeLocation(n);s&&!s.endTag&&e._setEndLocation(n,t)}}}}function Gi(e,t){e._err(t,ar.missingDoctype,!0),e.treeAdapter.setDocumentMode(e.document,Er.QUIRKS),e.insertionMode=Si.BEFORE_HTML,e._processToken(t)}function qi(e,t){e._insertFakeRootElement(),e.insertionMode=Si.BEFORE_HEAD,e._processToken(t)}function $i(e,t){e._insertFakeElement(_r.HEAD,gr.HEAD),e.headElement=e.openElements.current,e.insertionMode=Si.IN_HEAD,e._processToken(t)}function Yi(e,t){switch(t.tagID){case gr.HTML:no(e,t);break;case gr.BASE:case gr.BASEFONT:case gr.BGSOUND:case gr.LINK:case gr.META:e._appendElement(t,dr.HTML),t.ackSelfClosing=!0;break;case gr.TITLE:e._switchToTextParsing(t,Mr.RCDATA);break;case gr.NOSCRIPT:e.options.scriptingEnabled?e._switchToTextParsing(t,Mr.RAWTEXT):(e._insertElement(t,dr.HTML),e.insertionMode=Si.IN_HEAD_NO_SCRIPT);break;case gr.NOFRAMES:case gr.STYLE:e._switchToTextParsing(t,Mr.RAWTEXT);break;case gr.SCRIPT:e._switchToTextParsing(t,Mr.SCRIPT_DATA);break;case gr.TEMPLATE:e._insertTemplate(t),e.activeFormattingElements.insertMarker(),e.framesetOk=!1,e.insertionMode=Si.IN_TEMPLATE,e.tmplInsertionModeStack.unshift(Si.IN_TEMPLATE);break;case gr.HEAD:e._err(t,ar.misplacedStartTagForHeadElement);break;default:Ki(e,t)}}function ji(e,t){e.openElements.tmplCount>0?(e.openElements.generateImpliedEndTagsThoroughly(),e.openElements.currentTagId!==gr.TEMPLATE&&e._err(t,ar.closingOfElementWithOpenChildElements),e.openElements.popUntilTagNamePopped(gr.TEMPLATE),e.activeFormattingElements.clearToLastMarker(),e.tmplInsertionModeStack.shift(),e._resetInsertionMode()):e._err(t,ar.endTagWithoutMatchingOpenElement)}function Ki(e,t){e.openElements.pop(),e.insertionMode=Si.AFTER_HEAD,e._processToken(t)}function Vi(e,t){const n=t.type===ur.EOF?ar.openElementsLeftAfterEof:ar.disallowedContentInNoscriptInHead;e._err(t,n),e.openElements.pop(),e.insertionMode=Si.IN_HEAD,e._processToken(t)}function zi(e,t){e._insertFakeElement(_r.BODY,gr.BODY),e.insertionMode=Si.IN_BODY,Qi(e,t)}function Qi(e,t){switch(t.type){case ur.CHARACTER:Xi(e,t);break;case ur.WHITESPACE_CHARACTER:Wi(e,t);break;case ur.COMMENT:Ui(e,t);break;case ur.START_TAG:no(e,t);break;case ur.END_TAG:ro(e,t);break;case ur.EOF:io(e,t)}}function Wi(e,t){e._reconstructActiveFormattingElements(),e._insertCharacters(t)}function Xi(e,t){e._reconstructActiveFormattingElements(),e._insertCharacters(t),e.framesetOk=!1}function Zi(e,t){e._reconstructActiveFormattingElements(),e._appendElement(t,dr.HTML),e.framesetOk=!1,t.ackSelfClosing=!0}function Ji(e){const t=br(e,fr.TYPE);return null!=t&&t.toLowerCase()===Ni}function eo(e,t){e._switchToTextParsing(t,Mr.RAWTEXT)}function to(e,t){e._reconstructActiveFormattingElements(),e._insertElement(t,dr.HTML)}function no(e,t){switch(t.tagID){case gr.I:case gr.S:case gr.B:case gr.U:case gr.EM:case gr.TT:case gr.BIG:case gr.CODE:case gr.FONT:case gr.SMALL:case gr.STRIKE:case gr.STRONG:!function(e,t){e._reconstructActiveFormattingElements(),e._insertElement(t,dr.HTML),e.activeFormattingElements.pushElement(e.openElements.current,t)}(e,t);break;case gr.A:!function(e,t){const n=e.activeFormattingElements.getElementEntryInScopeWithTagName(_r.A);n&&(Bi(e,t),e.openElements.remove(n.element),e.activeFormattingElements.removeEntry(n)),e._reconstructActiveFormattingElements(),e._insertElement(t,dr.HTML),e.activeFormattingElements.pushElement(e.openElements.current,t)}(e,t);break;case gr.H1:case gr.H2:case gr.H3:case gr.H4:case gr.H5:case gr.H6:!function(e,t){e.openElements.hasInButtonScope(gr.P)&&e._closePElement(),Dr(e.openElements.currentTagId)&&e.openElements.pop(),e._insertElement(t,dr.HTML)}(e,t);break;case gr.P:case gr.DL:case gr.OL:case gr.UL:case gr.DIV:case gr.DIR:case gr.NAV:case gr.MAIN:case gr.MENU:case gr.ASIDE:case gr.CENTER:case gr.FIGURE:case gr.FOOTER:case gr.HEADER:case gr.HGROUP:case gr.DIALOG:case gr.DETAILS:case gr.ADDRESS:case gr.ARTICLE:case gr.SECTION:case gr.SUMMARY:case gr.FIELDSET:case gr.BLOCKQUOTE:case gr.FIGCAPTION:!function(e,t){e.openElements.hasInButtonScope(gr.P)&&e._closePElement(),e._insertElement(t,dr.HTML)}(e,t);break;case gr.LI:case gr.DD:case gr.DT:!function(e,t){e.framesetOk=!1;const n=t.tagID;for(let t=e.openElements.stackTop;t>=0;t--){const s=e.openElements.tagIDs[t];if(n===gr.LI&&s===gr.LI||(n===gr.DD||n===gr.DT)&&(s===gr.DD||s===gr.DT)){e.openElements.generateImpliedEndTagsWithExclusion(s),e.openElements.popUntilTagNamePopped(s);break}if(s!==gr.ADDRESS&&s!==gr.DIV&&s!==gr.P&&e._isSpecialElement(e.openElements.items[t],s))break}e.openElements.hasInButtonScope(gr.P)&&e._closePElement(),e._insertElement(t,dr.HTML)}(e,t);break;case gr.BR:case gr.IMG:case gr.WBR:case gr.AREA:case gr.EMBED:case gr.KEYGEN:Zi(e,t);break;case gr.HR:!function(e,t){e.openElements.hasInButtonScope(gr.P)&&e._closePElement(),e._appendElement(t,dr.HTML),e.framesetOk=!1,t.ackSelfClosing=!0}(e,t);break;case gr.RB:case gr.RTC:!function(e,t){e.openElements.hasInScope(gr.RUBY)&&e.openElements.generateImpliedEndTags(),e._insertElement(t,dr.HTML)}(e,t);break;case gr.RT:case gr.RP:!function(e,t){e.openElements.hasInScope(gr.RUBY)&&e.openElements.generateImpliedEndTagsWithExclusion(gr.RTC),e._insertElement(t,dr.HTML)}(e,t);break;case gr.PRE:case gr.LISTING:!function(e,t){e.openElements.hasInButtonScope(gr.P)&&e._closePElement(),e._insertElement(t,dr.HTML),e.skipNextNewLine=!0,e.framesetOk=!1}(e,t);break;case gr.XMP:!function(e,t){e.openElements.hasInButtonScope(gr.P)&&e._closePElement(),e._reconstructActiveFormattingElements(),e.framesetOk=!1,e._switchToTextParsing(t,Mr.RAWTEXT)}(e,t);break;case gr.SVG:!function(e,t){e._reconstructActiveFormattingElements(),gi(t),Ci(t),t.selfClosing?e._appendElement(t,dr.SVG):e._insertElement(t,dr.SVG),t.ackSelfClosing=!0}(e,t);break;case gr.HTML:!function(e,t){0===e.openElements.tmplCount&&e.treeAdapter.adoptAttributes(e.openElements.items[0],t.attrs)}(e,t);break;case gr.BASE:case gr.LINK:case gr.META:case gr.STYLE:case gr.TITLE:case gr.SCRIPT:case gr.BGSOUND:case gr.BASEFONT:case gr.TEMPLATE:Yi(e,t);break;case gr.BODY:!function(e,t){const n=e.openElements.tryPeekProperlyNestedBodyElement();n&&0===e.openElements.tmplCount&&(e.framesetOk=!1,e.treeAdapter.adoptAttributes(n,t.attrs))}(e,t);break;case gr.FORM:!function(e,t){const n=e.openElements.tmplCount>0;e.formElement&&!n||(e.openElements.hasInButtonScope(gr.P)&&e._closePElement(),e._insertElement(t,dr.HTML),n||(e.formElement=e.openElements.current))}(e,t);break;case gr.NOBR:!function(e,t){e._reconstructActiveFormattingElements(),e.openElements.hasInScope(gr.NOBR)&&(Bi(e,t),e._reconstructActiveFormattingElements()),e._insertElement(t,dr.HTML),e.activeFormattingElements.pushElement(e.openElements.current,t)}(e,t);break;case gr.MATH:!function(e,t){e._reconstructActiveFormattingElements(),Ai(t),Ci(t),t.selfClosing?e._appendElement(t,dr.MATHML):e._insertElement(t,dr.MATHML),t.ackSelfClosing=!0}(e,t);break;case gr.TABLE:!function(e,t){e.treeAdapter.getDocumentMode(e.document)!==Er.QUIRKS&&e.openElements.hasInButtonScope(gr.P)&&e._closePElement(),e._insertElement(t,dr.HTML),e.framesetOk=!1,e.insertionMode=Si.IN_TABLE}(e,t);break;case gr.INPUT:!function(e,t){e._reconstructActiveFormattingElements(),e._appendElement(t,dr.HTML),Ji(t)||(e.framesetOk=!1),t.ackSelfClosing=!0}(e,t);break;case gr.PARAM:case gr.TRACK:case gr.SOURCE:!function(e,t){e._appendElement(t,dr.HTML),t.ackSelfClosing=!0}(e,t);break;case gr.IMAGE:!function(e,t){t.tagName=_r.IMG,t.tagID=gr.IMG,Zi(e,t)}(e,t);break;case gr.BUTTON:!function(e,t){e.openElements.hasInScope(gr.BUTTON)&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilTagNamePopped(gr.BUTTON)),e._reconstructActiveFormattingElements(),e._insertElement(t,dr.HTML),e.framesetOk=!1}(e,t);break;case gr.APPLET:case gr.OBJECT:case gr.MARQUEE:!function(e,t){e._reconstructActiveFormattingElements(),e._insertElement(t,dr.HTML),e.activeFormattingElements.insertMarker(),e.framesetOk=!1}(e,t);break;case gr.IFRAME:!function(e,t){e.framesetOk=!1,e._switchToTextParsing(t,Mr.RAWTEXT)}(e,t);break;case gr.SELECT:!function(e,t){e._reconstructActiveFormattingElements(),e._insertElement(t,dr.HTML),e.framesetOk=!1,e.insertionMode=e.insertionMode===Si.IN_TABLE||e.insertionMode===Si.IN_CAPTION||e.insertionMode===Si.IN_TABLE_BODY||e.insertionMode===Si.IN_ROW||e.insertionMode===Si.IN_CELL?Si.IN_SELECT_IN_TABLE:Si.IN_SELECT}(e,t);break;case gr.OPTION:case gr.OPTGROUP:!function(e,t){e.openElements.currentTagId===gr.OPTION&&e.openElements.pop(),e._reconstructActiveFormattingElements(),e._insertElement(t,dr.HTML)}(e,t);break;case gr.NOEMBED:eo(e,t);break;case gr.FRAMESET:!function(e,t){const n=e.openElements.tryPeekProperlyNestedBodyElement();e.framesetOk&&n&&(e.treeAdapter.detachNode(n),e.openElements.popAllUpToHtmlElement(),e._insertElement(t,dr.HTML),e.insertionMode=Si.IN_FRAMESET)}(e,t);break;case gr.TEXTAREA:!function(e,t){e._insertElement(t,dr.HTML),e.skipNextNewLine=!0,e.tokenizer.state=Mr.RCDATA,e.originalInsertionMode=e.insertionMode,e.framesetOk=!1,e.insertionMode=Si.TEXT}(e,t);break;case gr.NOSCRIPT:e.options.scriptingEnabled?eo(e,t):to(e,t);break;case gr.PLAINTEXT:!function(e,t){e.openElements.hasInButtonScope(gr.P)&&e._closePElement(),e._insertElement(t,dr.HTML),e.tokenizer.state=Mr.PLAINTEXT}(e,t);break;case gr.COL:case gr.TH:case gr.TD:case gr.TR:case gr.HEAD:case gr.FRAME:case gr.TBODY:case gr.TFOOT:case gr.THEAD:case gr.CAPTION:case gr.COLGROUP:break;default:to(e,t)}}function so(e,t){const n=t.tagName,s=t.tagID;for(let t=e.openElements.stackTop;t>0;t--){const r=e.openElements.items[t],i=e.openElements.tagIDs[t];if(s===i&&(s!==gr.UNKNOWN||e.treeAdapter.getTagName(r)===n)){e.openElements.generateImpliedEndTagsWithExclusion(s),e.openElements.stackTop>=t&&e.openElements.shortenToLength(t);break}if(e._isSpecialElement(r,i))break}}function ro(e,t){switch(t.tagID){case gr.A:case gr.B:case gr.I:case gr.S:case gr.U:case gr.EM:case gr.TT:case gr.BIG:case gr.CODE:case gr.FONT:case gr.NOBR:case gr.SMALL:case gr.STRIKE:case gr.STRONG:Bi(e,t);break;case gr.P:!function(e){e.openElements.hasInButtonScope(gr.P)||e._insertFakeElement(_r.P,gr.P),e._closePElement()}(e);break;case gr.DL:case gr.UL:case gr.OL:case gr.DIR:case gr.DIV:case gr.NAV:case gr.PRE:case gr.MAIN:case gr.MENU:case gr.ASIDE:case gr.BUTTON:case gr.CENTER:case gr.FIGURE:case gr.FOOTER:case gr.HEADER:case gr.HGROUP:case gr.DIALOG:case gr.ADDRESS:case gr.ARTICLE:case gr.DETAILS:case gr.SECTION:case gr.SUMMARY:case gr.LISTING:case gr.FIELDSET:case gr.BLOCKQUOTE:case gr.FIGCAPTION:!function(e,t){const n=t.tagID;e.openElements.hasInScope(n)&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilTagNamePopped(n))}(e,t);break;case gr.LI:!function(e){e.openElements.hasInListItemScope(gr.LI)&&(e.openElements.generateImpliedEndTagsWithExclusion(gr.LI),e.openElements.popUntilTagNamePopped(gr.LI))}(e);break;case gr.DD:case gr.DT:!function(e,t){const n=t.tagID;e.openElements.hasInScope(n)&&(e.openElements.generateImpliedEndTagsWithExclusion(n),e.openElements.popUntilTagNamePopped(n))}(e,t);break;case gr.H1:case gr.H2:case gr.H3:case gr.H4:case gr.H5:case gr.H6:!function(e){e.openElements.hasNumberedHeaderInScope()&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilNumberedHeaderPopped())}(e);break;case gr.BR:!function(e){e._reconstructActiveFormattingElements(),e._insertFakeElement(_r.BR,gr.BR),e.openElements.pop(),e.framesetOk=!1}(e);break;case gr.BODY:!function(e,t){if(e.openElements.hasInScope(gr.BODY)&&(e.insertionMode=Si.AFTER_BODY,e.options.sourceCodeLocationInfo)){const n=e.openElements.tryPeekProperlyNestedBodyElement();n&&e._setEndLocation(n,t)}}(e,t);break;case gr.HTML:!function(e,t){e.openElements.hasInScope(gr.BODY)&&(e.insertionMode=Si.AFTER_BODY,Io(e,t))}(e,t);break;case gr.FORM:!function(e){const t=e.openElements.tmplCount>0,{formElement:n}=e;t||(e.formElement=null),(n||t)&&e.openElements.hasInScope(gr.FORM)&&(e.openElements.generateImpliedEndTags(),t?e.openElements.popUntilTagNamePopped(gr.FORM):n&&e.openElements.remove(n))}(e);break;case gr.APPLET:case gr.OBJECT:case gr.MARQUEE:!function(e,t){const n=t.tagID;e.openElements.hasInScope(n)&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilTagNamePopped(n),e.activeFormattingElements.clearToLastMarker())}(e,t);break;case gr.TEMPLATE:ji(e,t);break;default:so(e,t)}}function io(e,t){e.tmplInsertionModeStack.length>0?No(e,t):Hi(e,t)}function oo(e,t){if(Oi.has(e.openElements.currentTagId))switch(e.pendingCharacterTokens.length=0,e.hasNonWhitespacePendingCharacterToken=!1,e.originalInsertionMode=e.insertionMode,e.insertionMode=Si.IN_TABLE_TEXT,t.type){case ur.CHARACTER:ho(e,t);break;case ur.WHITESPACE_CHARACTER:uo(e,t)}else lo(e,t)}function ao(e,t){switch(t.tagID){case gr.TD:case gr.TH:case gr.TR:!function(e,t){e.openElements.clearBackToTableContext(),e._insertFakeElement(_r.TBODY,gr.TBODY),e.insertionMode=Si.IN_TABLE_BODY,To(e,t)}(e,t);break;case gr.STYLE:case gr.SCRIPT:case gr.TEMPLATE:Yi(e,t);break;case gr.COL:!function(e,t){e.openElements.clearBackToTableContext(),e._insertFakeElement(_r.COLGROUP,gr.COLGROUP),e.insertionMode=Si.IN_COLUMN_GROUP,mo(e,t)}(e,t);break;case gr.FORM:!function(e,t){e.formElement||0!==e.openElements.tmplCount||(e._insertElement(t,dr.HTML),e.formElement=e.openElements.current,e.openElements.pop())}(e,t);break;case gr.TABLE:!function(e,t){e.openElements.hasInTableScope(gr.TABLE)&&(e.openElements.popUntilTagNamePopped(gr.TABLE),e._resetInsertionMode(),e._processStartTag(t))}(e,t);break;case gr.TBODY:case gr.TFOOT:case gr.THEAD:!function(e,t){e.openElements.clearBackToTableContext(),e._insertElement(t,dr.HTML),e.insertionMode=Si.IN_TABLE_BODY}(e,t);break;case gr.INPUT:!function(e,t){Ji(t)?e._appendElement(t,dr.HTML):lo(e,t),t.ackSelfClosing=!0}(e,t);break;case gr.CAPTION:!function(e,t){e.openElements.clearBackToTableContext(),e.activeFormattingElements.insertMarker(),e._insertElement(t,dr.HTML),e.insertionMode=Si.IN_CAPTION}(e,t);break;case gr.COLGROUP:!function(e,t){e.openElements.clearBackToTableContext(),e._insertElement(t,dr.HTML),e.insertionMode=Si.IN_COLUMN_GROUP}(e,t);break;default:lo(e,t)}}function co(e,t){switch(t.tagID){case gr.TABLE:e.openElements.hasInTableScope(gr.TABLE)&&(e.openElements.popUntilTagNamePopped(gr.TABLE),e._resetInsertionMode());break;case gr.TEMPLATE:ji(e,t);break;case gr.BODY:case gr.CAPTION:case gr.COL:case gr.COLGROUP:case gr.HTML:case gr.TBODY:case gr.TD:case gr.TFOOT:case gr.TH:case gr.THEAD:case gr.TR:break;default:lo(e,t)}}function lo(e,t){const n=e.fosterParentingEnabled;e.fosterParentingEnabled=!0,Qi(e,t),e.fosterParentingEnabled=n}function uo(e,t){e.pendingCharacterTokens.push(t)}function ho(e,t){e.pendingCharacterTokens.push(t),e.hasNonWhitespacePendingCharacterToken=!0}function po(e,t){let n=0;if(e.hasNonWhitespacePendingCharacterToken)for(;n<e.pendingCharacterTokens.length;n++)lo(e,e.pendingCharacterTokens[n]);else for(;n<e.pendingCharacterTokens.length;n++)e._insertCharacters(e.pendingCharacterTokens[n]);e.insertionMode=e.originalInsertionMode,e._processToken(t)}const fo=new Set([gr.CAPTION,gr.COL,gr.COLGROUP,gr.TBODY,gr.TD,gr.TFOOT,gr.TH,gr.THEAD,gr.TR]);function mo(e,t){switch(t.tagID){case gr.HTML:no(e,t);break;case gr.COL:e._appendElement(t,dr.HTML),t.ackSelfClosing=!0;break;case gr.TEMPLATE:Yi(e,t);break;default:Eo(e,t)}}function Eo(e,t){e.openElements.currentTagId===gr.COLGROUP&&(e.openElements.pop(),e.insertionMode=Si.IN_TABLE,e._processToken(t))}function To(e,t){switch(t.tagID){case gr.TR:e.openElements.clearBackToTableBodyContext(),e._insertElement(t,dr.HTML),e.insertionMode=Si.IN_ROW;break;case gr.TH:case gr.TD:e.openElements.clearBackToTableBodyContext(),e._insertFakeElement(_r.TR,gr.TR),e.insertionMode=Si.IN_ROW,Ao(e,t);break;case gr.CAPTION:case gr.COL:case gr.COLGROUP:case gr.TBODY:case gr.TFOOT:case gr.THEAD:e.openElements.hasTableBodyContextInTableScope()&&(e.openElements.clearBackToTableBodyContext(),e.openElements.pop(),e.insertionMode=Si.IN_TABLE,ao(e,t));break;default:ao(e,t)}}function _o(e,t){const n=t.tagID;switch(t.tagID){case gr.TBODY:case gr.TFOOT:case gr.THEAD:e.openElements.hasInTableScope(n)&&(e.openElements.clearBackToTableBodyContext(),e.openElements.pop(),e.insertionMode=Si.IN_TABLE);break;case gr.TABLE:e.openElements.hasTableBodyContextInTableScope()&&(e.openElements.clearBackToTableBodyContext(),e.openElements.pop(),e.insertionMode=Si.IN_TABLE,co(e,t));break;case gr.BODY:case gr.CAPTION:case gr.COL:case gr.COLGROUP:case gr.HTML:case gr.TD:case gr.TH:case gr.TR:break;default:co(e,t)}}function Ao(e,t){switch(t.tagID){case gr.TH:case gr.TD:e.openElements.clearBackToTableRowContext(),e._insertElement(t,dr.HTML),e.insertionMode=Si.IN_CELL,e.activeFormattingElements.insertMarker();break;case gr.CAPTION:case gr.COL:case gr.COLGROUP:case gr.TBODY:case gr.TFOOT:case gr.THEAD:case gr.TR:e.openElements.hasInTableScope(gr.TR)&&(e.openElements.clearBackToTableRowContext(),e.openElements.pop(),e.insertionMode=Si.IN_TABLE_BODY,To(e,t));break;default:ao(e,t)}}function go(e,t){switch(t.tagID){case gr.TR:e.openElements.hasInTableScope(gr.TR)&&(e.openElements.clearBackToTableRowContext(),e.openElements.pop(),e.insertionMode=Si.IN_TABLE_BODY);break;case gr.TABLE:e.openElements.hasInTableScope(gr.TR)&&(e.openElements.clearBackToTableRowContext(),e.openElements.pop(),e.insertionMode=Si.IN_TABLE_BODY,_o(e,t));break;case gr.TBODY:case gr.TFOOT:case gr.THEAD:(e.openElements.hasInTableScope(t.tagID)||e.openElements.hasInTableScope(gr.TR))&&(e.openElements.clearBackToTableRowContext(),e.openElements.pop(),e.insertionMode=Si.IN_TABLE_BODY,_o(e,t));break;case gr.BODY:case gr.CAPTION:case gr.COL:case gr.COLGROUP:case gr.HTML:case gr.TD:case gr.TH:break;default:co(e,t)}}function Co(e,t){switch(t.tagID){case gr.HTML:no(e,t);break;case gr.OPTION:e.openElements.currentTagId===gr.OPTION&&e.openElements.pop(),e._insertElement(t,dr.HTML);break;case gr.OPTGROUP:e.openElements.currentTagId===gr.OPTION&&e.openElements.pop(),e.openElements.currentTagId===gr.OPTGROUP&&e.openElements.pop(),e._insertElement(t,dr.HTML);break;case gr.INPUT:case gr.KEYGEN:case gr.TEXTAREA:case gr.SELECT:e.openElements.hasInSelectScope(gr.SELECT)&&(e.openElements.popUntilTagNamePopped(gr.SELECT),e._resetInsertionMode(),t.tagID!==gr.SELECT&&e._processStartTag(t));break;case gr.SCRIPT:case gr.TEMPLATE:Yi(e,t)}}function bo(e,t){switch(t.tagID){case gr.OPTGROUP:e.openElements.stackTop>0&&e.openElements.currentTagId===gr.OPTION&&e.openElements.tagIDs[e.openElements.stackTop-1]===gr.OPTGROUP&&e.openElements.pop(),e.openElements.currentTagId===gr.OPTGROUP&&e.openElements.pop();break;case gr.OPTION:e.openElements.currentTagId===gr.OPTION&&e.openElements.pop();break;case gr.SELECT:e.openElements.hasInSelectScope(gr.SELECT)&&(e.openElements.popUntilTagNamePopped(gr.SELECT),e._resetInsertionMode());break;case gr.TEMPLATE:ji(e,t)}}function No(e,t){e.openElements.tmplCount>0?(e.openElements.popUntilTagNamePopped(gr.TEMPLATE),e.activeFormattingElements.clearToLastMarker(),e.tmplInsertionModeStack.shift(),e._resetInsertionMode(),e.onEof(t)):Hi(e,t)}function Io(e,t){var n;if(t.tagID===gr.HTML){if(e.fragmentContext||(e.insertionMode=Si.AFTER_AFTER_BODY),e.options.sourceCodeLocationInfo&&e.openElements.tagIDs[0]===gr.HTML){e._setEndLocation(e.openElements.items[0],t);const s=e.openElements.items[1];s&&!(null===(n=e.treeAdapter.getNodeSourceCodeLocation(s))||void 0===n?void 0:n.endTag)&&e._setEndLocation(s,t)}}else ko(e,t)}function ko(e,t){e.insertionMode=Si.IN_BODY,Qi(e,t)}function So(e,t){e.insertionMode=Si.IN_BODY,Qi(e,t)}function Do(e){for(;e.treeAdapter.getNamespaceURI(e.openElements.current)!==dr.HTML&&!e._isIntegrationPoint(e.openElements.currentTagId,e.openElements.current);)e.openElements.pop()}const yo=new Set([_r.AREA,_r.BASE,_r.BASEFONT,_r.BGSOUND,_r.BR,_r.COL,_r.EMBED,_r.FRAME,_r.HR,_r.IMG,_r.INPUT,_r.KEYGEN,_r.LINK,_r.META,_r.PARAM,_r.SOURCE,_r.TRACK,_r.WBR]);const Oo={treeAdapter:ni,scriptingEnabled:!0};function Lo(e,t){return Ro(e,{...Oo,...t})}function Ro(e,t){return t.treeAdapter.isElementNode(e)?function(e,t){const n=t.treeAdapter.getTagName(e);return`<${n}${function(e,{treeAdapter:t}){let n="";for(const s of t.getAttrList(e)){if(n+=" ",s.namespace)switch(s.namespace){case dr.XML:n+=`xml:${s.name}`;break;case dr.XMLNS:"xmlns"!==s.name&&(n+="xmlns:"),n+=s.name;break;case dr.XLINK:n+=`xlink:${s.name}`;break;default:n+=`${s.prefix}:${s.name}`}else n+=s.name;n+=`="${we(s.value)}"`}return n}(e,t)}>${function(e,t){return t.treeAdapter.isElementNode(e)&&t.treeAdapter.getNamespaceURI(e)===dr.HTML&&yo.has(t.treeAdapter.getTagName(e))}(e,t)?"":`${function(e,t){let n="";const s=t.treeAdapter.isElementNode(e)&&t.treeAdapter.getTagName(e)===_r.TEMPLATE&&t.treeAdapter.getNamespaceURI(e)===dr.HTML?t.treeAdapter.getTemplateContent(e):e,r=t.treeAdapter.getChildNodes(s);if(r)for(const e of r)n+=Ro(e,t);return n}(e,t)}</${n}>`}`}(e,t):t.treeAdapter.isTextNode(e)?function(e,t){const{treeAdapter:n}=t,s=n.getTextNodeContent(e),r=n.getParentNode(e),i=r&&n.isElementNode(r)&&n.getTagName(r);return i&&n.getNamespaceURI(r)===dr.HTML&&(o=i,a=t.scriptingEnabled,yr.has(o)||a&&o===_r.NOSCRIPT)?s:Pe(s);var o,a}(e,t):t.treeAdapter.isCommentNode(e)?function(e,{treeAdapter:t}){return`\x3c!--${t.getCommentNodeContent(e)}--\x3e`}(e,t):t.treeAdapter.isDocumentTypeNode(e)?function(e,{treeAdapter:t}){return`<!DOCTYPE ${t.getDocumentTypeNodeName(e)}>`}(e,t):""}function Mo(e){return new j(e)}function vo(e){const t=e.includes('"')?"'":'"';return t+e+t}const xo={isCommentNode:te,isElementNode:Z,isTextNode:ee,createDocument(){const e=new W([]);return e["x-mode"]=Er.NO_QUIRKS,e},createDocumentFragment:()=>new W([]),createElement(e,t,n){const s=Object.create(null),r=Object.create(null),i=Object.create(null);for(let e=0;e<n.length;e++){const t=n[e].name;s[t]=n[e].value,r[t]=n[e].namespace,i[t]=n[e].prefix}const o=new X(e,s,[]);return o.namespace=t,o["x-attribsNamespace"]=r,o["x-attribsPrefix"]=i,o},createCommentNode:e=>new K(e),appendChild(e,t){const n=e.children[e.children.length-1];n&&(n.next=t,t.prev=n),e.children.push(t),t.parent=e},insertBefore(e,t,n){const s=e.children.indexOf(n),{prev:r}=n;r&&(r.next=t,t.prev=r),n.prev=t,t.next=n,e.children.splice(s,0,t),t.parent=e},setTemplateContent(e,t){xo.appendChild(e,t)},getTemplateContent:e=>e.children[0],setDocumentType(e,t,n,s){const r=function(e,t,n){let s="!DOCTYPE ";return e&&(s+=e),t?s+=` PUBLIC ${vo(t)}`:n&&(s+=" SYSTEM"),n&&(s+=` ${vo(n)}`),s}(t,n,s);let i=e.children.find((e=>ne(e)&&"!doctype"===e.name));i?i.data=null!=r?r:null:(i=new V("!doctype",r),xo.appendChild(e,i)),i["x-name"]=null!=t?t:void 0,i["x-publicId"]=null!=n?n:void 0,i["x-systemId"]=null!=s?s:void 0},setDocumentMode(e,t){e["x-mode"]=t},getDocumentMode:e=>e["x-mode"],detachNode(e){if(e.parent){const t=e.parent.children.indexOf(e),{prev:n,next:s}=e;e.prev=null,e.next=null,n&&(n.next=s),s&&(s.prev=n),e.parent.children.splice(t,1),e.parent=null}},insertText(e,t){const n=e.children[e.children.length-1];n&&ee(n)?n.data+=t:xo.appendChild(e,Mo(t))},insertTextBefore(e,t,n){const s=e.children[e.children.indexOf(n)-1];s&&ee(s)?s.data+=t:xo.insertBefore(e,Mo(t),n)},adoptAttributes(e,t){for(let n=0;n<t.length;n++){const s=t[n].name;void 0===e.attribs[s]&&(e.attribs[s]=t[n].value,e["x-attribsNamespace"][s]=t[n].namespace,e["x-attribsPrefix"][s]=t[n].prefix)}},getFirstChild:e=>e.children[0],getChildNodes:e=>e.children,getParentNode:e=>e.parent,getAttrList:e=>e.attributes,getTagName:e=>e.name,getNamespaceURI:e=>e.namespace,getTextNodeContent:e=>e.data,getCommentNodeContent:e=>e.data,getDocumentTypeNodeName(e){var t;return null!==(t=e["x-name"])&&void 0!==t?t:""},getDocumentTypeNodePublicId(e){var t;return null!==(t=e["x-publicId"])&&void 0!==t?t:""},getDocumentTypeNodeSystemId(e){var t;return null!==(t=e["x-systemId"])&&void 0!==t?t:""},isDocumentTypeNode:e=>ne(e)&&"!doctype"===e.name,setNodeSourceCodeLocation(e,t){t&&(e.startIndex=t.startOffset,e.endIndex=t.endOffset),e.sourceCodeLocation=t},getNodeSourceCodeLocation:e=>e.sourceCodeLocation,updateNodeSourceCodeLocation(e,t){null!=t.endOffset&&(e.endIndex=t.endOffset),e.sourceCodeLocation={...e.sourceCodeLocation,...t}}};function wo(e,t,n,s){const r={scriptingEnabled:"boolean"!=typeof t.scriptingEnabled||t.scriptingEnabled,treeAdapter:xo,sourceCodeLocationInfo:t.sourceCodeLocationInfo};return n?function(e,t){return Ri.parse(e,t)}(e,r):function(e,t,n){"string"==typeof e&&(n=t,t=e,e=null);const s=Ri.getFragmentParser(e,n);return s.tokenizer.write(t,!0),s.getFragment()}(s,e,r)}const Po={treeAdapter:xo};var Fo,Bo,Uo,Ho;function Go(e){return e===Fo.Space||e===Fo.NewLine||e===Fo.Tab||e===Fo.FormFeed||e===Fo.CarriageReturn}function qo(e){return e===Fo.Slash||e===Fo.Gt||Go(e)}function $o(e){return e>=Fo.Zero&&e<=Fo.Nine}!function(e){e[e.Tab=9]="Tab",e[e.NewLine=10]="NewLine",e[e.FormFeed=12]="FormFeed",e[e.CarriageReturn=13]="CarriageReturn",e[e.Space=32]="Space",e[e.ExclamationMark=33]="ExclamationMark",e[e.Number=35]="Number",e[e.Amp=38]="Amp",e[e.SingleQuote=39]="SingleQuote",e[e.DoubleQuote=34]="DoubleQuote",e[e.Dash=45]="Dash",e[e.Slash=47]="Slash",e[e.Zero=48]="Zero",e[e.Nine=57]="Nine",e[e.Semi=59]="Semi",e[e.Lt=60]="Lt",e[e.Eq=61]="Eq",e[e.Gt=62]="Gt",e[e.Questionmark=63]="Questionmark",e[e.UpperA=65]="UpperA",e[e.LowerA=97]="LowerA",e[e.UpperF=70]="UpperF",e[e.LowerF=102]="LowerF",e[e.UpperZ=90]="UpperZ",e[e.LowerZ=122]="LowerZ",e[e.LowerX=120]="LowerX",e[e.OpeningSquareBracket=91]="OpeningSquareBracket"}(Fo||(Fo={})),function(e){e[e.Text=1]="Text",e[e.BeforeTagName=2]="BeforeTagName",e[e.InTagName=3]="InTagName",e[e.InSelfClosingTag=4]="InSelfClosingTag",e[e.BeforeClosingTagName=5]="BeforeClosingTagName",e[e.InClosingTagName=6]="InClosingTagName",e[e.AfterClosingTagName=7]="AfterClosingTagName",e[e.BeforeAttributeName=8]="BeforeAttributeName",e[e.InAttributeName=9]="InAttributeName",e[e.AfterAttributeName=10]="AfterAttributeName",e[e.BeforeAttributeValue=11]="BeforeAttributeValue",e[e.InAttributeValueDq=12]="InAttributeValueDq",e[e.InAttributeValueSq=13]="InAttributeValueSq",e[e.InAttributeValueNq=14]="InAttributeValueNq",e[e.BeforeDeclaration=15]="BeforeDeclaration",e[e.InDeclaration=16]="InDeclaration",e[e.InProcessingInstruction=17]="InProcessingInstruction",e[e.BeforeComment=18]="BeforeComment",e[e.CDATASequence=19]="CDATASequence",e[e.InSpecialComment=20]="InSpecialComment",e[e.InCommentLike=21]="InCommentLike",e[e.BeforeSpecialS=22]="BeforeSpecialS",e[e.SpecialStartSequence=23]="SpecialStartSequence",e[e.InSpecialTag=24]="InSpecialTag",e[e.BeforeEntity=25]="BeforeEntity",e[e.BeforeNumericEntity=26]="BeforeNumericEntity",e[e.InNamedEntity=27]="InNamedEntity",e[e.InNumericEntity=28]="InNumericEntity",e[e.InHexEntity=29]="InHexEntity"}(Bo||(Bo={})),(Ho=Uo||(Uo={}))[Ho.NoValue=0]="NoValue",Ho[Ho.Unquoted=1]="Unquoted",Ho[Ho.Single=2]="Single",Ho[Ho.Double=3]="Double";const Yo={Cdata:new Uint8Array([67,68,65,84,65,91]),CdataEnd:new Uint8Array([93,93,62]),CommentEnd:new Uint8Array([45,45,62]),ScriptEnd:new Uint8Array([60,47,115,99,114,105,112,116]),StyleEnd:new Uint8Array([60,47,115,116,121,108,101]),TitleEnd:new Uint8Array([60,47,116,105,116,108,101])};class jo{constructor({xmlMode:e=!1,decodeEntities:t=!0},n){this.cbs=n,this.state=Bo.Text,this.buffer="",this.sectionStart=0,this.index=0,this.baseState=Bo.Text,this.isSpecial=!1,this.running=!0,this.offset=0,this.currentSequence=void 0,this.sequenceIndex=0,this.trieIndex=0,this.trieCurrent=0,this.entityResult=0,this.entityExcess=0,this.xmlMode=e,this.decodeEntities=t,this.entityTrie=e?ue:le}reset(){this.state=Bo.Text,this.buffer="",this.sectionStart=0,this.index=0,this.baseState=Bo.Text,this.currentSequence=void 0,this.running=!0,this.offset=0}write(e){this.offset+=this.buffer.length,this.buffer=e,this.parse()}end(){this.running&&this.finish()}pause(){this.running=!1}resume(){this.running=!0,this.index<this.buffer.length+this.offset&&this.parse()}getIndex(){return this.index}getSectionStart(){return this.sectionStart}stateText(e){e===Fo.Lt||!this.decodeEntities&&this.fastForwardTo(Fo.Lt)?(this.index>this.sectionStart&&this.cbs.ontext(this.sectionStart,this.index),this.state=Bo.BeforeTagName,this.sectionStart=this.index):this.decodeEntities&&e===Fo.Amp&&(this.state=Bo.BeforeEntity)}stateSpecialStartSequence(e){const t=this.sequenceIndex===this.currentSequence.length;if(t?qo(e):(32|e)===this.currentSequence[this.sequenceIndex]){if(!t)return void this.sequenceIndex++}else this.isSpecial=!1;this.sequenceIndex=0,this.state=Bo.InTagName,this.stateInTagName(e)}stateInSpecialTag(e){if(this.sequenceIndex===this.currentSequence.length){if(e===Fo.Gt||Go(e)){const t=this.index-this.currentSequence.length;if(this.sectionStart<t){const e=this.index;this.index=t,this.cbs.ontext(this.sectionStart,t),this.index=e}return this.isSpecial=!1,this.sectionStart=t+2,void this.stateInClosingTagName(e)}this.sequenceIndex=0}(32|e)===this.currentSequence[this.sequenceIndex]?this.sequenceIndex+=1:0===this.sequenceIndex?this.currentSequence===Yo.TitleEnd?this.decodeEntities&&e===Fo.Amp&&(this.state=Bo.BeforeEntity):this.fastForwardTo(Fo.Lt)&&(this.sequenceIndex=1):this.sequenceIndex=Number(e===Fo.Lt)}stateCDATASequence(e){e===Yo.Cdata[this.sequenceIndex]?++this.sequenceIndex===Yo.Cdata.length&&(this.state=Bo.InCommentLike,this.currentSequence=Yo.CdataEnd,this.sequenceIndex=0,this.sectionStart=this.index+1):(this.sequenceIndex=0,this.state=Bo.InDeclaration,this.stateInDeclaration(e))}fastForwardTo(e){for(;++this.index<this.buffer.length+this.offset;)if(this.buffer.charCodeAt(this.index-this.offset)===e)return!0;return this.index=this.buffer.length+this.offset-1,!1}stateInCommentLike(e){e===this.currentSequence[this.sequenceIndex]?++this.sequenceIndex===this.currentSequence.length&&(this.currentSequence===Yo.CdataEnd?this.cbs.oncdata(this.sectionStart,this.index,2):this.cbs.oncomment(this.sectionStart,this.index,2),this.sequenceIndex=0,this.sectionStart=this.index+1,this.state=Bo.Text):0===this.sequenceIndex?this.fastForwardTo(this.currentSequence[0])&&(this.sequenceIndex=1):e!==this.currentSequence[this.sequenceIndex-1]&&(this.sequenceIndex=0)}isTagStartChar(e){return this.xmlMode?!qo(e):function(e){return e>=Fo.LowerA&&e<=Fo.LowerZ||e>=Fo.UpperA&&e<=Fo.UpperZ}(e)}startSpecial(e,t){this.isSpecial=!0,this.currentSequence=e,this.sequenceIndex=t,this.state=Bo.SpecialStartSequence}stateBeforeTagName(e){if(e===Fo.ExclamationMark)this.state=Bo.BeforeDeclaration,this.sectionStart=this.index+1;else if(e===Fo.Questionmark)this.state=Bo.InProcessingInstruction,this.sectionStart=this.index+1;else if(this.isTagStartChar(e)){const t=32|e;this.sectionStart=this.index,this.xmlMode||t!==Yo.TitleEnd[2]?this.state=this.xmlMode||t!==Yo.ScriptEnd[2]?Bo.InTagName:Bo.BeforeSpecialS:this.startSpecial(Yo.TitleEnd,3)}else e===Fo.Slash?this.state=Bo.BeforeClosingTagName:(this.state=Bo.Text,this.stateText(e))}stateInTagName(e){qo(e)&&(this.cbs.onopentagname(this.sectionStart,this.index),this.sectionStart=-1,this.state=Bo.BeforeAttributeName,this.stateBeforeAttributeName(e))}stateBeforeClosingTagName(e){Go(e)||(e===Fo.Gt?this.state=Bo.Text:(this.state=this.isTagStartChar(e)?Bo.InClosingTagName:Bo.InSpecialComment,this.sectionStart=this.index))}stateInClosingTagName(e){(e===Fo.Gt||Go(e))&&(this.cbs.onclosetag(this.sectionStart,this.index),this.sectionStart=-1,this.state=Bo.AfterClosingTagName,this.stateAfterClosingTagName(e))}stateAfterClosingTagName(e){(e===Fo.Gt||this.fastForwardTo(Fo.Gt))&&(this.state=Bo.Text,this.baseState=Bo.Text,this.sectionStart=this.index+1)}stateBeforeAttributeName(e){e===Fo.Gt?(this.cbs.onopentagend(this.index),this.isSpecial?(this.state=Bo.InSpecialTag,this.sequenceIndex=0):this.state=Bo.Text,this.baseState=this.state,this.sectionStart=this.index+1):e===Fo.Slash?this.state=Bo.InSelfClosingTag:Go(e)||(this.state=Bo.InAttributeName,this.sectionStart=this.index)}stateInSelfClosingTag(e){e===Fo.Gt?(this.cbs.onselfclosingtag(this.index),this.state=Bo.Text,this.baseState=Bo.Text,this.sectionStart=this.index+1,this.isSpecial=!1):Go(e)||(this.state=Bo.BeforeAttributeName,this.stateBeforeAttributeName(e))}stateInAttributeName(e){(e===Fo.Eq||qo(e))&&(this.cbs.onattribname(this.sectionStart,this.index),this.sectionStart=-1,this.state=Bo.AfterAttributeName,this.stateAfterAttributeName(e))}stateAfterAttributeName(e){e===Fo.Eq?this.state=Bo.BeforeAttributeValue:e===Fo.Slash||e===Fo.Gt?(this.cbs.onattribend(Uo.NoValue,this.index),this.state=Bo.BeforeAttributeName,this.stateBeforeAttributeName(e)):Go(e)||(this.cbs.onattribend(Uo.NoValue,this.index),this.state=Bo.InAttributeName,this.sectionStart=this.index)}stateBeforeAttributeValue(e){e===Fo.DoubleQuote?(this.state=Bo.InAttributeValueDq,this.sectionStart=this.index+1):e===Fo.SingleQuote?(this.state=Bo.InAttributeValueSq,this.sectionStart=this.index+1):Go(e)||(this.sectionStart=this.index,this.state=Bo.InAttributeValueNq,this.stateInAttributeValueNoQuotes(e))}handleInAttributeValue(e,t){e===t||!this.decodeEntities&&this.fastForwardTo(t)?(this.cbs.onattribdata(this.sectionStart,this.index),this.sectionStart=-1,this.cbs.onattribend(t===Fo.DoubleQuote?Uo.Double:Uo.Single,this.index),this.state=Bo.BeforeAttributeName):this.decodeEntities&&e===Fo.Amp&&(this.baseState=this.state,this.state=Bo.BeforeEntity)}stateInAttributeValueDoubleQuotes(e){this.handleInAttributeValue(e,Fo.DoubleQuote)}stateInAttributeValueSingleQuotes(e){this.handleInAttributeValue(e,Fo.SingleQuote)}stateInAttributeValueNoQuotes(e){Go(e)||e===Fo.Gt?(this.cbs.onattribdata(this.sectionStart,this.index),this.sectionStart=-1,this.cbs.onattribend(Uo.Unquoted,this.index),this.state=Bo.BeforeAttributeName,this.stateBeforeAttributeName(e)):this.decodeEntities&&e===Fo.Amp&&(this.baseState=this.state,this.state=Bo.BeforeEntity)}stateBeforeDeclaration(e){e===Fo.OpeningSquareBracket?(this.state=Bo.CDATASequence,this.sequenceIndex=0):this.state=e===Fo.Dash?Bo.BeforeComment:Bo.InDeclaration}stateInDeclaration(e){(e===Fo.Gt||this.fastForwardTo(Fo.Gt))&&(this.cbs.ondeclaration(this.sectionStart,this.index),this.state=Bo.Text,this.sectionStart=this.index+1)}stateInProcessingInstruction(e){(e===Fo.Gt||this.fastForwardTo(Fo.Gt))&&(this.cbs.onprocessinginstruction(this.sectionStart,this.index),this.state=Bo.Text,this.sectionStart=this.index+1)}stateBeforeComment(e){e===Fo.Dash?(this.state=Bo.InCommentLike,this.currentSequence=Yo.CommentEnd,this.sequenceIndex=2,this.sectionStart=this.index+1):this.state=Bo.InDeclaration}stateInSpecialComment(e){(e===Fo.Gt||this.fastForwardTo(Fo.Gt))&&(this.cbs.oncomment(this.sectionStart,this.index,0),this.state=Bo.Text,this.sectionStart=this.index+1)}stateBeforeSpecialS(e){const t=32|e;t===Yo.ScriptEnd[3]?this.startSpecial(Yo.ScriptEnd,4):t===Yo.StyleEnd[3]?this.startSpecial(Yo.StyleEnd,4):(this.state=Bo.InTagName,this.stateInTagName(e))}stateBeforeEntity(e){this.entityExcess=1,this.entityResult=0,e===Fo.Number?this.state=Bo.BeforeNumericEntity:e===Fo.Amp||(this.trieIndex=0,this.trieCurrent=this.entityTrie[0],this.state=Bo.InNamedEntity,this.stateInNamedEntity(e))}stateInNamedEntity(e){if(this.entityExcess+=1,this.trieIndex=De(this.entityTrie,this.trieCurrent,this.trieIndex+1,e),this.trieIndex<0)return this.emitNamedEntity(),void this.index--;this.trieCurrent=this.entityTrie[this.trieIndex];const t=this.trieCurrent&Te.VALUE_LENGTH;if(t){const n=(t>>14)-1;if(this.allowLegacyEntity()||e===Fo.Semi){const e=this.index-this.entityExcess+1;e>this.sectionStart&&this.emitPartial(this.sectionStart,e),this.entityResult=this.trieIndex,this.trieIndex+=n,this.entityExcess=0,this.sectionStart=this.index+1,0===n&&this.emitNamedEntity()}else this.trieIndex+=n}}emitNamedEntity(){if(this.state=this.baseState,0===this.entityResult)return;switch((this.entityTrie[this.entityResult]&Te.VALUE_LENGTH)>>14){case 1:this.emitCodePoint(this.entityTrie[this.entityResult]&~Te.VALUE_LENGTH);break;case 2:this.emitCodePoint(this.entityTrie[this.entityResult+1]);break;case 3:this.emitCodePoint(this.entityTrie[this.entityResult+1]),this.emitCodePoint(this.entityTrie[this.entityResult+2])}}stateBeforeNumericEntity(e){(32|e)===Fo.LowerX?(this.entityExcess++,this.state=Bo.InHexEntity):(this.state=Bo.InNumericEntity,this.stateInNumericEntity(e))}emitNumericEntity(e){const t=this.index-this.entityExcess-1;t+2+Number(this.state===Bo.InHexEntity)!==this.index&&(t>this.sectionStart&&this.emitPartial(this.sectionStart,t),this.sectionStart=this.index+Number(e),this.emitCodePoint(fe(this.entityResult))),this.state=this.baseState}stateInNumericEntity(e){e===Fo.Semi?this.emitNumericEntity(!0):$o(e)?(this.entityResult=10*this.entityResult+(e-Fo.Zero),this.entityExcess++):(this.allowLegacyEntity()?this.emitNumericEntity(!1):this.state=this.baseState,this.index--)}stateInHexEntity(e){e===Fo.Semi?this.emitNumericEntity(!0):$o(e)?(this.entityResult=16*this.entityResult+(e-Fo.Zero),this.entityExcess++):!function(e){return e>=Fo.UpperA&&e<=Fo.UpperF||e>=Fo.LowerA&&e<=Fo.LowerF}(e)?(this.allowLegacyEntity()?this.emitNumericEntity(!1):this.state=this.baseState,this.index--):(this.entityResult=16*this.entityResult+((32|e)-Fo.LowerA+10),this.entityExcess++)}allowLegacyEntity(){return!this.xmlMode&&(this.baseState===Bo.Text||this.baseState===Bo.InSpecialTag)}cleanup(){this.running&&this.sectionStart!==this.index&&(this.state===Bo.Text||this.state===Bo.InSpecialTag&&0===this.sequenceIndex?(this.cbs.ontext(this.sectionStart,this.index),this.sectionStart=this.index):this.state!==Bo.InAttributeValueDq&&this.state!==Bo.InAttributeValueSq&&this.state!==Bo.InAttributeValueNq||(this.cbs.onattribdata(this.sectionStart,this.index),this.sectionStart=this.index))}shouldContinue(){return this.index<this.buffer.length+this.offset&&this.running}parse(){for(;this.shouldContinue();){const e=this.buffer.charCodeAt(this.index-this.offset);switch(this.state){case Bo.Text:this.stateText(e);break;case Bo.SpecialStartSequence:this.stateSpecialStartSequence(e);break;case Bo.InSpecialTag:this.stateInSpecialTag(e);break;case Bo.CDATASequence:this.stateCDATASequence(e);break;case Bo.InAttributeValueDq:this.stateInAttributeValueDoubleQuotes(e);break;case Bo.InAttributeName:this.stateInAttributeName(e);break;case Bo.InCommentLike:this.stateInCommentLike(e);break;case Bo.InSpecialComment:this.stateInSpecialComment(e);break;case Bo.BeforeAttributeName:this.stateBeforeAttributeName(e);break;case Bo.InTagName:this.stateInTagName(e);break;case Bo.InClosingTagName:this.stateInClosingTagName(e);break;case Bo.BeforeTagName:this.stateBeforeTagName(e);break;case Bo.AfterAttributeName:this.stateAfterAttributeName(e);break;case Bo.InAttributeValueSq:this.stateInAttributeValueSingleQuotes(e);break;case Bo.BeforeAttributeValue:this.stateBeforeAttributeValue(e);break;case Bo.BeforeClosingTagName:this.stateBeforeClosingTagName(e);break;case Bo.AfterClosingTagName:this.stateAfterClosingTagName(e);break;case Bo.BeforeSpecialS:this.stateBeforeSpecialS(e);break;case Bo.InAttributeValueNq:this.stateInAttributeValueNoQuotes(e);break;case Bo.InSelfClosingTag:this.stateInSelfClosingTag(e);break;case Bo.InDeclaration:this.stateInDeclaration(e);break;case Bo.BeforeDeclaration:this.stateBeforeDeclaration(e);break;case Bo.BeforeComment:this.stateBeforeComment(e);break;case Bo.InProcessingInstruction:this.stateInProcessingInstruction(e);break;case Bo.InNamedEntity:this.stateInNamedEntity(e);break;case Bo.BeforeEntity:this.stateBeforeEntity(e);break;case Bo.InHexEntity:this.stateInHexEntity(e);break;case Bo.InNumericEntity:this.stateInNumericEntity(e);break;default:this.stateBeforeNumericEntity(e)}this.index++}this.cleanup()}finish(){this.state===Bo.InNamedEntity&&this.emitNamedEntity(),this.sectionStart<this.index&&this.handleTrailingData(),this.cbs.onend()}handleTrailingData(){const e=this.buffer.length+this.offset;this.state===Bo.InCommentLike?this.currentSequence===Yo.CdataEnd?this.cbs.oncdata(this.sectionStart,e,0):this.cbs.oncomment(this.sectionStart,e,0):this.state===Bo.InNumericEntity&&this.allowLegacyEntity()||this.state===Bo.InHexEntity&&this.allowLegacyEntity()?this.emitNumericEntity(!1):this.state===Bo.InTagName||this.state===Bo.BeforeAttributeName||this.state===Bo.BeforeAttributeValue||this.state===Bo.AfterAttributeName||this.state===Bo.InAttributeName||this.state===Bo.InAttributeValueSq||this.state===Bo.InAttributeValueDq||this.state===Bo.InAttributeValueNq||this.state===Bo.InClosingTagName||this.cbs.ontext(this.sectionStart,e)}emitPartial(e,t){this.baseState!==Bo.Text&&this.baseState!==Bo.InSpecialTag?this.cbs.onattribdata(e,t):this.cbs.ontext(e,t)}emitCodePoint(e){this.baseState!==Bo.Text&&this.baseState!==Bo.InSpecialTag?this.cbs.onattribentity(e):this.cbs.ontextentity(e)}}const Ko=new Set(["input","option","optgroup","select","button","datalist","textarea"]),Vo=new Set(["p"]),zo=new Set(["thead","tbody"]),Qo=new Set(["dd","dt"]),Wo=new Set(["rt","rp"]),Xo=new Map([["tr",new Set(["tr","th","td"])],["th",new Set(["th"])],["td",new Set(["thead","th","td"])],["body",new Set(["head","link","script"])],["li",new Set(["li"])],["p",Vo],["h1",Vo],["h2",Vo],["h3",Vo],["h4",Vo],["h5",Vo],["h6",Vo],["select",Ko],["input",Ko],["output",Ko],["button",Ko],["datalist",Ko],["textarea",Ko],["option",new Set(["option"])],["optgroup",new Set(["optgroup","option"])],["dd",Qo],["dt",Qo],["address",Vo],["article",Vo],["aside",Vo],["blockquote",Vo],["details",Vo],["div",Vo],["dl",Vo],["fieldset",Vo],["figcaption",Vo],["figure",Vo],["footer",Vo],["form",Vo],["header",Vo],["hr",Vo],["main",Vo],["nav",Vo],["ol",Vo],["pre",Vo],["section",Vo],["table",Vo],["ul",Vo],["rt",Wo],["rp",Wo],["tbody",zo],["tfoot",zo]]),Zo=new Set(["area","base","basefont","br","col","command","embed","frame","hr","img","input","isindex","keygen","link","meta","param","source","track","wbr"]),Jo=new Set(["math","svg"]),ea=new Set(["mi","mo","mn","ms","mtext","annotation-xml","foreignobject","desc","title"]),ta=/\s|\//;function na(e,t){const n=new ce(void 0,t);return new class{constructor(e,t={}){var n,s,r,i,o;this.options=t,this.startIndex=0,this.endIndex=0,this.openTagStart=0,this.tagname="",this.attribname="",this.attribvalue="",this.attribs=null,this.stack=[],this.foreignContext=[],this.buffers=[],this.bufferOffset=0,this.writeIndex=0,this.ended=!1,this.cbs=null!=e?e:{},this.lowerCaseTagNames=null!==(n=t.lowerCaseTags)&&void 0!==n?n:!t.xmlMode,this.lowerCaseAttributeNames=null!==(s=t.lowerCaseAttributeNames)&&void 0!==s?s:!t.xmlMode,this.tokenizer=new(null!==(r=t.Tokenizer)&&void 0!==r?r:jo)(this.options,this),null===(o=(i=this.cbs).onparserinit)||void 0===o||o.call(i,this)}ontext(e,t){var n,s;const r=this.getSlice(e,t);this.endIndex=t-1,null===(s=(n=this.cbs).ontext)||void 0===s||s.call(n,r),this.startIndex=t}ontextentity(e){var t,n;const s=this.tokenizer.getSectionStart();this.endIndex=s-1,null===(n=(t=this.cbs).ontext)||void 0===n||n.call(t,pe(e)),this.startIndex=s}isVoidElement(e){return!this.options.xmlMode&&Zo.has(e)}onopentagname(e,t){this.endIndex=t;let n=this.getSlice(e,t);this.lowerCaseTagNames&&(n=n.toLowerCase()),this.emitOpenTag(n)}emitOpenTag(e){var t,n,s,r;this.openTagStart=this.startIndex,this.tagname=e;const i=!this.options.xmlMode&&Xo.get(e);if(i)for(;this.stack.length>0&&i.has(this.stack[this.stack.length-1]);){const e=this.stack.pop();null===(n=(t=this.cbs).onclosetag)||void 0===n||n.call(t,e,!0)}this.isVoidElement(e)||(this.stack.push(e),Jo.has(e)?this.foreignContext.push(!0):ea.has(e)&&this.foreignContext.push(!1)),null===(r=(s=this.cbs).onopentagname)||void 0===r||r.call(s,e),this.cbs.onopentag&&(this.attribs={})}endOpenTag(e){var t,n;this.startIndex=this.openTagStart,this.attribs&&(null===(n=(t=this.cbs).onopentag)||void 0===n||n.call(t,this.tagname,this.attribs,e),this.attribs=null),this.cbs.onclosetag&&this.isVoidElement(this.tagname)&&this.cbs.onclosetag(this.tagname,!0),this.tagname=""}onopentagend(e){this.endIndex=e,this.endOpenTag(!1),this.startIndex=e+1}onclosetag(e,t){var n,s,r,i,o,a;this.endIndex=t;let c=this.getSlice(e,t);if(this.lowerCaseTagNames&&(c=c.toLowerCase()),(Jo.has(c)||ea.has(c))&&this.foreignContext.pop(),this.isVoidElement(c))this.options.xmlMode||"br"!==c||(null===(s=(n=this.cbs).onopentagname)||void 0===s||s.call(n,"br"),null===(i=(r=this.cbs).onopentag)||void 0===i||i.call(r,"br",{},!0),null===(a=(o=this.cbs).onclosetag)||void 0===a||a.call(o,"br",!1));else{const e=this.stack.lastIndexOf(c);if(-1!==e)if(this.cbs.onclosetag){let t=this.stack.length-e;for(;t--;)this.cbs.onclosetag(this.stack.pop(),0!==t)}else this.stack.length=e;else this.options.xmlMode||"p"!==c||(this.emitOpenTag("p"),this.closeCurrentTag(!0))}this.startIndex=t+1}onselfclosingtag(e){this.endIndex=e,this.options.xmlMode||this.options.recognizeSelfClosing||this.foreignContext[this.foreignContext.length-1]?(this.closeCurrentTag(!1),this.startIndex=e+1):this.onopentagend(e)}closeCurrentTag(e){var t,n;const s=this.tagname;this.endOpenTag(e),this.stack[this.stack.length-1]===s&&(null===(n=(t=this.cbs).onclosetag)||void 0===n||n.call(t,s,!e),this.stack.pop())}onattribname(e,t){this.startIndex=e;const n=this.getSlice(e,t);this.attribname=this.lowerCaseAttributeNames?n.toLowerCase():n}onattribdata(e,t){this.attribvalue+=this.getSlice(e,t)}onattribentity(e){this.attribvalue+=pe(e)}onattribend(e,t){var n,s;this.endIndex=t,null===(s=(n=this.cbs).onattribute)||void 0===s||s.call(n,this.attribname,this.attribvalue,e===Uo.Double?'"':e===Uo.Single?"'":e===Uo.NoValue?void 0:null),this.attribs&&!Object.prototype.hasOwnProperty.call(this.attribs,this.attribname)&&(this.attribs[this.attribname]=this.attribvalue),this.attribvalue=""}getInstructionName(e){const t=e.search(ta);let n=t<0?e:e.substr(0,t);return this.lowerCaseTagNames&&(n=n.toLowerCase()),n}ondeclaration(e,t){this.endIndex=t;const n=this.getSlice(e,t);if(this.cbs.onprocessinginstruction){const e=this.getInstructionName(n);this.cbs.onprocessinginstruction(`!${e}`,`!${n}`)}this.startIndex=t+1}onprocessinginstruction(e,t){this.endIndex=t;const n=this.getSlice(e,t);if(this.cbs.onprocessinginstruction){const e=this.getInstructionName(n);this.cbs.onprocessinginstruction(`?${e}`,`?${n}`)}this.startIndex=t+1}oncomment(e,t,n){var s,r,i,o;this.endIndex=t,null===(r=(s=this.cbs).oncomment)||void 0===r||r.call(s,this.getSlice(e,t-n)),null===(o=(i=this.cbs).oncommentend)||void 0===o||o.call(i),this.startIndex=t+1}oncdata(e,t,n){var s,r,i,o,a,c,l,u,h,d;this.endIndex=t;const p=this.getSlice(e,t-n);this.options.xmlMode||this.options.recognizeCDATA?(null===(r=(s=this.cbs).oncdatastart)||void 0===r||r.call(s),null===(o=(i=this.cbs).ontext)||void 0===o||o.call(i,p),null===(c=(a=this.cbs).oncdataend)||void 0===c||c.call(a)):(null===(u=(l=this.cbs).oncomment)||void 0===u||u.call(l,`[CDATA[${p}]]`),null===(d=(h=this.cbs).oncommentend)||void 0===d||d.call(h)),this.startIndex=t+1}onend(){var e,t;if(this.cbs.onclosetag){this.endIndex=this.startIndex;for(let e=this.stack.length;e>0;this.cbs.onclosetag(this.stack[--e],!0));}null===(t=(e=this.cbs).onend)||void 0===t||t.call(e)}reset(){var e,t,n,s;null===(t=(e=this.cbs).onreset)||void 0===t||t.call(e),this.tokenizer.reset(),this.tagname="",this.attribname="",this.attribs=null,this.stack.length=0,this.startIndex=0,this.endIndex=0,null===(s=(n=this.cbs).onparserinit)||void 0===s||s.call(n,this),this.buffers.length=0,this.bufferOffset=0,this.writeIndex=0,this.ended=!1}parseComplete(e){this.reset(),this.end(e)}getSlice(e,t){for(;e-this.bufferOffset>=this.buffers[0].length;)this.shiftBuffer();let n=this.buffers[0].slice(e-this.bufferOffset,t-this.bufferOffset);for(;t-this.bufferOffset>this.buffers[0].length;)this.shiftBuffer(),n+=this.buffers[0].slice(0,t-this.bufferOffset);return n}shiftBuffer(){this.bufferOffset+=this.buffers[0].length,this.writeIndex--,this.buffers.shift()}write(e){var t,n;this.ended?null===(n=(t=this.cbs).onerror)||void 0===n||n.call(t,new Error(".write() after done!")):(this.buffers.push(e),this.tokenizer.running&&(this.tokenizer.write(e),this.writeIndex++))}end(e){var t,n;this.ended?null===(n=(t=this.cbs).onerror)||void 0===n||n.call(t,new Error(".end() after done!")):(e&&this.write(e),this.ended=!0,this.tokenizer.end())}pause(){this.tokenizer.pause()}resume(){for(this.tokenizer.resume();this.tokenizer.running&&this.writeIndex<this.buffers.length;)this.tokenizer.write(this.buffers[this.writeIndex++]);this.ended&&this.tokenizer.end()}parseChunk(e){this.write(e)}done(e){this.end(e)}}(n,t).end(e),n.root}const sa=(ra=(e,t,n,s)=>t.xmlMode||t._useHtmlParser2?na(e,t):wo(e,t,n,s),function(e,t,n,s){if("undefined"!=typeof Buffer&&Buffer.isBuffer(e)&&(e=e.toString()),"string"==typeof e)return ra(e,t,n,s);const r=e;if(!Array.isArray(r)&&se(r))return r;const i=new W([]);return Rs(r,i),i});var ra;const ia=(oa=sa,aa=(e,t)=>t.xmlMode||t._useHtmlParser2?qe(e,t):function(e){const t="length"in e?e:[e];for(let e=0;e<t.length;e+=1){const n=t[e];se(n)&&Array.prototype.splice.call(t,e,1,...n.children)}let n="";for(let e=0;e<t.length;e+=1)n+=Lo(t[e],Po);return n}(e),function e(t,n,s=!0){if(null==t)throw new Error("cheerio.load() expects a string");const r={...O,...R(n)},i=oa(t,r,s,null);class o extends Vs{_make(e,t){const n=a(e,t);return n.prevObject=this,n}_parse(e,t,n,s){return oa(e,t,n,s)}_render(e){return aa(e,this.options)}}function a(e,t,n=i,s){if(e&&St(e))return e;const a={...r,...R(s)},c="string"==typeof n?[oa(n,a,!1,null)]:"length"in n?n:[n],l=St(c)?c:new o(c,null,a);if(l._root=l,!e)return new o(void 0,l,a);const u="string"==typeof e&&Rt(e)?oa(e,a,!1,null).children:(h=e).name||"root"===h.type||"text"===h.type||"comment"===h.type?[e]:Array.isArray(e)?e:void 0;var h;const d=new o(u,l,a);if(u)return d;if("string"!=typeof e)throw new Error("Unexpected type of selector");let p=e;const f=t?"string"==typeof t?Rt(t)?new o([oa(t,a,!1,null)],l,a):(p=`${t} ${p}`,l):St(t)?t:new o(Array.isArray(t)?t:[t],l,a):l;return f?f.find(p):d}return Object.assign(a,kt,{load:e,_root:i,_options:r,fn:o.prototype,prototype:o.prototype}),a});var oa,aa;ia([]);const ca={selector:"h1,h2,h3,h4,h5,h6,ul,ol,li,table,pre,p>img:only-child",selectorRules:{"div,p":({$node:e})=>({queue:e.children()}),"h1,h2,h3,h4,h5,h6":({$node:e,getContent:t})=>({...t(e.contents())}),"ul,ol":({$node:e})=>({queue:e.children(),nesting:!0}),li:({$node:e,getContent:t})=>{const n=e.children().filter("ul,ol");let s;if(e.contents().first().is("div,p"))s=t(e.children().first());else{let r=e.contents();const i=r.index(n);i>=0&&(r=r.slice(0,i)),s=t(r)}return{queue:n,nesting:!0,...s}},"table,pre,p>img:only-child":({$node:e,getContent:t})=>({...t(e)})}},la="markmap: ",ua=/^h[1-6]$/,ha=/^[uo]l$/,da=/^li$/;function pa(e,t){const n={...ca,...t},s=ia(e),r=s("body");let i=0;const o={id:i,tag:"",html:"",level:0,parent:0,childrenLevel:0,children:[]},a=[];let c=0;return function e(t,r){t.each(((t,o)=>{var h;const d=s(o),p=null==(h=Object.entries(n.selectorRules).find((([e])=>d.is(e))))?void 0:h[1],f=null==p?void 0:p({$node:d,$:s,getContent:u});if((null==f?void 0:f.queue)&&!f.nesting)return void e(f.queue,r);const m=(E=o.tagName,ua.test(E)?+E[1]:ha.test(E)?8:da.test(E)?9:7);var E;if(!f)return void(m<=6&&(c=m));if(c>0&&m>c)return;if(!d.is(n.selector))return;c=0;const T=m<=6;let _=d.data();d.children("code:only-child").length&&(_={..._,...d.children().data()});const A=function(e){var t;const{parent:n}=e,s={id:++i,tag:e.tagName,level:e.level,html:e.html,childrenLevel:0,children:e.nesting?[]:void 0,parent:n.id};(null==(t=e.comments)?void 0:t.length)&&(s.comments=e.comments);Object.keys(e.data||{}).length&&(s.data=e.data);n.children&&((0===n.childrenLevel||n.childrenLevel>s.level)&&(n.children=[],n.childrenLevel=s.level),n.childrenLevel===s.level&&n.children.push(s));return s}({parent:r||l(m),nesting:!!f.queue||T,tagName:o.tagName,level:m,html:f.html||"",comments:f.comments,data:_});T&&a.push(A),f.queue&&e(f.queue,A)}))}(r.children()),o;function l(e){let t;for(;(t=a.at(-1))&&t.level>=e;)a.pop();return t||o}function u(e){var t;const n=function(e){const t=[];return e=e.filter(((e,n)=>{if("comment"===n.type){const e=n.data.trim();if(e.startsWith(la))return t.push(e.slice(la.length).trim()),!1}return!0})),{$node:e,comments:t}}(e),r=null==(t=s.html(n.$node))?void 0:t.trimEnd();return{comments:n.comments,html:r}}}const fa={};function ma(e,t){"string"!=typeof t&&(t=ma.defaultChars);const n=function(e){let t=fa[e];if(t)return t;t=fa[e]=[];for(let e=0;e<128;e++){const n=String.fromCharCode(e);t.push(n)}for(let n=0;n<e.length;n++){const s=e.charCodeAt(n);t[s]="%"+("0"+s.toString(16).toUpperCase()).slice(-2)}return t}(t);return e.replace(/(%[a-f0-9]{2})+/gi,(function(e){let t="";for(let s=0,r=e.length;s<r;s+=3){const i=parseInt(e.slice(s+1,s+3),16);if(i<128)t+=n[i];else{if(192==(224&i)&&s+3<r){const n=parseInt(e.slice(s+4,s+6),16);if(128==(192&n)){const e=i<<6&1984|63&n;t+=e<128?"��":String.fromCharCode(e),s+=3;continue}}if(224==(240&i)&&s+6<r){const n=parseInt(e.slice(s+4,s+6),16),r=parseInt(e.slice(s+7,s+9),16);if(128==(192&n)&&128==(192&r)){const e=i<<12&61440|n<<6&4032|63&r;t+=e<2048||e>=55296&&e<=57343?"���":String.fromCharCode(e),s+=6;continue}}if(240==(248&i)&&s+9<r){const n=parseInt(e.slice(s+4,s+6),16),r=parseInt(e.slice(s+7,s+9),16),o=parseInt(e.slice(s+10,s+12),16);if(128==(192&n)&&128==(192&r)&&128==(192&o)){let e=i<<18&1835008|n<<12&258048|r<<6&4032|63&o;e<65536||e>1114111?t+="����":(e-=65536,t+=String.fromCharCode(55296+(e>>10),56320+(1023&e))),s+=9;continue}}t+="�"}}return t}))}ma.defaultChars=";/?:@&=+$,#",ma.componentChars="";const Ea={};function Ta(e,t,n){"string"!=typeof t&&(n=t,t=Ta.defaultChars),void 0===n&&(n=!0);const s=function(e){let t=Ea[e];if(t)return t;t=Ea[e]=[];for(let e=0;e<128;e++){const n=String.fromCharCode(e);/^[0-9a-z]$/i.test(n)?t.push(n):t.push("%"+("0"+e.toString(16).toUpperCase()).slice(-2))}for(let n=0;n<e.length;n++)t[e.charCodeAt(n)]=e[n];return t}(t);let r="";for(let t=0,i=e.length;t<i;t++){const o=e.charCodeAt(t);if(n&&37===o&&t+2<i&&/^[0-9a-f]{2}$/i.test(e.slice(t+1,t+3)))r+=e.slice(t,t+3),t+=2;else if(o<128)r+=s[o];else if(o>=55296&&o<=57343){if(o>=55296&&o<=56319&&t+1<i){const n=e.charCodeAt(t+1);if(n>=56320&&n<=57343){r+=encodeURIComponent(e[t]+e[t+1]),t++;continue}}r+="%EF%BF%BD"}else r+=encodeURIComponent(e[t])}return r}function _a(e){let t="";return t+=e.protocol||"",t+=e.slashes?"//":"",t+=e.auth?e.auth+"@":"",e.hostname&&-1!==e.hostname.indexOf(":")?t+="["+e.hostname+"]":t+=e.hostname||"",t+=e.port?":"+e.port:"",t+=e.pathname||"",t+=e.search||"",t+=e.hash||"",t}function Aa(){this.protocol=null,this.slashes=null,this.auth=null,this.port=null,this.hostname=null,this.hash=null,this.search=null,this.pathname=null}Ta.defaultChars=";/?:@&=+$,-_.!~*'()#",Ta.componentChars="-_.!~*'()";const ga=/^([a-z0-9.+-]+:)/i,Ca=/:[0-9]*$/,ba=/^(\/\/?(?!\/)[^\?\s]*)(\?[^\s]*)?$/,Na=["{","}","|","\\","^","`"].concat(["<",">",'"',"`"," ","\r","\n","\t"]),Ia=["'"].concat(Na),ka=["%","/","?",";","#"].concat(Ia),Sa=["/","?","#"],Da=/^[+a-z0-9A-Z_-]{0,63}$/,ya=/^([+a-z0-9A-Z_-]{0,63})(.*)$/,Oa={javascript:!0,"javascript:":!0},La={http:!0,https:!0,ftp:!0,gopher:!0,file:!0,"http:":!0,"https:":!0,"ftp:":!0,"gopher:":!0,"file:":!0};function Ra(e,t){if(e&&e instanceof Aa)return e;const n=new Aa;return n.parse(e,t),n}Aa.prototype.parse=function(e,t){let n,s,r,i=e;if(i=i.trim(),!t&&1===e.split("#").length){const e=ba.exec(i);if(e)return this.pathname=e[1],e[2]&&(this.search=e[2]),this}let o=ga.exec(i);if(o&&(o=o[0],n=o.toLowerCase(),this.protocol=o,i=i.substr(o.length)),(t||o||i.match(/^\/\/[^@\/]+@[^@\/]+/))&&(r="//"===i.substr(0,2),!r||o&&Oa[o]||(i=i.substr(2),this.slashes=!0)),!Oa[o]&&(r||o&&!La[o])){let e,t,n=-1;for(let e=0;e<Sa.length;e++)s=i.indexOf(Sa[e]),-1!==s&&(-1===n||s<n)&&(n=s);t=-1===n?i.lastIndexOf("@"):i.lastIndexOf("@",n),-1!==t&&(e=i.slice(0,t),i=i.slice(t+1),this.auth=e),n=-1;for(let e=0;e<ka.length;e++)s=i.indexOf(ka[e]),-1!==s&&(-1===n||s<n)&&(n=s);-1===n&&(n=i.length),":"===i[n-1]&&n--;const r=i.slice(0,n);i=i.slice(n),this.parseHost(r),this.hostname=this.hostname||"";const o="["===this.hostname[0]&&"]"===this.hostname[this.hostname.length-1];if(!o){const e=this.hostname.split(/\./);for(let t=0,n=e.length;t<n;t++){const n=e[t];if(n&&!n.match(Da)){let s="";for(let e=0,t=n.length;e<t;e++)n.charCodeAt(e)>127?s+="x":s+=n[e];if(!s.match(Da)){const s=e.slice(0,t),r=e.slice(t+1),o=n.match(ya);o&&(s.push(o[1]),r.unshift(o[2])),r.length&&(i=r.join(".")+i),this.hostname=s.join(".");break}}}}this.hostname.length>255&&(this.hostname=""),o&&(this.hostname=this.hostname.substr(1,this.hostname.length-2))}const a=i.indexOf("#");-1!==a&&(this.hash=i.substr(a),i=i.slice(0,a));const c=i.indexOf("?");return-1!==c&&(this.search=i.substr(c),i=i.slice(0,c)),i&&(this.pathname=i),La[n]&&this.hostname&&!this.pathname&&(this.pathname=""),this},Aa.prototype.parseHost=function(e){let t=Ca.exec(e);t&&(t=t[0],":"!==t&&(this.port=t.substr(1)),e=e.substr(0,e.length-t.length)),e&&(this.hostname=e)};const Ma=Object.freeze(Object.defineProperty({__proto__:null,decode:ma,encode:Ta,format:_a,parse:Ra},Symbol.toStringTag,{value:"Module"})),va=/[\0-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/,xa=/[\0-\x1F\x7F-\x9F]/,wa=/[!-#%-\*,-\/:;\?@\[-\]_\{\}\xA1\xA7\xAB\xB6\xB7\xBB\xBF\u037E\u0387\u055A-\u055F\u0589\u058A\u05BE\u05C0\u05C3\u05C6\u05F3\u05F4\u0609\u060A\u060C\u060D\u061B\u061D-\u061F\u066A-\u066D\u06D4\u0700-\u070D\u07F7-\u07F9\u0830-\u083E\u085E\u0964\u0965\u0970\u09FD\u0A76\u0AF0\u0C77\u0C84\u0DF4\u0E4F\u0E5A\u0E5B\u0F04-\u0F12\u0F14\u0F3A-\u0F3D\u0F85\u0FD0-\u0FD4\u0FD9\u0FDA\u104A-\u104F\u10FB\u1360-\u1368\u1400\u166E\u169B\u169C\u16EB-\u16ED\u1735\u1736\u17D4-\u17D6\u17D8-\u17DA\u1800-\u180A\u1944\u1945\u1A1E\u1A1F\u1AA0-\u1AA6\u1AA8-\u1AAD\u1B5A-\u1B60\u1B7D\u1B7E\u1BFC-\u1BFF\u1C3B-\u1C3F\u1C7E\u1C7F\u1CC0-\u1CC7\u1CD3\u2010-\u2027\u2030-\u2043\u2045-\u2051\u2053-\u205E\u207D\u207E\u208D\u208E\u2308-\u230B\u2329\u232A\u2768-\u2775\u27C5\u27C6\u27E6-\u27EF\u2983-\u2998\u29D8-\u29DB\u29FC\u29FD\u2CF9-\u2CFC\u2CFE\u2CFF\u2D70\u2E00-\u2E2E\u2E30-\u2E4F\u2E52-\u2E5D\u3001-\u3003\u3008-\u3011\u3014-\u301F\u3030\u303D\u30A0\u30FB\uA4FE\uA4FF\uA60D-\uA60F\uA673\uA67E\uA6F2-\uA6F7\uA874-\uA877\uA8CE\uA8CF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA95F\uA9C1-\uA9CD\uA9DE\uA9DF\uAA5C-\uAA5F\uAADE\uAADF\uAAF0\uAAF1\uABEB\uFD3E\uFD3F\uFE10-\uFE19\uFE30-\uFE52\uFE54-\uFE61\uFE63\uFE68\uFE6A\uFE6B\uFF01-\uFF03\uFF05-\uFF0A\uFF0C-\uFF0F\uFF1A\uFF1B\uFF1F\uFF20\uFF3B-\uFF3D\uFF3F\uFF5B\uFF5D\uFF5F-\uFF65]|\uD800[\uDD00-\uDD02\uDF9F\uDFD0]|\uD801\uDD6F|\uD802[\uDC57\uDD1F\uDD3F\uDE50-\uDE58\uDE7F\uDEF0-\uDEF6\uDF39-\uDF3F\uDF99-\uDF9C]|\uD803[\uDEAD\uDF55-\uDF59\uDF86-\uDF89]|\uD804[\uDC47-\uDC4D\uDCBB\uDCBC\uDCBE-\uDCC1\uDD40-\uDD43\uDD74\uDD75\uDDC5-\uDDC8\uDDCD\uDDDB\uDDDD-\uDDDF\uDE38-\uDE3D\uDEA9]|\uD805[\uDC4B-\uDC4F\uDC5A\uDC5B\uDC5D\uDCC6\uDDC1-\uDDD7\uDE41-\uDE43\uDE60-\uDE6C\uDEB9\uDF3C-\uDF3E]|\uD806[\uDC3B\uDD44-\uDD46\uDDE2\uDE3F-\uDE46\uDE9A-\uDE9C\uDE9E-\uDEA2\uDF00-\uDF09]|\uD807[\uDC41-\uDC45\uDC70\uDC71\uDEF7\uDEF8\uDF43-\uDF4F\uDFFF]|\uD809[\uDC70-\uDC74]|\uD80B[\uDFF1\uDFF2]|\uD81A[\uDE6E\uDE6F\uDEF5\uDF37-\uDF3B\uDF44]|\uD81B[\uDE97-\uDE9A\uDFE2]|\uD82F\uDC9F|\uD836[\uDE87-\uDE8B]|\uD83A[\uDD5E\uDD5F]/,Pa=/[\$\+<->\^`\|~\xA2-\xA6\xA8\xA9\xAC\xAE-\xB1\xB4\xB8\xD7\xF7\u02C2-\u02C5\u02D2-\u02DF\u02E5-\u02EB\u02ED\u02EF-\u02FF\u0375\u0384\u0385\u03F6\u0482\u058D-\u058F\u0606-\u0608\u060B\u060E\u060F\u06DE\u06E9\u06FD\u06FE\u07F6\u07FE\u07FF\u0888\u09F2\u09F3\u09FA\u09FB\u0AF1\u0B70\u0BF3-\u0BFA\u0C7F\u0D4F\u0D79\u0E3F\u0F01-\u0F03\u0F13\u0F15-\u0F17\u0F1A-\u0F1F\u0F34\u0F36\u0F38\u0FBE-\u0FC5\u0FC7-\u0FCC\u0FCE\u0FCF\u0FD5-\u0FD8\u109E\u109F\u1390-\u1399\u166D\u17DB\u1940\u19DE-\u19FF\u1B61-\u1B6A\u1B74-\u1B7C\u1FBD\u1FBF-\u1FC1\u1FCD-\u1FCF\u1FDD-\u1FDF\u1FED-\u1FEF\u1FFD\u1FFE\u2044\u2052\u207A-\u207C\u208A-\u208C\u20A0-\u20C0\u2100\u2101\u2103-\u2106\u2108\u2109\u2114\u2116-\u2118\u211E-\u2123\u2125\u2127\u2129\u212E\u213A\u213B\u2140-\u2144\u214A-\u214D\u214F\u218A\u218B\u2190-\u2307\u230C-\u2328\u232B-\u2426\u2440-\u244A\u249C-\u24E9\u2500-\u2767\u2794-\u27C4\u27C7-\u27E5\u27F0-\u2982\u2999-\u29D7\u29DC-\u29FB\u29FE-\u2B73\u2B76-\u2B95\u2B97-\u2BFF\u2CE5-\u2CEA\u2E50\u2E51\u2E80-\u2E99\u2E9B-\u2EF3\u2F00-\u2FD5\u2FF0-\u2FFF\u3004\u3012\u3013\u3020\u3036\u3037\u303E\u303F\u309B\u309C\u3190\u3191\u3196-\u319F\u31C0-\u31E3\u31EF\u3200-\u321E\u322A-\u3247\u3250\u3260-\u327F\u328A-\u32B0\u32C0-\u33FF\u4DC0-\u4DFF\uA490-\uA4C6\uA700-\uA716\uA720\uA721\uA789\uA78A\uA828-\uA82B\uA836-\uA839\uAA77-\uAA79\uAB5B\uAB6A\uAB6B\uFB29\uFBB2-\uFBC2\uFD40-\uFD4F\uFDCF\uFDFC-\uFDFF\uFE62\uFE64-\uFE66\uFE69\uFF04\uFF0B\uFF1C-\uFF1E\uFF3E\uFF40\uFF5C\uFF5E\uFFE0-\uFFE6\uFFE8-\uFFEE\uFFFC\uFFFD]|\uD800[\uDD37-\uDD3F\uDD79-\uDD89\uDD8C-\uDD8E\uDD90-\uDD9C\uDDA0\uDDD0-\uDDFC]|\uD802[\uDC77\uDC78\uDEC8]|\uD805\uDF3F|\uD807[\uDFD5-\uDFF1]|\uD81A[\uDF3C-\uDF3F\uDF45]|\uD82F\uDC9C|\uD833[\uDF50-\uDFC3]|\uD834[\uDC00-\uDCF5\uDD00-\uDD26\uDD29-\uDD64\uDD6A-\uDD6C\uDD83\uDD84\uDD8C-\uDDA9\uDDAE-\uDDEA\uDE00-\uDE41\uDE45\uDF00-\uDF56]|\uD835[\uDEC1\uDEDB\uDEFB\uDF15\uDF35\uDF4F\uDF6F\uDF89\uDFA9\uDFC3]|\uD836[\uDC00-\uDDFF\uDE37-\uDE3A\uDE6D-\uDE74\uDE76-\uDE83\uDE85\uDE86]|\uD838[\uDD4F\uDEFF]|\uD83B[\uDCAC\uDCB0\uDD2E\uDEF0\uDEF1]|\uD83C[\uDC00-\uDC2B\uDC30-\uDC93\uDCA0-\uDCAE\uDCB1-\uDCBF\uDCC1-\uDCCF\uDCD1-\uDCF5\uDD0D-\uDDAD\uDDE6-\uDE02\uDE10-\uDE3B\uDE40-\uDE48\uDE50\uDE51\uDE60-\uDE65\uDF00-\uDFFF]|\uD83D[\uDC00-\uDED7\uDEDC-\uDEEC\uDEF0-\uDEFC\uDF00-\uDF76\uDF7B-\uDFD9\uDFE0-\uDFEB\uDFF0]|\uD83E[\uDC00-\uDC0B\uDC10-\uDC47\uDC50-\uDC59\uDC60-\uDC87\uDC90-\uDCAD\uDCB0\uDCB1\uDD00-\uDE53\uDE60-\uDE6D\uDE70-\uDE7C\uDE80-\uDE88\uDE90-\uDEBD\uDEBF-\uDEC5\uDECE-\uDEDB\uDEE0-\uDEE8\uDEF0-\uDEF8\uDF00-\uDF92\uDF94-\uDFCA]/,Fa=/[ \xA0\u1680\u2000-\u200A\u2028\u2029\u202F\u205F\u3000]/,Ba=Object.freeze(Object.defineProperty({__proto__:null,Any:va,Cc:xa,Cf:/[\xAD\u0600-\u0605\u061C\u06DD\u070F\u0890\u0891\u08E2\u180E\u200B-\u200F\u202A-\u202E\u2060-\u2064\u2066-\u206F\uFEFF\uFFF9-\uFFFB]|\uD804[\uDCBD\uDCCD]|\uD80D[\uDC30-\uDC3F]|\uD82F[\uDCA0-\uDCA3]|\uD834[\uDD73-\uDD7A]|\uDB40[\uDC01\uDC20-\uDC7F]/,P:wa,S:Pa,Z:Fa},Symbol.toStringTag,{value:"Module"}));function Ua(e){return"[object String]"===function(e){return Object.prototype.toString.call(e)}(e)}const Ha=Object.prototype.hasOwnProperty;function Ga(e){return Array.prototype.slice.call(arguments,1).forEach((function(t){if(t){if("object"!=typeof t)throw new TypeError(t+"must be object");Object.keys(t).forEach((function(n){e[n]=t[n]}))}})),e}function qa(e,t,n){return[].concat(e.slice(0,t),n,e.slice(t+1))}function $a(e){return!(e>=55296&&e<=57343)&&(!(e>=64976&&e<=65007)&&(65535!=(65535&e)&&65534!=(65535&e)&&(!(e>=0&&e<=8)&&(11!==e&&(!(e>=14&&e<=31)&&(!(e>=127&&e<=159)&&!(e>1114111)))))))}function Ya(e){if(e>65535){const t=55296+((e-=65536)>>10),n=56320+(1023&e);return String.fromCharCode(t,n)}return String.fromCharCode(e)}const ja=/\\([!"#$%&'()*+,\-./:;<=>?@[\\\]^_`{|}~])/g,Ka=new RegExp(ja.source+"|"+/&([a-z#][a-z0-9]{1,31});/gi.source,"gi"),Va=/^#((?:x[a-f0-9]{1,8}|[0-9]{1,8}))$/i;function za(e){return e.indexOf("\\")<0&&e.indexOf("&")<0?e:e.replace(Ka,(function(e,t,n){return t||function(e,t){if(35===t.charCodeAt(0)&&Va.test(t)){const n="x"===t[1].toLowerCase()?parseInt(t.slice(2),16):parseInt(t.slice(1),10);return $a(n)?Ya(n):e}const n=Oe(e);return n!==e?n:e}(e,n)}))}const Qa=/[&<>"]/,Wa=/[&<>"]/g,Xa={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;"};function Za(e){return Xa[e]}function Ja(e){return Qa.test(e)?e.replace(Wa,Za):e}const ec=/[.?*+^$[\]\\(){}|-]/g;function tc(e){switch(e){case 9:case 32:return!0}return!1}function nc(e){if(e>=8192&&e<=8202)return!0;switch(e){case 9:case 10:case 11:case 12:case 13:case 32:case 160:case 5760:case 8239:case 8287:case 12288:return!0}return!1}function sc(e){return wa.test(e)||Pa.test(e)}function rc(e){switch(e){case 33:case 34:case 35:case 36:case 37:case 38:case 39:case 40:case 41:case 42:case 43:case 44:case 45:case 46:case 47:case 58:case 59:case 60:case 61:case 62:case 63:case 64:case 91:case 92:case 93:case 94:case 95:case 96:case 123:case 124:case 125:case 126:return!0;default:return!1}}function ic(e){return e=e.trim().replace(/\s+/g," "),"Ṿ"==="ẞ".toLowerCase()&&(e=e.replace(/ẞ/g,"ß")),e.toLowerCase().toUpperCase()}const oc={mdurl:Ma,ucmicro:Ba},ac=Object.freeze(Object.defineProperty({__proto__:null,arrayReplaceAt:qa,assign:Ga,escapeHtml:Ja,escapeRE:function(e){return e.replace(ec,"\\$&")},fromCodePoint:Ya,has:function(e,t){return Ha.call(e,t)},isMdAsciiPunct:rc,isPunctChar:sc,isSpace:tc,isString:Ua,isValidEntityCode:$a,isWhiteSpace:nc,lib:oc,normalizeReference:ic,unescapeAll:za,unescapeMd:function(e){return e.indexOf("\\")<0?e:e.replace(ja,"$1")}},Symbol.toStringTag,{value:"Module"}));const cc=Object.freeze(Object.defineProperty({__proto__:null,parseLinkDestination:function(e,t,n){let s,r=t;const i={ok:!1,pos:0,str:""};if(60===e.charCodeAt(r)){for(r++;r<n;){if(s=e.charCodeAt(r),10===s)return i;if(60===s)return i;if(62===s)return i.pos=r+1,i.str=za(e.slice(t+1,r)),i.ok=!0,i;92===s&&r+1<n?r+=2:r++}return i}let o=0;for(;r<n&&(s=e.charCodeAt(r),32!==s)&&!(s<32||127===s);)if(92===s&&r+1<n){if(32===e.charCodeAt(r+1))break;r+=2}else{if(40===s&&(o++,o>32))return i;if(41===s){if(0===o)break;o--}r++}return t===r||0!==o||(i.str=za(e.slice(t,r)),i.pos=r,i.ok=!0),i},parseLinkLabel:function(e,t,n){let s,r,i,o;const a=e.posMax,c=e.pos;for(e.pos=t+1,s=1;e.pos<a;){if(i=e.src.charCodeAt(e.pos),93===i&&(s--,0===s)){r=!0;break}if(o=e.pos,e.md.inline.skipToken(e),91===i)if(o===e.pos-1)s++;else if(n)return e.pos=c,-1}let l=-1;return r&&(l=e.pos),e.pos=c,l},parseLinkTitle:function(e,t,n,s){let r,i=t;const o={ok:!1,can_continue:!1,pos:0,str:"",marker:0};if(s)o.str=s.str,o.marker=s.marker;else{if(i>=n)return o;let s=e.charCodeAt(i);if(34!==s&&39!==s&&40!==s)return o;t++,i++,40===s&&(s=41),o.marker=s}for(;i<n;){if(r=e.charCodeAt(i),r===o.marker)return o.pos=i+1,o.str+=za(e.slice(t,i)),o.ok=!0,o;if(40===r&&41===o.marker)return o;92===r&&i+1<n&&i++,i++}return o.can_continue=!0,o.str+=za(e.slice(t,i)),o}},Symbol.toStringTag,{value:"Module"})),lc={};function uc(){this.rules=Ga({},lc)}function hc(){this.__rules__=[],this.__cache__=null}function dc(e,t,n){this.type=e,this.tag=t,this.attrs=null,this.map=null,this.nesting=n,this.level=0,this.children=null,this.content="",this.markup="",this.info="",this.meta=null,this.block=!1,this.hidden=!1}function pc(e,t,n){this.src=e,this.env=n,this.tokens=[],this.inlineMode=!1,this.md=t}lc.code_inline=function(e,t,n,s,r){const i=e[t];return"<code"+r.renderAttrs(i)+">"+Ja(i.content)+"</code>"},lc.code_block=function(e,t,n,s,r){const i=e[t];return"<pre"+r.renderAttrs(i)+"><code>"+Ja(e[t].content)+"</code></pre>\n"},lc.fence=function(e,t,n,s,r){const i=e[t],o=i.info?za(i.info).trim():"";let a,c="",l="";if(o){const e=o.split(/(\s+)/g);c=e[0],l=e.slice(2).join("")}if(a=n.highlight&&n.highlight(i.content,c,l)||Ja(i.content),0===a.indexOf("<pre"))return a+"\n";if(o){const e=i.attrIndex("class"),t=i.attrs?i.attrs.slice():[];e<0?t.push(["class",n.langPrefix+c]):(t[e]=t[e].slice(),t[e][1]+=" "+n.langPrefix+c);const s={attrs:t};return`<pre><code${r.renderAttrs(s)}>${a}</code></pre>\n`}return`<pre><code${r.renderAttrs(i)}>${a}</code></pre>\n`},lc.image=function(e,t,n,s,r){const i=e[t];return i.attrs[i.attrIndex("alt")][1]=r.renderInlineAsText(i.children,n,s),r.renderToken(e,t,n)},lc.hardbreak=function(e,t,n){return n.xhtmlOut?"<br />\n":"<br>\n"},lc.softbreak=function(e,t,n){return n.breaks?n.xhtmlOut?"<br />\n":"<br>\n":"\n"},lc.text=function(e,t){return Ja(e[t].content)},lc.html_block=function(e,t){return e[t].content},lc.html_inline=function(e,t){return e[t].content},uc.prototype.renderAttrs=function(e){let t,n,s;if(!e.attrs)return"";for(s="",t=0,n=e.attrs.length;t<n;t++)s+=" "+Ja(e.attrs[t][0])+'="'+Ja(e.attrs[t][1])+'"';return s},uc.prototype.renderToken=function(e,t,n){const s=e[t];let r="";if(s.hidden)return"";s.block&&-1!==s.nesting&&t&&e[t-1].hidden&&(r+="\n"),r+=(-1===s.nesting?"</":"<")+s.tag,r+=this.renderAttrs(s),0===s.nesting&&n.xhtmlOut&&(r+=" /");let i=!1;if(s.block&&(i=!0,1===s.nesting&&t+1<e.length)){const n=e[t+1];("inline"===n.type||n.hidden||-1===n.nesting&&n.tag===s.tag)&&(i=!1)}return r+=i?">\n":">",r},uc.prototype.renderInline=function(e,t,n){let s="";const r=this.rules;for(let i=0,o=e.length;i<o;i++){const o=e[i].type;void 0!==r[o]?s+=r[o](e,i,t,n,this):s+=this.renderToken(e,i,t)}return s},uc.prototype.renderInlineAsText=function(e,t,n){let s="";for(let r=0,i=e.length;r<i;r++)switch(e[r].type){case"text":case"html_inline":case"html_block":s+=e[r].content;break;case"image":s+=this.renderInlineAsText(e[r].children,t,n);break;case"softbreak":case"hardbreak":s+="\n"}return s},uc.prototype.render=function(e,t,n){let s="";const r=this.rules;for(let i=0,o=e.length;i<o;i++){const o=e[i].type;"inline"===o?s+=this.renderInline(e[i].children,t,n):void 0!==r[o]?s+=r[o](e,i,t,n,this):s+=this.renderToken(e,i,t,n)}return s},hc.prototype.__find__=function(e){for(let t=0;t<this.__rules__.length;t++)if(this.__rules__[t].name===e)return t;return-1},hc.prototype.__compile__=function(){const e=this,t=[""];e.__rules__.forEach((function(e){e.enabled&&e.alt.forEach((function(e){t.indexOf(e)<0&&t.push(e)}))})),e.__cache__={},t.forEach((function(t){e.__cache__[t]=[],e.__rules__.forEach((function(n){n.enabled&&(t&&n.alt.indexOf(t)<0||e.__cache__[t].push(n.fn))}))}))},hc.prototype.at=function(e,t,n){const s=this.__find__(e),r=n||{};if(-1===s)throw new Error("Parser rule not found: "+e);this.__rules__[s].fn=t,this.__rules__[s].alt=r.alt||[],this.__cache__=null},hc.prototype.before=function(e,t,n,s){const r=this.__find__(e),i=s||{};if(-1===r)throw new Error("Parser rule not found: "+e);this.__rules__.splice(r,0,{name:t,enabled:!0,fn:n,alt:i.alt||[]}),this.__cache__=null},hc.prototype.after=function(e,t,n,s){const r=this.__find__(e),i=s||{};if(-1===r)throw new Error("Parser rule not found: "+e);this.__rules__.splice(r+1,0,{name:t,enabled:!0,fn:n,alt:i.alt||[]}),this.__cache__=null},hc.prototype.push=function(e,t,n){const s=n||{};this.__rules__.push({name:e,enabled:!0,fn:t,alt:s.alt||[]}),this.__cache__=null},hc.prototype.enable=function(e,t){Array.isArray(e)||(e=[e]);const n=[];return e.forEach((function(e){const s=this.__find__(e);if(s<0){if(t)return;throw new Error("Rules manager: invalid rule name "+e)}this.__rules__[s].enabled=!0,n.push(e)}),this),this.__cache__=null,n},hc.prototype.enableOnly=function(e,t){Array.isArray(e)||(e=[e]),this.__rules__.forEach((function(e){e.enabled=!1})),this.enable(e,t)},hc.prototype.disable=function(e,t){Array.isArray(e)||(e=[e]);const n=[];return e.forEach((function(e){const s=this.__find__(e);if(s<0){if(t)return;throw new Error("Rules manager: invalid rule name "+e)}this.__rules__[s].enabled=!1,n.push(e)}),this),this.__cache__=null,n},hc.prototype.getRules=function(e){return null===this.__cache__&&this.__compile__(),this.__cache__[e]||[]},dc.prototype.attrIndex=function(e){if(!this.attrs)return-1;const t=this.attrs;for(let n=0,s=t.length;n<s;n++)if(t[n][0]===e)return n;return-1},dc.prototype.attrPush=function(e){this.attrs?this.attrs.push(e):this.attrs=[e]},dc.prototype.attrSet=function(e,t){const n=this.attrIndex(e),s=[e,t];n<0?this.attrPush(s):this.attrs[n]=s},dc.prototype.attrGet=function(e){const t=this.attrIndex(e);let n=null;return t>=0&&(n=this.attrs[t][1]),n},dc.prototype.attrJoin=function(e,t){const n=this.attrIndex(e);n<0?this.attrPush([e,t]):this.attrs[n][1]=this.attrs[n][1]+" "+t},pc.prototype.Token=dc;const fc=/\r\n?|\n/g,mc=/\0/g;function Ec(e){return/^<\/a\s*>/i.test(e)}const Tc=/\+-|\.\.|\?\?\?\?|!!!!|,,|--/,_c=/\((c|tm|r)\)/i,Ac=/\((c|tm|r)\)/gi,gc={c:"©",r:"®",tm:"™"};function Cc(e,t){return gc[t.toLowerCase()]}function bc(e){let t=0;for(let n=e.length-1;n>=0;n--){const s=e[n];"text"!==s.type||t||(s.content=s.content.replace(Ac,Cc)),"link_open"===s.type&&"auto"===s.info&&t--,"link_close"===s.type&&"auto"===s.info&&t++}}function Nc(e){let t=0;for(let n=e.length-1;n>=0;n--){const s=e[n];"text"!==s.type||t||Tc.test(s.content)&&(s.content=s.content.replace(/\+-/g,"±").replace(/\.{2,}/g,"…").replace(/([?!])…/g,"$1..").replace(/([?!]){4,}/g,"$1$1$1").replace(/,{2,}/g,",").replace(/(^|[^-])---(?=[^-]|$)/gm,"$1—").replace(/(^|\s)--(?=\s|$)/gm,"$1–").replace(/(^|[^-\s])--(?=[^-\s]|$)/gm,"$1–")),"link_open"===s.type&&"auto"===s.info&&t--,"link_close"===s.type&&"auto"===s.info&&t++}}const Ic=/['"]/,kc=/['"]/g,Sc="’";function Dc(e,t,n){return e.slice(0,t)+n+e.slice(t+1)}function yc(e,t){let n;const s=[];for(let r=0;r<e.length;r++){const i=e[r],o=e[r].level;for(n=s.length-1;n>=0&&!(s[n].level<=o);n--);if(s.length=n+1,"text"!==i.type)continue;let a=i.content,c=0,l=a.length;e:for(;c<l;){kc.lastIndex=c;const u=kc.exec(a);if(!u)break;let h=!0,d=!0;c=u.index+1;const p="'"===u[0];let f=32;if(u.index-1>=0)f=a.charCodeAt(u.index-1);else for(n=r-1;n>=0&&("softbreak"!==e[n].type&&"hardbreak"!==e[n].type);n--)if(e[n].content){f=e[n].content.charCodeAt(e[n].content.length-1);break}let m=32;if(c<l)m=a.charCodeAt(c);else for(n=r+1;n<e.length&&("softbreak"!==e[n].type&&"hardbreak"!==e[n].type);n++)if(e[n].content){m=e[n].content.charCodeAt(0);break}const E=rc(f)||sc(String.fromCharCode(f)),T=rc(m)||sc(String.fromCharCode(m)),_=nc(f),A=nc(m);if(A?h=!1:T&&(_||E||(h=!1)),_?d=!1:E&&(A||T||(d=!1)),34===m&&'"'===u[0]&&f>=48&&f<=57&&(d=h=!1),h&&d&&(h=E,d=T),h||d){if(d)for(n=s.length-1;n>=0;n--){let h=s[n];if(s[n].level<o)break;if(h.single===p&&s[n].level===o){let o,d;h=s[n],p?(o=t.md.options.quotes[2],d=t.md.options.quotes[3]):(o=t.md.options.quotes[0],d=t.md.options.quotes[1]),i.content=Dc(i.content,u.index,d),e[h.token].content=Dc(e[h.token].content,h.pos,o),c+=d.length-1,h.token===r&&(c+=o.length-1),a=i.content,l=a.length,s.length=n;continue e}}h?s.push({token:r,pos:u.index,single:p,level:o}):d&&p&&(i.content=Dc(i.content,u.index,Sc))}else p&&(i.content=Dc(i.content,u.index,Sc))}}}const Oc=[["normalize",function(e){let t;t=e.src.replace(fc,"\n"),t=t.replace(mc,"�"),e.src=t}],["block",function(e){let t;e.inlineMode?(t=new e.Token("inline","",0),t.content=e.src,t.map=[0,1],t.children=[],e.tokens.push(t)):e.md.block.parse(e.src,e.md,e.env,e.tokens)}],["inline",function(e){const t=e.tokens;for(let n=0,s=t.length;n<s;n++){const s=t[n];"inline"===s.type&&e.md.inline.parse(s.content,e.md,e.env,s.children)}}],["linkify",function(e){const t=e.tokens;var n;if(e.md.options.linkify)for(let s=0,r=t.length;s<r;s++){if("inline"!==t[s].type||!e.md.linkify.pretest(t[s].content))continue;let r=t[s].children,i=0;for(let o=r.length-1;o>=0;o--){const a=r[o];if("link_close"!==a.type){if("html_inline"===a.type&&(n=a.content,/^<a[>\s]/i.test(n)&&i>0&&i--,Ec(a.content)&&i++),!(i>0)&&"text"===a.type&&e.md.linkify.test(a.content)){const n=a.content;let i=e.md.linkify.match(n);const c=[];let l=a.level,u=0;i.length>0&&0===i[0].index&&o>0&&"text_special"===r[o-1].type&&(i=i.slice(1));for(let t=0;t<i.length;t++){const s=i[t].url,r=e.md.normalizeLink(s);if(!e.md.validateLink(r))continue;let o=i[t].text;o=i[t].schema?"mailto:"!==i[t].schema||/^mailto:/i.test(o)?e.md.normalizeLinkText(o):e.md.normalizeLinkText("mailto:"+o).replace(/^mailto:/,""):e.md.normalizeLinkText("http://"+o).replace(/^http:\/\//,"");const a=i[t].index;if(a>u){const t=new e.Token("text","",0);t.content=n.slice(u,a),t.level=l,c.push(t)}const h=new e.Token("link_open","a",1);h.attrs=[["href",r]],h.level=l++,h.markup="linkify",h.info="auto",c.push(h);const d=new e.Token("text","",0);d.content=o,d.level=l,c.push(d);const p=new e.Token("link_close","a",-1);p.level=--l,p.markup="linkify",p.info="auto",c.push(p),u=i[t].lastIndex}if(u<n.length){const t=new e.Token("text","",0);t.content=n.slice(u),t.level=l,c.push(t)}t[s].children=r=qa(r,o,c)}}else for(o--;r[o].level!==a.level&&"link_open"!==r[o].type;)o--}}}],["replacements",function(e){let t;if(e.md.options.typographer)for(t=e.tokens.length-1;t>=0;t--)"inline"===e.tokens[t].type&&(_c.test(e.tokens[t].content)&&bc(e.tokens[t].children),Tc.test(e.tokens[t].content)&&Nc(e.tokens[t].children))}],["smartquotes",function(e){if(e.md.options.typographer)for(let t=e.tokens.length-1;t>=0;t--)"inline"===e.tokens[t].type&&Ic.test(e.tokens[t].content)&&yc(e.tokens[t].children,e)}],["text_join",function(e){let t,n;const s=e.tokens,r=s.length;for(let e=0;e<r;e++){if("inline"!==s[e].type)continue;const r=s[e].children,i=r.length;for(t=0;t<i;t++)"text_special"===r[t].type&&(r[t].type="text");for(t=n=0;t<i;t++)"text"===r[t].type&&t+1<i&&"text"===r[t+1].type?r[t+1].content=r[t].content+r[t+1].content:(t!==n&&(r[n]=r[t]),n++);t!==n&&(r.length=n)}}]];function Lc(){this.ruler=new hc;for(let e=0;e<Oc.length;e++)this.ruler.push(Oc[e][0],Oc[e][1])}function Rc(e,t,n,s){this.src=e,this.md=t,this.env=n,this.tokens=s,this.bMarks=[],this.eMarks=[],this.tShift=[],this.sCount=[],this.bsCount=[],this.blkIndent=0,this.line=0,this.lineMax=0,this.tight=!1,this.ddIndent=-1,this.listIndent=-1,this.parentType="root",this.level=0;const r=this.src;for(let e=0,t=0,n=0,s=0,i=r.length,o=!1;t<i;t++){const a=r.charCodeAt(t);if(!o){if(tc(a)){n++,9===a?s+=4-s%4:s++;continue}o=!0}10!==a&&t!==i-1||(10!==a&&t++,this.bMarks.push(e),this.eMarks.push(t),this.tShift.push(n),this.sCount.push(s),this.bsCount.push(0),o=!1,n=0,s=0,e=t+1)}this.bMarks.push(r.length),this.eMarks.push(r.length),this.tShift.push(0),this.sCount.push(0),this.bsCount.push(0),this.lineMax=this.bMarks.length-1}Lc.prototype.process=function(e){const t=this.ruler.getRules("");for(let n=0,s=t.length;n<s;n++)t[n](e)},Lc.prototype.State=pc,Rc.prototype.push=function(e,t,n){const s=new dc(e,t,n);return s.block=!0,n<0&&this.level--,s.level=this.level,n>0&&this.level++,this.tokens.push(s),s},Rc.prototype.isEmpty=function(e){return this.bMarks[e]+this.tShift[e]>=this.eMarks[e]},Rc.prototype.skipEmptyLines=function(e){for(let t=this.lineMax;e<t&&!(this.bMarks[e]+this.tShift[e]<this.eMarks[e]);e++);return e},Rc.prototype.skipSpaces=function(e){for(let t=this.src.length;e<t;e++){if(!tc(this.src.charCodeAt(e)))break}return e},Rc.prototype.skipSpacesBack=function(e,t){if(e<=t)return e;for(;e>t;)if(!tc(this.src.charCodeAt(--e)))return e+1;return e},Rc.prototype.skipChars=function(e,t){for(let n=this.src.length;e<n&&this.src.charCodeAt(e)===t;e++);return e},Rc.prototype.skipCharsBack=function(e,t,n){if(e<=n)return e;for(;e>n;)if(t!==this.src.charCodeAt(--e))return e+1;return e},Rc.prototype.getLines=function(e,t,n,s){if(e>=t)return"";const r=new Array(t-e);for(let i=0,o=e;o<t;o++,i++){let e=0;const a=this.bMarks[o];let c,l=a;for(c=o+1<t||s?this.eMarks[o]+1:this.eMarks[o];l<c&&e<n;){const t=this.src.charCodeAt(l);if(tc(t))9===t?e+=4-(e+this.bsCount[o])%4:e++;else{if(!(l-a<this.tShift[o]))break;e++}l++}r[i]=e>n?new Array(e-n+1).join(" ")+this.src.slice(l,c):this.src.slice(l,c)}return r.join("")},Rc.prototype.Token=dc;function Mc(e,t){const n=e.bMarks[t]+e.tShift[t],s=e.eMarks[t];return e.src.slice(n,s)}function vc(e){const t=[],n=e.length;let s=0,r=e.charCodeAt(s),i=!1,o=0,a="";for(;s<n;)124===r&&(i?(a+=e.substring(o,s-1),o=s):(t.push(a+e.substring(o,s)),a="",o=s+1)),i=92===r,s++,r=e.charCodeAt(s);return t.push(a+e.substring(o)),t}function xc(e,t){const n=e.eMarks[t];let s=e.bMarks[t]+e.tShift[t];const r=e.src.charCodeAt(s++);if(42!==r&&45!==r&&43!==r)return-1;if(s<n){if(!tc(e.src.charCodeAt(s)))return-1}return s}function wc(e,t){const n=e.bMarks[t]+e.tShift[t],s=e.eMarks[t];let r=n;if(r+1>=s)return-1;let i=e.src.charCodeAt(r++);if(i<48||i>57)return-1;for(;;){if(r>=s)return-1;if(i=e.src.charCodeAt(r++),!(i>=48&&i<=57)){if(41===i||46===i)break;return-1}if(r-n>=10)return-1}return r<s&&(i=e.src.charCodeAt(r),!tc(i))?-1:r}const Pc="<[A-Za-z][A-Za-z0-9\\-]*(?:\\s+[a-zA-Z_:][a-zA-Z0-9:._-]*(?:\\s*=\\s*(?:[^\"'=<>`\\x00-\\x20]+|'[^']*'|\"[^\"]*\"))?)*\\s*\\/?>",Fc="<\\/[A-Za-z][A-Za-z0-9\\-]*\\s*>",Bc=new RegExp("^(?:"+Pc+"|"+Fc+"|\x3c!---?>|\x3c!--(?:[^-]|-[^-]|--[^>])*--\x3e|<[?][\\s\\S]*?[?]>|<![A-Za-z][^>]*>|<!\\[CDATA\\[[\\s\\S]*?\\]\\]>)"),Uc=new RegExp("^(?:"+Pc+"|"+Fc+")"),Hc=[[/^<(script|pre|style|textarea)(?=(\s|>|$))/i,/<\/(script|pre|style|textarea)>/i,!0],[/^<!--/,/-->/,!0],[/^<\?/,/\?>/,!0],[/^<![A-Z]/,/>/,!0],[/^<!\[CDATA\[/,/\]\]>/,!0],[new RegExp("^</?("+["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"].join("|")+")(?=(\\s|/?>|$))","i"),/^$/,!0],[new RegExp(Uc.source+"\\s*$"),/^$/,!1]];const Gc=[["table",function(e,t,n,s){if(t+2>n)return!1;let r=t+1;if(e.sCount[r]<e.blkIndent)return!1;if(e.sCount[r]-e.blkIndent>=4)return!1;let i=e.bMarks[r]+e.tShift[r];if(i>=e.eMarks[r])return!1;const o=e.src.charCodeAt(i++);if(124!==o&&45!==o&&58!==o)return!1;if(i>=e.eMarks[r])return!1;const a=e.src.charCodeAt(i++);if(124!==a&&45!==a&&58!==a&&!tc(a))return!1;if(45===o&&tc(a))return!1;for(;i<e.eMarks[r];){const t=e.src.charCodeAt(i);if(124!==t&&45!==t&&58!==t&&!tc(t))return!1;i++}let c=Mc(e,t+1),l=c.split("|");const u=[];for(let e=0;e<l.length;e++){const t=l[e].trim();if(!t){if(0===e||e===l.length-1)continue;return!1}if(!/^:?-+:?$/.test(t))return!1;58===t.charCodeAt(t.length-1)?u.push(58===t.charCodeAt(0)?"center":"right"):58===t.charCodeAt(0)?u.push("left"):u.push("")}if(c=Mc(e,t).trim(),-1===c.indexOf("|"))return!1;if(e.sCount[t]-e.blkIndent>=4)return!1;l=vc(c),l.length&&""===l[0]&&l.shift(),l.length&&""===l[l.length-1]&&l.pop();const h=l.length;if(0===h||h!==u.length)return!1;if(s)return!0;const d=e.parentType;e.parentType="table";const p=e.md.block.ruler.getRules("blockquote"),f=[t,0];e.push("table_open","table",1).map=f,e.push("thead_open","thead",1).map=[t,t+1],e.push("tr_open","tr",1).map=[t,t+1];for(let t=0;t<l.length;t++){const n=e.push("th_open","th",1);u[t]&&(n.attrs=[["style","text-align:"+u[t]]]);const s=e.push("inline","",0);s.content=l[t].trim(),s.children=[],e.push("th_close","th",-1)}let m;e.push("tr_close","tr",-1),e.push("thead_close","thead",-1);let E=0;for(r=t+2;r<n&&!(e.sCount[r]<e.blkIndent);r++){let s=!1;for(let t=0,i=p.length;t<i;t++)if(p[t](e,r,n,!0)){s=!0;break}if(s)break;if(c=Mc(e,r).trim(),!c)break;if(e.sCount[r]-e.blkIndent>=4)break;if(l=vc(c),l.length&&""===l[0]&&l.shift(),l.length&&""===l[l.length-1]&&l.pop(),E+=h-l.length,E>65536)break;if(r===t+2){e.push("tbody_open","tbody",1).map=m=[t+2,0]}e.push("tr_open","tr",1).map=[r,r+1];for(let t=0;t<h;t++){const n=e.push("td_open","td",1);u[t]&&(n.attrs=[["style","text-align:"+u[t]]]);const s=e.push("inline","",0);s.content=l[t]?l[t].trim():"",s.children=[],e.push("td_close","td",-1)}e.push("tr_close","tr",-1)}return m&&(e.push("tbody_close","tbody",-1),m[1]=r),e.push("table_close","table",-1),f[1]=r,e.parentType=d,e.line=r,!0},["paragraph","reference"]],["code",function(e,t,n){if(e.sCount[t]-e.blkIndent<4)return!1;let s=t+1,r=s;for(;s<n;)if(e.isEmpty(s))s++;else{if(!(e.sCount[s]-e.blkIndent>=4))break;s++,r=s}e.line=r;const i=e.push("code_block","code",0);return i.content=e.getLines(t,r,4+e.blkIndent,!1)+"\n",i.map=[t,e.line],!0}],["fence",function(e,t,n,s){let r=e.bMarks[t]+e.tShift[t],i=e.eMarks[t];if(e.sCount[t]-e.blkIndent>=4)return!1;if(r+3>i)return!1;const o=e.src.charCodeAt(r);if(126!==o&&96!==o)return!1;let a=r;r=e.skipChars(r,o);let c=r-a;if(c<3)return!1;const l=e.src.slice(a,r),u=e.src.slice(r,i);if(96===o&&u.indexOf(String.fromCharCode(o))>=0)return!1;if(s)return!0;let h=t,d=!1;for(;(h++,!(h>=n))&&(r=a=e.bMarks[h]+e.tShift[h],i=e.eMarks[h],!(r<i&&e.sCount[h]<e.blkIndent));)if(e.src.charCodeAt(r)===o&&!(e.sCount[h]-e.blkIndent>=4||(r=e.skipChars(r,o),r-a<c||(r=e.skipSpaces(r),r<i)))){d=!0;break}c=e.sCount[t],e.line=h+(d?1:0);const p=e.push("fence","code",0);return p.info=u,p.content=e.getLines(t+1,h,c,!0),p.markup=l,p.map=[t,e.line],!0},["paragraph","reference","blockquote","list"]],["blockquote",function(e,t,n,s){let r=e.bMarks[t]+e.tShift[t],i=e.eMarks[t];const o=e.lineMax;if(e.sCount[t]-e.blkIndent>=4)return!1;if(62!==e.src.charCodeAt(r))return!1;if(s)return!0;const a=[],c=[],l=[],u=[],h=e.md.block.ruler.getRules("blockquote"),d=e.parentType;e.parentType="blockquote";let p,f=!1;for(p=t;p<n;p++){const t=e.sCount[p]<e.blkIndent;if(r=e.bMarks[p]+e.tShift[p],i=e.eMarks[p],r>=i)break;if(62===e.src.charCodeAt(r++)&&!t){let t,n,s=e.sCount[p]+1;32===e.src.charCodeAt(r)?(r++,s++,n=!1,t=!0):9===e.src.charCodeAt(r)?(t=!0,(e.bsCount[p]+s)%4==3?(r++,s++,n=!1):n=!0):t=!1;let o=s;for(a.push(e.bMarks[p]),e.bMarks[p]=r;r<i;){const t=e.src.charCodeAt(r);if(!tc(t))break;9===t?o+=4-(o+e.bsCount[p]+(n?1:0))%4:o++,r++}f=r>=i,c.push(e.bsCount[p]),e.bsCount[p]=e.sCount[p]+1+(t?1:0),l.push(e.sCount[p]),e.sCount[p]=o-s,u.push(e.tShift[p]),e.tShift[p]=r-e.bMarks[p];continue}if(f)break;let s=!1;for(let t=0,r=h.length;t<r;t++)if(h[t](e,p,n,!0)){s=!0;break}if(s){e.lineMax=p,0!==e.blkIndent&&(a.push(e.bMarks[p]),c.push(e.bsCount[p]),u.push(e.tShift[p]),l.push(e.sCount[p]),e.sCount[p]-=e.blkIndent);break}a.push(e.bMarks[p]),c.push(e.bsCount[p]),u.push(e.tShift[p]),l.push(e.sCount[p]),e.sCount[p]=-1}const m=e.blkIndent;e.blkIndent=0;const E=e.push("blockquote_open","blockquote",1);E.markup=">";const T=[t,0];E.map=T,e.md.block.tokenize(e,t,p),e.push("blockquote_close","blockquote",-1).markup=">",e.lineMax=o,e.parentType=d,T[1]=e.line;for(let n=0;n<u.length;n++)e.bMarks[n+t]=a[n],e.tShift[n+t]=u[n],e.sCount[n+t]=l[n],e.bsCount[n+t]=c[n];return e.blkIndent=m,!0},["paragraph","reference","blockquote","list"]],["hr",function(e,t,n,s){const r=e.eMarks[t];if(e.sCount[t]-e.blkIndent>=4)return!1;let i=e.bMarks[t]+e.tShift[t];const o=e.src.charCodeAt(i++);if(42!==o&&45!==o&&95!==o)return!1;let a=1;for(;i<r;){const t=e.src.charCodeAt(i++);if(t!==o&&!tc(t))return!1;t===o&&a++}if(a<3)return!1;if(s)return!0;e.line=t+1;const c=e.push("hr","hr",0);return c.map=[t,e.line],c.markup=Array(a+1).join(String.fromCharCode(o)),!0},["paragraph","reference","blockquote","list"]],["list",function(e,t,n,s){let r,i,o,a,c=t,l=!0;if(e.sCount[c]-e.blkIndent>=4)return!1;if(e.listIndent>=0&&e.sCount[c]-e.listIndent>=4&&e.sCount[c]<e.blkIndent)return!1;let u,h,d,p=!1;if(s&&"paragraph"===e.parentType&&e.sCount[c]>=e.blkIndent&&(p=!0),(d=wc(e,c))>=0){if(u=!0,o=e.bMarks[c]+e.tShift[c],h=Number(e.src.slice(o,d-1)),p&&1!==h)return!1}else{if(!((d=xc(e,c))>=0))return!1;u=!1}if(p&&e.skipSpaces(d)>=e.eMarks[c])return!1;if(s)return!0;const f=e.src.charCodeAt(d-1),m=e.tokens.length;u?(a=e.push("ordered_list_open","ol",1),1!==h&&(a.attrs=[["start",h]])):a=e.push("bullet_list_open","ul",1);const E=[c,0];a.map=E,a.markup=String.fromCharCode(f);let T=!1;const _=e.md.block.ruler.getRules("list"),A=e.parentType;for(e.parentType="list";c<n;){i=d,r=e.eMarks[c];const t=e.sCount[c]+d-(e.bMarks[c]+e.tShift[c]);let s=t;for(;i<r;){const t=e.src.charCodeAt(i);if(9===t)s+=4-(s+e.bsCount[c])%4;else{if(32!==t)break;s++}i++}const h=i;let p;p=h>=r?1:s-t,p>4&&(p=1);const m=t+p;a=e.push("list_item_open","li",1),a.markup=String.fromCharCode(f);const E=[c,0];a.map=E,u&&(a.info=e.src.slice(o,d-1));const A=e.tight,g=e.tShift[c],C=e.sCount[c],b=e.listIndent;if(e.listIndent=e.blkIndent,e.blkIndent=m,e.tight=!0,e.tShift[c]=h-e.bMarks[c],e.sCount[c]=s,h>=r&&e.isEmpty(c+1)?e.line=Math.min(e.line+2,n):e.md.block.tokenize(e,c,n,!0),e.tight&&!T||(l=!1),T=e.line-c>1&&e.isEmpty(e.line-1),e.blkIndent=e.listIndent,e.listIndent=b,e.tShift[c]=g,e.sCount[c]=C,e.tight=A,a=e.push("list_item_close","li",-1),a.markup=String.fromCharCode(f),c=e.line,E[1]=c,c>=n)break;if(e.sCount[c]<e.blkIndent)break;if(e.sCount[c]-e.blkIndent>=4)break;let N=!1;for(let t=0,s=_.length;t<s;t++)if(_[t](e,c,n,!0)){N=!0;break}if(N)break;if(u){if(d=wc(e,c),d<0)break;o=e.bMarks[c]+e.tShift[c]}else if(d=xc(e,c),d<0)break;if(f!==e.src.charCodeAt(d-1))break}return a=u?e.push("ordered_list_close","ol",-1):e.push("bullet_list_close","ul",-1),a.markup=String.fromCharCode(f),E[1]=c,e.line=c,e.parentType=A,l&&function(e,t){const n=e.level+2;for(let s=t+2,r=e.tokens.length-2;s<r;s++)e.tokens[s].level===n&&"paragraph_open"===e.tokens[s].type&&(e.tokens[s+2].hidden=!0,e.tokens[s].hidden=!0,s+=2)}(e,m),!0},["paragraph","reference","blockquote"]],["reference",function(e,t,n,s){let r=e.bMarks[t]+e.tShift[t],i=e.eMarks[t],o=t+1;if(e.sCount[t]-e.blkIndent>=4)return!1;if(91!==e.src.charCodeAt(r))return!1;function a(t){const n=e.lineMax;if(t>=n||e.isEmpty(t))return null;let s=!1;if(e.sCount[t]-e.blkIndent>3&&(s=!0),e.sCount[t]<0&&(s=!0),!s){const s=e.md.block.ruler.getRules("reference"),r=e.parentType;e.parentType="reference";let i=!1;for(let r=0,o=s.length;r<o;r++)if(s[r](e,t,n,!0)){i=!0;break}if(e.parentType=r,i)return null}const r=e.bMarks[t]+e.tShift[t],i=e.eMarks[t];return e.src.slice(r,i+1)}let c=e.src.slice(r,i+1);i=c.length;let l=-1;for(r=1;r<i;r++){const e=c.charCodeAt(r);if(91===e)return!1;if(93===e){l=r;break}if(10===e){const e=a(o);null!==e&&(c+=e,i=c.length,o++)}else if(92===e&&(r++,r<i&&10===c.charCodeAt(r))){const e=a(o);null!==e&&(c+=e,i=c.length,o++)}}if(l<0||58!==c.charCodeAt(l+1))return!1;for(r=l+2;r<i;r++){const e=c.charCodeAt(r);if(10===e){const e=a(o);null!==e&&(c+=e,i=c.length,o++)}else if(!tc(e))break}const u=e.md.helpers.parseLinkDestination(c,r,i);if(!u.ok)return!1;const h=e.md.normalizeLink(u.str);if(!e.md.validateLink(h))return!1;r=u.pos;const d=r,p=o,f=r;for(;r<i;r++){const e=c.charCodeAt(r);if(10===e){const e=a(o);null!==e&&(c+=e,i=c.length,o++)}else if(!tc(e))break}let m,E=e.md.helpers.parseLinkTitle(c,r,i);for(;E.can_continue;){const t=a(o);if(null===t)break;c+=t,r=i,i=c.length,o++,E=e.md.helpers.parseLinkTitle(c,r,i,E)}for(r<i&&f!==r&&E.ok?(m=E.str,r=E.pos):(m="",r=d,o=p);r<i;){if(!tc(c.charCodeAt(r)))break;r++}if(r<i&&10!==c.charCodeAt(r)&&m)for(m="",r=d,o=p;r<i;){if(!tc(c.charCodeAt(r)))break;r++}if(r<i&&10!==c.charCodeAt(r))return!1;const T=ic(c.slice(1,l));return!!T&&(s||(void 0===e.env.references&&(e.env.references={}),void 0===e.env.references[T]&&(e.env.references[T]={title:m,href:h}),e.line=o),!0)}],["html_block",function(e,t,n,s){let r=e.bMarks[t]+e.tShift[t],i=e.eMarks[t];if(e.sCount[t]-e.blkIndent>=4)return!1;if(!e.md.options.html)return!1;if(60!==e.src.charCodeAt(r))return!1;let o=e.src.slice(r,i),a=0;for(;a<Hc.length&&!Hc[a][0].test(o);a++);if(a===Hc.length)return!1;if(s)return Hc[a][2];let c=t+1;if(!Hc[a][1].test(o))for(;c<n&&!(e.sCount[c]<e.blkIndent);c++)if(r=e.bMarks[c]+e.tShift[c],i=e.eMarks[c],o=e.src.slice(r,i),Hc[a][1].test(o)){0!==o.length&&c++;break}e.line=c;const l=e.push("html_block","",0);return l.map=[t,c],l.content=e.getLines(t,c,e.blkIndent,!0),!0},["paragraph","reference","blockquote"]],["heading",function(e,t,n,s){let r=e.bMarks[t]+e.tShift[t],i=e.eMarks[t];if(e.sCount[t]-e.blkIndent>=4)return!1;let o=e.src.charCodeAt(r);if(35!==o||r>=i)return!1;let a=1;for(o=e.src.charCodeAt(++r);35===o&&r<i&&a<=6;)a++,o=e.src.charCodeAt(++r);if(a>6||r<i&&!tc(o))return!1;if(s)return!0;i=e.skipSpacesBack(i,r);const c=e.skipCharsBack(i,35,r);c>r&&tc(e.src.charCodeAt(c-1))&&(i=c),e.line=t+1;const l=e.push("heading_open","h"+String(a),1);l.markup="########".slice(0,a),l.map=[t,e.line];const u=e.push("inline","",0);return u.content=e.src.slice(r,i).trim(),u.map=[t,e.line],u.children=[],e.push("heading_close","h"+String(a),-1).markup="########".slice(0,a),!0},["paragraph","reference","blockquote"]],["lheading",function(e,t,n){const s=e.md.block.ruler.getRules("paragraph");if(e.sCount[t]-e.blkIndent>=4)return!1;const r=e.parentType;e.parentType="paragraph";let i,o=0,a=t+1;for(;a<n&&!e.isEmpty(a);a++){if(e.sCount[a]-e.blkIndent>3)continue;if(e.sCount[a]>=e.blkIndent){let t=e.bMarks[a]+e.tShift[a];const n=e.eMarks[a];if(t<n&&(i=e.src.charCodeAt(t),(45===i||61===i)&&(t=e.skipChars(t,i),t=e.skipSpaces(t),t>=n))){o=61===i?1:2;break}}if(e.sCount[a]<0)continue;let t=!1;for(let r=0,i=s.length;r<i;r++)if(s[r](e,a,n,!0)){t=!0;break}if(t)break}if(!o)return!1;const c=e.getLines(t,a,e.blkIndent,!1).trim();e.line=a+1;const l=e.push("heading_open","h"+String(o),1);l.markup=String.fromCharCode(i),l.map=[t,e.line];const u=e.push("inline","",0);return u.content=c,u.map=[t,e.line-1],u.children=[],e.push("heading_close","h"+String(o),-1).markup=String.fromCharCode(i),e.parentType=r,!0}],["paragraph",function(e,t,n){const s=e.md.block.ruler.getRules("paragraph"),r=e.parentType;let i=t+1;for(e.parentType="paragraph";i<n&&!e.isEmpty(i);i++){if(e.sCount[i]-e.blkIndent>3)continue;if(e.sCount[i]<0)continue;let t=!1;for(let r=0,o=s.length;r<o;r++)if(s[r](e,i,n,!0)){t=!0;break}if(t)break}const o=e.getLines(t,i,e.blkIndent,!1).trim();e.line=i,e.push("paragraph_open","p",1).map=[t,e.line];const a=e.push("inline","",0);return a.content=o,a.map=[t,e.line],a.children=[],e.push("paragraph_close","p",-1),e.parentType=r,!0}]];function qc(){this.ruler=new hc;for(let e=0;e<Gc.length;e++)this.ruler.push(Gc[e][0],Gc[e][1],{alt:(Gc[e][2]||[]).slice()})}function $c(e,t,n,s){this.src=e,this.env=n,this.md=t,this.tokens=s,this.tokens_meta=Array(s.length),this.pos=0,this.posMax=this.src.length,this.level=0,this.pending="",this.pendingLevel=0,this.cache={},this.delimiters=[],this._prev_delimiters=[],this.backticks={},this.backticksScanned=!1,this.linkLevel=0}function Yc(e){switch(e){case 10:case 33:case 35:case 36:case 37:case 38:case 42:case 43:case 45:case 58:case 60:case 61:case 62:case 64:case 91:case 92:case 93:case 94:case 95:case 96:case 123:case 125:case 126:return!0;default:return!1}}qc.prototype.tokenize=function(e,t,n){const s=this.ruler.getRules(""),r=s.length,i=e.md.options.maxNesting;let o=t,a=!1;for(;o<n&&(e.line=o=e.skipEmptyLines(o),!(o>=n))&&!(e.sCount[o]<e.blkIndent);){if(e.level>=i){e.line=n;break}const t=e.line;let c=!1;for(let i=0;i<r;i++)if(c=s[i](e,o,n,!1),c){if(t>=e.line)throw new Error("block rule didn't increment state.line");break}if(!c)throw new Error("none of the block rules matched");e.tight=!a,e.isEmpty(e.line-1)&&(a=!0),o=e.line,o<n&&e.isEmpty(o)&&(a=!0,o++,e.line=o)}},qc.prototype.parse=function(e,t,n,s){if(!e)return;const r=new this.State(e,t,n,s);this.tokenize(r,r.line,r.lineMax)},qc.prototype.State=Rc,$c.prototype.pushPending=function(){const e=new dc("text","",0);return e.content=this.pending,e.level=this.pendingLevel,this.tokens.push(e),this.pending="",e},$c.prototype.push=function(e,t,n){this.pending&&this.pushPending();const s=new dc(e,t,n);let r=null;return n<0&&(this.level--,this.delimiters=this._prev_delimiters.pop()),s.level=this.level,n>0&&(this.level++,this._prev_delimiters.push(this.delimiters),this.delimiters=[],r={delimiters:this.delimiters}),this.pendingLevel=this.level,this.tokens.push(s),this.tokens_meta.push(r),s},$c.prototype.scanDelims=function(e,t){const n=this.posMax,s=this.src.charCodeAt(e),r=e>0?this.src.charCodeAt(e-1):32;let i=e;for(;i<n&&this.src.charCodeAt(i)===s;)i++;const o=i-e,a=i<n?this.src.charCodeAt(i):32,c=rc(r)||sc(String.fromCharCode(r)),l=rc(a)||sc(String.fromCharCode(a)),u=nc(r),h=nc(a),d=!h&&(!l||u||c),p=!u&&(!c||h||l);return{can_open:d&&(t||!p||c),can_close:p&&(t||!d||l),length:o}},$c.prototype.Token=dc;const jc=/(?:^|[^a-z0-9.+-])([a-z][a-z0-9.+-]*)$/i;const Kc=[];for(let e=0;e<256;e++)Kc.push(0);function Vc(e,t){let n;const s=[],r=t.length;for(let i=0;i<r;i++){const r=t[i];if(126!==r.marker)continue;if(-1===r.end)continue;const o=t[r.end];n=e.tokens[r.token],n.type="s_open",n.tag="s",n.nesting=1,n.markup="~~",n.content="",n=e.tokens[o.token],n.type="s_close",n.tag="s",n.nesting=-1,n.markup="~~",n.content="","text"===e.tokens[o.token-1].type&&"~"===e.tokens[o.token-1].content&&s.push(o.token-1)}for(;s.length;){const t=s.pop();let r=t+1;for(;r<e.tokens.length&&"s_close"===e.tokens[r].type;)r++;r--,t!==r&&(n=e.tokens[r],e.tokens[r]=e.tokens[t],e.tokens[t]=n)}}"\\!\"#$%&'()*+,./:;<=>?@[]^_`{|}~-".split("").forEach((function(e){Kc[e.charCodeAt(0)]=1}));const zc={tokenize:function(e,t){const n=e.pos,s=e.src.charCodeAt(n);if(t)return!1;if(126!==s)return!1;const r=e.scanDelims(e.pos,!0);let i=r.length;const o=String.fromCharCode(s);if(i<2)return!1;let a;i%2&&(a=e.push("text","",0),a.content=o,i--);for(let t=0;t<i;t+=2)a=e.push("text","",0),a.content=o+o,e.delimiters.push({marker:s,length:0,token:e.tokens.length-1,end:-1,open:r.can_open,close:r.can_close});return e.pos+=r.length,!0},postProcess:function(e){const t=e.tokens_meta,n=e.tokens_meta.length;Vc(e,e.delimiters);for(let s=0;s<n;s++)t[s]&&t[s].delimiters&&Vc(e,t[s].delimiters)}};function Qc(e,t){for(let n=t.length-1;n>=0;n--){const s=t[n];if(95!==s.marker&&42!==s.marker)continue;if(-1===s.end)continue;const r=t[s.end],i=n>0&&t[n-1].end===s.end+1&&t[n-1].marker===s.marker&&t[n-1].token===s.token-1&&t[s.end+1].token===r.token+1,o=String.fromCharCode(s.marker),a=e.tokens[s.token];a.type=i?"strong_open":"em_open",a.tag=i?"strong":"em",a.nesting=1,a.markup=i?o+o:o,a.content="";const c=e.tokens[r.token];c.type=i?"strong_close":"em_close",c.tag=i?"strong":"em",c.nesting=-1,c.markup=i?o+o:o,c.content="",i&&(e.tokens[t[n-1].token].content="",e.tokens[t[s.end+1].token].content="",n--)}}const Wc={tokenize:function(e,t){const n=e.pos,s=e.src.charCodeAt(n);if(t)return!1;if(95!==s&&42!==s)return!1;const r=e.scanDelims(e.pos,42===s);for(let t=0;t<r.length;t++){e.push("text","",0).content=String.fromCharCode(s),e.delimiters.push({marker:s,length:r.length,token:e.tokens.length-1,end:-1,open:r.can_open,close:r.can_close})}return e.pos+=r.length,!0},postProcess:function(e){const t=e.tokens_meta,n=e.tokens_meta.length;Qc(e,e.delimiters);for(let s=0;s<n;s++)t[s]&&t[s].delimiters&&Qc(e,t[s].delimiters)}};const Xc=/^([a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*)$/,Zc=/^([a-zA-Z][a-zA-Z0-9+.-]{1,31}):([^<>\x00-\x20]*)$/;const Jc=/^&#((?:x[a-f0-9]{1,6}|[0-9]{1,7}));/i,el=/^&([a-z][a-z0-9]{1,31});/i;function tl(e){const t={},n=e.length;if(!n)return;let s=0,r=-2;const i=[];for(let o=0;o<n;o++){const n=e[o];if(i.push(0),e[s].marker===n.marker&&r===n.token-1||(s=o),r=n.token,n.length=n.length||0,!n.close)continue;t.hasOwnProperty(n.marker)||(t[n.marker]=[-1,-1,-1,-1,-1,-1]);const a=t[n.marker][(n.open?3:0)+n.length%3];let c=s-i[s]-1,l=c;for(;c>a;c-=i[c]+1){const t=e[c];if(t.marker===n.marker&&(t.open&&t.end<0)){let s=!1;if((t.close||n.open)&&(t.length+n.length)%3==0&&(t.length%3==0&&n.length%3==0||(s=!0)),!s){const s=c>0&&!e[c-1].open?i[c-1]+1:0;i[o]=o-c+s,i[c]=s,n.open=!1,t.end=o,t.close=!1,l=-1,r=-2;break}}}-1!==l&&(t[n.marker][(n.open?3:0)+(n.length||0)%3]=l)}}const nl=[["text",function(e,t){let n=e.pos;for(;n<e.posMax&&!Yc(e.src.charCodeAt(n));)n++;return n!==e.pos&&(t||(e.pending+=e.src.slice(e.pos,n)),e.pos=n,!0)}],["linkify",function(e,t){if(!e.md.options.linkify)return!1;if(e.linkLevel>0)return!1;const n=e.pos;if(n+3>e.posMax)return!1;if(58!==e.src.charCodeAt(n))return!1;if(47!==e.src.charCodeAt(n+1))return!1;if(47!==e.src.charCodeAt(n+2))return!1;const s=e.pending.match(jc);if(!s)return!1;const r=s[1],i=e.md.linkify.matchAtStart(e.src.slice(n-r.length));if(!i)return!1;let o=i.url;if(o.length<=r.length)return!1;o=o.replace(/\*+$/,"");const a=e.md.normalizeLink(o);if(!e.md.validateLink(a))return!1;if(!t){e.pending=e.pending.slice(0,-r.length);const t=e.push("link_open","a",1);t.attrs=[["href",a]],t.markup="linkify",t.info="auto";e.push("text","",0).content=e.md.normalizeLinkText(o);const n=e.push("link_close","a",-1);n.markup="linkify",n.info="auto"}return e.pos+=o.length-r.length,!0}],["newline",function(e,t){let n=e.pos;if(10!==e.src.charCodeAt(n))return!1;const s=e.pending.length-1,r=e.posMax;if(!t)if(s>=0&&32===e.pending.charCodeAt(s))if(s>=1&&32===e.pending.charCodeAt(s-1)){let t=s-1;for(;t>=1&&32===e.pending.charCodeAt(t-1);)t--;e.pending=e.pending.slice(0,t),e.push("hardbreak","br",0)}else e.pending=e.pending.slice(0,-1),e.push("softbreak","br",0);else e.push("softbreak","br",0);for(n++;n<r&&tc(e.src.charCodeAt(n));)n++;return e.pos=n,!0}],["escape",function(e,t){let n=e.pos;const s=e.posMax;if(92!==e.src.charCodeAt(n))return!1;if(n++,n>=s)return!1;let r=e.src.charCodeAt(n);if(10===r){for(t||e.push("hardbreak","br",0),n++;n<s&&(r=e.src.charCodeAt(n),tc(r));)n++;return e.pos=n,!0}let i=e.src[n];if(r>=55296&&r<=56319&&n+1<s){const t=e.src.charCodeAt(n+1);t>=56320&&t<=57343&&(i+=e.src[n+1],n++)}const o="\\"+i;if(!t){const t=e.push("text_special","",0);r<256&&0!==Kc[r]?t.content=i:t.content=o,t.markup=o,t.info="escape"}return e.pos=n+1,!0}],["backticks",function(e,t){let n=e.pos;if(96!==e.src.charCodeAt(n))return!1;const s=n;n++;const r=e.posMax;for(;n<r&&96===e.src.charCodeAt(n);)n++;const i=e.src.slice(s,n),o=i.length;if(e.backticksScanned&&(e.backticks[o]||0)<=s)return t||(e.pending+=i),e.pos+=o,!0;let a,c=n;for(;-1!==(a=e.src.indexOf("`",c));){for(c=a+1;c<r&&96===e.src.charCodeAt(c);)c++;const s=c-a;if(s===o){if(!t){const t=e.push("code_inline","code",0);t.markup=i,t.content=e.src.slice(n,a).replace(/\n/g," ").replace(/^ (.+) $/,"$1")}return e.pos=c,!0}e.backticks[s]=a}return e.backticksScanned=!0,t||(e.pending+=i),e.pos+=o,!0}],["strikethrough",zc.tokenize],["emphasis",Wc.tokenize],["link",function(e,t){let n,s,r,i,o="",a="",c=e.pos,l=!0;if(91!==e.src.charCodeAt(e.pos))return!1;const u=e.pos,h=e.posMax,d=e.pos+1,p=e.md.helpers.parseLinkLabel(e,e.pos,!0);if(p<0)return!1;let f=p+1;if(f<h&&40===e.src.charCodeAt(f)){for(l=!1,f++;f<h&&(n=e.src.charCodeAt(f),tc(n)||10===n);f++);if(f>=h)return!1;if(c=f,r=e.md.helpers.parseLinkDestination(e.src,f,e.posMax),r.ok){for(o=e.md.normalizeLink(r.str),e.md.validateLink(o)?f=r.pos:o="",c=f;f<h&&(n=e.src.charCodeAt(f),tc(n)||10===n);f++);if(r=e.md.helpers.parseLinkTitle(e.src,f,e.posMax),f<h&&c!==f&&r.ok)for(a=r.str,f=r.pos;f<h&&(n=e.src.charCodeAt(f),tc(n)||10===n);f++);}(f>=h||41!==e.src.charCodeAt(f))&&(l=!0),f++}if(l){if(void 0===e.env.references)return!1;if(f<h&&91===e.src.charCodeAt(f)?(c=f+1,f=e.md.helpers.parseLinkLabel(e,f),f>=0?s=e.src.slice(c,f++):f=p+1):f=p+1,s||(s=e.src.slice(d,p)),i=e.env.references[ic(s)],!i)return e.pos=u,!1;o=i.href,a=i.title}if(!t){e.pos=d,e.posMax=p;const t=[["href",o]];e.push("link_open","a",1).attrs=t,a&&t.push(["title",a]),e.linkLevel++,e.md.inline.tokenize(e),e.linkLevel--,e.push("link_close","a",-1)}return e.pos=f,e.posMax=h,!0}],["image",function(e,t){let n,s,r,i,o,a,c,l,u="";const h=e.pos,d=e.posMax;if(33!==e.src.charCodeAt(e.pos))return!1;if(91!==e.src.charCodeAt(e.pos+1))return!1;const p=e.pos+2,f=e.md.helpers.parseLinkLabel(e,e.pos+1,!1);if(f<0)return!1;if(i=f+1,i<d&&40===e.src.charCodeAt(i)){for(i++;i<d&&(n=e.src.charCodeAt(i),tc(n)||10===n);i++);if(i>=d)return!1;for(l=i,a=e.md.helpers.parseLinkDestination(e.src,i,e.posMax),a.ok&&(u=e.md.normalizeLink(a.str),e.md.validateLink(u)?i=a.pos:u=""),l=i;i<d&&(n=e.src.charCodeAt(i),tc(n)||10===n);i++);if(a=e.md.helpers.parseLinkTitle(e.src,i,e.posMax),i<d&&l!==i&&a.ok)for(c=a.str,i=a.pos;i<d&&(n=e.src.charCodeAt(i),tc(n)||10===n);i++);else c="";if(i>=d||41!==e.src.charCodeAt(i))return e.pos=h,!1;i++}else{if(void 0===e.env.references)return!1;if(i<d&&91===e.src.charCodeAt(i)?(l=i+1,i=e.md.helpers.parseLinkLabel(e,i),i>=0?r=e.src.slice(l,i++):i=f+1):i=f+1,r||(r=e.src.slice(p,f)),o=e.env.references[ic(r)],!o)return e.pos=h,!1;u=o.href,c=o.title}if(!t){s=e.src.slice(p,f);const t=[];e.md.inline.parse(s,e.md,e.env,t);const n=e.push("image","img",0),r=[["src",u],["alt",""]];n.attrs=r,n.children=t,n.content=s,c&&r.push(["title",c])}return e.pos=i,e.posMax=d,!0}],["autolink",function(e,t){let n=e.pos;if(60!==e.src.charCodeAt(n))return!1;const s=e.pos,r=e.posMax;for(;;){if(++n>=r)return!1;const t=e.src.charCodeAt(n);if(60===t)return!1;if(62===t)break}const i=e.src.slice(s+1,n);if(Zc.test(i)){const n=e.md.normalizeLink(i);if(!e.md.validateLink(n))return!1;if(!t){const t=e.push("link_open","a",1);t.attrs=[["href",n]],t.markup="autolink",t.info="auto";e.push("text","",0).content=e.md.normalizeLinkText(i);const s=e.push("link_close","a",-1);s.markup="autolink",s.info="auto"}return e.pos+=i.length+2,!0}if(Xc.test(i)){const n=e.md.normalizeLink("mailto:"+i);if(!e.md.validateLink(n))return!1;if(!t){const t=e.push("link_open","a",1);t.attrs=[["href",n]],t.markup="autolink",t.info="auto";e.push("text","",0).content=e.md.normalizeLinkText(i);const s=e.push("link_close","a",-1);s.markup="autolink",s.info="auto"}return e.pos+=i.length+2,!0}return!1}],["html_inline",function(e,t){if(!e.md.options.html)return!1;const n=e.posMax,s=e.pos;if(60!==e.src.charCodeAt(s)||s+2>=n)return!1;const r=e.src.charCodeAt(s+1);if(33!==r&&63!==r&&47!==r&&!function(e){const t=32|e;return t>=97&&t<=122}(r))return!1;const i=e.src.slice(s).match(Bc);if(!i)return!1;if(!t){const t=e.push("html_inline","",0);t.content=i[0],o=t.content,/^<a[>\s]/i.test(o)&&e.linkLevel++,function(e){return/^<\/a\s*>/i.test(e)}(t.content)&&e.linkLevel--}var o;return e.pos+=i[0].length,!0}],["entity",function(e,t){const n=e.pos,s=e.posMax;if(38!==e.src.charCodeAt(n))return!1;if(n+1>=s)return!1;if(35===e.src.charCodeAt(n+1)){const s=e.src.slice(n).match(Jc);if(s){if(!t){const t="x"===s[1][0].toLowerCase()?parseInt(s[1].slice(1),16):parseInt(s[1],10),n=e.push("text_special","",0);n.content=$a(t)?Ya(t):Ya(65533),n.markup=s[0],n.info="entity"}return e.pos+=s[0].length,!0}}else{const s=e.src.slice(n).match(el);if(s){const n=Oe(s[0]);if(n!==s[0]){if(!t){const t=e.push("text_special","",0);t.content=n,t.markup=s[0],t.info="entity"}return e.pos+=s[0].length,!0}}}return!1}]],sl=[["balance_pairs",function(e){const t=e.tokens_meta,n=e.tokens_meta.length;tl(e.delimiters);for(let e=0;e<n;e++)t[e]&&t[e].delimiters&&tl(t[e].delimiters)}],["strikethrough",zc.postProcess],["emphasis",Wc.postProcess],["fragments_join",function(e){let t,n,s=0;const r=e.tokens,i=e.tokens.length;for(t=n=0;t<i;t++)r[t].nesting<0&&s--,r[t].level=s,r[t].nesting>0&&s++,"text"===r[t].type&&t+1<i&&"text"===r[t+1].type?r[t+1].content=r[t].content+r[t+1].content:(t!==n&&(r[n]=r[t]),n++);t!==n&&(r.length=n)}]];function rl(){this.ruler=new hc;for(let e=0;e<nl.length;e++)this.ruler.push(nl[e][0],nl[e][1]);this.ruler2=new hc;for(let e=0;e<sl.length;e++)this.ruler2.push(sl[e][0],sl[e][1])}function il(e){return Array.prototype.slice.call(arguments,1).forEach((function(t){t&&Object.keys(t).forEach((function(n){e[n]=t[n]}))})),e}function ol(e){return Object.prototype.toString.call(e)}function al(e){return"[object Function]"===ol(e)}function cl(e){return e.replace(/[.?*+^$[\]\\(){}|-]/g,"\\$&")}rl.prototype.skipToken=function(e){const t=e.pos,n=this.ruler.getRules(""),s=n.length,r=e.md.options.maxNesting,i=e.cache;if(void 0!==i[t])return void(e.pos=i[t]);let o=!1;if(e.level<r){for(let r=0;r<s;r++)if(e.level++,o=n[r](e,!0),e.level--,o){if(t>=e.pos)throw new Error("inline rule didn't increment state.pos");break}}else e.pos=e.posMax;o||e.pos++,i[t]=e.pos},rl.prototype.tokenize=function(e){const t=this.ruler.getRules(""),n=t.length,s=e.posMax,r=e.md.options.maxNesting;for(;e.pos<s;){const i=e.pos;let o=!1;if(e.level<r)for(let s=0;s<n;s++)if(o=t[s](e,!1),o){if(i>=e.pos)throw new Error("inline rule didn't increment state.pos");break}if(o){if(e.pos>=s)break}else e.pending+=e.src[e.pos++]}e.pending&&e.pushPending()},rl.prototype.parse=function(e,t,n,s){const r=new this.State(e,t,n,s);this.tokenize(r);const i=this.ruler2.getRules(""),o=i.length;for(let e=0;e<o;e++)i[e](r)},rl.prototype.State=$c;const ll={fuzzyLink:!0,fuzzyEmail:!0,fuzzyIP:!1};const ul={"http:":{validate:function(e,t,n){const s=e.slice(t);return n.re.http||(n.re.http=new RegExp("^\\/\\/"+n.re.src_auth+n.re.src_host_port_strict+n.re.src_path,"i")),n.re.http.test(s)?s.match(n.re.http)[0].length:0}},"https:":"http:","ftp:":"http:","//":{validate:function(e,t,n){const s=e.slice(t);return n.re.no_http||(n.re.no_http=new RegExp("^"+n.re.src_auth+"(?:localhost|(?:(?:"+n.re.src_domain+")\\.)+"+n.re.src_domain_root+")"+n.re.src_port+n.re.src_host_terminator+n.re.src_path,"i")),n.re.no_http.test(s)?t>=3&&":"===e[t-3]||t>=3&&"/"===e[t-3]?0:s.match(n.re.no_http)[0].length:0}},"mailto:":{validate:function(e,t,n){const s=e.slice(t);return n.re.mailto||(n.re.mailto=new RegExp("^"+n.re.src_email_name+"@"+n.re.src_host_strict,"i")),n.re.mailto.test(s)?s.match(n.re.mailto)[0].length:0}}},hl="a[cdefgilmnoqrstuwxz]|b[abdefghijmnorstvwyz]|c[acdfghiklmnoruvwxyz]|d[ejkmoz]|e[cegrstu]|f[ijkmor]|g[abdefghilmnpqrstuwy]|h[kmnrtu]|i[delmnoqrst]|j[emop]|k[eghimnprwyz]|l[abcikrstuvy]|m[acdeghklmnopqrstuvwxyz]|n[acefgilopruz]|om|p[aefghklmnrstwy]|qa|r[eosuw]|s[abcdeghijklmnortuvxyz]|t[cdfghjklmnortvwz]|u[agksyz]|v[aceginu]|w[fs]|y[et]|z[amw]",dl="biz|com|edu|gov|net|org|pro|web|xxx|aero|asia|coop|info|museum|name|shop|рф".split("|");function pl(e){const t=e.re=function(e){const t={};e=e||{},t.src_Any=va.source,t.src_Cc=xa.source,t.src_Z=Fa.source,t.src_P=wa.source,t.src_ZPCc=[t.src_Z,t.src_P,t.src_Cc].join("|"),t.src_ZCc=[t.src_Z,t.src_Cc].join("|");const n="[><｜]";return t.src_pseudo_letter="(?:(?![><｜]|"+t.src_ZPCc+")"+t.src_Any+")",t.src_ip4="(?:(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)",t.src_auth="(?:(?:(?!"+t.src_ZCc+"|[@/\\[\\]()]).)+@)?",t.src_port="(?::(?:6(?:[0-4]\\d{3}|5(?:[0-4]\\d{2}|5(?:[0-2]\\d|3[0-5])))|[1-5]?\\d{1,4}))?",t.src_host_terminator="(?=$|[><｜]|"+t.src_ZPCc+")(?!"+(e["---"]?"-(?!--)|":"-|")+"_|:\\d|\\.-|\\.(?!$|"+t.src_ZPCc+"))",t.src_path="(?:[/?#](?:(?!"+t.src_ZCc+"|"+n+"|[()[\\]{}.,\"'?!\\-;]).|\\[(?:(?!"+t.src_ZCc+"|\\]).)*\\]|\\((?:(?!"+t.src_ZCc+"|[)]).)*\\)|\\{(?:(?!"+t.src_ZCc+'|[}]).)*\\}|\\"(?:(?!'+t.src_ZCc+'|["]).)+\\"|\\\'(?:(?!'+t.src_ZCc+"|[']).)+\\'|\\'(?="+t.src_pseudo_letter+"|[-])|\\.{2,}[a-zA-Z0-9%/&]|\\.(?!"+t.src_ZCc+"|[.]|$)|"+(e["---"]?"\\-(?!--(?:[^-]|$))(?:-*)|":"\\-+|")+",(?!"+t.src_ZCc+"|$)|;(?!"+t.src_ZCc+"|$)|\\!+(?!"+t.src_ZCc+"|[!]|$)|\\?(?!"+t.src_ZCc+"|[?]|$))+|\\/)?",t.src_email_name='[\\-;:&=\\+\\$,\\.a-zA-Z0-9_][\\-;:&=\\+\\$,\\"\\.a-zA-Z0-9_]*',t.src_xn="xn--[a-z0-9\\-]{1,59}",t.src_domain_root="(?:"+t.src_xn+"|"+t.src_pseudo_letter+"{1,63})",t.src_domain="(?:"+t.src_xn+"|(?:"+t.src_pseudo_letter+")|(?:"+t.src_pseudo_letter+"(?:-|"+t.src_pseudo_letter+"){0,61}"+t.src_pseudo_letter+"))",t.src_host="(?:(?:(?:(?:"+t.src_domain+")\\.)*"+t.src_domain+"))",t.tpl_host_fuzzy="(?:"+t.src_ip4+"|(?:(?:(?:"+t.src_domain+")\\.)+(?:%TLDS%)))",t.tpl_host_no_ip_fuzzy="(?:(?:(?:"+t.src_domain+")\\.)+(?:%TLDS%))",t.src_host_strict=t.src_host+t.src_host_terminator,t.tpl_host_fuzzy_strict=t.tpl_host_fuzzy+t.src_host_terminator,t.src_host_port_strict=t.src_host+t.src_port+t.src_host_terminator,t.tpl_host_port_fuzzy_strict=t.tpl_host_fuzzy+t.src_port+t.src_host_terminator,t.tpl_host_port_no_ip_fuzzy_strict=t.tpl_host_no_ip_fuzzy+t.src_port+t.src_host_terminator,t.tpl_host_fuzzy_test="localhost|www\\.|\\.\\d{1,3}\\.|(?:\\.(?:%TLDS%)(?:"+t.src_ZPCc+"|>|$))",t.tpl_email_fuzzy='(^|[><｜]|"|\\(|'+t.src_ZCc+")("+t.src_email_name+"@"+t.tpl_host_fuzzy_strict+")",t.tpl_link_fuzzy="(^|(?![.:/\\-_@])(?:[$+<=>^`|｜]|"+t.src_ZPCc+"))((?![$+<=>^`|｜])"+t.tpl_host_port_fuzzy_strict+t.src_path+")",t.tpl_link_no_ip_fuzzy="(^|(?![.:/\\-_@])(?:[$+<=>^`|｜]|"+t.src_ZPCc+"))((?![$+<=>^`|｜])"+t.tpl_host_port_no_ip_fuzzy_strict+t.src_path+")",t}(e.__opts__),n=e.__tlds__.slice();function s(e){return e.replace("%TLDS%",t.src_tlds)}e.onCompile(),e.__tlds_replaced__||n.push(hl),n.push(t.src_xn),t.src_tlds=n.join("|"),t.email_fuzzy=RegExp(s(t.tpl_email_fuzzy),"i"),t.link_fuzzy=RegExp(s(t.tpl_link_fuzzy),"i"),t.link_no_ip_fuzzy=RegExp(s(t.tpl_link_no_ip_fuzzy),"i"),t.host_fuzzy_test=RegExp(s(t.tpl_host_fuzzy_test),"i");const r=[];function i(e,t){throw new Error('(LinkifyIt) Invalid schema "'+e+'": '+t)}e.__compiled__={},Object.keys(e.__schemas__).forEach((function(t){const n=e.__schemas__[t];if(null===n)return;const s={validate:null,link:null};if(e.__compiled__[t]=s,"[object Object]"===ol(n))return!function(e){return"[object RegExp]"===ol(e)}(n.validate)?al(n.validate)?s.validate=n.validate:i(t,n):s.validate=function(e){return function(t,n){const s=t.slice(n);return e.test(s)?s.match(e)[0].length:0}}(n.validate),void(al(n.normalize)?s.normalize=n.normalize:n.normalize?i(t,n):s.normalize=function(e,t){t.normalize(e)});!function(e){return"[object String]"===ol(e)}(n)?i(t,n):r.push(t)})),r.forEach((function(t){e.__compiled__[e.__schemas__[t]]&&(e.__compiled__[t].validate=e.__compiled__[e.__schemas__[t]].validate,e.__compiled__[t].normalize=e.__compiled__[e.__schemas__[t]].normalize)})),e.__compiled__[""]={validate:null,normalize:function(e,t){t.normalize(e)}};const o=Object.keys(e.__compiled__).filter((function(t){return t.length>0&&e.__compiled__[t]})).map(cl).join("|");e.re.schema_test=RegExp("(^|(?!_)(?:[><｜]|"+t.src_ZPCc+"))("+o+")","i"),e.re.schema_search=RegExp("(^|(?!_)(?:[><｜]|"+t.src_ZPCc+"))("+o+")","ig"),e.re.schema_at_start=RegExp("^"+e.re.schema_search.source,"i"),e.re.pretest=RegExp("("+e.re.schema_test.source+")|("+e.re.host_fuzzy_test.source+")|@","i"),function(e){e.__index__=-1,e.__text_cache__=""}(e)}function fl(e,t){const n=e.__index__,s=e.__last_index__,r=e.__text_cache__.slice(n,s);this.schema=e.__schema__.toLowerCase(),this.index=n+t,this.lastIndex=s+t,this.raw=r,this.text=r,this.url=r}function ml(e,t){const n=new fl(e,t);return e.__compiled__[n.schema].normalize(n,e),n}function El(e,t){if(!(this instanceof El))return new El(e,t);var n;t||(n=e,Object.keys(n||{}).reduce((function(e,t){return e||ll.hasOwnProperty(t)}),!1)&&(t=e,e={})),this.__opts__=il({},ll,t),this.__index__=-1,this.__last_index__=-1,this.__schema__="",this.__text_cache__="",this.__schemas__=il({},ul,e),this.__compiled__={},this.__tlds__=dl,this.__tlds_replaced__=!1,this.re={},pl(this)}El.prototype.add=function(e,t){return this.__schemas__[e]=t,pl(this),this},El.prototype.set=function(e){return this.__opts__=il(this.__opts__,e),this},El.prototype.test=function(e){if(this.__text_cache__=e,this.__index__=-1,!e.length)return!1;let t,n,s,r,i,o,a,c,l;if(this.re.schema_test.test(e))for(a=this.re.schema_search,a.lastIndex=0;null!==(t=a.exec(e));)if(r=this.testSchemaAt(e,t[2],a.lastIndex),r){this.__schema__=t[2],this.__index__=t.index+t[1].length,this.__last_index__=t.index+t[0].length+r;break}return this.__opts__.fuzzyLink&&this.__compiled__["http:"]&&(c=e.search(this.re.host_fuzzy_test),c>=0&&(this.__index__<0||c<this.__index__)&&null!==(n=e.match(this.__opts__.fuzzyIP?this.re.link_fuzzy:this.re.link_no_ip_fuzzy))&&(i=n.index+n[1].length,(this.__index__<0||i<this.__index__)&&(this.__schema__="",this.__index__=i,this.__last_index__=n.index+n[0].length))),this.__opts__.fuzzyEmail&&this.__compiled__["mailto:"]&&(l=e.indexOf("@"),l>=0&&null!==(s=e.match(this.re.email_fuzzy))&&(i=s.index+s[1].length,o=s.index+s[0].length,(this.__index__<0||i<this.__index__||i===this.__index__&&o>this.__last_index__)&&(this.__schema__="mailto:",this.__index__=i,this.__last_index__=o))),this.__index__>=0},El.prototype.pretest=function(e){return this.re.pretest.test(e)},El.prototype.testSchemaAt=function(e,t,n){return this.__compiled__[t.toLowerCase()]?this.__compiled__[t.toLowerCase()].validate(e,n,this):0},El.prototype.match=function(e){const t=[];let n=0;this.__index__>=0&&this.__text_cache__===e&&(t.push(ml(this,n)),n=this.__last_index__);let s=n?e.slice(n):e;for(;this.test(s);)t.push(ml(this,n)),s=s.slice(this.__last_index__),n+=this.__last_index__;return t.length?t:null},El.prototype.matchAtStart=function(e){if(this.__text_cache__=e,this.__index__=-1,!e.length)return null;const t=this.re.schema_at_start.exec(e);if(!t)return null;const n=this.testSchemaAt(e,t[2],t[0].length);return n?(this.__schema__=t[2],this.__index__=t.index+t[1].length,this.__last_index__=t.index+t[0].length+n,ml(this,0)):null},El.prototype.tlds=function(e,t){return e=Array.isArray(e)?e:[e],t?(this.__tlds__=this.__tlds__.concat(e).sort().filter((function(e,t,n){return e!==n[t-1]})).reverse(),pl(this),this):(this.__tlds__=e.slice(),this.__tlds_replaced__=!0,pl(this),this)},El.prototype.normalize=function(e){e.schema||(e.url="http://"+e.url),"mailto:"!==e.schema||/^mailto:/i.test(e.url)||(e.url="mailto:"+e.url)},El.prototype.onCompile=function(){};const Tl=2147483647,_l=36,Al=/^xn--/,gl=/[^\0-\x7F]/,Cl=/[\x2E\u3002\uFF0E\uFF61]/g,bl={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},Nl=Math.floor,Il=String.fromCharCode;function kl(e){throw new RangeError(bl[e])}function Sl(e,t){const n=e.split("@");let s="";n.length>1&&(s=n[0]+"@",e=n[1]);const r=function(e,t){const n=[];let s=e.length;for(;s--;)n[s]=t(e[s]);return n}((e=e.replace(Cl,".")).split("."),t).join(".");return s+r}function Dl(e){const t=[];let n=0;const s=e.length;for(;n<s;){const r=e.charCodeAt(n++);if(r>=55296&&r<=56319&&n<s){const s=e.charCodeAt(n++);56320==(64512&s)?t.push(((1023&r)<<10)+(1023&s)+65536):(t.push(r),n--)}else t.push(r)}return t}const yl=function(e,t){return e+22+75*(e<26)-((0!=t)<<5)},Ol=function(e,t,n){let s=0;for(e=n?Nl(e/700):e>>1,e+=Nl(e/t);e>455;s+=_l)e=Nl(e/35);return Nl(s+36*e/(e+38))},Ll=function(e){const t=[],n=e.length;let s=0,r=128,i=72,o=e.lastIndexOf("-");o<0&&(o=0);for(let n=0;n<o;++n)e.charCodeAt(n)>=128&&kl("not-basic"),t.push(e.charCodeAt(n));for(let c=o>0?o+1:0;c<n;){const o=s;for(let t=1,r=_l;;r+=_l){c>=n&&kl("invalid-input");const o=(a=e.charCodeAt(c++))>=48&&a<58?a-48+26:a>=65&&a<91?a-65:a>=97&&a<123?a-97:_l;o>=_l&&kl("invalid-input"),o>Nl((Tl-s)/t)&&kl("overflow"),s+=o*t;const l=r<=i?1:r>=i+26?26:r-i;if(o<l)break;const u=_l-l;t>Nl(Tl/u)&&kl("overflow"),t*=u}const l=t.length+1;i=Ol(s-o,l,0==o),Nl(s/l)>Tl-r&&kl("overflow"),r+=Nl(s/l),s%=l,t.splice(s++,0,r)}var a;return String.fromCodePoint(...t)},Rl=function(e){const t=[],n=(e=Dl(e)).length;let s=128,r=0,i=72;for(const n of e)n<128&&t.push(Il(n));const o=t.length;let a=o;for(o&&t.push("-");a<n;){let n=Tl;for(const t of e)t>=s&&t<n&&(n=t);const c=a+1;n-s>Nl((Tl-r)/c)&&kl("overflow"),r+=(n-s)*c,s=n;for(const n of e)if(n<s&&++r>Tl&&kl("overflow"),n===s){let e=r;for(let n=_l;;n+=_l){const s=n<=i?1:n>=i+26?26:n-i;if(e<s)break;const r=e-s,o=_l-s;t.push(Il(yl(s+r%o,0))),e=Nl(r/o)}t.push(Il(yl(e,0))),i=Ol(r,c,a===o),r=0,++a}++r,++s}return t.join("")},Ml={version:"2.3.1",ucs2:{decode:Dl,encode:e=>String.fromCodePoint(...e)},decode:Ll,encode:Rl,toASCII:function(e){return Sl(e,(function(e){return gl.test(e)?"xn--"+Rl(e):e}))},toUnicode:function(e){return Sl(e,(function(e){return Al.test(e)?Ll(e.slice(4).toLowerCase()):e}))}},vl={default:{options:{html:!1,xhtmlOut:!1,breaks:!1,langPrefix:"language-",linkify:!1,typographer:!1,quotes:"“”‘’",highlight:null,maxNesting:100},components:{core:{},block:{},inline:{}}},zero:{options:{html:!1,xhtmlOut:!1,breaks:!1,langPrefix:"language-",linkify:!1,typographer:!1,quotes:"“”‘’",highlight:null,maxNesting:20},components:{core:{rules:["normalize","block","inline","text_join"]},block:{rules:["paragraph"]},inline:{rules:["text"],rules2:["balance_pairs","fragments_join"]}}},commonmark:{options:{html:!0,xhtmlOut:!0,breaks:!1,langPrefix:"language-",linkify:!1,typographer:!1,quotes:"“”‘’",highlight:null,maxNesting:20},components:{core:{rules:["normalize","block","inline","text_join"]},block:{rules:["blockquote","code","fence","heading","hr","html_block","lheading","list","reference","paragraph"]},inline:{rules:["autolink","backticks","emphasis","entity","escape","html_inline","image","link","newline","text"],rules2:["balance_pairs","emphasis","fragments_join"]}}}},xl=/^(vbscript|javascript|file|data):/,wl=/^data:image\/(gif|png|jpeg|webp);/;function Pl(e){const t=e.trim().toLowerCase();return!xl.test(t)||wl.test(t)}const Fl=["http:","https:","mailto:"];function Bl(e){const t=Ra(e,!0);if(t.hostname&&(!t.protocol||Fl.indexOf(t.protocol)>=0))try{t.hostname=Ml.toASCII(t.hostname)}catch(e){}return Ta(_a(t))}function Ul(e){const t=Ra(e,!0);if(t.hostname&&(!t.protocol||Fl.indexOf(t.protocol)>=0))try{t.hostname=Ml.toUnicode(t.hostname)}catch(e){}return ma(_a(t),ma.defaultChars+"%")}function Hl(e,t){if(!(this instanceof Hl))return new Hl(e,t);t||Ua(e)||(t=e||{},e="default"),this.inline=new rl,this.block=new qc,this.core=new Lc,this.renderer=new uc,this.linkify=new El,this.validateLink=Pl,this.normalizeLink=Bl,this.normalizeLinkText=Ul,this.utils=ac,this.helpers=Ga({},cc),this.options={},this.configure(e),t&&this.set(t)}function Gl(e){function t(e,t){let n;const s=[],r=t.length;for(let i=0;i<r;i++){const r=t[i];if(43!==r.marker)continue;if(-1===r.end)continue;const o=t[r.end];n=e.tokens[r.token],n.type="ins_open",n.tag="ins",n.nesting=1,n.markup="++",n.content="",n=e.tokens[o.token],n.type="ins_close",n.tag="ins",n.nesting=-1,n.markup="++",n.content="","text"===e.tokens[o.token-1].type&&"+"===e.tokens[o.token-1].content&&s.push(o.token-1)}for(;s.length;){const t=s.pop();let r=t+1;for(;r<e.tokens.length&&"ins_close"===e.tokens[r].type;)r++;r--,t!==r&&(n=e.tokens[r],e.tokens[r]=e.tokens[t],e.tokens[t]=n)}}e.inline.ruler.before("emphasis","ins",(function(e,t){const n=e.pos,s=e.src.charCodeAt(n);if(t)return!1;if(43!==s)return!1;const r=e.scanDelims(e.pos,!0);let i=r.length;const o=String.fromCharCode(s);if(i<2)return!1;if(i%2){e.push("text","",0).content=o,i--}for(let t=0;t<i;t+=2){e.push("text","",0).content=o+o,(r.can_open||r.can_close)&&e.delimiters.push({marker:s,length:0,jump:t/2,token:e.tokens.length-1,end:-1,open:r.can_open,close:r.can_close})}return e.pos+=r.length,!0})),e.inline.ruler2.before("emphasis","ins",(function(e){const n=e.tokens_meta,s=(e.tokens_meta||[]).length;t(e,e.delimiters);for(let r=0;r<s;r++)n[r]&&n[r].delimiters&&t(e,n[r].delimiters)}))}function ql(e){function t(e,t){const n=[],s=t.length;for(let r=0;r<s;r++){const s=t[r];if(61!==s.marker)continue;if(-1===s.end)continue;const i=t[s.end],o=e.tokens[s.token];o.type="mark_open",o.tag="mark",o.nesting=1,o.markup="==",o.content="";const a=e.tokens[i.token];a.type="mark_close",a.tag="mark",a.nesting=-1,a.markup="==",a.content="","text"===e.tokens[i.token-1].type&&"="===e.tokens[i.token-1].content&&n.push(i.token-1)}for(;n.length;){const t=n.pop();let s=t+1;for(;s<e.tokens.length&&"mark_close"===e.tokens[s].type;)s++;if(s--,t!==s){const n=e.tokens[s];e.tokens[s]=e.tokens[t],e.tokens[t]=n}}}e.inline.ruler.before("emphasis","mark",(function(e,t){const n=e.pos,s=e.src.charCodeAt(n);if(t)return!1;if(61!==s)return!1;const r=e.scanDelims(e.pos,!0);let i=r.length;const o=String.fromCharCode(s);if(i<2)return!1;if(i%2){e.push("text","",0).content=o,i--}for(let t=0;t<i;t+=2){e.push("text","",0).content=o+o,(r.can_open||r.can_close)&&e.delimiters.push({marker:s,length:0,jump:t/2,token:e.tokens.length-1,end:-1,open:r.can_open,close:r.can_close})}return e.pos+=r.length,!0})),e.inline.ruler2.before("emphasis","mark",(function(e){let n;const s=e.tokens_meta,r=(e.tokens_meta||[]).length;for(t(e,e.delimiters),n=0;n<r;n++)s[n]&&s[n].delimiters&&t(e,s[n].delimiters)}))}Hl.prototype.set=function(e){return Ga(this.options,e),this},Hl.prototype.configure=function(e){const t=this;if(Ua(e)){const t=e;if(!(e=vl[t]))throw new Error('Wrong `markdown-it` preset "'+t+'", check name')}if(!e)throw new Error("Wrong `markdown-it` preset, can't be empty");return e.options&&t.set(e.options),e.components&&Object.keys(e.components).forEach((function(n){e.components[n].rules&&t[n].ruler.enableOnly(e.components[n].rules),e.components[n].rules2&&t[n].ruler2.enableOnly(e.components[n].rules2)})),this},Hl.prototype.enable=function(e,t){let n=[];Array.isArray(e)||(e=[e]),["core","block","inline"].forEach((function(t){n=n.concat(this[t].ruler.enable(e,!0))}),this),n=n.concat(this.inline.ruler2.enable(e,!0));const s=e.filter((function(e){return n.indexOf(e)<0}));if(s.length&&!t)throw new Error("MarkdownIt. Failed to enable unknown rule(s): "+s);return this},Hl.prototype.disable=function(e,t){let n=[];Array.isArray(e)||(e=[e]),["core","block","inline"].forEach((function(t){n=n.concat(this[t].ruler.disable(e,!0))}),this),n=n.concat(this.inline.ruler2.disable(e,!0));const s=e.filter((function(e){return n.indexOf(e)<0}));if(s.length&&!t)throw new Error("MarkdownIt. Failed to disable unknown rule(s): "+s);return this},Hl.prototype.use=function(e){const t=[this].concat(Array.prototype.slice.call(arguments,1));return e.apply(e,t),this},Hl.prototype.parse=function(e,t){if("string"!=typeof e)throw new Error("Input data should be a String");const n=new this.core.State(e,this,t);return this.core.process(n),n.tokens},Hl.prototype.render=function(e,t){return t=t||{},this.renderer.render(this.parse(e,t),this.options,t)},Hl.prototype.parseInline=function(e,t){const n=new this.core.State(e,this,t);return n.inlineMode=!0,this.core.process(n),n.tokens},Hl.prototype.renderInline=function(e,t){return t=t||{},this.renderer.render(this.parseInline(e,t),this.options,t)};const $l=/\\([ \\!"#$%&'()*+,./:;<=>?@[\]^_`{|}~-])/g;function Yl(e,t){const n=e.posMax,s=e.pos;if(126!==e.src.charCodeAt(s))return!1;if(t)return!1;if(s+2>=n)return!1;e.pos=s+1;let r=!1;for(;e.pos<n;){if(126===e.src.charCodeAt(e.pos)){r=!0;break}e.md.inline.skipToken(e)}if(!r||s+1===e.pos)return e.pos=s,!1;const i=e.src.slice(s+1,e.pos);if(i.match(/(^|[^\\])(\\\\)*\s/))return e.pos=s,!1;e.posMax=e.pos,e.pos=s+1;e.push("sub_open","sub",1).markup="~";e.push("text","",0).content=i.replace($l,"$1");return e.push("sub_close","sub",-1).markup="~",e.pos=e.posMax+1,e.posMax=n,!0}function jl(e){e.inline.ruler.after("emphasis","sub",Yl)}const Kl=/\\([ \\!"#$%&'()*+,./:;<=>?@[\]^_`{|}~-])/g;function Vl(e,t){const n=e.posMax,s=e.pos;if(94!==e.src.charCodeAt(s))return!1;if(t)return!1;if(s+2>=n)return!1;e.pos=s+1;let r=!1;for(;e.pos<n;){if(94===e.src.charCodeAt(e.pos)){r=!0;break}e.md.inline.skipToken(e)}if(!r||s+1===e.pos)return e.pos=s,!1;const i=e.src.slice(s+1,e.pos);if(i.match(/(^|[^\\])(\\\\)*\s/))return e.pos=s,!1;e.posMax=e.pos,e.pos=s+1;e.push("sup_open","sup",1).markup="^";e.push("text","",0).content=i.replace(Kl,"$1");return e.push("sup_close","sup",-1).markup="^",e.pos=e.posMax+1,e.posMax=n,!0}function zl(e){e.inline.ruler.after("emphasis","sup",Vl)}const Ql={" ":'<svg width="16" height="16" viewBox="0 -3 24 24"><path fill-rule="evenodd" d="M6 5a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V6a1 1 0 0 0-1-1zM3 6a3 3 0 0 1 3-3h12a3 3 0 0 1 3 3v12a3 3 0 0 1-3 3H6a3 3 0 0 1-3-3v-5z" clip-rule="evenodd"/></svg>\n'.trim(),x:'<svg width="16" height="16" viewBox="0 -3 24 24"><path d="M19 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V5a2 2 0 0 0-2-2m-9 14-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8z"/></svg>\n'.trim()},Wl={name:"checkbox",transform:e=>(e.parser.tap((e=>{e.core.ruler.before("inline","checkbox",(e=>{for(let t=2;t<e.tokens.length;t+=1){const n=e.tokens[t];if("inline"===n.type&&n.content){const s=e.tokens[t-1].type,r=e.tokens[t-2].type;("heading_open"===s||"paragraph_open"===s&&"list_item_open"===r)&&(n.content=n.content.replace(/^\[(.)\] /,((e,t)=>Ql[t]?`${Ql[t]} `:e)))}}return!1}))})),{})},Xl=Symbol.for("yaml.alias"),Zl=Symbol.for("yaml.document"),Jl=Symbol.for("yaml.map"),eu=Symbol.for("yaml.pair"),tu=Symbol.for("yaml.scalar"),nu=Symbol.for("yaml.seq"),su=Symbol.for("yaml.node.type"),ru=e=>!!e&&"object"==typeof e&&e[su]===Xl,iu=e=>!!e&&"object"==typeof e&&e[su]===Zl,ou=e=>!!e&&"object"==typeof e&&e[su]===Jl,au=e=>!!e&&"object"==typeof e&&e[su]===eu,cu=e=>!!e&&"object"==typeof e&&e[su]===tu,lu=e=>!!e&&"object"==typeof e&&e[su]===nu;function uu(e){if(e&&"object"==typeof e)switch(e[su]){case Jl:case nu:return!0}return!1}function hu(e){if(e&&"object"==typeof e)switch(e[su]){case Xl:case Jl:case tu:case nu:return!0}return!1}const du=e=>(cu(e)||uu(e))&&!!e.anchor,pu=Symbol("break visit"),fu=Symbol("skip children"),mu=Symbol("remove node");function Eu(e,t){const n=function(e){if("object"==typeof e&&(e.Collection||e.Node||e.Value))return Object.assign({Alias:e.Node,Map:e.Node,Scalar:e.Node,Seq:e.Node},e.Value&&{Map:e.Value,Scalar:e.Value,Seq:e.Value},e.Collection&&{Map:e.Collection,Seq:e.Collection},e);return e}(t);if(iu(e)){Tu(null,e.contents,n,Object.freeze([e]))===mu&&(e.contents=null)}else Tu(null,e,n,Object.freeze([]))}function Tu(e,t,n,s){const r=function(e,t,n,s){var r,i,o,a,c;return"function"==typeof n?n(e,t,s):ou(t)?null==(r=n.Map)?void 0:r.call(n,e,t,s):lu(t)?null==(i=n.Seq)?void 0:i.call(n,e,t,s):au(t)?null==(o=n.Pair)?void 0:o.call(n,e,t,s):cu(t)?null==(a=n.Scalar)?void 0:a.call(n,e,t,s):ru(t)?null==(c=n.Alias)?void 0:c.call(n,e,t,s):void 0}(e,t,n,s);if(hu(r)||au(r))return function(e,t,n){const s=t[t.length-1];if(uu(s))s.items[e]=n;else if(au(s))"key"===e?s.key=n:s.value=n;else{if(!iu(s)){const e=ru(s)?"alias":"scalar";throw new Error(`Cannot replace node with ${e} parent`)}s.contents=n}}(e,s,r),Tu(e,r,n,s);if("symbol"!=typeof r)if(uu(t)){s=Object.freeze(s.concat(t));for(let e=0;e<t.items.length;++e){const r=Tu(e,t.items[e],n,s);if("number"==typeof r)e=r-1;else{if(r===pu)return pu;r===mu&&(t.items.splice(e,1),e-=1)}}}else if(au(t)){s=Object.freeze(s.concat(t));const e=Tu("key",t.key,n,s);if(e===pu)return pu;e===mu&&(t.key=null);const r=Tu("value",t.value,n,s);if(r===pu)return pu;r===mu&&(t.value=null)}return r}Eu.BREAK=pu,Eu.SKIP=fu,Eu.REMOVE=mu;const _u={"!":"%21",",":"%2C","[":"%5B","]":"%5D","{":"%7B","}":"%7D"};class Au{constructor(e,t){this.docStart=null,this.docEnd=!1,this.yaml=Object.assign({},Au.defaultYaml,e),this.tags=Object.assign({},Au.defaultTags,t)}clone(){const e=new Au(this.yaml,this.tags);return e.docStart=this.docStart,e}atDocument(){const e=new Au(this.yaml,this.tags);switch(this.yaml.version){case"1.1":this.atNextDocument=!0;break;case"1.2":this.atNextDocument=!1,this.yaml={explicit:Au.defaultYaml.explicit,version:"1.2"},this.tags=Object.assign({},Au.defaultTags)}return e}add(e,t){this.atNextDocument&&(this.yaml={explicit:Au.defaultYaml.explicit,version:"1.1"},this.tags=Object.assign({},Au.defaultTags),this.atNextDocument=!1);const n=e.trim().split(/[ \t]+/),s=n.shift();switch(s){case"%TAG":{if(2!==n.length&&(t(0,"%TAG directive should contain exactly two parts"),n.length<2))return!1;const[e,s]=n;return this.tags[e]=s,!0}case"%YAML":{if(this.yaml.explicit=!0,1!==n.length)return t(0,"%YAML directive should contain exactly one part"),!1;const[e]=n;if("1.1"===e||"1.2"===e)return this.yaml.version=e,!0;return t(6,`Unsupported YAML version ${e}`,/^\d+\.\d+$/.test(e)),!1}default:return t(0,`Unknown directive ${s}`,!0),!1}}tagName(e,t){if("!"===e)return"!";if("!"!==e[0])return t(`Not a valid tag: ${e}`),null;if("<"===e[1]){const n=e.slice(2,-1);return"!"===n||"!!"===n?(t(`Verbatim tags aren't resolved, so ${e} is invalid.`),null):(">"!==e[e.length-1]&&t("Verbatim tags must end with a >"),n)}const[,n,s]=e.match(/^(.*!)([^!]*)$/s);s||t(`The ${e} tag has no suffix`);const r=this.tags[n];if(r)try{return r+decodeURIComponent(s)}catch(e){return t(String(e)),null}return"!"===n?e:(t(`Could not resolve tag: ${e}`),null)}tagString(e){for(const[t,n]of Object.entries(this.tags))if(e.startsWith(n))return t+e.substring(n.length).replace(/[!,[\]{}]/g,(e=>_u[e]));return"!"===e[0]?e:`!<${e}>`}toString(e){const t=this.yaml.explicit?[`%YAML ${this.yaml.version||"1.2"}`]:[],n=Object.entries(this.tags);let s;if(e&&n.length>0&&hu(e.contents)){const t={};Eu(e.contents,((e,n)=>{hu(n)&&n.tag&&(t[n.tag]=!0)})),s=Object.keys(t)}else s=[];for(const[r,i]of n)"!!"===r&&"tag:yaml.org,2002:"===i||e&&!s.some((e=>e.startsWith(i)))||t.push(`%TAG ${r} ${i}`);return t.join("\n")}}function gu(e){if(/[\x00-\x19\s,[\]{}]/.test(e)){const t=JSON.stringify(e);throw new Error(`Anchor must not contain whitespace or control characters: ${t}`)}return!0}function Cu(e){const t=new Set;return Eu(e,{Value(e,n){n.anchor&&t.add(n.anchor)}}),t}function bu(e,t){for(let n=1;;++n){const s=`${e}${n}`;if(!t.has(s))return s}}function Nu(e,t,n,s){if(s&&"object"==typeof s)if(Array.isArray(s))for(let t=0,n=s.length;t<n;++t){const n=s[t],r=Nu(e,s,String(t),n);void 0===r?delete s[t]:r!==n&&(s[t]=r)}else if(s instanceof Map)for(const t of Array.from(s.keys())){const n=s.get(t),r=Nu(e,s,t,n);void 0===r?s.delete(t):r!==n&&s.set(t,r)}else if(s instanceof Set)for(const t of Array.from(s)){const n=Nu(e,s,t,t);void 0===n?s.delete(t):n!==t&&(s.delete(t),s.add(n))}else for(const[t,n]of Object.entries(s)){const r=Nu(e,s,t,n);void 0===r?delete s[t]:r!==n&&(s[t]=r)}return e.call(t,n,s)}function Iu(e,t,n){if(Array.isArray(e))return e.map(((e,t)=>Iu(e,String(t),n)));if(e&&"function"==typeof e.toJSON){if(!n||!du(e))return e.toJSON(t,n);const s={aliasCount:0,count:1,res:void 0};n.anchors.set(e,s),n.onCreate=e=>{s.res=e,delete n.onCreate};const r=e.toJSON(t,n);return n.onCreate&&n.onCreate(r),r}return"bigint"!=typeof e||(null==n?void 0:n.keep)?e:Number(e)}Au.defaultYaml={explicit:!1,version:"1.2"},Au.defaultTags={"!!":"tag:yaml.org,2002:"};class ku{constructor(e){Object.defineProperty(this,su,{value:e})}clone(){const e=Object.create(Object.getPrototypeOf(this),Object.getOwnPropertyDescriptors(this));return this.range&&(e.range=this.range.slice()),e}toJS(e,{mapAsMap:t,maxAliasCount:n,onAnchor:s,reviver:r}={}){if(!iu(e))throw new TypeError("A document argument is required");const i={anchors:new Map,doc:e,keep:!0,mapAsMap:!0===t,mapKeyWarned:!1,maxAliasCount:"number"==typeof n?n:100},o=Iu(this,"",i);if("function"==typeof s)for(const{count:e,res:t}of i.anchors.values())s(t,e);return"function"==typeof r?Nu(r,{"":o},"",o):o}}class Su extends ku{constructor(e){super(Xl),this.source=e,Object.defineProperty(this,"tag",{set(){throw new Error("Alias nodes cannot have tags")}})}resolve(e){let t;return Eu(e,{Node:(e,n)=>{if(n===this)return Eu.BREAK;n.anchor===this.source&&(t=n)}}),t}toJSON(e,t){if(!t)return{source:this.source};const{anchors:n,doc:s,maxAliasCount:r}=t,i=this.resolve(s);if(!i){const e=`Unresolved alias (the anchor must be set before the alias): ${this.source}`;throw new ReferenceError(e)}let o=n.get(i);if(o||(Iu(i,null,t),o=n.get(i)),!o||void 0===o.res){throw new ReferenceError("This should not happen: Alias anchor was not resolved?")}if(r>=0&&(o.count+=1,0===o.aliasCount&&(o.aliasCount=Du(s,i,n)),o.count*o.aliasCount>r)){throw new ReferenceError("Excessive alias count indicates a resource exhaustion attack")}return o.res}toString(e,t,n){const s=`*${this.source}`;if(e){if(gu(this.source),e.options.verifyAliasOrder&&!e.anchors.has(this.source)){const e=`Unresolved alias (the anchor must be set before the alias): ${this.source}`;throw new Error(e)}if(e.implicitKey)return`${s} `}return s}}function Du(e,t,n){if(ru(t)){const s=t.resolve(e),r=n&&s&&n.get(s);return r?r.count*r.aliasCount:0}if(uu(t)){let s=0;for(const r of t.items){const t=Du(e,r,n);t>s&&(s=t)}return s}if(au(t)){const s=Du(e,t.key,n),r=Du(e,t.value,n);return Math.max(s,r)}return 1}const yu=e=>!e||"function"!=typeof e&&"object"!=typeof e;class Ou extends ku{constructor(e){super(tu),this.value=e}toJSON(e,t){return(null==t?void 0:t.keep)?this.value:Iu(this.value,e,t)}toString(){return String(this.value)}}Ou.BLOCK_FOLDED="BLOCK_FOLDED",Ou.BLOCK_LITERAL="BLOCK_LITERAL",Ou.PLAIN="PLAIN",Ou.QUOTE_DOUBLE="QUOTE_DOUBLE",Ou.QUOTE_SINGLE="QUOTE_SINGLE";const Lu="tag:yaml.org,2002:";function Ru(e,t,n){var s,r,i;if(iu(e)&&(e=e.contents),hu(e))return e;if(au(e)){const t=null==(r=(s=n.schema[Jl]).createNode)?void 0:r.call(s,n.schema,null,n);return t.items.push(e),t}(e instanceof String||e instanceof Number||e instanceof Boolean||"undefined"!=typeof BigInt&&e instanceof BigInt)&&(e=e.valueOf());const{aliasDuplicateObjects:o,onAnchor:a,onTagObj:c,schema:l,sourceObjects:u}=n;let h;if(o&&e&&"object"==typeof e){if(h=u.get(e),h)return h.anchor||(h.anchor=a(e)),new Su(h.anchor);h={anchor:null,node:null},u.set(e,h)}(null==t?void 0:t.startsWith("!!"))&&(t=Lu+t.slice(2));let d=function(e,t,n){if(t){const e=n.filter((e=>e.tag===t)),s=e.find((e=>!e.format))??e[0];if(!s)throw new Error(`Tag ${t} not found`);return s}return n.find((t=>{var n;return(null==(n=t.identify)?void 0:n.call(t,e))&&!t.format}))}(e,t,l.tags);if(!d){if(e&&"function"==typeof e.toJSON&&(e=e.toJSON()),!e||"object"!=typeof e){const t=new Ou(e);return h&&(h.node=t),t}d=e instanceof Map?l[Jl]:Symbol.iterator in Object(e)?l[nu]:l[Jl]}c&&(c(d),delete n.onTagObj);const p=(null==d?void 0:d.createNode)?d.createNode(n.schema,e,n):"function"==typeof(null==(i=null==d?void 0:d.nodeClass)?void 0:i.from)?d.nodeClass.from(n.schema,e,n):new Ou(e);return t?p.tag=t:d.default||(p.tag=d.tag),h&&(h.node=p),p}function Mu(e,t,n){let s=n;for(let e=t.length-1;e>=0;--e){const n=t[e];if("number"==typeof n&&Number.isInteger(n)&&n>=0){const e=[];e[n]=s,s=e}else s=new Map([[n,s]])}return Ru(s,void 0,{aliasDuplicateObjects:!1,keepUndefined:!1,onAnchor:()=>{throw new Error("This should not happen, please report a bug.")},schema:e,sourceObjects:new Map})}const vu=e=>null==e||"object"==typeof e&&!!e[Symbol.iterator]().next().done;class xu extends ku{constructor(e,t){super(e),Object.defineProperty(this,"schema",{value:t,configurable:!0,enumerable:!1,writable:!0})}clone(e){const t=Object.create(Object.getPrototypeOf(this),Object.getOwnPropertyDescriptors(this));return e&&(t.schema=e),t.items=t.items.map((t=>hu(t)||au(t)?t.clone(e):t)),this.range&&(t.range=this.range.slice()),t}addIn(e,t){if(vu(e))this.add(t);else{const[n,...s]=e,r=this.get(n,!0);if(uu(r))r.addIn(s,t);else{if(void 0!==r||!this.schema)throw new Error(`Expected YAML collection at ${n}. Remaining path: ${s}`);this.set(n,Mu(this.schema,s,t))}}}deleteIn(e){const[t,...n]=e;if(0===n.length)return this.delete(t);const s=this.get(t,!0);if(uu(s))return s.deleteIn(n);throw new Error(`Expected YAML collection at ${t}. Remaining path: ${n}`)}getIn(e,t){const[n,...s]=e,r=this.get(n,!0);return 0===s.length?!t&&cu(r)?r.value:r:uu(r)?r.getIn(s,t):void 0}hasAllNullValues(e){return this.items.every((t=>{if(!au(t))return!1;const n=t.value;return null==n||e&&cu(n)&&null==n.value&&!n.commentBefore&&!n.comment&&!n.tag}))}hasIn(e){const[t,...n]=e;if(0===n.length)return this.has(t);const s=this.get(t,!0);return!!uu(s)&&s.hasIn(n)}setIn(e,t){const[n,...s]=e;if(0===s.length)this.set(n,t);else{const e=this.get(n,!0);if(uu(e))e.setIn(s,t);else{if(void 0!==e||!this.schema)throw new Error(`Expected YAML collection at ${n}. Remaining path: ${s}`);this.set(n,Mu(this.schema,s,t))}}}}const wu=e=>e.replace(/^(?!$)(?: $)?/gm,"#");function Pu(e,t){return/^\n+$/.test(e)?e.substring(1):t?e.replace(/^(?! *$)/gm,t):e}const Fu=(e,t,n)=>e.endsWith("\n")?Pu(n,t):n.includes("\n")?"\n"+Pu(n,t):(e.endsWith(" ")?"":" ")+n,Bu="flow",Uu="block",Hu="quoted";function Gu(e,t,n="flow",{indentAtStart:s,lineWidth:r=80,minContentWidth:i=20,onFold:o,onOverflow:a}={}){if(!r||r<0)return e;r<i&&(i=0);const c=Math.max(1+i,1+r-t.length);if(e.length<=c)return e;const l=[],u={};let h,d,p=r-t.length;"number"==typeof s&&(s>r-Math.max(2,i)?l.push(0):p=r-s);let f=!1,m=-1,E=-1,T=-1;n===Uu&&(m=qu(e,m,t.length),-1!==m&&(p=m+c));for(let s;s=e[m+=1];){if(n===Hu&&"\\"===s){switch(E=m,e[m+1]){case"x":m+=3;break;case"u":m+=5;break;case"U":m+=9;break;default:m+=1}T=m}if("\n"===s)n===Uu&&(m=qu(e,m,t.length)),p=m+t.length+c,h=void 0;else{if(" "===s&&d&&" "!==d&&"\n"!==d&&"\t"!==d){const t=e[m+1];t&&" "!==t&&"\n"!==t&&"\t"!==t&&(h=m)}if(m>=p)if(h)l.push(h),p=h+c,h=void 0;else if(n===Hu){for(;" "===d||"\t"===d;)d=s,s=e[m+=1],f=!0;const t=m>T+1?m-2:E-1;if(u[t])return e;l.push(t),u[t]=!0,p=t+c,h=void 0}else f=!0}d=s}if(f&&a&&a(),0===l.length)return e;o&&o();let _=e.slice(0,l[0]);for(let s=0;s<l.length;++s){const r=l[s],i=l[s+1]||e.length;0===r?_=`\n${t}${e.slice(0,i)}`:(n===Hu&&u[r]&&(_+=`${e[r]}\\`),_+=`\n${t}${e.slice(r+1,i)}`)}return _}function qu(e,t,n){let s=t,r=t+1,i=e[r];for(;" "===i||"\t"===i;)if(t<r+n)i=e[++t];else{do{i=e[++t]}while(i&&"\n"!==i);s=t,r=t+1,i=e[r]}return s}const $u=(e,t)=>({indentAtStart:t?e.indent.length:e.indentAtStart,lineWidth:e.options.lineWidth,minContentWidth:e.options.minContentWidth}),Yu=e=>/^(%|---|\.\.\.)/m.test(e);function ju(e,t){const n=JSON.stringify(e);if(t.options.doubleQuotedAsJSON)return n;const{implicitKey:s}=t,r=t.options.doubleQuotedMinMultiLineLength,i=t.indent||(Yu(e)?"  ":"");let o="",a=0;for(let e=0,t=n[e];t;t=n[++e])if(" "===t&&"\\"===n[e+1]&&"n"===n[e+2]&&(o+=n.slice(a,e)+"\\ ",e+=1,a=e,t="\\"),"\\"===t)switch(n[e+1]){case"u":{o+=n.slice(a,e);const t=n.substr(e+2,4);switch(t){case"0000":o+="\\0";break;case"0007":o+="\\a";break;case"000b":o+="\\v";break;case"001b":o+="\\e";break;case"0085":o+="\\N";break;case"00a0":o+="\\_";break;case"2028":o+="\\L";break;case"2029":o+="\\P";break;default:"00"===t.substr(0,2)?o+="\\x"+t.substr(2):o+=n.substr(e,6)}e+=5,a=e+1}break;case"n":if(s||'"'===n[e+2]||n.length<r)e+=1;else{for(o+=n.slice(a,e)+"\n\n";"\\"===n[e+2]&&"n"===n[e+3]&&'"'!==n[e+4];)o+="\n",e+=2;o+=i," "===n[e+2]&&(o+="\\"),e+=1,a=e+1}break;default:e+=1}return o=a?o+n.slice(a):n,s?o:Gu(o,i,Hu,$u(t,!1))}function Ku(e,t){if(!1===t.options.singleQuote||t.implicitKey&&e.includes("\n")||/[ \t]\n|\n[ \t]/.test(e))return ju(e,t);const n=t.indent||(Yu(e)?"  ":""),s="'"+e.replace(/'/g,"''").replace(/\n+/g,`$&\n${n}`)+"'";return t.implicitKey?s:Gu(s,n,Bu,$u(t,!1))}function Vu(e,t){const{singleQuote:n}=t.options;let s;if(!1===n)s=ju;else{const t=e.includes('"'),r=e.includes("'");s=t&&!r?Ku:r&&!t?ju:n?Ku:ju}return s(e,t)}let zu;try{zu=new RegExp("(^|(?<!\n))\n+(?!\n|$)","g")}catch{zu=/\n+(?!\n|$)/g}function Qu({comment:e,type:t,value:n},s,r,i){const{blockQuote:o,commentString:a,lineWidth:c}=s.options;if(!o||/\n[\t ]+$/.test(n)||/^\s*$/.test(n))return Vu(n,s);const l=s.indent||(s.forceBlockIndent||Yu(n)?"  ":""),u="literal"===o||"folded"!==o&&t!==Ou.BLOCK_FOLDED&&(t===Ou.BLOCK_LITERAL||!function(e,t,n){if(!t||t<0)return!1;const s=t-n,r=e.length;if(r<=s)return!1;for(let t=0,n=0;t<r;++t)if("\n"===e[t]){if(t-n>s)return!0;if(n=t+1,r-n<=s)return!1}return!0}(n,c,l.length));if(!n)return u?"|\n":">\n";let h,d;for(d=n.length;d>0;--d){const e=n[d-1];if("\n"!==e&&"\t"!==e&&" "!==e)break}let p=n.substring(d);const f=p.indexOf("\n");-1===f?h="-":n===p||f!==p.length-1?(h="+",i&&i()):h="",p&&(n=n.slice(0,-p.length),"\n"===p[p.length-1]&&(p=p.slice(0,-1)),p=p.replace(zu,`$&${l}`));let m,E=!1,T=-1;for(m=0;m<n.length;++m){const e=n[m];if(" "===e)E=!0;else{if("\n"!==e)break;T=m}}let _=n.substring(0,T<m?T+1:m);_&&(n=n.substring(_.length),_=_.replace(/\n+/g,`$&${l}`));let A=(u?"|":">")+(E?l?"2":"1":"")+h;if(e&&(A+=" "+a(e.replace(/ ?[\r\n]+/g," ")),r&&r()),u)return`${A}\n${l}${_}${n=n.replace(/\n+/g,`$&${l}`)}${p}`;return`${A}\n${l}${Gu(`${_}${n=n.replace(/\n+/g,"\n$&").replace(/(?:^|\n)([\t ].*)(?:([\n\t ]*)\n(?![\n\t ]))?/g,"$1$2").replace(/\n+/g,`$&${l}`)}${p}`,l,Uu,$u(s,!0))}`}function Wu(e,t,n,s){const{implicitKey:r,inFlow:i}=t,o="string"==typeof e.value?e:Object.assign({},e,{value:String(e.value)});let{type:a}=e;a!==Ou.QUOTE_DOUBLE&&/[\x00-\x08\x0b-\x1f\x7f-\x9f\u{D800}-\u{DFFF}]/u.test(o.value)&&(a=Ou.QUOTE_DOUBLE);const c=e=>{switch(e){case Ou.BLOCK_FOLDED:case Ou.BLOCK_LITERAL:return r||i?Vu(o.value,t):Qu(o,t,n,s);case Ou.QUOTE_DOUBLE:return ju(o.value,t);case Ou.QUOTE_SINGLE:return Ku(o.value,t);case Ou.PLAIN:return function(e,t,n,s){const{type:r,value:i}=e,{actualString:o,implicitKey:a,indent:c,indentStep:l,inFlow:u}=t;if(a&&i.includes("\n")||u&&/[[\]{},]/.test(i))return Vu(i,t);if(!i||/^[\n\t ,[\]{}#&*!|>'"%@`]|^[?-]$|^[?-][ \t]|[\n:][ \t]|[ \t]\n|[\n\t ]#|[\n\t :]$/.test(i))return a||u||!i.includes("\n")?Vu(i,t):Qu(e,t,n,s);if(!a&&!u&&r!==Ou.PLAIN&&i.includes("\n"))return Qu(e,t,n,s);if(Yu(i)){if(""===c)return t.forceBlockIndent=!0,Qu(e,t,n,s);if(a&&c===l)return Vu(i,t)}const h=i.replace(/\n+/g,`$&\n${c}`);if(o){const e=e=>{var t;return e.default&&"tag:yaml.org,2002:str"!==e.tag&&(null==(t=e.test)?void 0:t.test(h))},{compat:n,tags:s}=t.doc.schema;if(s.some(e)||(null==n?void 0:n.some(e)))return Vu(i,t)}return a?h:Gu(h,c,Bu,$u(t,!1))}(o,t,n,s);default:return null}};let l=c(a);if(null===l){const{defaultKeyType:e,defaultStringType:n}=t.options,s=r&&e||n;if(l=c(s),null===l)throw new Error(`Unsupported default string type ${s}`)}return l}function Xu(e,t){const n=Object.assign({blockQuote:!0,commentString:wu,defaultKeyType:null,defaultStringType:"PLAIN",directives:null,doubleQuotedAsJSON:!1,doubleQuotedMinMultiLineLength:40,falseStr:"false",flowCollectionPadding:!0,indentSeq:!0,lineWidth:80,minContentWidth:20,nullStr:"null",simpleKeys:!1,singleQuote:null,trueStr:"true",verifyAliasOrder:!0},e.schema.toStringOptions,t);let s;switch(n.collectionStyle){case"block":s=!1;break;case"flow":s=!0;break;default:s=null}return{anchors:new Set,doc:e,flowCollectionPadding:n.flowCollectionPadding?" ":"",indent:"",indentStep:"number"==typeof n.indent?" ".repeat(n.indent):"  ",inFlow:s,options:n}}function Zu(e,t,n,s){var r;if(au(e))return e.toString(t,n,s);if(ru(e)){if(t.doc.directives)return e.toString(t);if(null==(r=t.resolvedAliases)?void 0:r.has(e))throw new TypeError("Cannot stringify circular structure without alias nodes");t.resolvedAliases?t.resolvedAliases.add(e):t.resolvedAliases=new Set([e]),e=e.resolve(t.doc)}let i;const o=hu(e)?e:t.doc.createNode(e,{onTagObj:e=>i=e});i||(i=function(e,t){var n;if(t.tag){const n=e.filter((e=>e.tag===t.tag));if(n.length>0)return n.find((e=>e.format===t.format))??n[0]}let s,r;if(cu(t)){r=t.value;const n=e.filter((e=>{var t;return null==(t=e.identify)?void 0:t.call(e,r)}));s=n.find((e=>e.format===t.format))??n.find((e=>!e.format))}else r=t,s=e.find((e=>e.nodeClass&&r instanceof e.nodeClass));if(!s){const e=(null==(n=null==r?void 0:r.constructor)?void 0:n.name)??typeof r;throw new Error(`Tag not resolved for ${e} value`)}return s}(t.doc.schema.tags,o));const a=function(e,t,{anchors:n,doc:s}){if(!s.directives)return"";const r=[],i=(cu(e)||uu(e))&&e.anchor;i&&gu(i)&&(n.add(i),r.push(`&${i}`));const o=e.tag?e.tag:t.default?null:t.tag;return o&&r.push(s.directives.tagString(o)),r.join(" ")}(o,i,t);a.length>0&&(t.indentAtStart=(t.indentAtStart??0)+a.length+1);const c="function"==typeof i.stringify?i.stringify(o,t,n,s):cu(o)?Wu(o,t,n,s):o.toString(t,n,s);return a?cu(o)||"{"===c[0]||"["===c[0]?`${a} ${c}`:`${a}\n${t.indent}${c}`:c}function Ju(e,t){"debug"!==e&&"warn"!==e||("undefined"!=typeof process&&process.emitWarning?process.emitWarning(t):console.warn(t))}function eh(e,t,{key:n,value:s}){if((null==e?void 0:e.doc.schema.merge)&&th(n))if(s=ru(s)?s.resolve(e.doc):s,lu(s))for(const n of s.items)nh(e,t,n);else if(Array.isArray(s))for(const n of s)nh(e,t,n);else nh(e,t,s);else{const r=Iu(n,"",e);if(t instanceof Map)t.set(r,Iu(s,r,e));else if(t instanceof Set)t.add(r);else{const i=function(e,t,n){if(null===t)return"";if("object"!=typeof t)return String(t);if(hu(e)&&(null==n?void 0:n.doc)){const t=Xu(n.doc,{});t.anchors=new Set;for(const e of n.anchors.keys())t.anchors.add(e.anchor);t.inFlow=!0,t.inStringifyKey=!0;const s=e.toString(t);if(!n.mapKeyWarned){let e=JSON.stringify(s);e.length>40&&(e=e.substring(0,36)+'..."'),Ju(n.doc.options.logLevel,`Keys with collection values will be stringified due to JS Object restrictions: ${e}. Set mapAsMap: true to use object keys.`),n.mapKeyWarned=!0}return s}return JSON.stringify(t)}(n,r,e),o=Iu(s,i,e);i in t?Object.defineProperty(t,i,{value:o,writable:!0,enumerable:!0,configurable:!0}):t[i]=o}}return t}const th=e=>"<<"===e||cu(e)&&"<<"===e.value&&(!e.type||e.type===Ou.PLAIN);function nh(e,t,n){const s=e&&ru(n)?n.resolve(e.doc):n;if(!ou(s))throw new Error("Merge sources must be maps or map aliases");const r=s.toJSON(null,e,Map);for(const[e,n]of r)t instanceof Map?t.has(e)||t.set(e,n):t instanceof Set?t.add(e):Object.prototype.hasOwnProperty.call(t,e)||Object.defineProperty(t,e,{value:n,writable:!0,enumerable:!0,configurable:!0});return t}function sh(e,t,n){const s=Ru(e,void 0,n),r=Ru(t,void 0,n);return new rh(s,r)}class rh{constructor(e,t=null){Object.defineProperty(this,su,{value:eu}),this.key=e,this.value=t}clone(e){let{key:t,value:n}=this;return hu(t)&&(t=t.clone(e)),hu(n)&&(n=n.clone(e)),new rh(t,n)}toJSON(e,t){return eh(t,(null==t?void 0:t.mapAsMap)?new Map:{},this)}toString(e,t,n){return(null==e?void 0:e.doc)?function({key:e,value:t},n,s,r){const{allNullValues:i,doc:o,indent:a,indentStep:c,options:{commentString:l,indentSeq:u,simpleKeys:h}}=n;let d=hu(e)&&e.comment||null;if(h){if(d)throw new Error("With simple keys, key nodes cannot have comments");if(uu(e)||!hu(e)&&"object"==typeof e)throw new Error("With simple keys, collection cannot be used as a key value")}let p=!h&&(!e||d&&null==t&&!n.inFlow||uu(e)||(cu(e)?e.type===Ou.BLOCK_FOLDED||e.type===Ou.BLOCK_LITERAL:"object"==typeof e));n=Object.assign({},n,{allNullValues:!1,implicitKey:!p&&(h||!i),indent:a+c});let f,m,E,T=!1,_=!1,A=Zu(e,n,(()=>T=!0),(()=>_=!0));if(!p&&!n.inFlow&&A.length>1024){if(h)throw new Error("With simple keys, single line scalar must not span more than 1024 characters");p=!0}if(n.inFlow){if(i||null==t)return T&&s&&s(),""===A?"?":p?`? ${A}`:A}else if(i&&!h||null==t&&p)return A=`? ${A}`,d&&!T?A+=Fu(A,n.indent,l(d)):_&&r&&r(),A;T&&(d=null),p?(d&&(A+=Fu(A,n.indent,l(d))),A=`? ${A}\n${a}:`):(A=`${A}:`,d&&(A+=Fu(A,n.indent,l(d)))),hu(t)?(f=!!t.spaceBefore,m=t.commentBefore,E=t.comment):(f=!1,m=null,E=null,t&&"object"==typeof t&&(t=o.createNode(t))),n.implicitKey=!1,p||d||!cu(t)||(n.indentAtStart=A.length+1),_=!1,u||!(c.length>=2)||n.inFlow||p||!lu(t)||t.flow||t.tag||t.anchor||(n.indent=n.indent.substring(2));let g=!1;const C=Zu(t,n,(()=>g=!0),(()=>_=!0));let b=" ";if(d||f||m)b=f?"\n":"",m&&(b+=`\n${Pu(l(m),n.indent)}`),""!==C||n.inFlow?b+=`\n${n.indent}`:"\n"===b&&(b="\n\n");else if(!p&&uu(t)){const e=C[0],s=C.indexOf("\n"),r=-1!==s,i=n.inFlow??t.flow??0===t.items.length;if(r||!i){let t=!1;if(r&&("&"===e||"!"===e)){let n=C.indexOf(" ");"&"===e&&-1!==n&&n<s&&"!"===C[n+1]&&(n=C.indexOf(" ",n+1)),(-1===n||s<n)&&(t=!0)}t||(b=`\n${n.indent}`)}}else""!==C&&"\n"!==C[0]||(b="");return A+=b+C,n.inFlow?g&&s&&s():E&&!g?A+=Fu(A,n.indent,l(E)):_&&r&&r(),A}(this,e,t,n):JSON.stringify(this)}}function ih(e,t,n){return(t.inFlow??e.flow?ah:oh)(e,t,n)}function oh({comment:e,items:t},n,{blockItemPrefix:s,flowChars:r,itemIndent:i,onChompKeep:o,onComment:a}){const{indent:c,options:{commentString:l}}=n,u=Object.assign({},n,{indent:i,type:null});let h=!1;const d=[];for(let e=0;e<t.length;++e){const r=t[e];let o=null;if(hu(r))!h&&r.spaceBefore&&d.push(""),ch(n,d,r.commentBefore,h),r.comment&&(o=r.comment);else if(au(r)){const e=hu(r.key)?r.key:null;e&&(!h&&e.spaceBefore&&d.push(""),ch(n,d,e.commentBefore,h))}h=!1;let a=Zu(r,u,(()=>o=null),(()=>h=!0));o&&(a+=Fu(a,i,l(o))),h&&o&&(h=!1),d.push(s+a)}let p;if(0===d.length)p=r.start+r.end;else{p=d[0];for(let e=1;e<d.length;++e){const t=d[e];p+=t?`\n${c}${t}`:"\n"}}return e?(p+="\n"+Pu(l(e),c),a&&a()):h&&o&&o(),p}function ah({items:e},t,{flowChars:n,itemIndent:s}){const{indent:r,indentStep:i,flowCollectionPadding:o,options:{commentString:a}}=t;s+=i;const c=Object.assign({},t,{indent:s,inFlow:!0,type:null});let l=!1,u=0;const h=[];for(let n=0;n<e.length;++n){const r=e[n];let i=null;if(hu(r))r.spaceBefore&&h.push(""),ch(t,h,r.commentBefore,!1),r.comment&&(i=r.comment);else if(au(r)){const e=hu(r.key)?r.key:null;e&&(e.spaceBefore&&h.push(""),ch(t,h,e.commentBefore,!1),e.comment&&(l=!0));const n=hu(r.value)?r.value:null;n?(n.comment&&(i=n.comment),n.commentBefore&&(l=!0)):null==r.value&&(null==e?void 0:e.comment)&&(i=e.comment)}i&&(l=!0);let o=Zu(r,c,(()=>i=null));n<e.length-1&&(o+=","),i&&(o+=Fu(o,s,a(i))),!l&&(h.length>u||o.includes("\n"))&&(l=!0),h.push(o),u=h.length}const{start:d,end:p}=n;if(0===h.length)return d+p;if(!l){const e=h.reduce(((e,t)=>e+t.length+2),2);l=t.options.lineWidth>0&&e>t.options.lineWidth}if(l){let e=d;for(const t of h)e+=t?`\n${i}${r}${t}`:"\n";return`${e}\n${r}${p}`}return`${d}${o}${h.join(" ")}${o}${p}`}function ch({indent:e,options:{commentString:t}},n,s,r){if(s&&r&&(s=s.replace(/^\n+/,"")),s){const r=Pu(t(s),e);n.push(r.trimStart())}}function lh(e,t){const n=cu(t)?t.value:t;for(const s of e)if(au(s)){if(s.key===t||s.key===n)return s;if(cu(s.key)&&s.key.value===n)return s}}class uh extends xu{static get tagName(){return"tag:yaml.org,2002:map"}constructor(e){super(Jl,e),this.items=[]}static from(e,t,n){const{keepUndefined:s,replacer:r}=n,i=new this(e),o=(e,o)=>{if("function"==typeof r)o=r.call(t,e,o);else if(Array.isArray(r)&&!r.includes(e))return;(void 0!==o||s)&&i.items.push(sh(e,o,n))};if(t instanceof Map)for(const[e,n]of t)o(e,n);else if(t&&"object"==typeof t)for(const e of Object.keys(t))o(e,t[e]);return"function"==typeof e.sortMapEntries&&i.items.sort(e.sortMapEntries),i}add(e,t){var n;let s;s=au(e)?e:e&&"object"==typeof e&&"key"in e?new rh(e.key,e.value):new rh(e,null==e?void 0:e.value);const r=lh(this.items,s.key),i=null==(n=this.schema)?void 0:n.sortMapEntries;if(r){if(!t)throw new Error(`Key ${s.key} already set`);cu(r.value)&&yu(s.value)?r.value.value=s.value:r.value=s.value}else if(i){const e=this.items.findIndex((e=>i(s,e)<0));-1===e?this.items.push(s):this.items.splice(e,0,s)}else this.items.push(s)}delete(e){const t=lh(this.items,e);if(!t)return!1;return this.items.splice(this.items.indexOf(t),1).length>0}get(e,t){const n=lh(this.items,e),s=null==n?void 0:n.value;return(!t&&cu(s)?s.value:s)??void 0}has(e){return!!lh(this.items,e)}set(e,t){this.add(new rh(e,t),!0)}toJSON(e,t,n){const s=n?new n:(null==t?void 0:t.mapAsMap)?new Map:{};(null==t?void 0:t.onCreate)&&t.onCreate(s);for(const e of this.items)eh(t,s,e);return s}toString(e,t,n){if(!e)return JSON.stringify(this);for(const e of this.items)if(!au(e))throw new Error(`Map items must all be pairs; found ${JSON.stringify(e)} instead`);return!e.allNullValues&&this.hasAllNullValues(!1)&&(e=Object.assign({},e,{allNullValues:!0})),ih(this,e,{blockItemPrefix:"",flowChars:{start:"{",end:"}"},itemIndent:e.indent||"",onChompKeep:n,onComment:t})}}const hh={collection:"map",default:!0,nodeClass:uh,tag:"tag:yaml.org,2002:map",resolve:(e,t)=>(ou(e)||t("Expected a mapping for this tag"),e),createNode:(e,t,n)=>uh.from(e,t,n)};class dh extends xu{static get tagName(){return"tag:yaml.org,2002:seq"}constructor(e){super(nu,e),this.items=[]}add(e){this.items.push(e)}delete(e){const t=ph(e);if("number"!=typeof t)return!1;return this.items.splice(t,1).length>0}get(e,t){const n=ph(e);if("number"!=typeof n)return;const s=this.items[n];return!t&&cu(s)?s.value:s}has(e){const t=ph(e);return"number"==typeof t&&t<this.items.length}set(e,t){const n=ph(e);if("number"!=typeof n)throw new Error(`Expected a valid index, not ${e}.`);const s=this.items[n];cu(s)&&yu(t)?s.value=t:this.items[n]=t}toJSON(e,t){const n=[];(null==t?void 0:t.onCreate)&&t.onCreate(n);let s=0;for(const e of this.items)n.push(Iu(e,String(s++),t));return n}toString(e,t,n){return e?ih(this,e,{blockItemPrefix:"- ",flowChars:{start:"[",end:"]"},itemIndent:(e.indent||"")+"  ",onChompKeep:n,onComment:t}):JSON.stringify(this)}static from(e,t,n){const{replacer:s}=n,r=new this(e);if(t&&Symbol.iterator in Object(t)){let e=0;for(let i of t){if("function"==typeof s){const n=t instanceof Set?i:String(e++);i=s.call(t,n,i)}r.items.push(Ru(i,void 0,n))}}return r}}function ph(e){let t=cu(e)?e.value:e;return t&&"string"==typeof t&&(t=Number(t)),"number"==typeof t&&Number.isInteger(t)&&t>=0?t:null}const fh={collection:"seq",default:!0,nodeClass:dh,tag:"tag:yaml.org,2002:seq",resolve:(e,t)=>(lu(e)||t("Expected a sequence for this tag"),e),createNode:(e,t,n)=>dh.from(e,t,n)},mh={identify:e=>"string"==typeof e,default:!0,tag:"tag:yaml.org,2002:str",resolve:e=>e,stringify:(e,t,n,s)=>Wu(e,t=Object.assign({actualString:!0},t),n,s)},Eh={identify:e=>null==e,createNode:()=>new Ou(null),default:!0,tag:"tag:yaml.org,2002:null",test:/^(?:~|[Nn]ull|NULL)?$/,resolve:()=>new Ou(null),stringify:({source:e},t)=>"string"==typeof e&&Eh.test.test(e)?e:t.options.nullStr},Th={identify:e=>"boolean"==typeof e,default:!0,tag:"tag:yaml.org,2002:bool",test:/^(?:[Tt]rue|TRUE|[Ff]alse|FALSE)$/,resolve:e=>new Ou("t"===e[0]||"T"===e[0]),stringify({source:e,value:t},n){if(e&&Th.test.test(e)){if(t===("t"===e[0]||"T"===e[0]))return e}return t?n.options.trueStr:n.options.falseStr}};function _h({format:e,minFractionDigits:t,tag:n,value:s}){if("bigint"==typeof s)return String(s);const r="number"==typeof s?s:Number(s);if(!isFinite(r))return isNaN(r)?".nan":r<0?"-.inf":".inf";let i=JSON.stringify(s);if(!e&&t&&(!n||"tag:yaml.org,2002:float"===n)&&/^\d/.test(i)){let e=i.indexOf(".");e<0&&(e=i.length,i+=".");let n=t-(i.length-e-1);for(;n-- >0;)i+="0"}return i}const Ah={identify:e=>"number"==typeof e,default:!0,tag:"tag:yaml.org,2002:float",test:/^(?:[-+]?\.(?:inf|Inf|INF)|\.nan|\.NaN|\.NAN)$/,resolve:e=>"nan"===e.slice(-3).toLowerCase()?NaN:"-"===e[0]?Number.NEGATIVE_INFINITY:Number.POSITIVE_INFINITY,stringify:_h},gh={identify:e=>"number"==typeof e,default:!0,tag:"tag:yaml.org,2002:float",format:"EXP",test:/^[-+]?(?:\.[0-9]+|[0-9]+(?:\.[0-9]*)?)[eE][-+]?[0-9]+$/,resolve:e=>parseFloat(e),stringify(e){const t=Number(e.value);return isFinite(t)?t.toExponential():_h(e)}},Ch={identify:e=>"number"==typeof e,default:!0,tag:"tag:yaml.org,2002:float",test:/^[-+]?(?:\.[0-9]+|[0-9]+\.[0-9]*)$/,resolve(e){const t=new Ou(parseFloat(e)),n=e.indexOf(".");return-1!==n&&"0"===e[e.length-1]&&(t.minFractionDigits=e.length-n-1),t},stringify:_h},bh=e=>"bigint"==typeof e||Number.isInteger(e),Nh=(e,t,n,{intAsBigInt:s})=>s?BigInt(e):parseInt(e.substring(t),n);function Ih(e,t,n){const{value:s}=e;return bh(s)&&s>=0?n+s.toString(t):_h(e)}const kh={identify:e=>bh(e)&&e>=0,default:!0,tag:"tag:yaml.org,2002:int",format:"OCT",test:/^0o[0-7]+$/,resolve:(e,t,n)=>Nh(e,2,8,n),stringify:e=>Ih(e,8,"0o")},Sh={identify:bh,default:!0,tag:"tag:yaml.org,2002:int",test:/^[-+]?[0-9]+$/,resolve:(e,t,n)=>Nh(e,0,10,n),stringify:_h},Dh={identify:e=>bh(e)&&e>=0,default:!0,tag:"tag:yaml.org,2002:int",format:"HEX",test:/^0x[0-9a-fA-F]+$/,resolve:(e,t,n)=>Nh(e,2,16,n),stringify:e=>Ih(e,16,"0x")},yh=[hh,fh,mh,Eh,Th,kh,Sh,Dh,Ah,gh,Ch];function Oh(e){return"bigint"==typeof e||Number.isInteger(e)}const Lh=({value:e})=>JSON.stringify(e),Rh=[hh,fh].concat([{identify:e=>"string"==typeof e,default:!0,tag:"tag:yaml.org,2002:str",resolve:e=>e,stringify:Lh},{identify:e=>null==e,createNode:()=>new Ou(null),default:!0,tag:"tag:yaml.org,2002:null",test:/^null$/,resolve:()=>null,stringify:Lh},{identify:e=>"boolean"==typeof e,default:!0,tag:"tag:yaml.org,2002:bool",test:/^true|false$/,resolve:e=>"true"===e,stringify:Lh},{identify:Oh,default:!0,tag:"tag:yaml.org,2002:int",test:/^-?(?:0|[1-9][0-9]*)$/,resolve:(e,t,{intAsBigInt:n})=>n?BigInt(e):parseInt(e,10),stringify:({value:e})=>Oh(e)?e.toString():JSON.stringify(e)},{identify:e=>"number"==typeof e,default:!0,tag:"tag:yaml.org,2002:float",test:/^-?(?:0|[1-9][0-9]*)(?:\.[0-9]*)?(?:[eE][-+]?[0-9]+)?$/,resolve:e=>parseFloat(e),stringify:Lh}],{default:!0,tag:"",test:/^/,resolve:(e,t)=>(t(`Unresolved plain scalar ${JSON.stringify(e)}`),e)}),Mh={identify:e=>e instanceof Uint8Array,default:!1,tag:"tag:yaml.org,2002:binary",resolve(e,t){if("function"==typeof Buffer)return Buffer.from(e,"base64");if("function"==typeof atob){const t=atob(e.replace(/[\n\r]/g,"")),n=new Uint8Array(t.length);for(let e=0;e<t.length;++e)n[e]=t.charCodeAt(e);return n}return t("This environment does not support reading binary tags; either Buffer or atob is required"),e},stringify({comment:e,type:t,value:n},s,r,i){const o=n;let a;if("function"==typeof Buffer)a=o instanceof Buffer?o.toString("base64"):Buffer.from(o.buffer).toString("base64");else{if("function"!=typeof btoa)throw new Error("This environment does not support writing binary tags; either Buffer or btoa is required");{let e="";for(let t=0;t<o.length;++t)e+=String.fromCharCode(o[t]);a=btoa(e)}}if(t||(t=Ou.BLOCK_LITERAL),t!==Ou.QUOTE_DOUBLE){const e=Math.max(s.options.lineWidth-s.indent.length,s.options.minContentWidth),n=Math.ceil(a.length/e),r=new Array(n);for(let t=0,s=0;t<n;++t,s+=e)r[t]=a.substr(s,e);a=r.join(t===Ou.BLOCK_LITERAL?"\n":" ")}return Wu({comment:e,type:t,value:a},s,r,i)}};function vh(e,t){if(lu(e))for(let n=0;n<e.items.length;++n){let s=e.items[n];if(!au(s)){if(ou(s)){s.items.length>1&&t("Each pair must have its own sequence indicator");const e=s.items[0]||new rh(new Ou(null));if(s.commentBefore&&(e.key.commentBefore=e.key.commentBefore?`${s.commentBefore}\n${e.key.commentBefore}`:s.commentBefore),s.comment){const t=e.value??e.key;t.comment=t.comment?`${s.comment}\n${t.comment}`:s.comment}s=e}e.items[n]=au(s)?s:new rh(s)}}else t("Expected a sequence for this tag");return e}function xh(e,t,n){const{replacer:s}=n,r=new dh(e);r.tag="tag:yaml.org,2002:pairs";let i=0;if(t&&Symbol.iterator in Object(t))for(let e of t){let o,a;if("function"==typeof s&&(e=s.call(t,String(i++),e)),Array.isArray(e)){if(2!==e.length)throw new TypeError(`Expected [key, value] tuple: ${e}`);o=e[0],a=e[1]}else if(e&&e instanceof Object){const t=Object.keys(e);if(1!==t.length)throw new TypeError(`Expected tuple with one key, not ${t.length} keys`);o=t[0],a=e[o]}else o=e;r.items.push(sh(o,a,n))}return r}const wh={collection:"seq",default:!1,tag:"tag:yaml.org,2002:pairs",resolve:vh,createNode:xh};class Ph extends dh{constructor(){super(),this.add=uh.prototype.add.bind(this),this.delete=uh.prototype.delete.bind(this),this.get=uh.prototype.get.bind(this),this.has=uh.prototype.has.bind(this),this.set=uh.prototype.set.bind(this),this.tag=Ph.tag}toJSON(e,t){if(!t)return super.toJSON(e);const n=new Map;(null==t?void 0:t.onCreate)&&t.onCreate(n);for(const e of this.items){let s,r;if(au(e)?(s=Iu(e.key,"",t),r=Iu(e.value,s,t)):s=Iu(e,"",t),n.has(s))throw new Error("Ordered maps must not include duplicate keys");n.set(s,r)}return n}static from(e,t,n){const s=xh(e,t,n),r=new this;return r.items=s.items,r}}Ph.tag="tag:yaml.org,2002:omap";const Fh={collection:"seq",identify:e=>e instanceof Map,nodeClass:Ph,default:!1,tag:"tag:yaml.org,2002:omap",resolve(e,t){const n=vh(e,t),s=[];for(const{key:e}of n.items)cu(e)&&(s.includes(e.value)?t(`Ordered maps must not include duplicate keys: ${e.value}`):s.push(e.value));return Object.assign(new Ph,n)},createNode:(e,t,n)=>Ph.from(e,t,n)};function Bh({value:e,source:t},n){return t&&(e?Uh:Hh).test.test(t)?t:e?n.options.trueStr:n.options.falseStr}const Uh={identify:e=>!0===e,default:!0,tag:"tag:yaml.org,2002:bool",test:/^(?:Y|y|[Yy]es|YES|[Tt]rue|TRUE|[Oo]n|ON)$/,resolve:()=>new Ou(!0),stringify:Bh},Hh={identify:e=>!1===e,default:!0,tag:"tag:yaml.org,2002:bool",test:/^(?:N|n|[Nn]o|NO|[Ff]alse|FALSE|[Oo]ff|OFF)$/,resolve:()=>new Ou(!1),stringify:Bh},Gh={identify:e=>"number"==typeof e,default:!0,tag:"tag:yaml.org,2002:float",test:/^(?:[-+]?\.(?:inf|Inf|INF)|\.nan|\.NaN|\.NAN)$/,resolve:e=>"nan"===e.slice(-3).toLowerCase()?NaN:"-"===e[0]?Number.NEGATIVE_INFINITY:Number.POSITIVE_INFINITY,stringify:_h},qh={identify:e=>"number"==typeof e,default:!0,tag:"tag:yaml.org,2002:float",format:"EXP",test:/^[-+]?(?:[0-9][0-9_]*)?(?:\.[0-9_]*)?[eE][-+]?[0-9]+$/,resolve:e=>parseFloat(e.replace(/_/g,"")),stringify(e){const t=Number(e.value);return isFinite(t)?t.toExponential():_h(e)}},$h={identify:e=>"number"==typeof e,default:!0,tag:"tag:yaml.org,2002:float",test:/^[-+]?(?:[0-9][0-9_]*)?\.[0-9_]*$/,resolve(e){const t=new Ou(parseFloat(e.replace(/_/g,""))),n=e.indexOf(".");if(-1!==n){const s=e.substring(n+1).replace(/_/g,"");"0"===s[s.length-1]&&(t.minFractionDigits=s.length)}return t},stringify:_h},Yh=e=>"bigint"==typeof e||Number.isInteger(e);function jh(e,t,n,{intAsBigInt:s}){const r=e[0];if("-"!==r&&"+"!==r||(t+=1),e=e.substring(t).replace(/_/g,""),s){switch(n){case 2:e=`0b${e}`;break;case 8:e=`0o${e}`;break;case 16:e=`0x${e}`}const t=BigInt(e);return"-"===r?BigInt(-1)*t:t}const i=parseInt(e,n);return"-"===r?-1*i:i}function Kh(e,t,n){const{value:s}=e;if(Yh(s)){const e=s.toString(t);return s<0?"-"+n+e.substr(1):n+e}return _h(e)}const Vh={identify:Yh,default:!0,tag:"tag:yaml.org,2002:int",format:"BIN",test:/^[-+]?0b[0-1_]+$/,resolve:(e,t,n)=>jh(e,2,2,n),stringify:e=>Kh(e,2,"0b")},zh={identify:Yh,default:!0,tag:"tag:yaml.org,2002:int",format:"OCT",test:/^[-+]?0[0-7_]+$/,resolve:(e,t,n)=>jh(e,1,8,n),stringify:e=>Kh(e,8,"0")},Qh={identify:Yh,default:!0,tag:"tag:yaml.org,2002:int",test:/^[-+]?[0-9][0-9_]*$/,resolve:(e,t,n)=>jh(e,0,10,n),stringify:_h},Wh={identify:Yh,default:!0,tag:"tag:yaml.org,2002:int",format:"HEX",test:/^[-+]?0x[0-9a-fA-F_]+$/,resolve:(e,t,n)=>jh(e,2,16,n),stringify:e=>Kh(e,16,"0x")};class Xh extends uh{constructor(e){super(e),this.tag=Xh.tag}add(e){let t;t=au(e)?e:e&&"object"==typeof e&&"key"in e&&"value"in e&&null===e.value?new rh(e.key,null):new rh(e,null);lh(this.items,t.key)||this.items.push(t)}get(e,t){const n=lh(this.items,e);return!t&&au(n)?cu(n.key)?n.key.value:n.key:n}set(e,t){if("boolean"!=typeof t)throw new Error("Expected boolean value for set(key, value) in a YAML set, not "+typeof t);const n=lh(this.items,e);n&&!t?this.items.splice(this.items.indexOf(n),1):!n&&t&&this.items.push(new rh(e))}toJSON(e,t){return super.toJSON(e,t,Set)}toString(e,t,n){if(!e)return JSON.stringify(this);if(this.hasAllNullValues(!0))return super.toString(Object.assign({},e,{allNullValues:!0}),t,n);throw new Error("Set items must all have null values")}static from(e,t,n){const{replacer:s}=n,r=new this(e);if(t&&Symbol.iterator in Object(t))for(let e of t)"function"==typeof s&&(e=s.call(t,e,e)),r.items.push(sh(e,null,n));return r}}Xh.tag="tag:yaml.org,2002:set";const Zh={collection:"map",identify:e=>e instanceof Set,nodeClass:Xh,default:!1,tag:"tag:yaml.org,2002:set",createNode:(e,t,n)=>Xh.from(e,t,n),resolve(e,t){if(ou(e)){if(e.hasAllNullValues(!0))return Object.assign(new Xh,e);t("Set items must all have null values")}else t("Expected a mapping for this tag");return e}};function Jh(e,t){const n=e[0],s="-"===n||"+"===n?e.substring(1):e,r=e=>t?BigInt(e):Number(e),i=s.replace(/_/g,"").split(":").reduce(((e,t)=>e*r(60)+r(t)),r(0));return"-"===n?r(-1)*i:i}function ed(e){let{value:t}=e,n=e=>e;if("bigint"==typeof t)n=e=>BigInt(e);else if(isNaN(t)||!isFinite(t))return _h(e);let s="";t<0&&(s="-",t*=n(-1));const r=n(60),i=[t%r];return t<60?i.unshift(0):(t=(t-i[0])/r,i.unshift(t%r),t>=60&&(t=(t-i[0])/r,i.unshift(t))),s+i.map((e=>String(e).padStart(2,"0"))).join(":").replace(/000000\d*$/,"")}const td={identify:e=>"bigint"==typeof e||Number.isInteger(e),default:!0,tag:"tag:yaml.org,2002:int",format:"TIME",test:/^[-+]?[0-9][0-9_]*(?::[0-5]?[0-9])+$/,resolve:(e,t,{intAsBigInt:n})=>Jh(e,n),stringify:ed},nd={identify:e=>"number"==typeof e,default:!0,tag:"tag:yaml.org,2002:float",format:"TIME",test:/^[-+]?[0-9][0-9_]*(?::[0-5]?[0-9])+\.[0-9_]*$/,resolve:e=>Jh(e,!1),stringify:ed},sd={identify:e=>e instanceof Date,default:!0,tag:"tag:yaml.org,2002:timestamp",test:RegExp("^([0-9]{4})-([0-9]{1,2})-([0-9]{1,2})(?:(?:t|T|[ \\t]+)([0-9]{1,2}):([0-9]{1,2}):([0-9]{1,2}(\\.[0-9]+)?)(?:[ \\t]*(Z|[-+][012]?[0-9](?::[0-9]{2})?))?)?$"),resolve(e){const t=e.match(sd.test);if(!t)throw new Error("!!timestamp expects a date, starting with yyyy-mm-dd");const[,n,s,r,i,o,a]=t.map(Number),c=t[7]?Number((t[7]+"00").substr(1,3)):0;let l=Date.UTC(n,s-1,r,i||0,o||0,a||0,c);const u=t[8];if(u&&"Z"!==u){let e=Jh(u,!1);Math.abs(e)<30&&(e*=60),l-=6e4*e}return new Date(l)},stringify:({value:e})=>e.toISOString().replace(/((T00:00)?:00)?\.000Z$/,"")},rd=[hh,fh,mh,Eh,Uh,Hh,Vh,zh,Qh,Wh,Gh,qh,$h,Mh,Fh,wh,Zh,td,nd,sd],id=new Map([["core",yh],["failsafe",[hh,fh,mh]],["json",Rh],["yaml11",rd],["yaml-1.1",rd]]),od={binary:Mh,bool:Th,float:Ch,floatExp:gh,floatNaN:Ah,floatTime:nd,int:Sh,intHex:Dh,intOct:kh,intTime:td,map:hh,null:Eh,omap:Fh,pairs:wh,seq:fh,set:Zh,timestamp:sd},ad={"tag:yaml.org,2002:binary":Mh,"tag:yaml.org,2002:omap":Fh,"tag:yaml.org,2002:pairs":wh,"tag:yaml.org,2002:set":Zh,"tag:yaml.org,2002:timestamp":sd};function cd(e,t){let n=id.get(t);if(!n){if(!Array.isArray(e)){const e=Array.from(id.keys()).filter((e=>"yaml11"!==e)).map((e=>JSON.stringify(e))).join(", ");throw new Error(`Unknown schema "${t}"; use one of ${e} or define customTags array`)}n=[]}if(Array.isArray(e))for(const t of e)n=n.concat(t);else"function"==typeof e&&(n=e(n.slice()));return n.map((e=>{if("string"!=typeof e)return e;const t=od[e];if(t)return t;const n=Object.keys(od).map((e=>JSON.stringify(e))).join(", ");throw new Error(`Unknown custom tag "${e}"; use one of ${n}`)}))}const ld=(e,t)=>e.key<t.key?-1:e.key>t.key?1:0;class ud{constructor({compat:e,customTags:t,merge:n,resolveKnownTags:s,schema:r,sortMapEntries:i,toStringDefaults:o}){this.compat=Array.isArray(e)?cd(e,"compat"):e?cd(null,e):null,this.merge=!!n,this.name="string"==typeof r&&r||"core",this.knownTags=s?ad:{},this.tags=cd(t,this.name),this.toStringOptions=o??null,Object.defineProperty(this,Jl,{value:hh}),Object.defineProperty(this,tu,{value:mh}),Object.defineProperty(this,nu,{value:fh}),this.sortMapEntries="function"==typeof i?i:!0===i?ld:null}clone(){const e=Object.create(ud.prototype,Object.getOwnPropertyDescriptors(this));return e.tags=this.tags.slice(),e}}class hd{constructor(e,t,n){this.commentBefore=null,this.comment=null,this.errors=[],this.warnings=[],Object.defineProperty(this,su,{value:Zl});let s=null;"function"==typeof t||Array.isArray(t)?s=t:void 0===n&&t&&(n=t,t=void 0);const r=Object.assign({intAsBigInt:!1,keepSourceTokens:!1,logLevel:"warn",prettyErrors:!0,strict:!0,uniqueKeys:!0,version:"1.2"},n);this.options=r;let{version:i}=r;(null==n?void 0:n._directives)?(this.directives=n._directives.atDocument(),this.directives.yaml.explicit&&(i=this.directives.yaml.version)):this.directives=new Au({version:i}),this.setSchema(i,n),this.contents=void 0===e?null:this.createNode(e,s,n)}clone(){const e=Object.create(hd.prototype,{[su]:{value:Zl}});return e.commentBefore=this.commentBefore,e.comment=this.comment,e.errors=this.errors.slice(),e.warnings=this.warnings.slice(),e.options=Object.assign({},this.options),this.directives&&(e.directives=this.directives.clone()),e.schema=this.schema.clone(),e.contents=hu(this.contents)?this.contents.clone(e.schema):this.contents,this.range&&(e.range=this.range.slice()),e}add(e){dd(this.contents)&&this.contents.add(e)}addIn(e,t){dd(this.contents)&&this.contents.addIn(e,t)}createAlias(e,t){if(!e.anchor){const n=Cu(this);e.anchor=!t||n.has(t)?bu(t||"a",n):t}return new Su(e.anchor)}createNode(e,t,n){let s;if("function"==typeof t)e=t.call({"":e},"",e),s=t;else if(Array.isArray(t)){const e=e=>"number"==typeof e||e instanceof String||e instanceof Number,n=t.filter(e).map(String);n.length>0&&(t=t.concat(n)),s=t}else void 0===n&&t&&(n=t,t=void 0);const{aliasDuplicateObjects:r,anchorPrefix:i,flow:o,keepUndefined:a,onTagObj:c,tag:l}=n??{},{onAnchor:u,setAnchors:h,sourceObjects:d}=function(e,t){const n=[],s=new Map;let r=null;return{onAnchor:s=>{n.push(s),r||(r=Cu(e));const i=bu(t,r);return r.add(i),i},setAnchors:()=>{for(const e of n){const t=s.get(e);if("object"!=typeof t||!t.anchor||!cu(t.node)&&!uu(t.node)){const t=new Error("Failed to resolve repeated object (this should not happen)");throw t.source=e,t}t.node.anchor=t.anchor}},sourceObjects:s}}(this,i||"a"),p=Ru(e,l,{aliasDuplicateObjects:r??!0,keepUndefined:a??!1,onAnchor:u,onTagObj:c,replacer:s,schema:this.schema,sourceObjects:d});return o&&uu(p)&&(p.flow=!0),h(),p}createPair(e,t,n={}){const s=this.createNode(e,null,n),r=this.createNode(t,null,n);return new rh(s,r)}delete(e){return!!dd(this.contents)&&this.contents.delete(e)}deleteIn(e){return vu(e)?null!=this.contents&&(this.contents=null,!0):!!dd(this.contents)&&this.contents.deleteIn(e)}get(e,t){return uu(this.contents)?this.contents.get(e,t):void 0}getIn(e,t){return vu(e)?!t&&cu(this.contents)?this.contents.value:this.contents:uu(this.contents)?this.contents.getIn(e,t):void 0}has(e){return!!uu(this.contents)&&this.contents.has(e)}hasIn(e){return vu(e)?void 0!==this.contents:!!uu(this.contents)&&this.contents.hasIn(e)}set(e,t){null==this.contents?this.contents=Mu(this.schema,[e],t):dd(this.contents)&&this.contents.set(e,t)}setIn(e,t){vu(e)?this.contents=t:null==this.contents?this.contents=Mu(this.schema,Array.from(e),t):dd(this.contents)&&this.contents.setIn(e,t)}setSchema(e,t={}){let n;switch("number"==typeof e&&(e=String(e)),e){case"1.1":this.directives?this.directives.yaml.version="1.1":this.directives=new Au({version:"1.1"}),n={merge:!0,resolveKnownTags:!1,schema:"yaml-1.1"};break;case"1.2":case"next":this.directives?this.directives.yaml.version=e:this.directives=new Au({version:e}),n={merge:!1,resolveKnownTags:!0,schema:"core"};break;case null:this.directives&&delete this.directives,n=null;break;default:{const t=JSON.stringify(e);throw new Error(`Expected '1.1', '1.2' or null as first argument, but found: ${t}`)}}if(t.schema instanceof Object)this.schema=t.schema;else{if(!n)throw new Error("With a null YAML version, the { schema: Schema } option is required");this.schema=new ud(Object.assign(n,t))}}toJS({json:e,jsonArg:t,mapAsMap:n,maxAliasCount:s,onAnchor:r,reviver:i}={}){const o={anchors:new Map,doc:this,keep:!e,mapAsMap:!0===n,mapKeyWarned:!1,maxAliasCount:"number"==typeof s?s:100},a=Iu(this.contents,t??"",o);if("function"==typeof r)for(const{count:e,res:t}of o.anchors.values())r(t,e);return"function"==typeof i?Nu(i,{"":a},"",a):a}toJSON(e,t){return this.toJS({json:!0,jsonArg:e,mapAsMap:!1,onAnchor:t})}toString(e={}){if(this.errors.length>0)throw new Error("Document with errors cannot be stringified");if("indent"in e&&(!Number.isInteger(e.indent)||Number(e.indent)<=0)){const t=JSON.stringify(e.indent);throw new Error(`"indent" option must be a positive integer, not ${t}`)}return function(e,t){var n;const s=[];let r=!0===t.directives;if(!1!==t.directives&&e.directives){const t=e.directives.toString(e);t?(s.push(t),r=!0):e.directives.docStart&&(r=!0)}r&&s.push("---");const i=Xu(e,t),{commentString:o}=i.options;if(e.commentBefore){1!==s.length&&s.unshift("");const t=o(e.commentBefore);s.unshift(Pu(t,""))}let a=!1,c=null;if(e.contents){if(hu(e.contents)){if(e.contents.spaceBefore&&r&&s.push(""),e.contents.commentBefore){const t=o(e.contents.commentBefore);s.push(Pu(t,""))}i.forceBlockIndent=!!e.comment,c=e.contents.comment}const t=c?void 0:()=>a=!0;let n=Zu(e.contents,i,(()=>c=null),t);c&&(n+=Fu(n,"",o(c))),"|"!==n[0]&&">"!==n[0]||"---"!==s[s.length-1]?s.push(n):s[s.length-1]=`--- ${n}`}else s.push(Zu(e.contents,i));if(null==(n=e.directives)?void 0:n.docEnd)if(e.comment){const t=o(e.comment);t.includes("\n")?(s.push("..."),s.push(Pu(t,""))):s.push(`... ${t}`)}else s.push("...");else{let t=e.comment;t&&a&&(t=t.replace(/^\n+/,"")),t&&(a&&!c||""===s[s.length-1]||s.push(""),s.push(Pu(o(t),"")))}return s.join("\n")+"\n"}(this,e)}}function dd(e){if(uu(e))return!0;throw new Error("Expected a YAML collection as document contents")}class pd extends Error{constructor(e,t,n,s){super(),this.name=e,this.code=n,this.message=s,this.pos=t}}class fd extends pd{constructor(e,t,n){super("YAMLParseError",e,t,n)}}class md extends pd{constructor(e,t,n){super("YAMLWarning",e,t,n)}}const Ed=(e,t)=>n=>{if(-1===n.pos[0])return;n.linePos=n.pos.map((e=>t.linePos(e)));const{line:s,col:r}=n.linePos[0];n.message+=` at line ${s}, column ${r}`;let i=r-1,o=e.substring(t.lineStarts[s-1],t.lineStarts[s]).replace(/[\n\r]+$/,"");if(i>=60&&o.length>80){const e=Math.min(i-39,o.length-79);o="…"+o.substring(e),i-=e-1}if(o.length>80&&(o=o.substring(0,79)+"…"),s>1&&/^ *$/.test(o.substring(0,i))){let n=e.substring(t.lineStarts[s-2],t.lineStarts[s-1]);n.length>80&&(n=n.substring(0,79)+"…\n"),o=n+o}if(/[^ ]/.test(o)){let e=1;const t=n.linePos[1];t&&t.line===s&&t.col>r&&(e=Math.max(1,Math.min(t.col-r,80-i)));const a=" ".repeat(i)+"^".repeat(e);n.message+=`:\n\n${o}\n${a}\n`}};function Td(e,{flow:t,indicator:n,next:s,offset:r,onError:i,parentIndent:o,startOnNewline:a}){let c=!1,l=a,u=a,h="",d="",p=!1,f=!1,m=null,E=null,T=null,_=null,A=null,g=null,C=null;for(const r of e)switch(f&&("space"!==r.type&&"newline"!==r.type&&"comma"!==r.type&&i(r.offset,"MISSING_CHAR","Tags and anchors must be separated from the next token by white space"),f=!1),m&&(l&&"comment"!==r.type&&"newline"!==r.type&&i(m,"TAB_AS_INDENT","Tabs are not allowed as indentation"),m=null),r.type){case"space":t||"doc-start"===n&&"flow-collection"===(null==s?void 0:s.type)||!r.source.includes("\t")||(m=r),u=!0;break;case"comment":{u||i(r,"MISSING_CHAR","Comments must be separated from other tokens by white space characters");const e=r.source.substring(1)||" ";h?h+=d+e:h=e,d="",l=!1;break}case"newline":l?h?h+=r.source:c=!0:d+=r.source,l=!0,p=!0,(E||T)&&(_=r),u=!0;break;case"anchor":E&&i(r,"MULTIPLE_ANCHORS","A node can have at most one anchor"),r.source.endsWith(":")&&i(r.offset+r.source.length-1,"BAD_ALIAS","Anchor ending in : is ambiguous",!0),E=r,null===C&&(C=r.offset),l=!1,u=!1,f=!0;break;case"tag":T&&i(r,"MULTIPLE_TAGS","A node can have at most one tag"),T=r,null===C&&(C=r.offset),l=!1,u=!1,f=!0;break;case n:(E||T)&&i(r,"BAD_PROP_ORDER",`Anchors and tags must be after the ${r.source} indicator`),g&&i(r,"UNEXPECTED_TOKEN",`Unexpected ${r.source} in ${t??"collection"}`),g=r,l="seq-item-ind"===n||"explicit-key-ind"===n,u=!1;break;case"comma":if(t){A&&i(r,"UNEXPECTED_TOKEN",`Unexpected , in ${t}`),A=r,l=!1,u=!1;break}default:i(r,"UNEXPECTED_TOKEN",`Unexpected ${r.type} token`),l=!1,u=!1}const b=e[e.length-1],N=b?b.offset+b.source.length:r;return f&&s&&"space"!==s.type&&"newline"!==s.type&&"comma"!==s.type&&("scalar"!==s.type||""!==s.source)&&i(s.offset,"MISSING_CHAR","Tags and anchors must be separated from the next token by white space"),m&&(l&&m.indent<=o||"block-map"===(null==s?void 0:s.type)||"block-seq"===(null==s?void 0:s.type))&&i(m,"TAB_AS_INDENT","Tabs are not allowed as indentation"),{comma:A,found:g,spaceBefore:c,comment:h,hasNewline:p,anchor:E,tag:T,newlineAfterProp:_,end:N,start:C??N}}function _d(e){if(!e)return null;switch(e.type){case"alias":case"scalar":case"double-quoted-scalar":case"single-quoted-scalar":if(e.source.includes("\n"))return!0;if(e.end)for(const t of e.end)if("newline"===t.type)return!0;return!1;case"flow-collection":for(const t of e.items){for(const e of t.start)if("newline"===e.type)return!0;if(t.sep)for(const e of t.sep)if("newline"===e.type)return!0;if(_d(t.key)||_d(t.value))return!0}return!1;default:return!0}}function Ad(e,t,n){if("flow-collection"===(null==t?void 0:t.type)){const s=t.end[0];if(s.indent===e&&("]"===s.source||"}"===s.source)&&_d(t)){n(s,"BAD_INDENT","Flow end indicator should be more indented than parent",!0)}}}function gd(e,t,n){const{uniqueKeys:s}=e.options;if(!1===s)return!1;const r="function"==typeof s?s:(t,n)=>t===n||cu(t)&&cu(n)&&t.value===n.value&&!("<<"===t.value&&e.schema.merge);return t.some((e=>r(e.key,n)))}const Cd="All mapping items must start at the same column";function bd(e,t,n,s){let r="";if(e){let i=!1,o="";for(const a of e){const{source:e,type:c}=a;switch(c){case"space":i=!0;break;case"comment":{n&&!i&&s(a,"MISSING_CHAR","Comments must be separated from other tokens by white space characters");const t=e.substring(1)||" ";r?r+=o+t:r=t,o="";break}case"newline":r&&(o+=e),i=!0;break;default:s(a,"UNEXPECTED_TOKEN",`Unexpected ${c} at node end`)}t+=e.length}}return{comment:r,offset:t}}const Nd="Block collections are not allowed within flow collections",Id=e=>e&&("block-map"===e.type||"block-seq"===e.type);function kd(e,t,n,s,r,i){const o="block-map"===n.type?function({composeNode:e,composeEmptyNode:t},n,s,r,i){var o;const a=new((null==i?void 0:i.nodeClass)??uh)(n.schema);n.atRoot&&(n.atRoot=!1);let c=s.offset,l=null;for(const i of s.items){const{start:u,key:h,sep:d,value:p}=i,f=Td(u,{indicator:"explicit-key-ind",next:h??(null==d?void 0:d[0]),offset:c,onError:r,parentIndent:s.indent,startOnNewline:!0}),m=!f.found;if(m){if(h&&("block-seq"===h.type?r(c,"BLOCK_AS_IMPLICIT_KEY","A block sequence may not be used as an implicit map key"):"indent"in h&&h.indent!==s.indent&&r(c,"BAD_INDENT",Cd)),!f.anchor&&!f.tag&&!d){l=f.end,f.comment&&(a.comment?a.comment+="\n"+f.comment:a.comment=f.comment);continue}(f.newlineAfterProp||_d(h))&&r(h??u[u.length-1],"MULTILINE_IMPLICIT_KEY","Implicit keys need to be on a single line")}else(null==(o=f.found)?void 0:o.indent)!==s.indent&&r(c,"BAD_INDENT",Cd);const E=f.end,T=h?e(n,h,f,r):t(n,E,u,null,f,r);n.schema.compat&&Ad(s.indent,h,r),gd(n,a.items,T)&&r(E,"DUPLICATE_KEY","Map keys must be unique");const _=Td(d??[],{indicator:"map-value-ind",next:p,offset:T.range[2],onError:r,parentIndent:s.indent,startOnNewline:!h||"block-scalar"===h.type});if(c=_.end,_.found){m&&("block-map"!==(null==p?void 0:p.type)||_.hasNewline||r(c,"BLOCK_AS_IMPLICIT_KEY","Nested mappings are not allowed in compact mappings"),n.options.strict&&f.start<_.found.offset-1024&&r(T.range,"KEY_OVER_1024_CHARS","The : indicator must be at most 1024 chars after the start of an implicit block mapping key"));const o=p?e(n,p,_,r):t(n,c,d,null,_,r);n.schema.compat&&Ad(s.indent,p,r),c=o.range[2];const l=new rh(T,o);n.options.keepSourceTokens&&(l.srcToken=i),a.items.push(l)}else{m&&r(T.range,"MISSING_CHAR","Implicit map keys need to be followed by map values"),_.comment&&(T.comment?T.comment+="\n"+_.comment:T.comment=_.comment);const e=new rh(T);n.options.keepSourceTokens&&(e.srcToken=i),a.items.push(e)}}return l&&l<c&&r(l,"IMPOSSIBLE","Map comment with trailing content"),a.range=[s.offset,c,l??c],a}(e,t,n,s,i):"block-seq"===n.type?function({composeNode:e,composeEmptyNode:t},n,s,r,i){const o=new((null==i?void 0:i.nodeClass)??dh)(n.schema);n.atRoot&&(n.atRoot=!1);let a=s.offset,c=null;for(const{start:i,value:l}of s.items){const u=Td(i,{indicator:"seq-item-ind",next:l,offset:a,onError:r,parentIndent:s.indent,startOnNewline:!0});if(!u.found){if(!(u.anchor||u.tag||l)){c=u.end,u.comment&&(o.comment=u.comment);continue}l&&"block-seq"===l.type?r(u.end,"BAD_INDENT","All sequence items must start at the same column"):r(a,"MISSING_CHAR","Sequence item without - indicator")}const h=l?e(n,l,u,r):t(n,u.end,i,null,u,r);n.schema.compat&&Ad(s.indent,l,r),a=h.range[2],o.items.push(h)}return o.range=[s.offset,a,c??a],o}(e,t,n,s,i):function({composeNode:e,composeEmptyNode:t},n,s,r,i){const o="{"===s.start.source,a=o?"flow map":"flow sequence",c=new((null==i?void 0:i.nodeClass)??(o?uh:dh))(n.schema);c.flow=!0;const l=n.atRoot;l&&(n.atRoot=!1);let u=s.offset+s.start.source.length;for(let i=0;i<s.items.length;++i){const l=s.items[i],{start:h,key:d,sep:p,value:f}=l,m=Td(h,{flow:a,indicator:"explicit-key-ind",next:d??(null==p?void 0:p[0]),offset:u,onError:r,parentIndent:s.indent,startOnNewline:!1});if(!m.found){if(!(m.anchor||m.tag||p||f)){0===i&&m.comma?r(m.comma,"UNEXPECTED_TOKEN",`Unexpected , in ${a}`):i<s.items.length-1&&r(m.start,"UNEXPECTED_TOKEN",`Unexpected empty item in ${a}`),m.comment&&(c.comment?c.comment+="\n"+m.comment:c.comment=m.comment),u=m.end;continue}!o&&n.options.strict&&_d(d)&&r(d,"MULTILINE_IMPLICIT_KEY","Implicit keys of flow sequence pairs need to be on a single line")}if(0===i)m.comma&&r(m.comma,"UNEXPECTED_TOKEN",`Unexpected , in ${a}`);else if(m.comma||r(m.start,"MISSING_CHAR",`Missing , between ${a} items`),m.comment){let e="";e:for(const t of h)switch(t.type){case"comma":case"space":break;case"comment":e=t.source.substring(1);break e;default:break e}if(e){let t=c.items[c.items.length-1];au(t)&&(t=t.value??t.key),t.comment?t.comment+="\n"+e:t.comment=e,m.comment=m.comment.substring(e.length+1)}}if(o||p||m.found){const i=m.end,E=d?e(n,d,m,r):t(n,i,h,null,m,r);Id(d)&&r(E.range,"BLOCK_IN_FLOW",Nd);const T=Td(p??[],{flow:a,indicator:"map-value-ind",next:f,offset:E.range[2],onError:r,parentIndent:s.indent,startOnNewline:!1});if(T.found){if(!o&&!m.found&&n.options.strict){if(p)for(const e of p){if(e===T.found)break;if("newline"===e.type){r(e,"MULTILINE_IMPLICIT_KEY","Implicit keys of flow sequence pairs need to be on a single line");break}}m.start<T.found.offset-1024&&r(T.found,"KEY_OVER_1024_CHARS","The : indicator must be at most 1024 chars after the start of an implicit flow sequence key")}}else f&&("source"in f&&f.source&&":"===f.source[0]?r(f,"MISSING_CHAR",`Missing space after : in ${a}`):r(T.start,"MISSING_CHAR",`Missing , or : between ${a} items`));const _=f?e(n,f,T,r):T.found?t(n,T.end,p,null,T,r):null;_?Id(f)&&r(_.range,"BLOCK_IN_FLOW",Nd):T.comment&&(E.comment?E.comment+="\n"+T.comment:E.comment=T.comment);const A=new rh(E,_);if(n.options.keepSourceTokens&&(A.srcToken=l),o){const e=c;gd(n,e.items,E)&&r(i,"DUPLICATE_KEY","Map keys must be unique"),e.items.push(A)}else{const e=new uh(n.schema);e.flow=!0,e.items.push(A);const t=(_??E).range;e.range=[E.range[0],t[1],t[2]],c.items.push(e)}u=_?_.range[2]:T.end}else{const s=f?e(n,f,m,r):t(n,m.end,p,null,m,r);c.items.push(s),u=s.range[2],Id(f)&&r(s.range,"BLOCK_IN_FLOW",Nd)}}const h=o?"}":"]",[d,...p]=s.end;let f=u;if(d&&d.source===h)f=d.offset+d.source.length;else{const e=a[0].toUpperCase()+a.substring(1);r(u,l?"MISSING_CHAR":"BAD_INDENT",l?`${e} must end with a ${h}`:`${e} in block collection must be sufficiently indented and end with a ${h}`),d&&1!==d.source.length&&p.unshift(d)}if(p.length>0){const e=bd(p,f,n.options.strict,r);e.comment&&(c.comment?c.comment+="\n"+e.comment:c.comment=e.comment),c.range=[s.offset,f,e.offset]}else c.range=[s.offset,f,f];return c}(e,t,n,s,i),a=o.constructor;return"!"===r||r===a.tagName?(o.tag=a.tagName,o):(r&&(o.tag=r),o)}function Sd(e,t,n){const s=t.offset,r=function({offset:e,props:t},n,s){if("block-scalar-header"!==t[0].type)return s(t[0],"IMPOSSIBLE","Block scalar header not found"),null;const{source:r}=t[0],i=r[0];let o=0,a="",c=-1;for(let t=1;t<r.length;++t){const n=r[t];if(a||"-"!==n&&"+"!==n){const s=Number(n);!o&&s?o=s:-1===c&&(c=e+t)}else a=n}-1!==c&&s(c,"UNEXPECTED_TOKEN",`Block scalar header includes extra characters: ${r}`);let l=!1,u="",h=r.length;for(let e=1;e<t.length;++e){const r=t[e];switch(r.type){case"space":l=!0;case"newline":h+=r.source.length;break;case"comment":if(n&&!l){s(r,"MISSING_CHAR","Comments must be separated from other tokens by white space characters")}h+=r.source.length,u=r.source.substring(1);break;case"error":s(r,"UNEXPECTED_TOKEN",r.message),h+=r.source.length;break;default:{s(r,"UNEXPECTED_TOKEN",`Unexpected token in block scalar header: ${r.type}`);const e=r.source;e&&"string"==typeof e&&(h+=e.length)}}}return{mode:i,indent:o,chomp:a,comment:u,length:h}}(t,e.options.strict,n);if(!r)return{value:"",type:null,comment:"",range:[s,s,s]};const i=">"===r.mode?Ou.BLOCK_FOLDED:Ou.BLOCK_LITERAL,o=t.source?function(e){const t=e.split(/\n( *)/),n=t[0],s=n.match(/^( *)/),r=[(null==s?void 0:s[1])?[s[1],n.slice(s[1].length)]:["",n]];for(let e=1;e<t.length;e+=2)r.push([t[e],t[e+1]]);return r}(t.source):[];let a=o.length;for(let e=o.length-1;e>=0;--e){const t=o[e][1];if(""!==t&&"\r"!==t)break;a=e}if(0===a){const e="+"===r.chomp&&o.length>0?"\n".repeat(Math.max(1,o.length-1)):"";let n=s+r.length;return t.source&&(n+=t.source.length),{value:e,type:i,comment:r.comment,range:[s,n,n]}}let c=t.indent+r.indent,l=t.offset+r.length,u=0;for(let t=0;t<a;++t){const[s,i]=o[t];if(""!==i&&"\r"!==i){if(s.length<c){const e="Block scalars with more-indented leading empty lines must use an explicit indentation indicator";n(l+s.length,"MISSING_CHAR",e)}if(0===r.indent&&(c=s.length),u=t,0===c&&!e.atRoot){n(l,"BAD_INDENT","Block scalar values in collections must be indented")}break}0===r.indent&&s.length>c&&(c=s.length),l+=s.length+i.length+1}for(let e=o.length-1;e>=a;--e)o[e][0].length>c&&(a=e+1);let h="",d="",p=!1;for(let e=0;e<u;++e)h+=o[e][0].slice(c)+"\n";for(let e=u;e<a;++e){let[t,s]=o[e];l+=t.length+s.length+1;const a="\r"===s[s.length-1];if(a&&(s=s.slice(0,-1)),s&&t.length<c){const e=`Block scalar lines must not be less indented than their ${r.indent?"explicit indentation indicator":"first line"}`;n(l-s.length-(a?2:1),"BAD_INDENT",e),t=""}i===Ou.BLOCK_LITERAL?(h+=d+t.slice(c)+s,d="\n"):t.length>c||"\t"===s[0]?(" "===d?d="\n":p||"\n"!==d||(d="\n\n"),h+=d+t.slice(c)+s,d="\n",p=!0):""===s?"\n"===d?h+="\n":d="\n":(h+=d+s,d=" ",p=!1)}switch(r.chomp){case"-":break;case"+":for(let e=a;e<o.length;++e)h+="\n"+o[e][0].slice(c);"\n"!==h[h.length-1]&&(h+="\n");break;default:h+="\n"}const f=s+r.length+t.source.length;return{value:h,type:i,comment:r.comment,range:[s,f,f]}}function Dd(e,t,n){const{offset:s,type:r,source:i,end:o}=e;let a,c;const l=(e,t,r)=>n(s+e,t,r);switch(r){case"scalar":a=Ou.PLAIN,c=function(e,t){let n="";switch(e[0]){case"\t":n="a tab character";break;case",":n="flow indicator character ,";break;case"%":n="directive indicator character %";break;case"|":case">":n=`block scalar indicator ${e[0]}`;break;case"@":case"`":n=`reserved character ${e[0]}`}n&&t(0,"BAD_SCALAR_START",`Plain value cannot start with ${n}`);return yd(e)}(i,l);break;case"single-quoted-scalar":a=Ou.QUOTE_SINGLE,c=function(e,t){"'"===e[e.length-1]&&1!==e.length||t(e.length,"MISSING_CHAR","Missing closing 'quote");return yd(e.slice(1,-1)).replace(/''/g,"'")}(i,l);break;case"double-quoted-scalar":a=Ou.QUOTE_DOUBLE,c=function(e,t){let n="";for(let s=1;s<e.length-1;++s){const r=e[s];if("\r"!==r||"\n"!==e[s+1])if("\n"===r){const{fold:t,offset:r}=Od(e,s);n+=t,s=r}else if("\\"===r){let r=e[++s];const i=Ld[r];if(i)n+=i;else if("\n"===r)for(r=e[s+1];" "===r||"\t"===r;)r=e[1+ ++s];else if("\r"===r&&"\n"===e[s+1])for(r=e[1+ ++s];" "===r||"\t"===r;)r=e[1+ ++s];else if("x"===r||"u"===r||"U"===r){const i={x:2,u:4,U:8}[r];n+=Rd(e,s+1,i,t),s+=i}else{const r=e.substr(s-1,2);t(s-1,"BAD_DQ_ESCAPE",`Invalid escape sequence ${r}`),n+=r}}else if(" "===r||"\t"===r){const t=s;let i=e[s+1];for(;" "===i||"\t"===i;)i=e[1+ ++s];"\n"===i||"\r"===i&&"\n"===e[s+2]||(n+=s>t?e.slice(t,s+1):r)}else n+=r}'"'===e[e.length-1]&&1!==e.length||t(e.length,"MISSING_CHAR",'Missing closing "quote');return n}(i,l);break;default:return n(e,"UNEXPECTED_TOKEN",`Expected a flow scalar value, but found: ${r}`),{value:"",type:null,comment:"",range:[s,s+i.length,s+i.length]}}const u=s+i.length,h=bd(o,u,t,n);return{value:c,type:a,comment:h.comment,range:[s,u,h.offset]}}function yd(e){let t,n;try{t=new RegExp("(.*?)(?<![ \t])[ \t]*\r?\n","sy"),n=new RegExp("[ \t]*(.*?)(?:(?<![ \t])[ \t]*)?\r?\n","sy")}catch{t=/(.*?)[ \t]*\r?\n/sy,n=/[ \t]*(.*?)[ \t]*\r?\n/sy}let s=t.exec(e);if(!s)return e;let r=s[1],i=" ",o=t.lastIndex;for(n.lastIndex=o;s=n.exec(e);)""===s[1]?"\n"===i?r+=i:i="\n":(r+=i+s[1],i=" "),o=n.lastIndex;const a=/[ \t]*(.*)/sy;return a.lastIndex=o,s=a.exec(e),r+i+((null==s?void 0:s[1])??"")}function Od(e,t){let n="",s=e[t+1];for(;!(" "!==s&&"\t"!==s&&"\n"!==s&&"\r"!==s||"\r"===s&&"\n"!==e[t+2]);)"\n"===s&&(n+="\n"),s=e[(t+=1)+1];return n||(n=" "),{fold:n,offset:t}}const Ld={0:"\0",a:"",b:"\b",e:"",f:"\f",n:"\n",r:"\r",t:"\t",v:"\v",N:"",_:" ",L:"\u2028",P:"\u2029"," ":" ",'"':'"',"/":"/","\\":"\\","\t":"\t"};function Rd(e,t,n,s){const r=e.substr(t,n),i=r.length===n&&/^[0-9a-fA-F]+$/.test(r)?parseInt(r,16):NaN;if(isNaN(i)){const r=e.substr(t-2,n+2);return s(t-2,"BAD_DQ_ESCAPE",`Invalid escape sequence ${r}`),r}return String.fromCodePoint(i)}function Md(e,t,n,s){const{value:r,type:i,comment:o,range:a}="block-scalar"===t.type?Sd(e,t,s):Dd(t,e.options.strict,s),c=n?e.directives.tagName(n.source,(e=>s(n,"TAG_RESOLVE_FAILED",e))):null,l=n&&c?function(e,t,n,s,r){var i;if("!"===n)return e[tu];const o=[];for(const t of e.tags)if(!t.collection&&t.tag===n){if(!t.default||!t.test)return t;o.push(t)}for(const e of o)if(null==(i=e.test)?void 0:i.test(t))return e;const a=e.knownTags[n];if(a&&!a.collection)return e.tags.push(Object.assign({},a,{default:!1,test:void 0})),a;return r(s,"TAG_RESOLVE_FAILED",`Unresolved tag: ${n}`,"tag:yaml.org,2002:str"!==n),e[tu]}(e.schema,r,c,n,s):"scalar"===t.type?function({directives:e,schema:t},n,s,r){const i=t.tags.find((e=>{var t;return e.default&&(null==(t=e.test)?void 0:t.test(n))}))||t[tu];if(t.compat){const o=t.compat.find((e=>{var t;return e.default&&(null==(t=e.test)?void 0:t.test(n))}))??t[tu];if(i.tag!==o.tag){r(s,"TAG_RESOLVE_FAILED",`Value may be parsed as either ${e.tagString(i.tag)} or ${e.tagString(o.tag)}`,!0)}}return i}(e,r,t,s):e.schema[tu];let u;try{const i=l.resolve(r,(e=>s(n??t,"TAG_RESOLVE_FAILED",e)),e.options);u=cu(i)?i:new Ou(i)}catch(e){const i=e instanceof Error?e.message:String(e);s(n??t,"TAG_RESOLVE_FAILED",i),u=new Ou(r)}return u.range=a,u.source=r,i&&(u.type=i),c&&(u.tag=c),l.format&&(u.format=l.format),o&&(u.comment=o),u}function vd(e,t,n){if(t){null===n&&(n=t.length);for(let s=n-1;s>=0;--s){let n=t[s];switch(n.type){case"space":case"comment":case"newline":e-=n.source.length;continue}for(n=t[++s];"space"===(null==n?void 0:n.type);)e+=n.source.length,n=t[++s];break}}return e}const xd={composeNode:wd,composeEmptyNode:Pd};function wd(e,t,n,s){const{spaceBefore:r,comment:i,anchor:o,tag:a}=n;let c,l=!0;switch(t.type){case"alias":c=function({options:e},{offset:t,source:n,end:s},r){const i=new Su(n.substring(1));""===i.source&&r(t,"BAD_ALIAS","Alias cannot be an empty string");i.source.endsWith(":")&&r(t+n.length-1,"BAD_ALIAS","Alias ending in : is ambiguous",!0);const o=t+n.length,a=bd(s,o,e.strict,r);i.range=[t,o,a.offset],a.comment&&(i.comment=a.comment);return i}(e,t,s),(o||a)&&s(t,"ALIAS_PROPS","An alias node must not specify any properties");break;case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":case"block-scalar":c=Md(e,t,a,s),o&&(c.anchor=o.source.substring(1));break;case"block-map":case"block-seq":case"flow-collection":c=function(e,t,n,s,r){var i;const o=s.tag,a=o?t.directives.tagName(o.source,(e=>r(o,"TAG_RESOLVE_FAILED",e))):null;if("block-seq"===n.type){const{anchor:e,newlineAfterProp:t}=s,n=e&&o?e.offset>o.offset?e:o:e??o;n&&(!t||t.offset<n.offset)&&r(n,"MISSING_CHAR","Missing newline after block sequence props")}const c="block-map"===n.type?"map":"block-seq"===n.type?"seq":"{"===n.start.source?"map":"seq";if(!o||!a||"!"===a||a===uh.tagName&&"map"===c||a===dh.tagName&&"seq"===c)return kd(e,t,n,r,a);let l=t.schema.tags.find((e=>e.tag===a&&e.collection===c));if(!l){const s=t.schema.knownTags[a];if(!s||s.collection!==c)return(null==s?void 0:s.collection)?r(o,"BAD_COLLECTION_TYPE",`${s.tag} used for ${c} collection, but expects ${s.collection}`,!0):r(o,"TAG_RESOLVE_FAILED",`Unresolved tag: ${a}`,!0),kd(e,t,n,r,a);t.schema.tags.push(Object.assign({},s,{default:!1})),l=s}const u=kd(e,t,n,r,a,l),h=(null==(i=l.resolve)?void 0:i.call(l,u,(e=>r(o,"TAG_RESOLVE_FAILED",e)),t.options))??u,d=hu(h)?h:new Ou(h);return d.range=u.range,d.tag=a,(null==l?void 0:l.format)&&(d.format=l.format),d}(xd,e,t,n,s),o&&(c.anchor=o.source.substring(1));break;default:s(t,"UNEXPECTED_TOKEN","error"===t.type?t.message:`Unsupported token (type: ${t.type})`),c=Pd(e,t.offset,void 0,null,n,s),l=!1}return o&&""===c.anchor&&s(o,"BAD_ALIAS","Anchor cannot be an empty string"),r&&(c.spaceBefore=!0),i&&("scalar"===t.type&&""===t.source?c.comment=i:c.commentBefore=i),e.options.keepSourceTokens&&l&&(c.srcToken=t),c}function Pd(e,t,n,s,{spaceBefore:r,comment:i,anchor:o,tag:a,end:c},l){const u=Md(e,{type:"scalar",offset:vd(t,n,s),indent:-1,source:""},a,l);return o&&(u.anchor=o.source.substring(1),""===u.anchor&&l(o,"BAD_ALIAS","Anchor cannot be an empty string")),r&&(u.spaceBefore=!0),i&&(u.comment=i,u.range[2]=c),u}function Fd(e){if("number"==typeof e)return[e,e+1];if(Array.isArray(e))return 2===e.length?e:[e[0],e[1]];const{offset:t,source:n}=e;return[t,t+("string"==typeof n?n.length:1)]}function Bd(e){var t;let n="",s=!1,r=!1;for(let i=0;i<e.length;++i){const o=e[i];switch(o[0]){case"#":n+=(""===n?"":r?"\n\n":"\n")+(o.substring(1)||" "),s=!0,r=!1;break;case"%":"#"!==(null==(t=e[i+1])?void 0:t[0])&&(i+=1),s=!1;break;default:s||(r=!0),s=!1}}return{comment:n,afterEmptyLine:r}}class Ud{constructor(e={}){this.doc=null,this.atDirectives=!1,this.prelude=[],this.errors=[],this.warnings=[],this.onError=(e,t,n,s)=>{const r=Fd(e);s?this.warnings.push(new md(r,t,n)):this.errors.push(new fd(r,t,n))},this.directives=new Au({version:e.version||"1.2"}),this.options=e}decorate(e,t){const{comment:n,afterEmptyLine:s}=Bd(this.prelude);if(n){const r=e.contents;if(t)e.comment=e.comment?`${e.comment}\n${n}`:n;else if(s||e.directives.docStart||!r)e.commentBefore=n;else if(uu(r)&&!r.flow&&r.items.length>0){let e=r.items[0];au(e)&&(e=e.key);const t=e.commentBefore;e.commentBefore=t?`${n}\n${t}`:n}else{const e=r.commentBefore;r.commentBefore=e?`${n}\n${e}`:n}}t?(Array.prototype.push.apply(e.errors,this.errors),Array.prototype.push.apply(e.warnings,this.warnings)):(e.errors=this.errors,e.warnings=this.warnings),this.prelude=[],this.errors=[],this.warnings=[]}streamInfo(){return{comment:Bd(this.prelude).comment,directives:this.directives,errors:this.errors,warnings:this.warnings}}*compose(e,t=!1,n=-1){for(const t of e)yield*this.next(t);yield*this.end(t,n)}*next(e){switch(e.type){case"directive":this.directives.add(e.source,((t,n,s)=>{const r=Fd(e);r[0]+=t,this.onError(r,"BAD_DIRECTIVE",n,s)})),this.prelude.push(e.source),this.atDirectives=!0;break;case"document":{const t=function(e,t,{offset:n,start:s,value:r,end:i},o){const a=Object.assign({_directives:t},e),c=new hd(void 0,a),l={atRoot:!0,directives:c.directives,options:c.options,schema:c.schema},u=Td(s,{indicator:"doc-start",next:r??(null==i?void 0:i[0]),offset:n,onError:o,parentIndent:0,startOnNewline:!0});u.found&&(c.directives.docStart=!0,!r||"block-map"!==r.type&&"block-seq"!==r.type||u.hasNewline||o(u.end,"MISSING_CHAR","Block collection cannot start on same line with directives-end marker")),c.contents=r?wd(l,r,u,o):Pd(l,u.end,s,null,u,o);const h=c.contents.range[2],d=bd(i,h,!1,o);return d.comment&&(c.comment=d.comment),c.range=[n,h,d.offset],c}(this.options,this.directives,e,this.onError);this.atDirectives&&!t.directives.docStart&&this.onError(e,"MISSING_CHAR","Missing directives-end/doc-start indicator line"),this.decorate(t,!1),this.doc&&(yield this.doc),this.doc=t,this.atDirectives=!1;break}case"byte-order-mark":case"space":break;case"comment":case"newline":this.prelude.push(e.source);break;case"error":{const t=e.source?`${e.message}: ${JSON.stringify(e.source)}`:e.message,n=new fd(Fd(e),"UNEXPECTED_TOKEN",t);this.atDirectives||!this.doc?this.errors.push(n):this.doc.errors.push(n);break}case"doc-end":{if(!this.doc){const t="Unexpected doc-end without preceding document";this.errors.push(new fd(Fd(e),"UNEXPECTED_TOKEN",t));break}this.doc.directives.docEnd=!0;const t=bd(e.end,e.offset+e.source.length,this.doc.options.strict,this.onError);if(this.decorate(this.doc,!0),t.comment){const e=this.doc.comment;this.doc.comment=e?`${e}\n${t.comment}`:t.comment}this.doc.range[2]=t.offset;break}default:this.errors.push(new fd(Fd(e),"UNEXPECTED_TOKEN",`Unsupported token ${e.type}`))}}*end(e=!1,t=-1){if(this.doc)this.decorate(this.doc,!0),yield this.doc,this.doc=null;else if(e){const e=Object.assign({_directives:this.directives},this.options),n=new hd(void 0,e);this.atDirectives&&this.onError(t,"MISSING_CHAR","Missing directives-end indicator line"),n.range=[0,t,t],this.decorate(n,!1),yield n}}}function Hd(e){switch(e){case void 0:case" ":case"\n":case"\r":case"\t":return!0;default:return!1}}const Gd=new Set("0123456789ABCDEFabcdef"),qd=new Set("0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-#;/?:@&=+$_.!~*'()"),$d=new Set(",[]{}"),Yd=new Set(" ,[]{}\n\r\t"),jd=e=>!e||Yd.has(e);class Kd{constructor(){this.atEnd=!1,this.blockScalarIndent=-1,this.blockScalarKeep=!1,this.buffer="",this.flowKey=!1,this.flowLevel=0,this.indentNext=0,this.indentValue=0,this.lineEndPos=null,this.next=null,this.pos=0}*lex(e,t=!1){if(e){if("string"!=typeof e)throw TypeError("source is not a string");this.buffer=this.buffer?this.buffer+e:e,this.lineEndPos=null}this.atEnd=!t;let n=this.next??"stream";for(;n&&(t||this.hasChars(1));)n=yield*this.parseNext(n)}atLineEnd(){let e=this.pos,t=this.buffer[e];for(;" "===t||"\t"===t;)t=this.buffer[++e];return!t||"#"===t||"\n"===t||"\r"===t&&"\n"===this.buffer[e+1]}charAt(e){return this.buffer[this.pos+e]}continueScalar(e){let t=this.buffer[e];if(this.indentNext>0){let n=0;for(;" "===t;)t=this.buffer[++n+e];if("\r"===t){const t=this.buffer[n+e+1];if("\n"===t||!t&&!this.atEnd)return e+n+1}return"\n"===t||n>=this.indentNext||!t&&!this.atEnd?e+n:-1}if("-"===t||"."===t){const t=this.buffer.substr(e,3);if(("---"===t||"..."===t)&&Hd(this.buffer[e+3]))return-1}return e}getLine(){let e=this.lineEndPos;return("number"!=typeof e||-1!==e&&e<this.pos)&&(e=this.buffer.indexOf("\n",this.pos),this.lineEndPos=e),-1===e?this.atEnd?this.buffer.substring(this.pos):null:("\r"===this.buffer[e-1]&&(e-=1),this.buffer.substring(this.pos,e))}hasChars(e){return this.pos+e<=this.buffer.length}setNext(e){return this.buffer=this.buffer.substring(this.pos),this.pos=0,this.lineEndPos=null,this.next=e,null}peek(e){return this.buffer.substr(this.pos,e)}*parseNext(e){switch(e){case"stream":return yield*this.parseStream();case"line-start":return yield*this.parseLineStart();case"block-start":return yield*this.parseBlockStart();case"doc":return yield*this.parseDocument();case"flow":return yield*this.parseFlowCollection();case"quoted-scalar":return yield*this.parseQuotedScalar();case"block-scalar":return yield*this.parseBlockScalar();case"plain-scalar":return yield*this.parsePlainScalar()}}*parseStream(){let e=this.getLine();if(null===e)return this.setNext("stream");if("\ufeff"===e[0]&&(yield*this.pushCount(1),e=e.substring(1)),"%"===e[0]){let t=e.length,n=e.indexOf("#");for(;-1!==n;){const s=e[n-1];if(" "===s||"\t"===s){t=n-1;break}n=e.indexOf("#",n+1)}for(;;){const n=e[t-1];if(" "!==n&&"\t"!==n)break;t-=1}const s=(yield*this.pushCount(t))+(yield*this.pushSpaces(!0));return yield*this.pushCount(e.length-s),this.pushNewline(),"stream"}if(this.atLineEnd()){const t=yield*this.pushSpaces(!0);return yield*this.pushCount(e.length-t),yield*this.pushNewline(),"stream"}return yield"",yield*this.parseLineStart()}*parseLineStart(){const e=this.charAt(0);if(!e&&!this.atEnd)return this.setNext("line-start");if("-"===e||"."===e){if(!this.atEnd&&!this.hasChars(4))return this.setNext("line-start");const e=this.peek(3);if(("---"===e||"..."===e)&&Hd(this.charAt(3)))return yield*this.pushCount(3),this.indentValue=0,this.indentNext=0,"---"===e?"doc":"stream"}return this.indentValue=yield*this.pushSpaces(!1),this.indentNext>this.indentValue&&!Hd(this.charAt(1))&&(this.indentNext=this.indentValue),yield*this.parseBlockStart()}*parseBlockStart(){const[e,t]=this.peek(2);if(!t&&!this.atEnd)return this.setNext("block-start");if(("-"===e||"?"===e||":"===e)&&Hd(t)){const e=(yield*this.pushCount(1))+(yield*this.pushSpaces(!0));return this.indentNext=this.indentValue+1,this.indentValue+=e,yield*this.parseBlockStart()}return"doc"}*parseDocument(){yield*this.pushSpaces(!0);const e=this.getLine();if(null===e)return this.setNext("doc");let t=yield*this.pushIndicators();switch(e[t]){case"#":yield*this.pushCount(e.length-t);case void 0:return yield*this.pushNewline(),yield*this.parseLineStart();case"{":case"[":return yield*this.pushCount(1),this.flowKey=!1,this.flowLevel=1,"flow";case"}":case"]":return yield*this.pushCount(1),"doc";case"*":return yield*this.pushUntil(jd),"doc";case'"':case"'":return yield*this.parseQuotedScalar();case"|":case">":return t+=(yield*this.parseBlockScalarHeader()),t+=(yield*this.pushSpaces(!0)),yield*this.pushCount(e.length-t),yield*this.pushNewline(),yield*this.parseBlockScalar();default:return yield*this.parsePlainScalar()}}*parseFlowCollection(){let e,t,n=-1;do{e=yield*this.pushNewline(),e>0?(t=yield*this.pushSpaces(!1),this.indentValue=n=t):t=0,t+=(yield*this.pushSpaces(!0))}while(e+t>0);const s=this.getLine();if(null===s)return this.setNext("flow");if(-1!==n&&n<this.indentNext&&"#"!==s[0]||0===n&&(s.startsWith("---")||s.startsWith("..."))&&Hd(s[3])){if(!(n===this.indentNext-1&&1===this.flowLevel&&("]"===s[0]||"}"===s[0])))return this.flowLevel=0,yield"",yield*this.parseLineStart()}let r=0;for(;","===s[r];)r+=(yield*this.pushCount(1)),r+=(yield*this.pushSpaces(!0)),this.flowKey=!1;switch(r+=(yield*this.pushIndicators()),s[r]){case void 0:return"flow";case"#":return yield*this.pushCount(s.length-r),"flow";case"{":case"[":return yield*this.pushCount(1),this.flowKey=!1,this.flowLevel+=1,"flow";case"}":case"]":return yield*this.pushCount(1),this.flowKey=!0,this.flowLevel-=1,this.flowLevel?"flow":"doc";case"*":return yield*this.pushUntil(jd),"flow";case'"':case"'":return this.flowKey=!0,yield*this.parseQuotedScalar();case":":{const e=this.charAt(1);if(this.flowKey||Hd(e)||","===e)return this.flowKey=!1,yield*this.pushCount(1),yield*this.pushSpaces(!0),"flow"}default:return this.flowKey=!1,yield*this.parsePlainScalar()}}*parseQuotedScalar(){const e=this.charAt(0);let t=this.buffer.indexOf(e,this.pos+1);if("'"===e)for(;-1!==t&&"'"===this.buffer[t+1];)t=this.buffer.indexOf("'",t+2);else for(;-1!==t;){let e=0;for(;"\\"===this.buffer[t-1-e];)e+=1;if(e%2==0)break;t=this.buffer.indexOf('"',t+1)}const n=this.buffer.substring(0,t);let s=n.indexOf("\n",this.pos);if(-1!==s){for(;-1!==s;){const e=this.continueScalar(s+1);if(-1===e)break;s=n.indexOf("\n",e)}-1!==s&&(t=s-("\r"===n[s-1]?2:1))}if(-1===t){if(!this.atEnd)return this.setNext("quoted-scalar");t=this.buffer.length}return yield*this.pushToIndex(t+1,!1),this.flowLevel?"flow":"doc"}*parseBlockScalarHeader(){this.blockScalarIndent=-1,this.blockScalarKeep=!1;let e=this.pos;for(;;){const t=this.buffer[++e];if("+"===t)this.blockScalarKeep=!0;else if(t>"0"&&t<="9")this.blockScalarIndent=Number(t)-1;else if("-"!==t)break}return yield*this.pushUntil((e=>Hd(e)||"#"===e))}*parseBlockScalar(){let e,t=this.pos-1,n=0;e:for(let s=this.pos;e=this.buffer[s];++s)switch(e){case" ":n+=1;break;case"\n":t=s,n=0;break;case"\r":{const e=this.buffer[s+1];if(!e&&!this.atEnd)return this.setNext("block-scalar");if("\n"===e)break}default:break e}if(!e&&!this.atEnd)return this.setNext("block-scalar");if(n>=this.indentNext){-1===this.blockScalarIndent?this.indentNext=n:this.indentNext=this.blockScalarIndent+(0===this.indentNext?1:this.indentNext);do{const e=this.continueScalar(t+1);if(-1===e)break;t=this.buffer.indexOf("\n",e)}while(-1!==t);if(-1===t){if(!this.atEnd)return this.setNext("block-scalar");t=this.buffer.length}}let s=t+1;for(e=this.buffer[s];" "===e;)e=this.buffer[++s];if("\t"===e){for(;"\t"===e||" "===e||"\r"===e||"\n"===e;)e=this.buffer[++s];t=s-1}else if(!this.blockScalarKeep)for(;;){let e=t-1,s=this.buffer[e];"\r"===s&&(s=this.buffer[--e]);const r=e;for(;" "===s;)s=this.buffer[--e];if(!("\n"===s&&e>=this.pos&&e+1+n>r))break;t=e}return yield"",yield*this.pushToIndex(t+1,!0),yield*this.parseLineStart()}*parsePlainScalar(){const e=this.flowLevel>0;let t,n=this.pos-1,s=this.pos-1;for(;t=this.buffer[++s];)if(":"===t){const t=this.buffer[s+1];if(Hd(t)||e&&$d.has(t))break;n=s}else if(Hd(t)){let r=this.buffer[s+1];if("\r"===t&&("\n"===r?(s+=1,t="\n",r=this.buffer[s+1]):n=s),"#"===r||e&&$d.has(r))break;if("\n"===t){const e=this.continueScalar(s+1);if(-1===e)break;s=Math.max(s,e-2)}}else{if(e&&$d.has(t))break;n=s}return t||this.atEnd?(yield"",yield*this.pushToIndex(n+1,!0),e?"flow":"doc"):this.setNext("plain-scalar")}*pushCount(e){return e>0?(yield this.buffer.substr(this.pos,e),this.pos+=e,e):0}*pushToIndex(e,t){const n=this.buffer.slice(this.pos,e);return n?(yield n,this.pos+=n.length,n.length):(t&&(yield""),0)}*pushIndicators(){switch(this.charAt(0)){case"!":return(yield*this.pushTag())+(yield*this.pushSpaces(!0))+(yield*this.pushIndicators());case"&":return(yield*this.pushUntil(jd))+(yield*this.pushSpaces(!0))+(yield*this.pushIndicators());case"-":case"?":case":":{const e=this.flowLevel>0,t=this.charAt(1);if(Hd(t)||e&&$d.has(t))return e?this.flowKey&&(this.flowKey=!1):this.indentNext=this.indentValue+1,(yield*this.pushCount(1))+(yield*this.pushSpaces(!0))+(yield*this.pushIndicators())}}return 0}*pushTag(){if("<"===this.charAt(1)){let e=this.pos+2,t=this.buffer[e];for(;!Hd(t)&&">"!==t;)t=this.buffer[++e];return yield*this.pushToIndex(">"===t?e+1:e,!1)}{let e=this.pos+1,t=this.buffer[e];for(;t;)if(qd.has(t))t=this.buffer[++e];else{if("%"!==t||!Gd.has(this.buffer[e+1])||!Gd.has(this.buffer[e+2]))break;t=this.buffer[e+=3]}return yield*this.pushToIndex(e,!1)}}*pushNewline(){const e=this.buffer[this.pos];return"\n"===e?yield*this.pushCount(1):"\r"===e&&"\n"===this.charAt(1)?yield*this.pushCount(2):0}*pushSpaces(e){let t,n=this.pos-1;do{t=this.buffer[++n]}while(" "===t||e&&"\t"===t);const s=n-this.pos;return s>0&&(yield this.buffer.substr(this.pos,s),this.pos=n),s}*pushUntil(e){let t=this.pos,n=this.buffer[t];for(;!e(n);)n=this.buffer[++t];return yield*this.pushToIndex(t,!1)}}class Vd{constructor(){this.lineStarts=[],this.addNewLine=e=>this.lineStarts.push(e),this.linePos=e=>{let t=0,n=this.lineStarts.length;for(;t<n;){const s=t+n>>1;this.lineStarts[s]<e?t=s+1:n=s}if(this.lineStarts[t]===e)return{line:t+1,col:1};if(0===t)return{line:0,col:e};return{line:t,col:e-this.lineStarts[t-1]+1}}}}function zd(e,t){for(let n=0;n<e.length;++n)if(e[n].type===t)return!0;return!1}function Qd(e){for(let t=0;t<e.length;++t)switch(e[t].type){case"space":case"comment":case"newline":break;default:return t}return-1}function Wd(e){switch(null==e?void 0:e.type){case"alias":case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":case"flow-collection":return!0;default:return!1}}function Xd(e){switch(e.type){case"document":return e.start;case"block-map":{const t=e.items[e.items.length-1];return t.sep??t.start}case"block-seq":return e.items[e.items.length-1].start;default:return[]}}function Zd(e){var t;if(0===e.length)return[];let n=e.length;e:for(;--n>=0;)switch(e[n].type){case"doc-start":case"explicit-key-ind":case"map-value-ind":case"seq-item-ind":case"newline":break e}for(;"space"===(null==(t=e[++n])?void 0:t.type););return e.splice(n,e.length)}function Jd(e){if("flow-seq-start"===e.start.type)for(const t of e.items)!t.sep||t.value||zd(t.start,"explicit-key-ind")||zd(t.sep,"map-value-ind")||(t.key&&(t.value=t.key),delete t.key,Wd(t.value)?t.value.end?Array.prototype.push.apply(t.value.end,t.sep):t.value.end=t.sep:Array.prototype.push.apply(t.start,t.sep),delete t.sep)}class ep{constructor(e){this.atNewLine=!0,this.atScalar=!1,this.indent=0,this.offset=0,this.onKeyLine=!1,this.stack=[],this.source="",this.type="",this.lexer=new Kd,this.onNewLine=e}*parse(e,t=!1){this.onNewLine&&0===this.offset&&this.onNewLine(0);for(const n of this.lexer.lex(e,t))yield*this.next(n);t||(yield*this.end())}*next(e){if(this.source=e,this.atScalar)return this.atScalar=!1,yield*this.step(),void(this.offset+=e.length);const t=function(e){switch(e){case"\ufeff":return"byte-order-mark";case"":return"doc-mode";case"":return"flow-error-end";case"":return"scalar";case"---":return"doc-start";case"...":return"doc-end";case"":case"\n":case"\r\n":return"newline";case"-":return"seq-item-ind";case"?":return"explicit-key-ind";case":":return"map-value-ind";case"{":return"flow-map-start";case"}":return"flow-map-end";case"[":return"flow-seq-start";case"]":return"flow-seq-end";case",":return"comma"}switch(e[0]){case" ":case"\t":return"space";case"#":return"comment";case"%":return"directive-line";case"*":return"alias";case"&":return"anchor";case"!":return"tag";case"'":return"single-quoted-scalar";case'"':return"double-quoted-scalar";case"|":case">":return"block-scalar-header"}return null}(e);if(t)if("scalar"===t)this.atNewLine=!1,this.atScalar=!0,this.type="scalar";else{switch(this.type=t,yield*this.step(),t){case"newline":this.atNewLine=!0,this.indent=0,this.onNewLine&&this.onNewLine(this.offset+e.length);break;case"space":this.atNewLine&&" "===e[0]&&(this.indent+=e.length);break;case"explicit-key-ind":case"map-value-ind":case"seq-item-ind":this.atNewLine&&(this.indent+=e.length);break;case"doc-mode":case"flow-error-end":return;default:this.atNewLine=!1}this.offset+=e.length}else{const t=`Not a YAML token: ${e}`;yield*this.pop({type:"error",offset:this.offset,message:t,source:e}),this.offset+=e.length}}*end(){for(;this.stack.length>0;)yield*this.pop()}get sourceToken(){return{type:this.type,offset:this.offset,indent:this.indent,source:this.source}}*step(){const e=this.peek(1);if("doc-end"!==this.type||e&&"doc-end"===e.type){if(!e)return yield*this.stream();switch(e.type){case"document":return yield*this.document(e);case"alias":case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":return yield*this.scalar(e);case"block-scalar":return yield*this.blockScalar(e);case"block-map":return yield*this.blockMap(e);case"block-seq":return yield*this.blockSequence(e);case"flow-collection":return yield*this.flowCollection(e);case"doc-end":return yield*this.documentEnd(e)}yield*this.pop()}else{for(;this.stack.length>0;)yield*this.pop();this.stack.push({type:"doc-end",offset:this.offset,source:this.source})}}peek(e){return this.stack[this.stack.length-e]}*pop(e){const t=e??this.stack.pop();if(t)if(0===this.stack.length)yield t;else{const e=this.peek(1);switch("block-scalar"===t.type?t.indent="indent"in e?e.indent:0:"flow-collection"===t.type&&"document"===e.type&&(t.indent=0),"flow-collection"===t.type&&Jd(t),e.type){case"document":e.value=t;break;case"block-scalar":e.props.push(t);break;case"block-map":{const n=e.items[e.items.length-1];if(n.value)return e.items.push({start:[],key:t,sep:[]}),void(this.onKeyLine=!0);if(!n.sep)return Object.assign(n,{key:t,sep:[]}),void(this.onKeyLine=!n.explicitKey);n.value=t;break}case"block-seq":{const n=e.items[e.items.length-1];n.value?e.items.push({start:[],value:t}):n.value=t;break}case"flow-collection":{const n=e.items[e.items.length-1];return void(!n||n.value?e.items.push({start:[],key:t,sep:[]}):n.sep?n.value=t:Object.assign(n,{key:t,sep:[]}))}default:yield*this.pop(),yield*this.pop(t)}if(!("document"!==e.type&&"block-map"!==e.type&&"block-seq"!==e.type||"block-map"!==t.type&&"block-seq"!==t.type)){const n=t.items[t.items.length-1];n&&!n.sep&&!n.value&&n.start.length>0&&-1===Qd(n.start)&&(0===t.indent||n.start.every((e=>"comment"!==e.type||e.indent<t.indent)))&&("document"===e.type?e.end=n.start:e.items.push({start:n.start}),t.items.splice(-1,1))}}else{const e="Tried to pop an empty stack";yield{type:"error",offset:this.offset,source:"",message:e}}}*stream(){switch(this.type){case"directive-line":return void(yield{type:"directive",offset:this.offset,source:this.source});case"byte-order-mark":case"space":case"comment":case"newline":return void(yield this.sourceToken);case"doc-mode":case"doc-start":{const e={type:"document",offset:this.offset,start:[]};return"doc-start"===this.type&&e.start.push(this.sourceToken),void this.stack.push(e)}}yield{type:"error",offset:this.offset,message:`Unexpected ${this.type} token in YAML stream`,source:this.source}}*document(e){if(e.value)return yield*this.lineEnd(e);switch(this.type){case"doc-start":return void(-1!==Qd(e.start)?(yield*this.pop(),yield*this.step()):e.start.push(this.sourceToken));case"anchor":case"tag":case"space":case"comment":case"newline":return void e.start.push(this.sourceToken)}const t=this.startBlockValue(e);t?this.stack.push(t):yield{type:"error",offset:this.offset,message:`Unexpected ${this.type} token in YAML document`,source:this.source}}*scalar(e){if("map-value-ind"===this.type){const t=Zd(Xd(this.peek(2)));let n;e.end?(n=e.end,n.push(this.sourceToken),delete e.end):n=[this.sourceToken];const s={type:"block-map",offset:e.offset,indent:e.indent,items:[{start:t,key:e,sep:n}]};this.onKeyLine=!0,this.stack[this.stack.length-1]=s}else yield*this.lineEnd(e)}*blockScalar(e){switch(this.type){case"space":case"comment":case"newline":return void e.props.push(this.sourceToken);case"scalar":if(e.source=this.source,this.atNewLine=!0,this.indent=0,this.onNewLine){let e=this.source.indexOf("\n")+1;for(;0!==e;)this.onNewLine(this.offset+e),e=this.source.indexOf("\n",e)+1}yield*this.pop();break;default:yield*this.pop(),yield*this.step()}}*blockMap(e){var t;const n=e.items[e.items.length-1];switch(this.type){case"newline":if(this.onKeyLine=!1,n.value){const t="end"in n.value?n.value.end:void 0,s=Array.isArray(t)?t[t.length-1]:void 0;"comment"===(null==s?void 0:s.type)?null==t||t.push(this.sourceToken):e.items.push({start:[this.sourceToken]})}else n.sep?n.sep.push(this.sourceToken):n.start.push(this.sourceToken);return;case"space":case"comment":if(n.value)e.items.push({start:[this.sourceToken]});else if(n.sep)n.sep.push(this.sourceToken);else{if(this.atIndentedComment(n.start,e.indent)){const s=e.items[e.items.length-2],r=null==(t=null==s?void 0:s.value)?void 0:t.end;if(Array.isArray(r))return Array.prototype.push.apply(r,n.start),r.push(this.sourceToken),void e.items.pop()}n.start.push(this.sourceToken)}return}if(this.indent>=e.indent){const t=!this.onKeyLine&&this.indent===e.indent,s=t&&(n.sep||n.explicitKey)&&"seq-item-ind"!==this.type;let r=[];if(s&&n.sep&&!n.value){const t=[];for(let s=0;s<n.sep.length;++s){const r=n.sep[s];switch(r.type){case"newline":t.push(s);break;case"space":break;case"comment":r.indent>e.indent&&(t.length=0);break;default:t.length=0}}t.length>=2&&(r=n.sep.splice(t[1]))}switch(this.type){case"anchor":case"tag":return void(s||n.value?(r.push(this.sourceToken),e.items.push({start:r}),this.onKeyLine=!0):n.sep?n.sep.push(this.sourceToken):n.start.push(this.sourceToken));case"explicit-key-ind":return n.sep||n.explicitKey?s||n.value?(r.push(this.sourceToken),e.items.push({start:r,explicitKey:!0})):this.stack.push({type:"block-map",offset:this.offset,indent:this.indent,items:[{start:[this.sourceToken],explicitKey:!0}]}):(n.start.push(this.sourceToken),n.explicitKey=!0),void(this.onKeyLine=!0);case"map-value-ind":if(n.explicitKey)if(n.sep)if(n.value)e.items.push({start:[],key:null,sep:[this.sourceToken]});else if(zd(n.sep,"map-value-ind"))this.stack.push({type:"block-map",offset:this.offset,indent:this.indent,items:[{start:r,key:null,sep:[this.sourceToken]}]});else if(Wd(n.key)&&!zd(n.sep,"newline")){const e=Zd(n.start),t=n.key,s=n.sep;s.push(this.sourceToken),delete n.key,delete n.sep,this.stack.push({type:"block-map",offset:this.offset,indent:this.indent,items:[{start:e,key:t,sep:s}]})}else r.length>0?n.sep=n.sep.concat(r,this.sourceToken):n.sep.push(this.sourceToken);else if(zd(n.start,"newline"))Object.assign(n,{key:null,sep:[this.sourceToken]});else{const e=Zd(n.start);this.stack.push({type:"block-map",offset:this.offset,indent:this.indent,items:[{start:e,key:null,sep:[this.sourceToken]}]})}else n.sep?n.value||s?e.items.push({start:r,key:null,sep:[this.sourceToken]}):zd(n.sep,"map-value-ind")?this.stack.push({type:"block-map",offset:this.offset,indent:this.indent,items:[{start:[],key:null,sep:[this.sourceToken]}]}):n.sep.push(this.sourceToken):Object.assign(n,{key:null,sep:[this.sourceToken]});return void(this.onKeyLine=!0);case"alias":case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":{const t=this.flowScalar(this.type);return void(s||n.value?(e.items.push({start:r,key:t,sep:[]}),this.onKeyLine=!0):n.sep?this.stack.push(t):(Object.assign(n,{key:t,sep:[]}),this.onKeyLine=!0))}default:{const n=this.startBlockValue(e);if(n)return t&&"block-seq"!==n.type&&e.items.push({start:r}),void this.stack.push(n)}}}yield*this.pop(),yield*this.step()}*blockSequence(e){var t;const n=e.items[e.items.length-1];switch(this.type){case"newline":if(n.value){const t="end"in n.value?n.value.end:void 0,s=Array.isArray(t)?t[t.length-1]:void 0;"comment"===(null==s?void 0:s.type)?null==t||t.push(this.sourceToken):e.items.push({start:[this.sourceToken]})}else n.start.push(this.sourceToken);return;case"space":case"comment":if(n.value)e.items.push({start:[this.sourceToken]});else{if(this.atIndentedComment(n.start,e.indent)){const s=e.items[e.items.length-2],r=null==(t=null==s?void 0:s.value)?void 0:t.end;if(Array.isArray(r))return Array.prototype.push.apply(r,n.start),r.push(this.sourceToken),void e.items.pop()}n.start.push(this.sourceToken)}return;case"anchor":case"tag":if(n.value||this.indent<=e.indent)break;return void n.start.push(this.sourceToken);case"seq-item-ind":if(this.indent!==e.indent)break;return void(n.value||zd(n.start,"seq-item-ind")?e.items.push({start:[this.sourceToken]}):n.start.push(this.sourceToken))}if(this.indent>e.indent){const t=this.startBlockValue(e);if(t)return void this.stack.push(t)}yield*this.pop(),yield*this.step()}*flowCollection(e){const t=e.items[e.items.length-1];if("flow-error-end"===this.type){let e;do{yield*this.pop(),e=this.peek(1)}while(e&&"flow-collection"===e.type)}else if(0===e.end.length){switch(this.type){case"comma":case"explicit-key-ind":return void(!t||t.sep?e.items.push({start:[this.sourceToken]}):t.start.push(this.sourceToken));case"map-value-ind":return void(!t||t.value?e.items.push({start:[],key:null,sep:[this.sourceToken]}):t.sep?t.sep.push(this.sourceToken):Object.assign(t,{key:null,sep:[this.sourceToken]}));case"space":case"comment":case"newline":case"anchor":case"tag":return void(!t||t.value?e.items.push({start:[this.sourceToken]}):t.sep?t.sep.push(this.sourceToken):t.start.push(this.sourceToken));case"alias":case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":{const n=this.flowScalar(this.type);return void(!t||t.value?e.items.push({start:[],key:n,sep:[]}):t.sep?this.stack.push(n):Object.assign(t,{key:n,sep:[]}))}case"flow-map-end":case"flow-seq-end":return void e.end.push(this.sourceToken)}const n=this.startBlockValue(e);n?this.stack.push(n):(yield*this.pop(),yield*this.step())}else{const t=this.peek(2);if("block-map"===t.type&&("map-value-ind"===this.type&&t.indent===e.indent||"newline"===this.type&&!t.items[t.items.length-1].sep))yield*this.pop(),yield*this.step();else if("map-value-ind"===this.type&&"flow-collection"!==t.type){const n=Zd(Xd(t));Jd(e);const s=e.end.splice(1,e.end.length);s.push(this.sourceToken);const r={type:"block-map",offset:e.offset,indent:e.indent,items:[{start:n,key:e,sep:s}]};this.onKeyLine=!0,this.stack[this.stack.length-1]=r}else yield*this.lineEnd(e)}}flowScalar(e){if(this.onNewLine){let e=this.source.indexOf("\n")+1;for(;0!==e;)this.onNewLine(this.offset+e),e=this.source.indexOf("\n",e)+1}return{type:e,offset:this.offset,indent:this.indent,source:this.source}}startBlockValue(e){switch(this.type){case"alias":case"scalar":case"single-quoted-scalar":case"double-quoted-scalar":return this.flowScalar(this.type);case"block-scalar-header":return{type:"block-scalar",offset:this.offset,indent:this.indent,props:[this.sourceToken],source:""};case"flow-map-start":case"flow-seq-start":return{type:"flow-collection",offset:this.offset,indent:this.indent,start:this.sourceToken,items:[],end:[]};case"seq-item-ind":return{type:"block-seq",offset:this.offset,indent:this.indent,items:[{start:[this.sourceToken]}]};case"explicit-key-ind":{this.onKeyLine=!0;const t=Zd(Xd(e));return t.push(this.sourceToken),{type:"block-map",offset:this.offset,indent:this.indent,items:[{start:t,explicitKey:!0}]}}case"map-value-ind":{this.onKeyLine=!0;const t=Zd(Xd(e));return{type:"block-map",offset:this.offset,indent:this.indent,items:[{start:t,key:null,sep:[this.sourceToken]}]}}}return null}atIndentedComment(e,t){return"comment"===this.type&&(!(this.indent<=t)&&e.every((e=>"newline"===e.type||"space"===e.type)))}*documentEnd(e){"doc-mode"!==this.type&&(e.end?e.end.push(this.sourceToken):e.end=[this.sourceToken],"newline"===this.type&&(yield*this.pop()))}*lineEnd(e){switch(this.type){case"comma":case"doc-start":case"doc-end":case"flow-seq-end":case"flow-map-end":case"map-value-ind":yield*this.pop(),yield*this.step();break;case"newline":this.onKeyLine=!1;default:e.end?e.end.push(this.sourceToken):e.end=[this.sourceToken],"newline"===this.type&&(yield*this.pop())}}}function tp(e,t={}){const{lineCounter:n,prettyErrors:s}=function(e){const t=!1!==e.prettyErrors;return{lineCounter:e.lineCounter||t&&new Vd||null,prettyErrors:t}}(t),r=new ep(null==n?void 0:n.addNewLine),i=new Ud(t);let o=null;for(const t of i.compose(r.parse(e),!0,e.length))if(o){if("silent"!==o.options.logLevel){o.errors.push(new fd(t.range.slice(0,2),"MULTIPLE_DOCS","Source contains multiple documents; please use YAML.parseAllDocuments()"));break}}else o=t;return s&&n&&(o.errors.forEach(Ed(e,n)),o.warnings.forEach(Ed(e,n))),o}const np={name:"frontmatter",transform:e=>(e.beforeParse.tap(((e,t)=>{var n;const{content:s}=t;if(!/^---\r?\n/.test(s))return;const r=/\n---\r?\n/.exec(s);if(!r)return;const i=s.slice(4,r.index).trimEnd();let o;try{o=function(e,t,n){let s;"function"==typeof t?s=t:void 0===n&&t&&"object"==typeof t&&(n=t);const r=tp(e,n);if(!r)return null;if(r.warnings.forEach((e=>Ju(r.options.logLevel,e))),r.errors.length>0){if("silent"!==r.options.logLevel)throw r.errors[0];r.errors=[]}return r.toJS(Object.assign({reviver:s},n))}(i.replace(/\r?\n|\r/g,"\n")),(null==o?void 0:o.markmap)&&(o.markmap=function(e){if(!e)return;return["color","extraJs","extraCss"].forEach((t=>{null!=e[t]&&(e[t]=function(e){let t;"string"==typeof e?t=[e]:Array.isArray(e)&&(t=e.filter((e=>e&&"string"==typeof e)));return(null==t?void 0:t.length)?t:void 0}(e[t]))})),["duration","maxWidth","initialExpandLevel"].forEach((t=>{null!=e[t]&&(e[t]=function(e){if(isNaN(+e))return;return+e}(e[t]))})),e}(o.markmap))}catch{return}t.frontmatter=o,t.parserOptions={...t.parserOptions,...null==(n=null==o?void 0:o.markmap)?void 0:n.htmlParser},t.content=s.slice(r.index+r[0].length),t.contentLineOffset=s.slice(0,r.index).split("\n").length+1})),{})};function sp(e,t){return"script"===t.type&&t.data.src?{...t,data:{...t.data,src:e.getFullUrl(t.data.src)}}:t}const rp="hljs",ip=["@highlightjs/cdn-assets@11.8.0/highlight.min.js"].map((e=>D(e))),op=["@highlightjs/cdn-assets@11.8.0/styles/default.min.css"].map((e=>y(e))),ap={name:rp,config:{versions:{hljs:"11.8.0"},preloadScripts:ip,styles:op},transform(e){var t,n,s;let r;const o=(null==(n=null==(t=ap.config)?void 0:t.preloadScripts)?void 0:n.map((t=>sp(e.transformer.urlBuilder,t))))||[];let a=i;return e.parser.tap((t=>{t.set({highlight:(t,n)=>{a();const{hljs:s}=window;return s?s.highlightAuto(t,n?[n]:void 0).value:((r||(r=S(o)),r).then((()=>{e.retransform.call()})),t)}})})),e.beforeParse.tap(((e,t)=>{a=()=>{t.features[rp]=!0}})),{styles:null==(s=ap.config)?void 0:s.styles}}},cp=ap;var lp=t;function up(e,t){var n,s,r=e.posMax,i=!0,o=!0;return n=t>0?e.src.charCodeAt(t-1):-1,s=t+1<=r?e.src.charCodeAt(t+1):-1,(32===n||9===n||s>=48&&s<=57)&&(o=!1),32!==s&&9!==s||(i=!1),{can_open:i,can_close:o}}function hp(e,t){var n,s,r,i;if("$"!==e.src[e.pos])return!1;if(!up(e,e.pos).can_open)return t||(e.pending+="$"),e.pos+=1,!0;for(s=n=e.pos+1;-1!==(s=e.src.indexOf("$",s));){for(i=s-1;"\\"===e.src[i];)i-=1;if((s-i)%2==1)break;s+=1}return-1===s?(t||(e.pending+="$"),e.pos=n,!0):s-n==0?(t||(e.pending+="$$"),e.pos=n+1,!0):up(e,s).can_close?(t||((r=e.push("math_inline","math",0)).markup="$",r.content=e.src.slice(n,s)),e.pos=s+1,!0):(t||(e.pending+="$"),e.pos=n,!0)}function dp(e,t,n,s){var r,i,o,a,c,l=!1,u=e.bMarks[t]+e.tShift[t],h=e.eMarks[t];if(u+2>h)return!1;if("$$"!==e.src.slice(u,u+2))return!1;if(u+=2,r=e.src.slice(u,h),s)return!0;for("$$"===r.trim().slice(-2)&&(r=r.trim().slice(0,-2),l=!0),o=t;!l&&!(++o>=n)&&!((u=e.bMarks[o]+e.tShift[o])<(h=e.eMarks[o])&&e.tShift[o]<e.blkIndent);)"$$"===e.src.slice(u,h).trim().slice(-2)&&(a=e.src.slice(0,h).lastIndexOf("$$"),i=e.src.slice(u,a),l=!0);return e.line=o+1,(c=e.push("math_block","math",0)).block=!0,c.content=(r&&r.trim()?r+"\n":"")+e.getLines(t+1,o,e.tShift[t],!0)+(i&&i.trim()?i:""),c.map=[t,e.line],c.markup="$$",!0}function pp(e){return e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&#039;")}const fp=un((function(e,t){t=t||{};e.inline.ruler.after("escape","math_inline",hp),e.block.ruler.after("blockquote","math_block",dp,{alt:["paragraph","reference","blockquote","list"]}),e.renderer.rules.math_inline=function(e,n){return function(e){t.displayMode=!1;try{return lp.renderToString(e,t)}catch(n){return t.throwOnError&&console.log(n),`<span class='katex-error' title='${pp(n.toString())}'>${pp(e)}</span>`}}(e[n].content)},e.renderer.rules.math_block=function(e,n){return function(e){t.displayMode=!0;try{return"<p class='katex-block'>"+lp.renderToString(e,t)+"</p>"}catch(n){return t.throwOnError&&console.log(n),`<p class='katex-block katex-error' title='${pp(n.toString())}'>${pp(e)}</p>`}}(e[n].content)+"\n"}}));const mp="katex",Ep=["katex@0.16.8/dist/katex.min.js"].map((e=>D(e))),Tp=D("webfontloader@1.6.28/webfontloader.js");Tp.data.defer=!0;const _p=["katex@0.16.8/dist/katex.min.css"].map((e=>y(e))),Ap={name:mp,config:{versions:{katex:"0.16.8",webfontloader:"1.6.28"},preloadScripts:Ep,scripts:[{type:"iife",data:{fn:e=>{window.WebFontConfig={custom:{families:["KaTeX_AMS","KaTeX_Caligraphic:n4,n7","KaTeX_Fraktur:n4,n7","KaTeX_Main:n4,n7,i4,i7","KaTeX_Math:i4,i7","KaTeX_Script","KaTeX_SansSerif:n4,n7,i4","KaTeX_Size1","KaTeX_Size2","KaTeX_Size3","KaTeX_Size4","KaTeX_Typewriter"]},active:()=>{e().refreshHook.call()}}},getParams:({getMarkmap:e})=>[e]}},Tp],styles:_p},transform(e){var t,n,s,r;let o;const a=(null==(n=null==(t=Ap.config)?void 0:t.preloadScripts)?void 0:n.map((t=>sp(e.transformer.urlBuilder,t))))||[],c=(t,n)=>{const{katex:s}=window;return s?s.renderToString(t,{displayMode:n,throwOnError:!1}):((o||(o=S(a)),o).then((()=>{e.retransform.call()})),t)};let l=i;return e.parser.tap((e=>{e.use(fp),["math_block","math_inline"].forEach((t=>{e.renderer.rules[t]=(e,t)=>{l();return c(e[t].content,!!e[t].block)}}))})),e.beforeParse.tap(((e,t)=>{l=()=>{t.features[mp]=!0}})),e.afterParse.tap(((e,t)=>{var n;const s=null==(n=t.frontmatter)?void 0:n.markmap;s&&["extraJs","extraCss"].forEach((e=>{var t,n;const r=s[e];var i,o,a;r&&(s[e]=(i=r,o=mp,a=(null==(n=null==(t=Ap.config)?void 0:t.versions)?void 0:n.katex)||"",i.map((e=>{if("string"==typeof e&&!e.includes("://")){e.startsWith("npm:")||(e=`npm:${e}`);const t=4+o.length;e.startsWith(`npm:${o}/`)&&(e=`${e.slice(0,t)}@${a}${e.slice(t)}`)}return e}))))}))})),{styles:null==(s=Ap.config)?void 0:s.styles,scripts:null==(r=Ap.config)?void 0:r.scripts}}},gp=[np,Ap,cp,{name:"npmUrl",transform:e=>(e.afterParse.tap(((t,n)=>{const{frontmatter:s}=n,r=null==s?void 0:s.markmap;r&&["extraJs","extraCss"].forEach((t=>{const n=r[t];n&&(r[t]=n.map((t=>t.startsWith("npm:")?e.transformer.urlBuilder.getFullUrl(t.slice(4)):t)))}))})),{})},Wl,{name:"sourceLines",transform:e=>(e.parser.tap((e=>{var t,n;e.renderer.renderAttrs=(t=e.renderer.renderAttrs,n=(e,t)=>{let n=e(t);return t.block&&t.map&&(n+=` data-lines=${t.map.join(",")}`),n},(...e)=>n(t,...e))})),{})}];function Cp(e){for(;!e.content&&1===e.children.length;)e=e.children[0];for(;1===e.children.length&&!e.children[0].content;)e={...e,children:e.children[0].children};return{...e,children:e.children.map(Cp)}}e.Transformer=class{constructor(e=gp){this.assetsMap={},this.urlBuilder=new s,this.hooks={transformer:this,parser:new r,beforeParse:new r,afterParse:new r,retransform:new r},this.plugins=e.map((e=>"function"==typeof e?e():e));const t={};for(const{name:e,transform:n}of this.plugins)t[e]=n(this.hooks);this.assetsMap=t;const n=function(){const e=Hl({html:!0,breaks:!0});return e.use(Gl).use(ql).use(jl).use(zl),e}();this.md=n,this.hooks.parser.call(n)}transform(e,t){var n;const s={content:e,features:{},contentLineOffset:0,parserOptions:t};this.hooks.beforeParse.call(this.md,s);const r=this.md.render(s.content,{});this.hooks.afterParse.call(this.md,s);const i=Cp(function(e,t){return n=pa(e,t),function(e,t){const n=(e,s)=>t(e,(()=>{var t;return null==(t=e.children)?void 0:t.map((t=>n(t,e)))}),s);return n(e)}(n,((e,t)=>{const n={content:e.html,children:t()||[]};return e.data&&(n.payload={...e.data}),e.comments&&(e.comments.includes("foldAll")?n.payload={...n.payload,fold:2}:e.comments.includes("fold")&&(n.payload={...n.payload,fold:1})),n}));var n}(r,s.parserOptions));return i.content||(i.content=`${(null==(n=s.frontmatter)?void 0:n.title)||""}`),{...s,root:i}}getAssets(e){const t=[],n=[];e??(e=this.plugins.map((e=>e.name)));for(const s of e.map((e=>this.assetsMap[e])))s&&(s.styles&&t.push(...s.styles),s.scripts&&n.push(...s.scripts));return{styles:t.map((e=>function(e,t){return"stylesheet"===t.type&&t.data.href?{...t,data:{...t.data,href:e.getFullUrl(t.data.href)}}:t}(this.urlBuilder,e))),scripts:n.map((e=>sp(this.urlBuilder,e)))}}getUsedAssets(e){const t=this.plugins.map((e=>e.name)).filter((t=>e[t]));return this.getAssets(t)}},e.builtInPlugins=gp,e.transformerVersions={"markmap-lib":"0.17.2"},Object.defineProperty(e,Symbol.toStringTag,{value:"Module"})}(this.markmap=this.markmap||{},window.katex);
//# sourceMappingURL=/sm/afa28b2cd78a80011a78de07005bbe060e69da5dd5a5527c23e66ad5595fdeb5.map