importScripts(
    '/static/workbox/workbox-sw.js',
);
workbox.setConfig({
    // debug: true,
    modulePathPrefix: "/static/workbox/"
});

workbox.routing.registerRoute(
    new RegExp('/static/'),
    new workbox.strategies.CacheFirst()
);
workbox.routing.registerRoute(
    new RegExp('/ocr/lang'),
    new workbox.strategies.CacheFirst()
);
workbox.routing.registerRoute(
    function (event) {
        // console.log(event.url.pathname)
        if (["/", '/favicon.png', '/sw.js', '/manifest.json', '/common.css'].indexOf(event.url.pathname) > -1) return true;
        // if (event.url.pathname.indexOf('.html') > -1) return true;
        if (event.url.pathname.indexOf('wd_sdk.js') > -1) return true;
        if (event.url.pathname.indexOf('/flow/') > -1) return true;
        if (event.url.pathname.indexOf('/auto/') > -1) return true;
        return false;
    },
    new workbox.strategies.StaleWhileRevalidate()
);