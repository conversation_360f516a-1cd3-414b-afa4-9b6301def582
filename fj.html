<!DOCTYPE html>
<html>

<head>
	<title>分镜</title>
	<meta charset="utf-8">
	<meta content="yes" name="mobile-web-app-capable" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<meta name="viewport" content="width=650,  user-scalable=no">
	<link rel="shortcut icon" href="#" />
	<link href="static/mdi_font/css/materialdesignicons.min.css" rel="stylesheet">
	<link href="static/3/vuetify.min.css" rel="stylesheet">
	<script src="static/3/vue.global.prod.js"></script>
	<script src="static/3/vuetify.min.js"></script>
	<link href="common.css" rel="stylesheet" />

	<style>
		div {
			transition: all 0.3s;
		}

		.logo-left {
			left: 20px;
			width: 100%;
			font-size: xx-large;
			position: relative;
		}

		.v-application--wrap {
			display: unset;
		}

		think {
			overflow: hidden;
			white-space: break-spaces;
		}

		think:hover::before {
			content: "点击折叠";
			color: red;
			position: absolute;
			right: 25px;
		}


		tool {
			margin: 2em;
			margin-top: 0;
			padding: 5px;
			border-radius: 5px;
			box-shadow: 0 3px 1px -2px rgba(0, 0, 0, .2), 0 2px 2px 0 rgba(0, 0, 0, .14), 0 1px 5px 0 rgba(0, 0, 0, .12) !important;
			background-color: #fff;
			display: block;
			color: #484848;
		}


		t {
			color: #517f8d;
		}
	</style>
</head>

<body>
	<div id="app" v-cloak>
		<v-app>
			<v-app-bar :elevation="5" style="position: sticky;top: 0;z-index: 1;min-width: 100vw;padding-right: 10px;">
				<b style="font-size: xx-large;padding-left: 20px;">分镜</b>
				<v-spacer></v-spacer>
				<v-btn class="ma-0 mr-5" size="large" color="red" @click="f终止()" v-if="loading"
					variant="tonal">终止</v-btn>
				<v-btn class="ma-0" size="large" variant="tonal" @click="f执行()" :loading="loading">
					执行
				</v-btn>
				<v-checkbox v-model="b跳过思考" class="ma-2" label="跳过思考" hide-details="auto"></v-checkbox>
			</v-app-bar>
			<v-row class="ma-2">
				<v-col cols="6" class="pa-0">
					<textarea v-model="s文章" placeholder="输入文章" style="
					width: calc(50vw - 20px);
					height: calc(100vh - 74px);
					outline: none;
					position: fixed;
					resize: none;
				"></textarea>
				</v-col>
				<v-col cols="6" class="pa-0">
					<template v-for="(item, index) in parsedOutput" :key="index">
						<template v-if="item.contentType === 'split'">
							<tool>
								<p>
									<t>原文：</t>
									<!-- <v-btn class="ma-0" size="small" @click="f定位原文(item)">
									定位
								</v-btn> -->
								{{ item.article }}
								</p>
								<p>
									<t>基础场景：</t>{{ item.from }}
								</p>
								<p>
									<t>场景：</t>{{ item.output }}
								</p>
							</tool>
						</template>
						<template v-else-if="item.contentType === 'source'">
							<tool>
								<p>
									<t>基础场景：</t>{{ item.article }}
								</p>
								<p v-if="item.from.length>0">
									<t>继承基础场景：</t>{{ item.from }}
								</p>
								<p>
									<t>描述：</t>{{ item.output }}
								</p>
							</tool>
						</template>
						<think v-else-if="item.contentType === 'think'">
							{{ item.content }}
						</think>
						<div v-else class="md" v-html="item.content"></div>
					</template>
					<!-- <v-btn v-if="parsedOutput.length>0" class="ma-2" size="large" @click="f全部应用()">
						全部应用
					</v-btn>
					<v-btn v-if="parsedOutput.length>0" class="ma-2" size="large" @click="f全部撤销()">
						全部撤销
					</v-btn> -->
					<br>
					<!-- <v-textarea solo counter v-model="s输出" label="输出" rows="10" :loading="loading" :auto-grow="!loading"
						hide-details="auto"></v-textarea> -->
				</v-col>
			</v-row>

			<v-snackbar v-model="b显示比对文本" :timeout="10000" style="white-space: pre-line">
				<div id="diffoutput"> </div>
				<template v-slot:actions> <v-btn color="red" variant="text" @click="b显示比对文本 = false"> X </v-btn>
				</template>
			</v-snackbar>

			<v-snackbar v-model="b显示提示文本" :timeout="3000" style="white-space: pre-line">{{s提示文本}}</v-snackbar>
		</v-app>

	</div>
	<script src="wd_sdk.js"></script>
	<script>
		const appStorage = new clyDBStorage('fj');
		const vuetify = Vuetify.createVuetify({
			defaults: {
				VBtn: {
					color: 'primary',
					variant: 'tonal'
				},
				VCheckbox: {
					color: 'primary',
					density: 'compact'
				},
			}
		})
		let data = Vue.reactive({
			s文章: "",
			s分镜要求: "",
			b跳过思考: true,
			s输出: "",
			loading: false,
			b显示比对文本: false,
			b显示提示文本: false,
			s提示文本: "",
			temperature: 0.2,
			top_p: 0.2,
			max_length: 40000,
			selected_articles: [],
			articles: [],
			auto_select_articles: false,
			l应用状态: [], // 独立维护应用状态
			isComposing: false,
		})
		let hash = decodeURI(document.location.hash).replace('#', '')
		if (hash.length > 10) {
			data.s文章 = hash
			document.location.hash = ""
		}
		app = Vue.createApp({
			data() {
				return data
			},
			computed: {
				parsedOutput() {
					if (!this.s输出) return [];
					function splitByXmlToolsTagSimple(input) {
						const parts = input.split(/(<(split|source)[^>]*>(?:[\s\S]*?<\/(split|source)>|[\s\S]*))/);
						return parts.filter(part => part?.trim()?.length > 0);
					}
					nodes = splitByXmlToolsTagSimple(this.s输出)
					// console.log(nodes)
					const result = [];
					nodes.forEach(node => {
						function nodeText(tagName) {
							return node?.querySelector(tagName)?.innerHTML || ''
						}
						if (node.indexOf("<split") == 0) {
							let parser = new DOMParser();
							node = parser.parseFromString(node, "text/html")
							const article = nodeText('s')
							const output = nodeText('output')
							// 查找对应的应用状态
							const replaceIndex = result.length;
							const isReplaced = this.l应用状态[replaceIndex] || false;
							result.push({
								contentType: 'split',
								article: article,
								output: output,
								from: nodeText('from'),
								isReplaced: isReplaced
							});
							return
						}
						if (node.indexOf("<source") == 0) {//<source><s>名称，如人名，场景名</s><output>描述</output></source>
							let parser = new DOMParser();
							node = parser.parseFromString(node, "text/html")
							const article = nodeText('s')
							const output = nodeText('output')
							result.push({
								contentType: 'source',
								article: article,
								from: nodeText('from'),
								output: output
							});

							return
						}

					});


					return result;
				},
				articlesNames() {
					try {
						let articlesNames = this.articles.map(i => i.title).filter(i => i.length > 0)
						return articlesNames
					} catch (error) {

					}
					return []
				},
			},
			methods: {
				removeMarkdownFormatting(text) {
					return text
						.replace(/^#+\s+/gm, '')
						.replace(/^[\*\-+]\s+/gm, '')
						// 去除粗体 (**bold** 或 __bold__)
						.replace(/(\*\*|__)(.*?)\1/g, '$2')
						// 去除斜体 (*italic* 或 _italic_)
						.replace(/(\*|_)(.*?)\1/g, '$2')
						// 去除引用 (> 引用内容)
						.replace(/^>\s+/gm, '')
						// 去除水平线 (---, ***, ___)
						.replace(/^[-*_]{3,}\s*$/gm, '')
						.replace(/<[^>]*>/g, '')
						// 去除多余的空行
						.replace(/\n\s+/g, '\n')
						.replace(/\n\n+/g, '\n')
				},
				f终止: () => {
					app.loading = false
					controller.abort()
				},
				f定位原文: (target) => {
					setSelection(target.isReplaced ? target.output : target.article)
				},
				f执行应用: (target) => {
					let article = target.article
					let output = target.output
					output = '<img>' + output + "</img>" + article
					console.log([article, output])
					let start = 0
					if (app.s文章.indexOf(article) > -1) {
						start = app.s文章.indexOf(article)
						app.s文章 = app.s文章.replace(article, output)
					} else {
						alert('执行失败，请检查原文是否存在')
						return false
					}
					alert('执行成功')
					target.isReplaced = true
					// 更新独立状态数组
					const index = app.parsedOutput.findIndex(item =>
						item.contentType === 'replace' &&
						item.article === article &&
						item.output === output
					);
					if (index !== -1) {
						app.l应用状态[index] = true;
					}
					setTimeout(() => {
						setSelectionRange(start, start + output.length)
					})
					return true
				},
				f撤销应用: (target) => {
					let article = target.article
					let output = target.output
					output = '<img>' + output + "</img>" + article
					console.log([article, output])
					let start = 0
					if (app.s文章.indexOf(output) > -1) {
						start = app.s文章.indexOf(output)
						app.s文章 = app.s文章.replace(output, article)
					} else {
						alert('撤销失败，请检查应用后的内容是否存在')
						return false
					}
					alert('撤销成功')
					// 更新独立状态数组
					const index = app.parsedOutput.findIndex(item =>
						item.contentType === 'replace' &&
						item.article === article &&
						item.output === output
					);
					if (index !== -1) {
						app.l应用状态[index] = false;
					}
					setTimeout(() => {
						setSelectionRange(start, start + article.length)
					})
					return true
				},
				f全部应用: () => {
					// 从后往前应用，避免位置偏移
					let replaceItems = app.parsedOutput

						.reverse();

					let successCount = 0;
					let failedItems = [];

					replaceItems.forEach(item => {
						if (!app.f执行应用(item)) {
							failedItems.push(item.article);
						} else {
							successCount++;
						}
					});

					let message = `已完成${successCount}处应用`;
					if (failedItems.length > 0) {
						message += `，${failedItems.length}处应用失败`;
					}
					alert(message);
				},
				f全部撤销: () => {
					// 从后往前撤销，避免位置偏移
					let replaceItems = app.parsedOutput
						.filter(item => item.contentType === 'replace' && item.isReplaced)
						.reverse();

					let successCount = 0;
					let failedItems = [];

					replaceItems.forEach(item => {
						if (!app.f撤销应用(item)) {
							failedItems.push(item.article);
						} else {
							successCount++;
						}
					});

					let message = `已完成${successCount}处撤销`;
					if (failedItems.length > 0) {
						message += `，${failedItems.length}处撤销失败`;
					}
					alert(message);
				},


				onEnter(e) {
					if (e) {
						e.stopPropagation()
						e.preventDefault()
					}
					this.f执行()
				},
				async f执行(old = '') {
					let input_prompt = `你的工作是为文章设计短视频分镜，以XML标签调用分镜工具，每一句都要生成：

<split><s>需要生成分镜的语句（完全原文，任何改动都将导致匹配失败，尽可能短）</s><from>图生图来源名称，人名+场景名</from><output>分镜场景</output></split>

为保证图像一致性，最后应当创建若干基础场景，包括建筑名称，人物等，作为图生图来源：
<source><s>名称，人名+场景名。与图生图来源相对应</s><from>可选，继承的基础场景。如果当前场景名称为"人名+场景名"，则应当从名称为"人名"的场景继承</from><output>描述</output></source>

不要在标签外进行任何输出，以下是需要被分镜的文章：
${app.s文章}}`
					// ---
					// 要求：${make_articles(app.s分镜要求)
					let prompt = make_prompt(input_prompt, (app.b跳过思考 ? templates.skip_think_prompt : "") + old)
					app.loading = true
					let stops = [templates.eos]
					app.l应用状态 = []
					await send_prompt_with_tool_call(prompt, stops, (s) => {
						app.s输出 = old + s
					})
					app.s输出 += abort_reaon
					data_to_save = { input_prompt, output_prompt: app.s输出 }
					app.loading = false
					if (typeof final_result != "undefined")
						final_result(app.s输出)
				}
			},
			watch: {
				s文章: throttle(async function (newVal) {
					await (appStorage["文章"] = newVal)
				}, 500),
				s输出: throttle(async function (newVal) {
					await (appStorage["输出"] = newVal)
				}, 500),
				s分镜要求: throttle(async function (newVal) {
					await (appStorage["分镜要求"] = newVal)
				}, 500),
				l应用状态: {
					handler: throttle(async function (newVal) {
						await (appStorage["应用状态"] = JSON.stringify(newVal))
					}, 500),
					deep: true
				},
			}
		}).use(vuetify).mount('#app')

		init = async () => {
			articlesStorage = new clyDBStorage('articles');
			let articles = await articlesStorage["main"]
			app.articles = articles ? JSON.parse(articles) : init_articles
			app.selected_articles = app.articles.filter(i => i.use).filter(i => i.title).map(i => i.title)
			if (hash.length > 10) {
				return
			}
			app.s文章 = await appStorage["文章"] || ""
			app.s分镜要求 = await appStorage["分镜要求"] || ""
			app.s输出 = await appStorage["输出"] || ""
			let l应用状态 = await appStorage["应用状态"]
			if (l应用状态) app.l应用状态 = JSON.parse(l应用状态)
		}
		init()

		abort_reaon = ''
		send_prompt_with_tool_call = async (prompt, stops, cb, old = '') => {
			let new_prompt = await send_prompt(prompt, stops, (s) => {
				cb(s)
			})
			while (abort_reaon == '</tool_call>') {
				console.log(new_prompt)
				let s = new_prompt
				s = s.slice(s.lastIndexOf("<tool_call>"))
				let parser = new DOMParser();
				let xmlDoc = parser.parseFromString(s + abort_reaon, "text/xml");
				let name = xmlDoc.querySelector('name').innerHTML
				let arguments = xmlDoc.querySelector('arguments').innerHTML

				console.log(name, arguments)
				let call_func_result = window[name](JSON.parse(arguments))
				try {
					call_func_result = JSON.stringify(call_func_result)
				} catch (error) {
				}
				new_prompt += abort_reaon + `\n\n<tool_response>${call_func_result}</tool_response>\n\n`
				abort_reaon = ''
				new_prompt += await send_prompt(prompt + new_prompt,
					stops, (s) => {
						cb(new_prompt + (s))
					})
			}
		}

		alert = (text) => {
			app.s提示文本 = text; //.replace(/\n/g,"<br>")
			app.b显示提示文本 = true;
		}
		document.addEventListener('mouseup', e => {
			let target = e.target
			if (target.tagName == 'THINK') {
				// target.style.maxHeight == '' ? target.style.maxHeight = '4em' : target.style.maxHeight = ''
				const styleTag = document.getElementById('think-global-style');
				if (!styleTag) {
					// 如果全局样式不存在，就创建并限制高度
					const style = document.createElement('style');
					style.id = 'think-global-style';
					style.textContent = 'think { max-height: 4em !important; }';
					document.head.appendChild(style);
				} else {
					// 如果全局样式存在，就移除
					styleTag.remove();
				}
			}
		})
		setSelectionRange = (start, end) => {
			let textarea = document.querySelector("textarea")
			textarea.setSelectionRange(start, start)
			textarea.focus()
			textarea.setSelectionRange(start, end)
		}
		setSelection = s => {
			let textarea = document.querySelector("textarea")
			let start = textarea.value.indexOf(s)
			console.log(s, start)
			// textarea.selectionStart=
			// textarea.selectionEnd=textarea.selectionStart+s.length
			if (start == -1) {
				alert("定位失败，请检查原文是否存在")
				return
			}
			setSelectionRange(start, start + s.length)
		}
	</script>
</body>

</html>