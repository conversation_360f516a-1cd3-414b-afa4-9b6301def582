<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8" />
  <title>闻达3 · 使用说明</title>
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" />
  <link href="static/mdi_font/css/materialdesignicons.min.css" rel="stylesheet" />
  <link href="static/vuetify.min.css" rel="stylesheet" />
  <script src="static/vue.min.js"></script>
  <script src="static/vuetify.min.js"></script>
  <style>
    html, body { margin: 0; padding: 0; font-family: 'Roboto', sans-serif; }
    [v-cloak] { display: none; }
    
    .hero-section {
      background: linear-gradient(135deg, #1565c0 0%, #1976d2 50%, #42a5f5 100%);
      color: white;
      padding: 80px 0;
      text-align: center;
    }
    
    .hero-title {
      font-size: 4rem;
      font-weight: 300;
      margin-bottom: 20px;
      text-shadow: 0 2px 8px rgba(0,0,0,0.3);
    }
    
    .hero-subtitle {
      font-size: 1.4rem;
      opacity: 0.95;
      max-width: 800px;
      margin: 0 auto 40px;
      line-height: 1.7;
    }
    
    .quick-start-btn {
      font-size: 1.2rem !important;
      padding: 16px 40px !important;
      border-radius: 30px !important;
      box-shadow: 0 4px 15px rgba(0,0,0,0.2) !important;
    }
    
    .section {
      padding: 60px 0;
    }
    
    .section-title {
      font-size: 2.5rem;
      font-weight: 400;
      margin-bottom: 40px;
      color: #1565c0;
      text-align: center;
    }
    
    .step-card {
      border-radius: 20px;
      transition: all 0.3s ease;
      border: 2px solid transparent;
      height: 100%;
    }
    
    .step-card:hover {
      border-color: #1976d2;
      transform: translateY(-8px);
      box-shadow: 0 12px 30px rgba(25, 118, 210, 0.2);
    }
    
    .step-number {
      background: linear-gradient(135deg, #1976d2, #42a5f5);
      color: white;
      width: 60px;
      height: 60px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.8rem;
      font-weight: bold;
      margin: 0 auto 20px;
    }
    
    .feature-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
      gap: 30px;
      margin-top: 40px;
    }
    
    .feature-card {
      border-radius: 20px;
      padding: 30px;
      text-align: center;
      transition: all 0.3s ease;
      border: 1px solid #e0e0e0;
      background: white;
    }
    
    .feature-card:hover {
      transform: translateY(-6px);
      box-shadow: 0 10px 30px rgba(0,0,0,0.15);
      border-color: #1976d2;
    }
    
    .feature-icon {
      font-size: 3.5rem !important;
      color: #1976d2;
      margin-bottom: 20px;
    }
    
    .code-block {
      background: #f8f9fa;
      border-radius: 12px;
      padding: 20px;
      font-family: 'Consolas', 'Monaco', monospace;
      border-left: 5px solid #1976d2;
      margin: 20px 0;
      font-size: 0.9rem;
    }
    
    .highlight-box {
      background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
      border-radius: 16px;
      padding: 30px;
      margin: 30px 0;
      border-left: 5px solid #1976d2;
    }
    
    .container { max-width: 1200px; margin: 0 auto; padding: 0 30px; }
    
    .config-card {
      border-radius: 16px;
      transition: all 0.3s ease;
    }
    
    .config-card:hover {
      transform: translateY(-4px);
      box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }
    
    @media (max-width: 768px) {
      .hero-title { font-size: 2.5rem; }
      .hero-subtitle { font-size: 1.1rem; }
      .section-title { font-size: 2rem; }
      .container { padding: 0 20px; }
    }
  </style>
</head>
<body>
<div id="app" v-cloak>
  <v-app>
    <!-- 导航栏 -->
    <v-app-bar color="#1565c0" dark flat elevation="0">
      <v-toolbar-title>
        <v-icon left>mdi-book-open-page-variant</v-icon>
        闻达3 使用说明
      </v-toolbar-title>
      <v-spacer></v-spacer>
      <v-btn text href="index.html" target="_self">
        <v-icon left>mdi-home</v-icon>返回首页
      </v-btn>
    </v-app-bar>

    <!-- 英雄区域 -->
    <div class="hero-section">
      <div class="container">
        <h1 class="hero-title">闻达3</h1>
        <p class="hero-subtitle">
          轻量级本地/远程大语言模型工具集<br>
          支持对话、翻译、续写、研究工具、角色扮演等多种功能
        </p>
        <v-btn 
          class="quick-start-btn" 
          color="white" 
          dark 
          large 
          @click="scrollTo('#quick-start')"
        >
          <v-icon left>mdi-rocket-launch</v-icon>
          快速开始
        </v-btn>
      </div>
    </div>

    <!-- 快速开始 -->
    <div id="quick-start" class="section" style="background: #fafafa;">
      <div class="container">
        <h2 class="section-title">
          <v-icon large color="#1565c0" class="mr-3">mdi-rocket-launch</v-icon>
          快速开始
        </h2>
        
        <v-row>
          <v-col cols="12" md="4" v-for="(step, index) in quickSteps" :key="index">
            <v-card class="step-card" elevation="3">
              <v-card-text class="text-center pa-6">
                <div class="step-number">{{ index + 1 }}</div>
                <h3 style="margin-bottom: 16px; color: #1565c0; font-size: 1.3rem;">{{ step.title }}</h3>
                <p style="line-height: 1.7; color: #666; margin-bottom: 20px;">{{ step.desc }}</p>
                <v-btn 
                  v-if="step.action" 
                  :color="step.color || 'primary'" 
                  :href="step.link" 
                  target="_self"
                  rounded
                  class="mt-2"
                >
                  <v-icon left>{{ step.icon }}</v-icon>
                  {{ step.action }}
                </v-btn>
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>

        <div class="highlight-box mt-8">
          <v-row align="center">
            <v-col cols="12" md="2" class="text-center">
              <v-icon size="60" color="#ff9800">mdi-alert-circle</v-icon>
            </v-col>
            <v-col cols="12" md="10">
              <h3 style="margin-bottom: 12px; color: #ff9800; font-size: 1.4rem;">重要提示</h3>
              <p style="margin: 0; line-height: 1.7; font-size: 1.1rem;">
                首次使用前，请确保已准备好 LLM 后端服务（本地 llama.cpp/ollama 或云端 OpenAI 兼容服务），
                然后在"设置"页面配置连接参数并测试连通性。
              </p>
            </v-col>
          </v-row>
        </div>
      </div>
    </div>

    <!-- 功能特性 -->
    <div id="features" class="section">
      <div class="container">
        <h2 class="section-title">
          <v-icon large color="#1565c0" class="mr-3">mdi-star</v-icon>
          核心功能
        </h2>

        <div class="feature-grid">
          <div class="feature-card" v-for="feature in features" :key="feature.name">
            <v-icon class="feature-icon">{{ feature.icon }}</v-icon>
            <h3 style="margin-bottom: 16px; color: #1565c0; font-size: 1.3rem;">{{ feature.name }}</h3>
            <p style="color: #666; line-height: 1.6; margin-bottom: 24px; font-size: 1rem;">{{ feature.desc }}</p>
            <v-btn color="primary" outlined rounded :href="feature.link" target="_self">
              <v-icon left small>mdi-open-in-new</v-icon>
              立即体验
            </v-btn>
          </div>
        </div>
      </div>
    </div>

    <!-- 配置说明 -->
    <div id="config" class="section" style="background: #fafafa;">
      <div class="container">
        <h2 class="section-title">
          <v-icon large color="#1565c0" class="mr-3">mdi-cog</v-icon>
          配置说明
        </h2>

        <v-row>
          <v-col cols="12" md="6">
            <v-card elevation="3" class="config-card">
              <v-card-title style="background: linear-gradient(135deg, #4caf50, #66bb6a); color: white;">
                <v-icon left color="white">mdi-server</v-icon>
                本地后端配置
              </v-card-title>
              <v-card-text class="pa-6">
                <div class="code-block">
                  <strong>API Endpoint:</strong> /completion<br>
                  <strong>协议:</strong> auto 或 llama<br>
                  <strong>模型:</strong> Qwen3-30B-A3B-Instruct-2507
                </div>
                <p style="color: #666; margin-top: 16px; line-height: 1.6;">
                  适用于 llama.cpp、ollama 等本地推理服务。需要先启动后端服务并加载 GGUF 模型文件。
                  推荐内存 20GB+ 以获得流畅体验。
                </p>
              </v-card-text>
            </v-card>
          </v-col>

          <v-col cols="12" md="6">
            <v-card elevation="3" class="config-card">
              <v-card-title style="background: linear-gradient(135deg, #2196f3, #42a5f5); color: white;">
                <v-icon left color="white">mdi-cloud</v-icon>
                云端 API 配置
              </v-card-title>
              <v-card-text class="pa-6">
                <div class="code-block">
                  <strong>API Endpoint:</strong> https://api.openai.com/v1/chat/completions<br>
                  <strong>协议:</strong> openai<br>
                  <strong>请求头:</strong> {"Authorization":"Bearer sk-xxx"}
                </div>
                <p style="color: #666; margin-top: 16px; line-height: 1.6;">
                  适用于 OpenAI 或兼容的云端服务。建议通过反向代理处理认证，避免在前端暴露 API Key。
                </p>
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>
      </div>
    </div>

    <!-- 常见问题 -->
    <div id="faq" class="section">
      <div class="container">
        <h2 class="section-title">
          <v-icon large color="#1565c0" class="mr-3">mdi-help-circle</v-icon>
          常见问题
        </h2>

        <v-expansion-panels multiple class="mt-4">
          <v-expansion-panel v-for="(faq, index) in faqs" :key="index" style="margin-bottom: 16px; border-radius: 12px; overflow: hidden;">
            <v-expansion-panel-header>
              <div style="display: flex; align-items: center;">
                <v-icon :color="faq.color" class="mr-3">{{ faq.icon }}</v-icon>
                <span style="font-weight: 500; font-size: 1.1rem;">{{ faq.question }}</span>
              </div>
            </v-expansion-panel-header>
            <v-expansion-panel-content>
              <div v-html="faq.answer" style="line-height: 1.6; color: #666; padding: 16px 0;"></div>
            </v-expansion-panel-content>
          </v-expansion-panel>
        </v-expansion-panels>
      </div>
    </div>

    <!-- 底部 -->
    <div class="section" style="background: #1565c0; color: white; text-align: center;">
      <div class="container">
        <h3 style="margin-bottom: 24px; font-size: 1.8rem;">开始使用闻达3</h3>
        <v-btn
          large
          color="white"
          dark
          href="index.html"
          target="_self"
          class="quick-start-btn"
        >
          <v-icon left>mdi-home</v-icon>
          返回首页
        </v-btn>
      </div>
    </div>
  </v-app>
</div>

<script>
  new Vue({
    el: '#app',
    vuetify: new Vuetify(),
    data() {
      return {
        quickSteps: [
          {
            title: '准备后端服务',
            desc: '启动本地 llama.cpp/ollama 服务，或准备云端 OpenAI 兼容 API',
            action: '查看配置',
            icon: 'mdi-server',
            link: '#config'
          },
          {
            title: '配置连接参数',
            desc: '在设置页面填写 API 端点、协议、模型名称等参数',
            action: '打开设置',
            icon: 'mdi-cog',
            link: 'settings.html'
          },
          {
            title: '开始使用',
            desc: '测试连通性成功后，即可使用对话、翻译、续写等功能',
            action: '立即体验',
            icon: 'mdi-rocket-launch',
            link: 'index.html',
            color: 'success'
          }
        ],
        features: [
          {
            name: '智能对话',
            desc: '多轮对话、工具选择、参数可调，支持中断/重新生成/多会话保存',
            icon: 'mdi-chat-processing',
            link: 'chat.html'
          },
          {
            name: '专业翻译',
            desc: '逐段翻译与术语表驱动的统一译法，确保翻译一致性与专业性',
            icon: 'mdi-translate',
            link: 'fy.html'
          },
          {
            name: '智能续写',
            desc: '根据已有片段进行续写扩写，调节温度与长度，多次生成对比选优',
            icon: 'mdi-file-document-edit',
            link: 'write.html'
          },
          {
            name: '研究工具',
            desc: '整合摘要、提纲、对比等研究工作流，快速形成结构化输出',
            icon: 'mdi-thought-bubble',
            link: 'yjgj.html'
          },
          {
            name: '角色扮演',
            desc: '预设不同角色场景，进行角色化对话模拟，适合专业领域咨询',
            icon: 'mdi-account-edit',
            link: 'infinityRP.html'
          },
          {
            name: '资料管理',
            desc: '本地管理资料文档，配合模型进行摘要提炼或重组整理',
            icon: 'mdi-folder-arrow-left-right-outline',
            link: 'zlgl.html'
          }
        ],
        faqs: [
          {
            question: '点击功能按钮没有反应？',
            answer: '请先在"设置"页面配置后端连接并测试连通性。确认后端服务正在运行，API 端点正确，模型已加载。如果路径包含中文字符，建议改为英文路径。',
            icon: 'mdi-alert-circle',
            color: '#f44336'
          },
          {
            question: '连通性测试失败怎么办？',
            answer: '检查以下几点：<br>1. 后端服务是否正常运行<br>2. API 端点地址是否正确<br>3. 协议选择是否匹配（auto/llama/openai）<br>4. 云端服务是否需要认证头<br>5. 是否存在网络或防火墙问题',
            icon: 'mdi-network-off',
            color: '#ff9800'
          },
          {
            question: '推荐什么模型和硬件配置？',
            answer: '推荐模型：Qwen3-30B-A3B-Instruct-2507-Q5_K_S.gguf<br>硬件要求：20GB+ 可用内存以获得流畅体验<br>对于资源有限的环境，可选择更小的模型（7B/14B）或更高的量化等级（Q4）',
            icon: 'mdi-memory',
            color: '#4caf50'
          },
          {
            question: '如何重置设置或清空历史？',
            answer: '在设置页面点击"恢复默认"按钮，或者在浏览器开发者工具中清空 localStorage（注意这会丢失所有本地数据）。',
            icon: 'mdi-backup-restore',
            color: '#2196f3'
          },
          {
            question: '支持核显加速吗？',
            answer: '如果使用 llama.cpp/ollama 等后端，可以在启动时选择 Vulkan 后端来利用核显（iGPU）进行加速。具体参数请参考对应后端的文档。',
            icon: 'mdi-speedometer',
            color: '#9c27b0'
          }
        ]
      }
    },
    methods: {
      scrollTo(selector) {
        const element = document.querySelector(selector);
        if (element) {
          element.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
        }
      }
    }
  })
</script>
</body>
</html>
