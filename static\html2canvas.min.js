/*!
 * html2canvas 1.2.2 <https://html2canvas.hertzen.com>
 * Copyright (c) 2021 <PERSON><PERSON> <https://hertzen.com>
 * Released under MIT License
 */
!function(A,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(A="undefined"!=typeof globalThis?globalThis:A||self).html2canvas=e()}(this,function(){"use strict";
/*! *****************************************************************************
    Copyright (c) Microsoft Corporation.

    Permission to use, copy, modify, and/or distribute this software for any
    purpose with or without fee is hereby granted.

    THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
    REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
    AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
    INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
    LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
    OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
    PERFORMANCE OF THIS SOFTWARE.
    ***************************************************************************** */var r=function(A,e){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(A,e){A.__proto__=e}||function(A,e){for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(A[t]=e[t])})(A,e)};function A(A,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function t(){this.constructor=A}r(A,e),A.prototype=null===e?Object.create(e):(t.prototype=e.prototype,new t)}var F=function(){return(F=Object.assign||function(A){for(var e,t=1,r=arguments.length;t<r;t++)for(var B in e=arguments[t])Object.prototype.hasOwnProperty.call(e,B)&&(A[B]=e[B]);return A}).apply(this,arguments)};function w(A,s,o,i){return new(o=o||Promise)(function(t,e){function r(A){try{n(i.next(A))}catch(A){e(A)}}function B(A){try{n(i.throw(A))}catch(A){e(A)}}function n(A){var e;A.done?t(A.value):((e=A.value)instanceof o?e:new o(function(A){A(e)})).then(r,B)}n((i=i.apply(A,s||[])).next())})}function H(t,r){var B,n,s,o={label:0,sent:function(){if(1&s[0])throw s[1];return s[1]},trys:[],ops:[]},A={next:e(0),throw:e(1),return:e(2)};return"function"==typeof Symbol&&(A[Symbol.iterator]=function(){return this}),A;function e(e){return function(A){return function(e){if(B)throw new TypeError("Generator is already executing.");for(;o;)try{if(B=1,n&&(s=2&e[0]?n.return:e[0]?n.throw||((s=n.return)&&s.call(n),0):n.next)&&!(s=s.call(n,e[1])).done)return s;switch(n=0,(e=s?[2&e[0],s.value]:e)[0]){case 0:case 1:s=e;break;case 4:return o.label++,{value:e[1],done:!1};case 5:o.label++,n=e[1],e=[0];continue;case 7:e=o.ops.pop(),o.trys.pop();continue;default:if(!(s=0<(s=o.trys).length&&s[s.length-1])&&(6===e[0]||2===e[0])){o=0;continue}if(3===e[0]&&(!s||e[1]>s[0]&&e[1]<s[3])){o.label=e[1];break}if(6===e[0]&&o.label<s[1]){o.label=s[1],s=e;break}if(s&&o.label<s[2]){o.label=s[2],o.ops.push(e);break}s[2]&&o.ops.pop(),o.trys.pop();continue}e=r.call(t,o)}catch(A){e=[6,A],n=0}finally{B=s=0}if(5&e[0])throw e[1];return{value:e[0]?e[1]:void 0,done:!0}}([e,A])}}}function t(A,e,t){if(t||2===arguments.length)for(var r,B=0,n=e.length;B<n;B++)!r&&B in e||((r=r||Array.prototype.slice.call(e,0,B))[B]=e[B]);return A.concat(r||e)}var h=(B.prototype.add=function(A,e,t,r){return new B(this.left+A,this.top+e,this.width+t,this.height+r)},B.fromClientRect=function(A,e){return new B(e.left+A.windowBounds.left,e.top+A.windowBounds.top,e.width,e.height)},B.fromDOMRectList=function(A,e){e=e[0];return e?new B(e.x+A.windowBounds.left,e.y+A.windowBounds.top,e.width,e.height):B.EMPTY},B.EMPTY=new B(0,0,0,0),B);function B(A,e,t,r){this.left=A,this.top=e,this.width=t,this.height=r}for(var d=function(A,e){return h.fromClientRect(A,e.getBoundingClientRect())},a=function(A){for(var e=[],t=0,r=A.length;t<r;){var B,n=A.charCodeAt(t++);55296<=n&&n<=56319&&t<r?56320==(64512&(B=A.charCodeAt(t++)))?e.push(((1023&n)<<10)+(1023&B)+65536):(e.push(n),t--):e.push(n)}return e},Q=function(){for(var A=[],e=0;e<arguments.length;e++)A[e]=arguments[e];if(String.fromCodePoint)return String.fromCodePoint.apply(String,A);var t=A.length;if(!t)return"";for(var r=[],B=-1,n="";++B<t;){var s=A[B];s<=65535?r.push(s):(s-=65536,r.push(55296+(s>>10),s%1024+56320)),(B+1===t||16384<r.length)&&(n+=String.fromCharCode.apply(String,r),r.length=0)}return n},e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",c="undefined"==typeof Uint8Array?[]:new Uint8Array(256),n=0;n<e.length;n++)c[e.charCodeAt(n)]=n;function s(A,e,t){return A.slice?A.slice(e,t):new Uint16Array(Array.prototype.slice.call(A,e,t))}var o=(i.prototype.get=function(A){var e;if(0<=A){if(A<55296||56319<A&&A<=65535)return e=this.index[A>>5],this.data[e=(e<<2)+(31&A)];if(A<=65535)return e=this.index[2048+(A-55296>>5)],this.data[e=(e<<2)+(31&A)];if(A<this.highStart)return e=this.index[e=2080+(A>>11)],e=this.index[e+=A>>5&63],this.data[e=(e<<2)+(31&A)];if(A<=1114111)return this.data[this.highValueIndex]}return this.errorValue},i);function i(A,e,t,r,B,n){this.initialValue=A,this.errorValue=e,this.highStart=t,this.highValueIndex=r,this.index=B,this.data=n}function u(A,e,t,r){var B=r[t];if(Array.isArray(A)?-1!==A.indexOf(B):A===B)for(var n=t;n<=r.length;){if((o=r[++n])===e)return 1;if(o!==K)break}if(B===K)for(n=t;0<n;){var s=r[--n];if(Array.isArray(A)?-1!==A.indexOf(s):A===s)for(var o,i=t;i<=r.length;){if((o=r[++i])===e)return 1;if(o!==K)break}if(s!==K)break}}function l(A,e){for(var t=A;0<=t;){var r=e[t];if(r!==K)return r;t--}return 0}function E(t,A){var e=(B=function(A,r){void 0===r&&(r="strict");var B=[],n=[],s=[];return A.forEach(function(A,e){var t=j.get(A);if(50<t?(s.push(!0),t-=50):s.push(!1),-1!==["normal","auto","loose"].indexOf(r)&&-1!==[8208,8211,12316,12448].indexOf(A))return n.push(e),B.push(16);if(4!==t&&11!==t)return n.push(e),31===t?B.push("strict"===r?R:X):t===W||29===t?B.push(_):43===t?131072<=A&&A<=196605||196608<=A&&A<=262141?B.push(X):B.push(_):void B.push(t);if(0===e)return n.push(e),B.push(_);t=B[e-1];return-1===tA.indexOf(t)?(n.push(n[e-1]),B.push(t)):(n.push(e),B.push(_))}),[n,B,s]}(t,(A=A||{lineBreak:"normal",wordBreak:"normal"}).lineBreak))[0],r=B[1],B=B[2];return[e,r="break-all"===A.wordBreak||"break-word"===A.wordBreak?r.map(function(A){return-1!==[D,_,W].indexOf(A)?X:A}):r,"keep-all"===A.wordBreak?B.map(function(A,e){return A&&19968<=t[e]&&t[e]<=40959}):void 0]}var C,U,g,f,p,K=10,L=13,y=15,I=17,T=18,m=19,N=20,R=21,S=22,O=24,D=25,b=26,M=27,v=28,_=30,x=32,G=33,P=34,V=35,X=37,k=38,J=39,Y=40,W=42,Z=[9001,65288],q="×",j=(g=function(A){var e,t,r,B,n=.75*A.length,s=A.length,o=0;"="===A[A.length-1]&&(n--,"="===A[A.length-2]&&n--);for(var n=new("undefined"!=typeof ArrayBuffer&&"undefined"!=typeof Uint8Array&&void 0!==Uint8Array.prototype.slice?ArrayBuffer:Array)(n),i=Array.isArray(n)?n:new Uint8Array(n),a=0;a<s;a+=4)e=c[A.charCodeAt(a)],t=c[A.charCodeAt(a+1)],r=c[A.charCodeAt(a+2)],B=c[A.charCodeAt(a+3)],i[o++]=e<<2|t>>4,i[o++]=(15&t)<<4|r>>2,i[o++]=(3&r)<<6|63&B;return n}(C="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"),f=Array.isArray(g)?function(A){for(var e=A.length,t=[],r=0;r<e;r+=4)t.push(A[r+3]<<24|A[r+2]<<16|A[r+1]<<8|A[r]);return t}(g):new Uint32Array(g),p=Array.isArray(g)?function(A){for(var e=A.length,t=[],r=0;r<e;r+=2)t.push(A[r+1]<<8|A[r]);return t}(g):new Uint16Array(g),C=s(p,12,f[4]/2),U=2===f[5]?s(p,(24+f[4])/2):(g=f,p=Math.ceil((24+f[4])/4),g.slice?g.slice(p,U):new Uint32Array(Array.prototype.slice.call(g,p,U))),new o(f[0],f[1],f[2],f[3],C,U)),z=[_,36],$=[1,2,3,5],AA=[K,8],eA=[M,b],tA=$.concat(AA),rA=[k,J,Y,P,V],BA=[y,L],nA=(sA.prototype.slice=function(){return Q.apply(void 0,this.codePoints.slice(this.start,this.end))},sA);function sA(A,e,t,r){this.codePoints=A,this.required="!"===e,this.start=t,this.end=r}function oA(A,e){var t=a(A),r=(e=E(t,e))[0],B=e[1],n=e[2],s=t.length,o=0,i=0;return{next:function(){if(s<=i)return{done:!0,value:null};for(var A=q;i<s&&(A=function(A,e,t,r,B){if(0===t[r])return q;var n=r-1;if(Array.isArray(B)&&!0===B[n])return q;var s=n-1,o=1+n,i=e[n],r=0<=s?e[s]:0,B=e[o];if(2===i&&3===B)return q;if(-1!==$.indexOf(i))return"!";if(-1!==$.indexOf(B))return q;if(-1!==AA.indexOf(B))return q;if(8===l(n,e))return"÷";if(11===j.get(A[n]))return q;if((i===x||i===G)&&11===j.get(A[o]))return q;if(7===i||7===B)return q;if(9===i)return q;if(-1===[K,L,y].indexOf(i)&&9===B)return q;if(-1!==[I,T,m,O,v].indexOf(B))return q;if(l(n,e)===S)return q;if(u(23,S,n,e))return q;if(u([I,T],R,n,e))return q;if(u(12,12,n,e))return q;if(i===K)return"÷";if(23===i||23===B)return q;if(16===B||16===i)return"÷";if(-1!==[L,y,R].indexOf(B)||14===i)return q;if(36===r&&-1!==BA.indexOf(i))return q;if(i===v&&36===B)return q;if(B===N)return q;if(-1!==z.indexOf(B)&&i===D||-1!==z.indexOf(i)&&B===D)return q;if(i===M&&-1!==[X,x,G].indexOf(B)||-1!==[X,x,G].indexOf(i)&&B===b)return q;if(-1!==z.indexOf(i)&&-1!==eA.indexOf(B)||-1!==eA.indexOf(i)&&-1!==z.indexOf(B))return q;if(-1!==[M,b].indexOf(i)&&(B===D||-1!==[S,y].indexOf(B)&&e[1+o]===D)||-1!==[S,y].indexOf(i)&&B===D||i===D&&-1!==[D,v,O].indexOf(B))return q;if(-1!==[D,v,O,I,T].indexOf(B))for(var a=n;0<=a;){if((c=e[a])===D)return q;if(-1===[v,O].indexOf(c))break;a--}if(-1!==[M,b].indexOf(B))for(var c,a=-1!==[I,T].indexOf(i)?s:n;0<=a;){if((c=e[a])===D)return q;if(-1===[v,O].indexOf(c))break;a--}if(k===i&&-1!==[k,J,P,V].indexOf(B)||-1!==[J,P].indexOf(i)&&-1!==[J,Y].indexOf(B)||-1!==[Y,V].indexOf(i)&&B===Y)return q;if(-1!==rA.indexOf(i)&&-1!==[N,b].indexOf(B)||-1!==rA.indexOf(B)&&i===M)return q;if(-1!==z.indexOf(i)&&-1!==z.indexOf(B))return q;if(i===O&&-1!==z.indexOf(B))return q;if(-1!==z.concat(D).indexOf(i)&&B===S&&-1===Z.indexOf(A[o])||-1!==z.concat(D).indexOf(B)&&i===T)return q;if(41===i&&41===B){for(var w=t[n],Q=1;0<w&&41===e[--w];)Q++;if(Q%2!=0)return q}return i===x&&B===G?q:"÷"}(t,B,r,++i,n))===q;);if(A===q&&i!==s)return{done:!0,value:null};var e=new nA(t,A,o,i);return o=i,{value:e,done:!1}}}}var iA;(be=iA=iA||{})[be.STRING_TOKEN=0]="STRING_TOKEN",be[be.BAD_STRING_TOKEN=1]="BAD_STRING_TOKEN",be[be.LEFT_PARENTHESIS_TOKEN=2]="LEFT_PARENTHESIS_TOKEN",be[be.RIGHT_PARENTHESIS_TOKEN=3]="RIGHT_PARENTHESIS_TOKEN",be[be.COMMA_TOKEN=4]="COMMA_TOKEN",be[be.HASH_TOKEN=5]="HASH_TOKEN",be[be.DELIM_TOKEN=6]="DELIM_TOKEN",be[be.AT_KEYWORD_TOKEN=7]="AT_KEYWORD_TOKEN",be[be.PREFIX_MATCH_TOKEN=8]="PREFIX_MATCH_TOKEN",be[be.DASH_MATCH_TOKEN=9]="DASH_MATCH_TOKEN",be[be.INCLUDE_MATCH_TOKEN=10]="INCLUDE_MATCH_TOKEN",be[be.LEFT_CURLY_BRACKET_TOKEN=11]="LEFT_CURLY_BRACKET_TOKEN",be[be.RIGHT_CURLY_BRACKET_TOKEN=12]="RIGHT_CURLY_BRACKET_TOKEN",be[be.SUFFIX_MATCH_TOKEN=13]="SUFFIX_MATCH_TOKEN",be[be.SUBSTRING_MATCH_TOKEN=14]="SUBSTRING_MATCH_TOKEN",be[be.DIMENSION_TOKEN=15]="DIMENSION_TOKEN",be[be.PERCENTAGE_TOKEN=16]="PERCENTAGE_TOKEN",be[be.NUMBER_TOKEN=17]="NUMBER_TOKEN",be[be.FUNCTION=18]="FUNCTION",be[be.FUNCTION_TOKEN=19]="FUNCTION_TOKEN",be[be.IDENT_TOKEN=20]="IDENT_TOKEN",be[be.COLUMN_TOKEN=21]="COLUMN_TOKEN",be[be.URL_TOKEN=22]="URL_TOKEN",be[be.BAD_URL_TOKEN=23]="BAD_URL_TOKEN",be[be.CDC_TOKEN=24]="CDC_TOKEN",be[be.CDO_TOKEN=25]="CDO_TOKEN",be[be.COLON_TOKEN=26]="COLON_TOKEN",be[be.SEMICOLON_TOKEN=27]="SEMICOLON_TOKEN",be[be.LEFT_SQUARE_BRACKET_TOKEN=28]="LEFT_SQUARE_BRACKET_TOKEN",be[be.RIGHT_SQUARE_BRACKET_TOKEN=29]="RIGHT_SQUARE_BRACKET_TOKEN",be[be.UNICODE_RANGE_TOKEN=30]="UNICODE_RANGE_TOKEN",be[be.WHITESPACE_TOKEN=31]="WHITESPACE_TOKEN",be[be.EOF_TOKEN=32]="EOF_TOKEN";function aA(A){return 48<=A&&A<=57}function cA(A){return aA(A)||65<=A&&A<=70||97<=A&&A<=102}function wA(A){return 10===A||9===A||32===A}function QA(A){return 97<=(t=e=A)&&t<=122||65<=(e=e)&&e<=90||128<=A||95===A;var e,t}function uA(A){return QA(A)||aA(A)||45===A}function lA(A,e){return 92===A&&10!==e}function EA(A,e,t){return 45===A?QA(e)||lA(e,t):!!QA(A)||92===A&&10!==e}function CA(A,e,t){return 43===A||45===A?!!aA(e)||46===e&&aA(t):aA(46===A?e:A)}var UA={type:iA.LEFT_PARENTHESIS_TOKEN},gA={type:iA.RIGHT_PARENTHESIS_TOKEN},FA={type:iA.COMMA_TOKEN},hA={type:iA.SUFFIX_MATCH_TOKEN},dA={type:iA.PREFIX_MATCH_TOKEN},HA={type:iA.COLUMN_TOKEN},fA={type:iA.DASH_MATCH_TOKEN},pA={type:iA.INCLUDE_MATCH_TOKEN},KA={type:iA.LEFT_CURLY_BRACKET_TOKEN},LA={type:iA.RIGHT_CURLY_BRACKET_TOKEN},yA={type:iA.SUBSTRING_MATCH_TOKEN},IA={type:iA.BAD_URL_TOKEN},TA={type:iA.BAD_STRING_TOKEN},mA={type:iA.CDO_TOKEN},NA={type:iA.CDC_TOKEN},RA={type:iA.COLON_TOKEN},SA={type:iA.SEMICOLON_TOKEN},OA={type:iA.LEFT_SQUARE_BRACKET_TOKEN},DA={type:iA.RIGHT_SQUARE_BRACKET_TOKEN},bA={type:iA.WHITESPACE_TOKEN},MA={type:iA.EOF_TOKEN},vA=(_A.prototype.write=function(A){this._value=this._value.concat(a(A))},_A.prototype.read=function(){for(var A=[],e=this.consumeToken();e!==MA;)A.push(e),e=this.consumeToken();return A},_A.prototype.consumeToken=function(){var A=this.consumeCodePoint();switch(A){case 34:return this.consumeStringToken(34);case 35:var e=this.peekCodePoint(0),t=this.peekCodePoint(1),r=this.peekCodePoint(2);if(uA(e)||lA(t,r)){var B=EA(e,t,r)?2:1,n=this.consumeName();return{type:iA.HASH_TOKEN,value:n,flags:B}}break;case 36:if(61===this.peekCodePoint(0))return this.consumeCodePoint(),hA;break;case 39:return this.consumeStringToken(39);case 40:return UA;case 41:return gA;case 42:if(61===this.peekCodePoint(0))return this.consumeCodePoint(),yA;break;case 43:if(CA(A,this.peekCodePoint(0),this.peekCodePoint(1)))return this.reconsumeCodePoint(A),this.consumeNumericToken();break;case 44:return FA;case 45:var B=A,s=this.peekCodePoint(0),o=this.peekCodePoint(1);if(CA(B,s,o))return this.reconsumeCodePoint(A),this.consumeNumericToken();if(EA(B,s,o))return this.reconsumeCodePoint(A),this.consumeIdentLikeToken();if(45===s&&62===o)return this.consumeCodePoint(),this.consumeCodePoint(),NA;break;case 46:if(CA(A,this.peekCodePoint(0),this.peekCodePoint(1)))return this.reconsumeCodePoint(A),this.consumeNumericToken();break;case 47:if(42===this.peekCodePoint(0))for(this.consumeCodePoint();;){var i=this.consumeCodePoint();if(42===i&&47===(i=this.consumeCodePoint()))return this.consumeToken();if(-1===i)return this.consumeToken()}break;case 58:return RA;case 59:return SA;case 60:if(33===this.peekCodePoint(0)&&45===this.peekCodePoint(1)&&45===this.peekCodePoint(2))return this.consumeCodePoint(),this.consumeCodePoint(),mA;break;case 64:var s=this.peekCodePoint(0),o=this.peekCodePoint(1),a=this.peekCodePoint(2);if(EA(s,o,a)){n=this.consumeName();return{type:iA.AT_KEYWORD_TOKEN,value:n}}break;case 91:return OA;case 92:if(lA(A,this.peekCodePoint(0)))return this.reconsumeCodePoint(A),this.consumeIdentLikeToken();break;case 93:return DA;case 61:if(61===this.peekCodePoint(0))return this.consumeCodePoint(),dA;break;case 123:return KA;case 125:return LA;case 117:case 85:a=this.peekCodePoint(0),n=this.peekCodePoint(1);return 43!==a||!cA(n)&&63!==n||(this.consumeCodePoint(),this.consumeUnicodeRangeToken()),this.reconsumeCodePoint(A),this.consumeIdentLikeToken();case 124:if(61===this.peekCodePoint(0))return this.consumeCodePoint(),fA;if(124===this.peekCodePoint(0))return this.consumeCodePoint(),HA;break;case 126:if(61===this.peekCodePoint(0))return this.consumeCodePoint(),pA;break;case-1:return MA}return wA(A)?(this.consumeWhiteSpace(),bA):aA(A)?(this.reconsumeCodePoint(A),this.consumeNumericToken()):QA(A)?(this.reconsumeCodePoint(A),this.consumeIdentLikeToken()):{type:iA.DELIM_TOKEN,value:Q(A)}},_A.prototype.consumeCodePoint=function(){var A=this._value.shift();return void 0===A?-1:A},_A.prototype.reconsumeCodePoint=function(A){this._value.unshift(A)},_A.prototype.peekCodePoint=function(A){return A>=this._value.length?-1:this._value[A]},_A.prototype.consumeUnicodeRangeToken=function(){for(var A=[],e=this.consumeCodePoint();cA(e)&&A.length<6;)A.push(e),e=this.consumeCodePoint();for(var t=!1;63===e&&A.length<6;)A.push(e),e=this.consumeCodePoint(),t=!0;if(t){var r=parseInt(Q.apply(void 0,A.map(function(A){return 63===A?48:A})),16),B=parseInt(Q.apply(void 0,A.map(function(A){return 63===A?70:A})),16);return{type:iA.UNICODE_RANGE_TOKEN,start:r,end:B}}r=parseInt(Q.apply(void 0,A),16);if(45===this.peekCodePoint(0)&&cA(this.peekCodePoint(1))){this.consumeCodePoint();for(var e=this.consumeCodePoint(),n=[];cA(e)&&n.length<6;)n.push(e),e=this.consumeCodePoint();B=parseInt(Q.apply(void 0,n),16);return{type:iA.UNICODE_RANGE_TOKEN,start:r,end:B}}return{type:iA.UNICODE_RANGE_TOKEN,start:r,end:r}},_A.prototype.consumeIdentLikeToken=function(){var A=this.consumeName();return"url"===A.toLowerCase()&&40===this.peekCodePoint(0)?(this.consumeCodePoint(),this.consumeUrlToken()):40===this.peekCodePoint(0)?(this.consumeCodePoint(),{type:iA.FUNCTION_TOKEN,value:A}):{type:iA.IDENT_TOKEN,value:A}},_A.prototype.consumeUrlToken=function(){var A=[];if(this.consumeWhiteSpace(),-1===this.peekCodePoint(0))return{type:iA.URL_TOKEN,value:""};var e,t=this.peekCodePoint(0);if(39===t||34===t){t=this.consumeStringToken(this.consumeCodePoint());return t.type===iA.STRING_TOKEN&&(this.consumeWhiteSpace(),-1===this.peekCodePoint(0)||41===this.peekCodePoint(0))?(this.consumeCodePoint(),{type:iA.URL_TOKEN,value:t.value}):(this.consumeBadUrlRemnants(),IA)}for(;;){var r=this.consumeCodePoint();if(-1===r||41===r)return{type:iA.URL_TOKEN,value:Q.apply(void 0,A)};if(wA(r))return this.consumeWhiteSpace(),-1===this.peekCodePoint(0)||41===this.peekCodePoint(0)?(this.consumeCodePoint(),{type:iA.URL_TOKEN,value:Q.apply(void 0,A)}):(this.consumeBadUrlRemnants(),IA);if(34===r||39===r||40===r||(0<=(e=r)&&e<=8||11===e||14<=e&&e<=31||127===e))return this.consumeBadUrlRemnants(),IA;if(92===r){if(!lA(r,this.peekCodePoint(0)))return this.consumeBadUrlRemnants(),IA;A.push(this.consumeEscapedCodePoint())}else A.push(r)}},_A.prototype.consumeWhiteSpace=function(){for(;wA(this.peekCodePoint(0));)this.consumeCodePoint()},_A.prototype.consumeBadUrlRemnants=function(){for(;;){var A=this.consumeCodePoint();if(41===A||-1===A)return;lA(A,this.peekCodePoint(0))&&this.consumeEscapedCodePoint()}},_A.prototype.consumeStringSlice=function(A){for(var e="";0<A;){var t=Math.min(6e4,A);e+=Q.apply(void 0,this._value.splice(0,t)),A-=t}return this._value.shift(),e},_A.prototype.consumeStringToken=function(A){for(var e="",t=0;;){var r,B=this._value[t];if(-1===B||void 0===B||B===A)return e+=this.consumeStringSlice(t),{type:iA.STRING_TOKEN,value:e};if(10===B)return this._value.splice(0,t),TA;92!==B||-1!==(r=this._value[t+1])&&void 0!==r&&(10===r?(e+=this.consumeStringSlice(t),t=-1,this._value.shift()):lA(B,r)&&(e+=this.consumeStringSlice(t),e+=Q(this.consumeEscapedCodePoint()),t=-1)),t++}},_A.prototype.consumeNumber=function(){var A=[],e=4;for(43!==(t=this.peekCodePoint(0))&&45!==t||A.push(this.consumeCodePoint());aA(this.peekCodePoint(0));)A.push(this.consumeCodePoint());var t=this.peekCodePoint(0),r=this.peekCodePoint(1);if(46===t&&aA(r))for(A.push(this.consumeCodePoint(),this.consumeCodePoint()),e=8;aA(this.peekCodePoint(0));)A.push(this.consumeCodePoint());t=this.peekCodePoint(0);var r=this.peekCodePoint(1),B=this.peekCodePoint(2);if((69===t||101===t)&&((43===r||45===r)&&aA(B)||aA(r)))for(A.push(this.consumeCodePoint(),this.consumeCodePoint()),e=8;aA(this.peekCodePoint(0));)A.push(this.consumeCodePoint());return[function(A){var e=0,t=1;43!==A[e]&&45!==A[e]||(45===A[e]&&(t=-1),e++);for(var r=[];aA(A[e]);)r.push(A[e++]);var B=r.length?parseInt(Q.apply(void 0,r),10):0;46===A[e]&&e++;for(var n=[];aA(A[e]);)n.push(A[e++]);var s=n.length,o=s?parseInt(Q.apply(void 0,n),10):0;69!==A[e]&&101!==A[e]||e++;var i=1;43!==A[e]&&45!==A[e]||(45===A[e]&&(i=-1),e++);for(var a=[];aA(A[e]);)a.push(A[e++]);var c=a.length?parseInt(Q.apply(void 0,a),10):0;return t*(B+o*Math.pow(10,-s))*Math.pow(10,i*c)}(A),e]},_A.prototype.consumeNumericToken=function(){var A=this.consumeNumber(),e=A[0],t=A[1],r=this.peekCodePoint(0),B=this.peekCodePoint(1),A=this.peekCodePoint(2);if(EA(r,B,A)){A=this.consumeName();return{type:iA.DIMENSION_TOKEN,number:e,flags:t,unit:A}}return 37===r?(this.consumeCodePoint(),{type:iA.PERCENTAGE_TOKEN,number:e,flags:t}):{type:iA.NUMBER_TOKEN,number:e,flags:t}},_A.prototype.consumeEscapedCodePoint=function(){var A,e=this.consumeCodePoint();if(cA(e)){for(var t=Q(e);cA(this.peekCodePoint(0))&&t.length<6;)t+=Q(this.consumeCodePoint());wA(this.peekCodePoint(0))&&this.consumeCodePoint();var r=parseInt(t,16);return 0===r||55296<=(A=r)&&A<=57343||1114111<r?65533:r}return-1===e?65533:e},_A.prototype.consumeName=function(){for(var A="";;){var e=this.consumeCodePoint();if(uA(e))A+=Q(e);else{if(!lA(e,this.peekCodePoint(0)))return this.reconsumeCodePoint(e),A;A+=Q(this.consumeEscapedCodePoint())}}},_A);function _A(){this._value=[]}var xA=(GA.create=function(A){var e=new vA;return e.write(A),new GA(e.read())},GA.parseValue=function(A){return GA.create(A).parseComponentValue()},GA.parseValues=function(A){return GA.create(A).parseComponentValues()},GA.prototype.parseComponentValue=function(){for(var A=this.consumeToken();A.type===iA.WHITESPACE_TOKEN;)A=this.consumeToken();if(A.type===iA.EOF_TOKEN)throw new SyntaxError("Error parsing CSS component value, unexpected EOF");this.reconsumeToken(A);for(var e=this.consumeComponentValue();(A=this.consumeToken()).type===iA.WHITESPACE_TOKEN;);if(A.type===iA.EOF_TOKEN)return e;throw new SyntaxError("Error parsing CSS component value, multiple values found when expecting only one")},GA.prototype.parseComponentValues=function(){for(var A=[];;){var e=this.consumeComponentValue();if(e.type===iA.EOF_TOKEN)return A;A.push(e),A.push()}},GA.prototype.consumeComponentValue=function(){var A=this.consumeToken();switch(A.type){case iA.LEFT_CURLY_BRACKET_TOKEN:case iA.LEFT_SQUARE_BRACKET_TOKEN:case iA.LEFT_PARENTHESIS_TOKEN:return this.consumeSimpleBlock(A.type);case iA.FUNCTION_TOKEN:return this.consumeFunction(A)}return A},GA.prototype.consumeSimpleBlock=function(A){for(var e={type:A,values:[]},t=this.consumeToken();;){if(t.type===iA.EOF_TOKEN||ne(t,A))return e;this.reconsumeToken(t),e.values.push(this.consumeComponentValue()),t=this.consumeToken()}},GA.prototype.consumeFunction=function(A){for(var e={name:A.value,values:[],type:iA.FUNCTION};;){var t=this.consumeToken();if(t.type===iA.EOF_TOKEN||t.type===iA.RIGHT_PARENTHESIS_TOKEN)return e;this.reconsumeToken(t),e.values.push(this.consumeComponentValue())}},GA.prototype.consumeToken=function(){var A=this._tokens.shift();return void 0===A?MA:A},GA.prototype.reconsumeToken=function(A){this._tokens.unshift(A)},GA);function GA(A){this._tokens=A}function PA(A){return A.type===iA.DIMENSION_TOKEN}function VA(A){return A.type===iA.NUMBER_TOKEN}function XA(A){return A.type===iA.IDENT_TOKEN}function kA(A){return A.type===iA.STRING_TOKEN}function JA(A,e){return XA(A)&&A.value===e}function YA(A){return A.type!==iA.WHITESPACE_TOKEN}function WA(A){return A.type!==iA.WHITESPACE_TOKEN&&A.type!==iA.COMMA_TOKEN}function ZA(A){var e=[],t=[];return A.forEach(function(A){if(A.type===iA.COMMA_TOKEN){if(0===t.length)throw new Error("Error parsing function args, zero tokens for arg");return e.push(t),void(t=[])}A.type!==iA.WHITESPACE_TOKEN&&t.push(A)}),t.length&&e.push(t),e}function qA(A){return A.type===iA.NUMBER_TOKEN||A.type===iA.DIMENSION_TOKEN}function jA(A){return A.type===iA.PERCENTAGE_TOKEN||qA(A)}function zA(A){return 1<A.length?[A[0],A[1]]:[A[0]]}function $A(A,e,t){var r=A[0],A=A[1];return[ae(r,e),ae(void 0!==A?A:r,t)]}function Ae(A){return A.type===iA.DIMENSION_TOKEN&&("deg"===A.unit||"grad"===A.unit||"rad"===A.unit||"turn"===A.unit)}function ee(A){switch(A.filter(XA).map(function(A){return A.value}).join(" ")){case"to bottom right":case"to right bottom":case"left top":case"top left":return[se,se];case"to top":case"bottom":return we(0);case"to bottom left":case"to left bottom":case"right top":case"top right":return[se,ie];case"to right":case"left":return we(90);case"to top left":case"to left top":case"right bottom":case"bottom right":return[ie,ie];case"to bottom":case"top":return we(180);case"to top right":case"to right top":case"left bottom":case"bottom left":return[ie,se];case"to left":case"right":return we(270)}return 0}function te(A){return 0==(255&A)}function re(A){var e=255&A,t=255&A>>8,r=255&A>>16,A=255&A>>24;return e<255?"rgba("+A+","+r+","+t+","+e/255+")":"rgb("+A+","+r+","+t+")"}function Be(A,e){if(A.type===iA.NUMBER_TOKEN)return A.number;if(A.type!==iA.PERCENTAGE_TOKEN)return 0;var t=3===e?1:255;return 3===e?A.number/100*t:Math.round(A.number/100*t)}var ne=function(A,e){return e===iA.LEFT_CURLY_BRACKET_TOKEN&&A.type===iA.RIGHT_CURLY_BRACKET_TOKEN||(e===iA.LEFT_SQUARE_BRACKET_TOKEN&&A.type===iA.RIGHT_SQUARE_BRACKET_TOKEN||e===iA.LEFT_PARENTHESIS_TOKEN&&A.type===iA.RIGHT_PARENTHESIS_TOKEN)},se={type:iA.NUMBER_TOKEN,number:0,flags:4},oe={type:iA.PERCENTAGE_TOKEN,number:50,flags:4},ie={type:iA.PERCENTAGE_TOKEN,number:100,flags:4},ae=function(A,e){if(A.type===iA.PERCENTAGE_TOKEN)return A.number/100*e;if(PA(A))switch(A.unit){case"rem":case"em":return 16*A.number;default:return A.number}return A.number},ce=function(A,e){if(e.type===iA.DIMENSION_TOKEN)switch(e.unit){case"deg":return Math.PI*e.number/180;case"grad":return Math.PI/200*e.number;case"rad":return e.number;case"turn":return 2*Math.PI*e.number}throw new Error("Unsupported angle type")},we=function(A){return Math.PI*A/180},Qe=function(A,e){if(e.type===iA.FUNCTION){var t=he[e.name];if(void 0===t)throw new Error('Attempting to parse an unsupported color function "'+e.name+'"');return t(A,e.values)}if(e.type===iA.HASH_TOKEN){if(3===e.value.length){var r=e.value.substring(0,1),B=e.value.substring(1,2),n=e.value.substring(2,3);return ue(parseInt(r+r,16),parseInt(B+B,16),parseInt(n+n,16),1)}if(4===e.value.length){var r=e.value.substring(0,1),B=e.value.substring(1,2),n=e.value.substring(2,3),s=e.value.substring(3,4);return ue(parseInt(r+r,16),parseInt(B+B,16),parseInt(n+n,16),parseInt(s+s,16)/255)}if(6===e.value.length){r=e.value.substring(0,2),B=e.value.substring(2,4),n=e.value.substring(4,6);return ue(parseInt(r,16),parseInt(B,16),parseInt(n,16),1)}if(8===e.value.length){r=e.value.substring(0,2),B=e.value.substring(2,4),n=e.value.substring(4,6),s=e.value.substring(6,8);return ue(parseInt(r,16),parseInt(B,16),parseInt(n,16),parseInt(s,16)/255)}}if(e.type===iA.IDENT_TOKEN){e=de[e.value.toUpperCase()];if(void 0!==e)return e}return de.TRANSPARENT},ue=function(A,e,t,r){return(A<<24|e<<16|t<<8|Math.round(255*r)<<0)>>>0},le=function(A,e){e=e.filter(WA);if(3===e.length){var t=e.map(Be),r=t[0],B=t[1],t=t[2];return ue(r,B,t,1)}if(4!==e.length)return 0;e=e.map(Be),r=e[0],B=e[1],t=e[2],e=e[3];return ue(r,B,t,e)};function Ee(A,e,t){return t<0&&(t+=1),1<=t&&--t,t<1/6?(e-A)*t*6+A:t<.5?e:t<2/3?6*(e-A)*(2/3-t)+A:A}function Ce(A,e){return Qe(A,xA.create(e).parseComponentValue())}var Ue,ge,Fe=function(A,e){var t=e.filter(WA),r=t[0],B=t[1],n=t[2],e=t[3],t=(r.type===iA.NUMBER_TOKEN?we(r.number):ce(A,r))/(2*Math.PI),A=jA(B)?B.number/100:0,r=jA(n)?n.number/100:0,B=void 0!==e&&jA(e)?ae(e,1):1;if(0==A)return ue(255*r,255*r,255*r,1);n=r<=.5?r*(1+A):r+A-r*A,e=2*r-n,A=Ee(e,n,t+1/3),r=Ee(e,n,t),t=Ee(e,n,t-1/3);return ue(255*A,255*r,255*t,B)},he={hsl:Fe,hsla:Fe,rgb:le,rgba:le},de={ALICEBLUE:4042850303,ANTIQUEWHITE:4209760255,AQUA:16777215,AQUAMARINE:2147472639,AZURE:4043309055,BEIGE:4126530815,BISQUE:4293182719,BLACK:255,BLANCHEDALMOND:4293643775,BLUE:65535,BLUEVIOLET:2318131967,BROWN:2771004159,BURLYWOOD:3736635391,CADETBLUE:1604231423,CHARTREUSE:2147418367,CHOCOLATE:3530104575,CORAL:4286533887,CORNFLOWERBLUE:1687547391,CORNSILK:4294499583,CRIMSON:3692313855,CYAN:16777215,DARKBLUE:35839,DARKCYAN:9145343,DARKGOLDENROD:3095837695,DARKGRAY:2846468607,DARKGREEN:6553855,DARKGREY:2846468607,DARKKHAKI:3182914559,DARKMAGENTA:2332068863,DARKOLIVEGREEN:1433087999,DARKORANGE:4287365375,DARKORCHID:2570243327,DARKRED:2332033279,DARKSALMON:3918953215,DARKSEAGREEN:2411499519,DARKSLATEBLUE:1211993087,DARKSLATEGRAY:793726975,DARKSLATEGREY:793726975,DARKTURQUOISE:13554175,DARKVIOLET:2483082239,DEEPPINK:4279538687,DEEPSKYBLUE:12582911,DIMGRAY:1768516095,DIMGREY:1768516095,DODGERBLUE:512819199,FIREBRICK:2988581631,FLORALWHITE:4294635775,FORESTGREEN:579543807,FUCHSIA:4278255615,GAINSBORO:3705462015,GHOSTWHITE:4177068031,GOLD:4292280575,GOLDENROD:3668254975,GRAY:2155905279,GREEN:8388863,GREENYELLOW:2919182335,GREY:2155905279,HONEYDEW:4043305215,HOTPINK:4285117695,INDIANRED:3445382399,INDIGO:1258324735,IVORY:4294963455,KHAKI:4041641215,LAVENDER:3873897215,LAVENDERBLUSH:4293981695,LAWNGREEN:2096890111,LEMONCHIFFON:4294626815,LIGHTBLUE:2916673279,LIGHTCORAL:4034953471,LIGHTCYAN:3774873599,LIGHTGOLDENRODYELLOW:4210742015,LIGHTGRAY:3553874943,LIGHTGREEN:2431553791,LIGHTGREY:3553874943,LIGHTPINK:4290167295,LIGHTSALMON:4288707327,LIGHTSEAGREEN:548580095,LIGHTSKYBLUE:2278488831,LIGHTSLATEGRAY:2005441023,LIGHTSLATEGREY:2005441023,LIGHTSTEELBLUE:2965692159,LIGHTYELLOW:4294959359,LIME:16711935,LIMEGREEN:852308735,LINEN:4210091775,MAGENTA:4278255615,MAROON:2147483903,MEDIUMAQUAMARINE:1724754687,MEDIUMBLUE:52735,MEDIUMORCHID:3126187007,MEDIUMPURPLE:2473647103,MEDIUMSEAGREEN:1018393087,MEDIUMSLATEBLUE:2070474495,MEDIUMSPRINGGREEN:16423679,MEDIUMTURQUOISE:1221709055,MEDIUMVIOLETRED:3340076543,MIDNIGHTBLUE:421097727,MINTCREAM:4127193855,MISTYROSE:4293190143,MOCCASIN:4293178879,NAVAJOWHITE:4292783615,NAVY:33023,OLDLACE:4260751103,OLIVE:2155872511,OLIVEDRAB:1804477439,ORANGE:4289003775,ORANGERED:4282712319,ORCHID:3664828159,PALEGOLDENROD:4008225535,PALEGREEN:2566625535,PALETURQUOISE:2951671551,PALEVIOLETRED:3681588223,PAPAYAWHIP:4293907967,PEACHPUFF:4292524543,PERU:3448061951,PINK:4290825215,PLUM:3718307327,POWDERBLUE:2967529215,PURPLE:2147516671,REBECCAPURPLE:1714657791,RED:4278190335,ROSYBROWN:3163525119,ROYALBLUE:1097458175,SADDLEBROWN:2336560127,SALMON:4202722047,SANDYBROWN:4104413439,SEAGREEN:780883967,SEASHELL:4294307583,SIENNA:2689740287,SILVER:3233857791,SKYBLUE:2278484991,SLATEBLUE:1784335871,SLATEGRAY:1887473919,SLATEGREY:1887473919,SNOW:4294638335,SPRINGGREEN:16744447,STEELBLUE:1182971135,TAN:3535047935,TEAL:8421631,THISTLE:3636451583,TOMATO:4284696575,TRANSPARENT:0,TURQUOISE:1088475391,VIOLET:4001558271,WHEAT:4125012991,WHITE:4294967295,WHITESMOKE:4126537215,YELLOW:4294902015,YELLOWGREEN:2597139199};(be=Ue=Ue||{})[be.VALUE=0]="VALUE",be[be.LIST=1]="LIST",be[be.IDENT_VALUE=2]="IDENT_VALUE",be[be.TYPE_VALUE=3]="TYPE_VALUE",be[be.TOKEN_VALUE=4]="TOKEN_VALUE",(Fe=ge=ge||{})[Fe.BORDER_BOX=0]="BORDER_BOX",Fe[Fe.PADDING_BOX=1]="PADDING_BOX";function He(A,e){return A=Qe(A,e[0]),(e=e[1])&&jA(e)?{color:A,stop:e}:{color:A,stop:null}}function fe(A,t){var e=A[0],r=A[A.length-1];null===e.stop&&(e.stop=se),null===r.stop&&(r.stop=ie);for(var B=[],n=0,s=0;s<A.length;s++){var o=A[s].stop;null!==o?(n<(o=ae(o,t))?B.push(o):B.push(n),n=o):B.push(null)}for(var i=null,s=0;s<B.length;s++){var a=B[s];if(null===a)null===i&&(i=s);else if(null!==i){for(var c=s-i,w=(a-B[i-1])/(1+c),Q=1;Q<=c;Q++)B[i+Q-1]=w*Q;i=null}}return A.map(function(A,e){return{color:A.color,stop:Math.max(Math.min(1,B[e]/t),0)}})}function pe(A,e,t){var r="number"==typeof A?A:(s=e/2,r=(n=t)/2,s=ae((B=A)[0],e)-s,n=r-ae(B[1],n),(Math.atan2(n,s)+2*Math.PI)%(2*Math.PI)),B=Math.abs(e*Math.sin(r))+Math.abs(t*Math.cos(r)),n=e/2,s=t/2,e=B/2,t=Math.sin(r-Math.PI/2)*e,e=Math.cos(r-Math.PI/2)*e;return[B,n-e,n+e,s-t,s+t]}function Ke(A,e){return Math.sqrt(A*A+e*e)}function Le(A,e,B,n,s){return[[0,0],[0,e],[A,0],[A,e]].reduce(function(A,e){var t=e[0],r=e[1],r=Ke(B-t,n-r);return(s?r<A.optimumDistance:r>A.optimumDistance)?{optimumCorner:e,optimumDistance:r}:A},{optimumDistance:s?1/0:-1/0,optimumCorner:null}).optimumCorner}var ye,Ie={name:"background-clip",initialValue:"border-box",prefix:!(Fe[Fe.CONTENT_BOX=2]="CONTENT_BOX"),type:Ue.LIST,parse:function(A,e){return e.map(function(A){if(XA(A))switch(A.value){case"padding-box":return ge.PADDING_BOX;case"content-box":return ge.CONTENT_BOX}return ge.BORDER_BOX})}},Te={name:"background-color",initialValue:"transparent",prefix:!1,type:Ue.TYPE_VALUE,format:"color"},le=function(t,A){var r=we(180),B=[];return ZA(A).forEach(function(A,e){if(0===e){e=A[0];if(e.type===iA.IDENT_TOKEN&&-1!==["top","left","right","bottom"].indexOf(e.value))return void(r=ee(A));if(Ae(e))return void(r=(ce(t,e)+we(270))%we(360))}A=He(t,A);B.push(A)}),{angle:r,stops:B,type:ye.LINEAR_GRADIENT}},me="closest-side",Ne="farthest-side",Re="closest-corner",Se="farthest-corner",Oe="ellipse",De="contain",be=function(r,A){var B=Me.CIRCLE,n=ve.FARTHEST_CORNER,s=[],o=[];return ZA(A).forEach(function(A,e){var t=!0;0===e?t=A.reduce(function(A,e){if(XA(e))switch(e.value){case"center":return o.push(oe),!1;case"top":case"left":return o.push(se),!1;case"right":case"bottom":return o.push(ie),!1}else if(jA(e)||qA(e))return o.push(e),!1;return A},t):1===e&&(t=A.reduce(function(A,e){if(XA(e))switch(e.value){case"circle":return B=Me.CIRCLE,!1;case Oe:return B=Me.ELLIPSE,!1;case De:case me:return n=ve.CLOSEST_SIDE,!1;case Ne:return n=ve.FARTHEST_SIDE,!1;case Re:return n=ve.CLOSEST_CORNER,!1;case"cover":case Se:return n=ve.FARTHEST_CORNER,!1}else if(qA(e)||jA(e))return(n=!Array.isArray(n)?[]:n).push(e),!1;return A},t)),t&&(A=He(r,A),s.push(A))}),{size:n,shape:B,stops:s,position:o,type:ye.RADIAL_GRADIENT}};(Fe=ye=ye||{})[Fe.URL=0]="URL",Fe[Fe.LINEAR_GRADIENT=1]="LINEAR_GRADIENT",Fe[Fe.RADIAL_GRADIENT=2]="RADIAL_GRADIENT";var Me,ve;(Fe=Me=Me||{})[Fe.CIRCLE=0]="CIRCLE",Fe[Fe.ELLIPSE=1]="ELLIPSE",(Fe=ve=ve||{})[Fe.CLOSEST_SIDE=0]="CLOSEST_SIDE",Fe[Fe.FARTHEST_SIDE=1]="FARTHEST_SIDE",Fe[Fe.CLOSEST_CORNER=2]="CLOSEST_CORNER",Fe[Fe.FARTHEST_CORNER=3]="FARTHEST_CORNER";var _e=function(A,e){if(e.type===iA.URL_TOKEN){var t={url:e.value,type:ye.URL};return A.cache.addImage(e.value),t}if(e.type!==iA.FUNCTION)throw new Error("Unsupported image type "+e.type);t=Ge[e.name];if(void 0===t)throw new Error('Attempting to parse an unsupported image function "'+e.name+'"');return t(A,e.values)};var xe,Ge={"linear-gradient":function(t,A){var r=we(180),B=[];return ZA(A).forEach(function(A,e){if(0===e){e=A[0];if(e.type===iA.IDENT_TOKEN&&"to"===e.value)return void(r=ee(A));if(Ae(e))return void(r=ce(t,e))}A=He(t,A);B.push(A)}),{angle:r,stops:B,type:ye.LINEAR_GRADIENT}},"-moz-linear-gradient":le,"-ms-linear-gradient":le,"-o-linear-gradient":le,"-webkit-linear-gradient":le,"radial-gradient":function(B,A){var n=Me.CIRCLE,s=ve.FARTHEST_CORNER,o=[],i=[];return ZA(A).forEach(function(A,e){var t,r=!0;0===e&&(t=!1,r=A.reduce(function(A,e){if(t)if(XA(e))switch(e.value){case"center":return i.push(oe),A;case"top":case"left":return i.push(se),A;case"right":case"bottom":return i.push(ie),A}else(jA(e)||qA(e))&&i.push(e);else if(XA(e))switch(e.value){case"circle":return n=Me.CIRCLE,!1;case Oe:return n=Me.ELLIPSE,!1;case"at":return!(t=!0);case me:return s=ve.CLOSEST_SIDE,!1;case"cover":case Ne:return s=ve.FARTHEST_SIDE,!1;case De:case Re:return s=ve.CLOSEST_CORNER,!1;case Se:return s=ve.FARTHEST_CORNER,!1}else if(qA(e)||jA(e))return(s=!Array.isArray(s)?[]:s).push(e),!1;return A},r)),r&&(A=He(B,A),o.push(A))}),{size:s,shape:n,stops:o,position:i,type:ye.RADIAL_GRADIENT}},"-moz-radial-gradient":be,"-ms-radial-gradient":be,"-o-radial-gradient":be,"-webkit-radial-gradient":be,"-webkit-gradient":function(r,A){var e=we(180),B=[],n=ye.LINEAR_GRADIENT,t=Me.CIRCLE,s=ve.FARTHEST_CORNER;return ZA(A).forEach(function(A,e){var t,A=A[0];if(0===e){if(XA(A)&&"linear"===A.value)return void(n=ye.LINEAR_GRADIENT);if(XA(A)&&"radial"===A.value)return void(n=ye.RADIAL_GRADIENT)}A.type===iA.FUNCTION&&("from"===A.name?(t=Qe(r,A.values[0]),B.push({stop:se,color:t})):"to"===A.name?(t=Qe(r,A.values[0]),B.push({stop:ie,color:t})):"color-stop"!==A.name||2===(A=A.values.filter(WA)).length&&(t=Qe(r,A[1]),A=A[0],VA(A)&&B.push({stop:{type:iA.PERCENTAGE_TOKEN,number:100*A.number,flags:A.flags},color:t})))}),n===ye.LINEAR_GRADIENT?{angle:(e+we(180))%we(360),stops:B,type:n}:{size:s,shape:t,stops:B,position:[],type:n}}},Pe={name:"background-image",initialValue:"none",type:Ue.LIST,prefix:!1,parse:function(e,A){if(0===A.length)return[];var t=A[0];return t.type===iA.IDENT_TOKEN&&"none"===t.value?[]:A.filter(function(A){return WA(A)&&!((A=A).type===iA.IDENT_TOKEN&&"none"===A.value||A.type===iA.FUNCTION&&!Ge[A.name])}).map(function(A){return _e(e,A)})}},Ve={name:"background-origin",initialValue:"border-box",prefix:!1,type:Ue.LIST,parse:function(A,e){return e.map(function(A){if(XA(A))switch(A.value){case"padding-box":return 1;case"content-box":return 2}return 0})}},Xe={name:"background-position",initialValue:"0% 0%",type:Ue.LIST,prefix:!1,parse:function(A,e){return ZA(e).map(function(A){return A.filter(jA)}).map(zA)}};(be=xe=xe||{})[be.REPEAT=0]="REPEAT",be[be.NO_REPEAT=1]="NO_REPEAT",be[be.REPEAT_X=2]="REPEAT_X";var ke,Je={name:"background-repeat",initialValue:"repeat",prefix:!(be[be.REPEAT_Y=3]="REPEAT_Y"),type:Ue.LIST,parse:function(A,e){return ZA(e).map(function(A){return A.filter(XA).map(function(A){return A.value}).join(" ")}).map(Ye)}},Ye=function(A){switch(A){case"no-repeat":return xe.NO_REPEAT;case"repeat-x":case"repeat no-repeat":return xe.REPEAT_X;case"repeat-y":case"no-repeat repeat":return xe.REPEAT_Y;default:return xe.REPEAT}};(be=ke=ke||{}).AUTO="auto",be.CONTAIN="contain";var We,Ze={name:"background-size",initialValue:"0",prefix:!(be.COVER="cover"),type:Ue.LIST,parse:function(A,e){return ZA(e).map(function(A){return A.filter(qe)})}},qe=function(A){return XA(A)||jA(A)},be=function(A){return{name:"border-"+A+"-color",initialValue:"transparent",prefix:!1,type:Ue.TYPE_VALUE,format:"color"}},je=be("top"),ze=be("right"),$e=be("bottom"),At=be("left"),be=function(A){return{name:"border-radius-"+A,initialValue:"0 0",prefix:!1,type:Ue.LIST,parse:function(A,e){return zA(e.filter(jA))}}},et=be("top-left"),tt=be("top-right"),rt=be("bottom-right"),Bt=be("bottom-left");(be=We=We||{})[be.NONE=0]="NONE",be[be.SOLID=1]="SOLID",be[be.DASHED=2]="DASHED",be[be.DOTTED=3]="DOTTED",be[be.DOUBLE=4]="DOUBLE";var nt,be=function(A){return{name:"border-"+A+"-style",initialValue:"solid",prefix:!1,type:Ue.IDENT_VALUE,parse:function(A,e){switch(e){case"none":return We.NONE;case"dashed":return We.DASHED;case"dotted":return We.DOTTED;case"double":return We.DOUBLE}return We.SOLID}}},st=be("top"),ot=be("right"),it=be("bottom"),at=be("left"),be=function(A){return{name:"border-"+A+"-width",initialValue:"0",type:Ue.VALUE,prefix:!1,parse:function(A,e){return PA(e)?e.number:0}}},ct=be("top"),wt=be("right"),Qt=be("bottom"),ut=be("left"),lt={name:"color",initialValue:"transparent",prefix:!1,type:Ue.TYPE_VALUE,format:"color"},Et={name:"display",initialValue:"inline-block",prefix:!1,type:Ue.LIST,parse:function(A,e){return e.filter(XA).reduce(function(A,e){return A|Ct(e.value)},0)}},Ct=function(A){switch(A){case"block":case"-webkit-box":return 2;case"inline":return 4;case"run-in":return 8;case"flow":return 16;case"flow-root":return 32;case"table":return 64;case"flex":case"-webkit-flex":return 128;case"grid":case"-ms-grid":return 256;case"ruby":return 512;case"subgrid":return 1024;case"list-item":return 2048;case"table-row-group":return 4096;case"table-header-group":return 8192;case"table-footer-group":return 16384;case"table-row":return 32768;case"table-cell":return 65536;case"table-column-group":return 131072;case"table-column":return 262144;case"table-caption":return 524288;case"ruby-base":return 1048576;case"ruby-text":return 2097152;case"ruby-base-container":return 4194304;case"ruby-text-container":return 8388608;case"contents":return 16777216;case"inline-block":return 33554432;case"inline-list-item":return 67108864;case"inline-table":return 134217728;case"inline-flex":return 268435456;case"inline-grid":return 536870912}return 0};(be=nt=nt||{})[be.NONE=0]="NONE",be[be.LEFT=1]="LEFT",be[be.RIGHT=2]="RIGHT",be[be.INLINE_START=3]="INLINE_START";function Ut(A,e){return XA(A)&&"normal"===A.value?1.2*e:A.type===iA.NUMBER_TOKEN?e*A.number:jA(A)?ae(A,e):e}var gt,Ft,ht={name:"float",initialValue:"none",prefix:!(be[be.INLINE_END=4]="INLINE_END"),type:Ue.IDENT_VALUE,parse:function(A,e){switch(e){case"left":return nt.LEFT;case"right":return nt.RIGHT;case"inline-start":return nt.INLINE_START;case"inline-end":return nt.INLINE_END}return nt.NONE}},dt={name:"letter-spacing",initialValue:"0",prefix:!1,type:Ue.VALUE,parse:function(A,e){return!(e.type===iA.IDENT_TOKEN&&"normal"===e.value||e.type!==iA.NUMBER_TOKEN&&e.type!==iA.DIMENSION_TOKEN)?e.number:0}},Ht={name:"line-break",initialValue:(be=gt=gt||{}).NORMAL="normal",prefix:!(be.STRICT="strict"),type:Ue.IDENT_VALUE,parse:function(A,e){return"strict"!==e?gt.NORMAL:gt.STRICT}},ft={name:"line-height",initialValue:"normal",prefix:!1,type:Ue.TOKEN_VALUE},pt={name:"list-style-image",initialValue:"none",type:Ue.VALUE,prefix:!1,parse:function(A,e){return e.type===iA.IDENT_TOKEN&&"none"===e.value?null:_e(A,e)}};(be=Ft=Ft||{})[be.INSIDE=0]="INSIDE";var Kt,Lt={name:"list-style-position",initialValue:"outside",prefix:!(be[be.OUTSIDE=1]="OUTSIDE"),type:Ue.IDENT_VALUE,parse:function(A,e){return"inside"!==e?Ft.OUTSIDE:Ft.INSIDE}};(be=Kt=Kt||{})[be.NONE=-1]="NONE",be[be.DISC=0]="DISC",be[be.CIRCLE=1]="CIRCLE",be[be.SQUARE=2]="SQUARE",be[be.DECIMAL=3]="DECIMAL",be[be.CJK_DECIMAL=4]="CJK_DECIMAL",be[be.DECIMAL_LEADING_ZERO=5]="DECIMAL_LEADING_ZERO",be[be.LOWER_ROMAN=6]="LOWER_ROMAN",be[be.UPPER_ROMAN=7]="UPPER_ROMAN",be[be.LOWER_GREEK=8]="LOWER_GREEK",be[be.LOWER_ALPHA=9]="LOWER_ALPHA",be[be.UPPER_ALPHA=10]="UPPER_ALPHA",be[be.ARABIC_INDIC=11]="ARABIC_INDIC",be[be.ARMENIAN=12]="ARMENIAN",be[be.BENGALI=13]="BENGALI",be[be.CAMBODIAN=14]="CAMBODIAN",be[be.CJK_EARTHLY_BRANCH=15]="CJK_EARTHLY_BRANCH",be[be.CJK_HEAVENLY_STEM=16]="CJK_HEAVENLY_STEM",be[be.CJK_IDEOGRAPHIC=17]="CJK_IDEOGRAPHIC",be[be.DEVANAGARI=18]="DEVANAGARI",be[be.ETHIOPIC_NUMERIC=19]="ETHIOPIC_NUMERIC",be[be.GEORGIAN=20]="GEORGIAN",be[be.GUJARATI=21]="GUJARATI",be[be.GURMUKHI=22]="GURMUKHI",be[be.HEBREW=22]="HEBREW",be[be.HIRAGANA=23]="HIRAGANA",be[be.HIRAGANA_IROHA=24]="HIRAGANA_IROHA",be[be.JAPANESE_FORMAL=25]="JAPANESE_FORMAL",be[be.JAPANESE_INFORMAL=26]="JAPANESE_INFORMAL",be[be.KANNADA=27]="KANNADA",be[be.KATAKANA=28]="KATAKANA",be[be.KATAKANA_IROHA=29]="KATAKANA_IROHA",be[be.KHMER=30]="KHMER",be[be.KOREAN_HANGUL_FORMAL=31]="KOREAN_HANGUL_FORMAL",be[be.KOREAN_HANJA_FORMAL=32]="KOREAN_HANJA_FORMAL",be[be.KOREAN_HANJA_INFORMAL=33]="KOREAN_HANJA_INFORMAL",be[be.LAO=34]="LAO",be[be.LOWER_ARMENIAN=35]="LOWER_ARMENIAN",be[be.MALAYALAM=36]="MALAYALAM",be[be.MONGOLIAN=37]="MONGOLIAN",be[be.MYANMAR=38]="MYANMAR",be[be.ORIYA=39]="ORIYA",be[be.PERSIAN=40]="PERSIAN",be[be.SIMP_CHINESE_FORMAL=41]="SIMP_CHINESE_FORMAL",be[be.SIMP_CHINESE_INFORMAL=42]="SIMP_CHINESE_INFORMAL",be[be.TAMIL=43]="TAMIL",be[be.TELUGU=44]="TELUGU",be[be.THAI=45]="THAI",be[be.TIBETAN=46]="TIBETAN",be[be.TRAD_CHINESE_FORMAL=47]="TRAD_CHINESE_FORMAL",be[be.TRAD_CHINESE_INFORMAL=48]="TRAD_CHINESE_INFORMAL",be[be.UPPER_ARMENIAN=49]="UPPER_ARMENIAN",be[be.DISCLOSURE_OPEN=50]="DISCLOSURE_OPEN";var yt,It={name:"list-style-type",initialValue:"none",prefix:!(be[be.DISCLOSURE_CLOSED=51]="DISCLOSURE_CLOSED"),type:Ue.IDENT_VALUE,parse:function(A,e){switch(e){case"disc":return Kt.DISC;case"circle":return Kt.CIRCLE;case"square":return Kt.SQUARE;case"decimal":return Kt.DECIMAL;case"cjk-decimal":return Kt.CJK_DECIMAL;case"decimal-leading-zero":return Kt.DECIMAL_LEADING_ZERO;case"lower-roman":return Kt.LOWER_ROMAN;case"upper-roman":return Kt.UPPER_ROMAN;case"lower-greek":return Kt.LOWER_GREEK;case"lower-alpha":return Kt.LOWER_ALPHA;case"upper-alpha":return Kt.UPPER_ALPHA;case"arabic-indic":return Kt.ARABIC_INDIC;case"armenian":return Kt.ARMENIAN;case"bengali":return Kt.BENGALI;case"cambodian":return Kt.CAMBODIAN;case"cjk-earthly-branch":return Kt.CJK_EARTHLY_BRANCH;case"cjk-heavenly-stem":return Kt.CJK_HEAVENLY_STEM;case"cjk-ideographic":return Kt.CJK_IDEOGRAPHIC;case"devanagari":return Kt.DEVANAGARI;case"ethiopic-numeric":return Kt.ETHIOPIC_NUMERIC;case"georgian":return Kt.GEORGIAN;case"gujarati":return Kt.GUJARATI;case"gurmukhi":return Kt.GURMUKHI;case"hebrew":return Kt.HEBREW;case"hiragana":return Kt.HIRAGANA;case"hiragana-iroha":return Kt.HIRAGANA_IROHA;case"japanese-formal":return Kt.JAPANESE_FORMAL;case"japanese-informal":return Kt.JAPANESE_INFORMAL;case"kannada":return Kt.KANNADA;case"katakana":return Kt.KATAKANA;case"katakana-iroha":return Kt.KATAKANA_IROHA;case"khmer":return Kt.KHMER;case"korean-hangul-formal":return Kt.KOREAN_HANGUL_FORMAL;case"korean-hanja-formal":return Kt.KOREAN_HANJA_FORMAL;case"korean-hanja-informal":return Kt.KOREAN_HANJA_INFORMAL;case"lao":return Kt.LAO;case"lower-armenian":return Kt.LOWER_ARMENIAN;case"malayalam":return Kt.MALAYALAM;case"mongolian":return Kt.MONGOLIAN;case"myanmar":return Kt.MYANMAR;case"oriya":return Kt.ORIYA;case"persian":return Kt.PERSIAN;case"simp-chinese-formal":return Kt.SIMP_CHINESE_FORMAL;case"simp-chinese-informal":return Kt.SIMP_CHINESE_INFORMAL;case"tamil":return Kt.TAMIL;case"telugu":return Kt.TELUGU;case"thai":return Kt.THAI;case"tibetan":return Kt.TIBETAN;case"trad-chinese-formal":return Kt.TRAD_CHINESE_FORMAL;case"trad-chinese-informal":return Kt.TRAD_CHINESE_INFORMAL;case"upper-armenian":return Kt.UPPER_ARMENIAN;case"disclosure-open":return Kt.DISCLOSURE_OPEN;case"disclosure-closed":return Kt.DISCLOSURE_CLOSED;default:return Kt.NONE}}},be=function(A){return{name:"margin-"+A,initialValue:"0",prefix:!1,type:Ue.TOKEN_VALUE}},Tt=be("top"),mt=be("right"),Nt=be("bottom"),Rt=be("left");(be=yt=yt||{})[be.VISIBLE=0]="VISIBLE",be[be.HIDDEN=1]="HIDDEN",be[be.SCROLL=2]="SCROLL";var St,Ot,Dt={name:"overflow",initialValue:"visible",prefix:!(be[be.AUTO=3]="AUTO"),type:Ue.LIST,parse:function(A,e){return e.filter(XA).map(function(A){switch(A.value){case"hidden":return yt.HIDDEN;case"scroll":return yt.SCROLL;case"auto":return yt.AUTO;default:return yt.VISIBLE}})}},bt={name:"overflow-wrap",initialValue:(be=St=St||{}).NORMAL="normal",prefix:!(be.BREAK_WORD="break-word"),type:Ue.IDENT_VALUE,parse:function(A,e){return"break-word"!==e?St.NORMAL:St.BREAK_WORD}},be=function(A){return{name:"padding-"+A,initialValue:"0",prefix:!1,type:Ue.TYPE_VALUE,format:"length-percentage"}},Mt=be("top"),vt=be("right"),_t=be("bottom"),xt=be("left");(be=Ot=Ot||{})[be.LEFT=0]="LEFT",be[be.CENTER=1]="CENTER";var Gt,Pt={name:"text-align",initialValue:"left",prefix:!(be[be.RIGHT=2]="RIGHT"),type:Ue.IDENT_VALUE,parse:function(A,e){switch(e){case"right":return Ot.RIGHT;case"center":case"justify":return Ot.CENTER;default:return Ot.LEFT}}};(be=Gt=Gt||{})[be.STATIC=0]="STATIC",be[be.RELATIVE=1]="RELATIVE",be[be.ABSOLUTE=2]="ABSOLUTE",be[be.FIXED=3]="FIXED";var Vt,Xt={name:"position",initialValue:"static",prefix:!(be[be.STICKY=4]="STICKY"),type:Ue.IDENT_VALUE,parse:function(A,e){switch(e){case"relative":return Gt.RELATIVE;case"absolute":return Gt.ABSOLUTE;case"fixed":return Gt.FIXED;case"sticky":return Gt.STICKY}return Gt.STATIC}},kt={name:"text-shadow",initialValue:"none",type:Ue.LIST,prefix:!1,parse:function(n,A){return 1===A.length&&JA(A[0],"none")?[]:ZA(A).map(function(A){for(var e={color:de.TRANSPARENT,offsetX:se,offsetY:se,blur:se},t=0,r=0;r<A.length;r++){var B=A[r];qA(B)?(0===t?e.offsetX=B:1===t?e.offsetY=B:e.blur=B,t++):e.color=Qe(n,B)}return e})}};(be=Vt=Vt||{})[be.NONE=0]="NONE",be[be.LOWERCASE=1]="LOWERCASE",be[be.UPPERCASE=2]="UPPERCASE";var Jt,Yt={name:"text-transform",initialValue:"none",prefix:!(be[be.CAPITALIZE=3]="CAPITALIZE"),type:Ue.IDENT_VALUE,parse:function(A,e){switch(e){case"uppercase":return Vt.UPPERCASE;case"lowercase":return Vt.LOWERCASE;case"capitalize":return Vt.CAPITALIZE}return Vt.NONE}},Wt={name:"transform",initialValue:"none",prefix:!0,type:Ue.VALUE,parse:function(A,e){if(e.type===iA.IDENT_TOKEN&&"none"===e.value)return null;if(e.type!==iA.FUNCTION)return null;var t=Zt[e.name];if(void 0===t)throw new Error('Attempting to parse an unsupported transform function "'+e.name+'"');return t(e.values)}},Zt={matrix:function(A){A=A.filter(function(A){return A.type===iA.NUMBER_TOKEN}).map(function(A){return A.number});return 6===A.length?A:null},matrix3d:function(A){var e=A.filter(function(A){return A.type===iA.NUMBER_TOKEN}).map(function(A){return A.number}),t=e[0],r=e[1];e[2],e[3];var B=e[4],n=e[5];e[6],e[7],e[8],e[9],e[10],e[11];var s=e[12],A=e[13];return e[14],e[15],16===e.length?[t,r,B,n,s,A]:null}},be={type:iA.PERCENTAGE_TOKEN,number:50,flags:4},qt=[be,be],jt={name:"transform-origin",initialValue:"50% 50%",prefix:!0,type:Ue.LIST,parse:function(A,e){e=e.filter(jA);return 2!==e.length?qt:[e[0],e[1]]}};(be=Jt=Jt||{})[be.VISIBLE=0]="VISIBLE",be[be.HIDDEN=1]="HIDDEN";var zt,$t={name:"visible",initialValue:"none",prefix:!(be[be.COLLAPSE=2]="COLLAPSE"),type:Ue.IDENT_VALUE,parse:function(A,e){switch(e){case"hidden":return Jt.HIDDEN;case"collapse":return Jt.COLLAPSE;default:return Jt.VISIBLE}}};(be=zt=zt||{}).NORMAL="normal",be.BREAK_ALL="break-all";var Ar,er={name:"word-break",initialValue:"normal",prefix:!(be.KEEP_ALL="keep-all"),type:Ue.IDENT_VALUE,parse:function(A,e){switch(e){case"break-all":return zt.BREAK_ALL;case"keep-all":return zt.KEEP_ALL;default:return zt.NORMAL}}},tr={name:"z-index",initialValue:"auto",prefix:!1,type:Ue.VALUE,parse:function(A,e){if(e.type===iA.IDENT_TOKEN)return{auto:!0,order:0};if(VA(e))return{auto:!1,order:e.number};throw new Error("Invalid z-index number parsed")}},rr={name:"opacity",initialValue:"1",type:Ue.VALUE,prefix:!1,parse:function(A,e){return VA(e)?e.number:1}},Br={name:"text-decoration-color",initialValue:"transparent",prefix:!1,type:Ue.TYPE_VALUE,format:"color"},nr={name:"text-decoration-line",initialValue:"none",prefix:!1,type:Ue.LIST,parse:function(A,e){return e.filter(XA).map(function(A){switch(A.value){case"underline":return 1;case"overline":return 2;case"line-through":return 3;case"none":return 4}return 0}).filter(function(A){return 0!==A})}},sr={name:"font-family",initialValue:"",prefix:!1,type:Ue.LIST,parse:function(A,e){var t=[],r=[];return e.forEach(function(A){switch(A.type){case iA.IDENT_TOKEN:case iA.STRING_TOKEN:t.push(A.value);break;case iA.NUMBER_TOKEN:t.push(A.number.toString());break;case iA.COMMA_TOKEN:r.push(t.join(" ")),t.length=0}}),t.length&&r.push(t.join(" ")),r.map(function(A){return-1===A.indexOf(" ")?A:"'"+A+"'"})}},or={name:"font-size",initialValue:"0",prefix:!1,type:Ue.TYPE_VALUE,format:"length"},ir={name:"font-weight",initialValue:"normal",type:Ue.VALUE,prefix:!1,parse:function(A,e){return VA(e)?e.number:!XA(e)||"bold"!==e.value?400:700}},ar={name:"font-variant",initialValue:"none",type:Ue.LIST,prefix:!1,parse:function(A,e){return e.filter(XA).map(function(A){return A.value})}};(be=Ar=Ar||{}).NORMAL="normal",be.ITALIC="italic";function cr(A,e){return 0!=(A&e)}function wr(A,e,t){return(A=A&&A[Math.min(e,A.length-1)])?t?A.open:A.close:""}var Qr,ur={name:"font-style",initialValue:"normal",prefix:!(be.OBLIQUE="oblique"),type:Ue.IDENT_VALUE,parse:function(A,e){switch(e){case"oblique":return Ar.OBLIQUE;case"italic":return Ar.ITALIC;default:return Ar.NORMAL}}},lr={name:"content",initialValue:"none",type:Ue.LIST,prefix:!1,parse:function(A,e){if(0===e.length)return[];var t=e[0];return t.type===iA.IDENT_TOKEN&&"none"===t.value?[]:e}},Er={name:"counter-increment",initialValue:"none",prefix:!0,type:Ue.LIST,parse:function(A,e){if(0===e.length)return null;var t=e[0];if(t.type===iA.IDENT_TOKEN&&"none"===t.value)return null;for(var r=[],B=e.filter(YA),n=0;n<B.length;n++){var s=B[n],o=B[n+1];s.type===iA.IDENT_TOKEN&&(o=o&&VA(o)?o.number:1,r.push({counter:s.value,increment:o}))}return r}},Cr={name:"counter-reset",initialValue:"none",prefix:!0,type:Ue.LIST,parse:function(A,e){if(0===e.length)return[];for(var t=[],r=e.filter(YA),B=0;B<r.length;B++){var n=r[B],s=r[B+1];XA(n)&&"none"!==n.value&&(s=s&&VA(s)?s.number:0,t.push({counter:n.value,reset:s}))}return t}},Ur={name:"quotes",initialValue:"none",prefix:!0,type:Ue.LIST,parse:function(A,e){if(0===e.length)return null;var t=e[0];if(t.type===iA.IDENT_TOKEN&&"none"===t.value)return null;var r=[],B=e.filter(kA);if(B.length%2!=0)return null;for(var n=0;n<B.length;n+=2){var s=B[n].value,o=B[n+1].value;r.push({open:s,close:o})}return r}},gr={name:"box-shadow",initialValue:"none",type:Ue.LIST,prefix:!1,parse:function(n,A){return 1===A.length&&JA(A[0],"none")?[]:ZA(A).map(function(A){for(var e={color:255,offsetX:se,offsetY:se,blur:se,spread:se,inset:!1},t=0,r=0;r<A.length;r++){var B=A[r];JA(B,"inset")?e.inset=!0:qA(B)?(0===t?e.offsetX=B:1===t?e.offsetY=B:2===t?e.blur=B:e.spread=B,t++):e.color=Qe(n,B)}return e})}};(be=Qr=Qr||{})[be.FILL=0]="FILL",be[be.STROKE=1]="STROKE";var Fr={name:"paint-order",initialValue:"normal",prefix:!(be[be.MARKERS=2]="MARKERS"),type:Ue.LIST,parse:function(A,e){var t=[Qr.FILL,Qr.STROKE,Qr.MARKERS],r=[];return e.filter(XA).forEach(function(A){switch(A.value){case"stroke":r.push(Qr.STROKE);break;case"fill":r.push(Qr.FILL);break;case"markers":r.push(Qr.MARKERS)}}),t.forEach(function(A){-1===r.indexOf(A)&&r.push(A)}),r}},hr={name:"-webkit-text-stroke-color",initialValue:"currentcolor",prefix:!1,type:Ue.TYPE_VALUE,format:"color"},dr={name:"-webkit-text-stroke-width",initialValue:"0",type:Ue.VALUE,prefix:!1,parse:function(A,e){return PA(e)?e.number:0}},Hr=(fr.prototype.isVisible=function(){return 0<this.display&&0<this.opacity&&this.visibility===Jt.VISIBLE},fr.prototype.isTransparent=function(){return te(this.backgroundColor)},fr.prototype.isTransformed=function(){return null!==this.transform},fr.prototype.isPositioned=function(){return this.position!==Gt.STATIC},fr.prototype.isPositionedWithZIndex=function(){return this.isPositioned()&&!this.zIndex.auto},fr.prototype.isFloating=function(){return this.float!==nt.NONE},fr.prototype.isInlineLevel=function(){return cr(this.display,4)||cr(this.display,33554432)||cr(this.display,268435456)||cr(this.display,536870912)||cr(this.display,67108864)||cr(this.display,134217728)},fr);function fr(A,e){this.backgroundClip=Ir(A,Ie,e.backgroundClip),this.backgroundColor=Ir(A,Te,e.backgroundColor),this.backgroundImage=Ir(A,Pe,e.backgroundImage),this.backgroundOrigin=Ir(A,Ve,e.backgroundOrigin),this.backgroundPosition=Ir(A,Xe,e.backgroundPosition),this.backgroundRepeat=Ir(A,Je,e.backgroundRepeat),this.backgroundSize=Ir(A,Ze,e.backgroundSize),this.borderTopColor=Ir(A,je,e.borderTopColor),this.borderRightColor=Ir(A,ze,e.borderRightColor),this.borderBottomColor=Ir(A,$e,e.borderBottomColor),this.borderLeftColor=Ir(A,At,e.borderLeftColor),this.borderTopLeftRadius=Ir(A,et,e.borderTopLeftRadius),this.borderTopRightRadius=Ir(A,tt,e.borderTopRightRadius),this.borderBottomRightRadius=Ir(A,rt,e.borderBottomRightRadius),this.borderBottomLeftRadius=Ir(A,Bt,e.borderBottomLeftRadius),this.borderTopStyle=Ir(A,st,e.borderTopStyle),this.borderRightStyle=Ir(A,ot,e.borderRightStyle),this.borderBottomStyle=Ir(A,it,e.borderBottomStyle),this.borderLeftStyle=Ir(A,at,e.borderLeftStyle),this.borderTopWidth=Ir(A,ct,e.borderTopWidth),this.borderRightWidth=Ir(A,wt,e.borderRightWidth),this.borderBottomWidth=Ir(A,Qt,e.borderBottomWidth),this.borderLeftWidth=Ir(A,ut,e.borderLeftWidth),this.boxShadow=Ir(A,gr,e.boxShadow),this.color=Ir(A,lt,e.color),this.display=Ir(A,Et,e.display),this.float=Ir(A,ht,e.cssFloat),this.fontFamily=Ir(A,sr,e.fontFamily),this.fontSize=Ir(A,or,e.fontSize),this.fontStyle=Ir(A,ur,e.fontStyle),this.fontVariant=Ir(A,ar,e.fontVariant),this.fontWeight=Ir(A,ir,e.fontWeight),this.letterSpacing=Ir(A,dt,e.letterSpacing),this.lineBreak=Ir(A,Ht,e.lineBreak),this.lineHeight=Ir(A,ft,e.lineHeight),this.listStyleImage=Ir(A,pt,e.listStyleImage),this.listStylePosition=Ir(A,Lt,e.listStylePosition),this.listStyleType=Ir(A,It,e.listStyleType),this.marginTop=Ir(A,Tt,e.marginTop),this.marginRight=Ir(A,mt,e.marginRight),this.marginBottom=Ir(A,Nt,e.marginBottom),this.marginLeft=Ir(A,Rt,e.marginLeft),this.opacity=Ir(A,rr,e.opacity);var t=Ir(A,Dt,e.overflow);this.overflowX=t[0],this.overflowY=t[1<t.length?1:0],this.overflowWrap=Ir(A,bt,e.overflowWrap),this.paddingTop=Ir(A,Mt,e.paddingTop),this.paddingRight=Ir(A,vt,e.paddingRight),this.paddingBottom=Ir(A,_t,e.paddingBottom),this.paddingLeft=Ir(A,xt,e.paddingLeft),this.paintOrder=Ir(A,Fr,e.paintOrder),this.position=Ir(A,Xt,e.position),this.textAlign=Ir(A,Pt,e.textAlign),this.textDecorationColor=Ir(A,Br,null!==(t=e.textDecorationColor)&&void 0!==t?t:e.color),this.textDecorationLine=Ir(A,nr,null!==(t=e.textDecorationLine)&&void 0!==t?t:e.textDecoration),this.textShadow=Ir(A,kt,e.textShadow),this.textTransform=Ir(A,Yt,e.textTransform),this.transform=Ir(A,Wt,e.transform),this.transformOrigin=Ir(A,jt,e.transformOrigin),this.visibility=Ir(A,$t,e.visibility),this.webkitTextStrokeColor=Ir(A,hr,e.webkitTextStrokeColor),this.webkitTextStrokeWidth=Ir(A,dr,e.webkitTextStrokeWidth),this.wordBreak=Ir(A,er,e.wordBreak),this.zIndex=Ir(A,tr,e.zIndex)}function pr(A){return 0===A[0]&&255===A[1]&&0===A[2]&&255===A[3]}var Kr,Lr=function(A,e){this.content=Ir(A,lr,e.content),this.quotes=Ir(A,Ur,e.quotes)},yr=function(A,e){this.counterIncrement=Ir(A,Er,e.counterIncrement),this.counterReset=Ir(A,Cr,e.counterReset)},Ir=function(A,e,t){var r=new vA,t=null!=t?t.toString():e.initialValue;r.write(t);var B=new xA(r.read());switch(e.type){case Ue.IDENT_VALUE:var n=B.parseComponentValue();return e.parse(A,XA(n)?n.value:e.initialValue);case Ue.VALUE:return e.parse(A,B.parseComponentValue());case Ue.LIST:return e.parse(A,B.parseComponentValues());case Ue.TOKEN_VALUE:return B.parseComponentValue();case Ue.TYPE_VALUE:switch(e.format){case"angle":return ce(A,B.parseComponentValue());case"color":return Qe(A,B.parseComponentValue());case"image":return _e(A,B.parseComponentValue());case"length":var s=B.parseComponentValue();return qA(s)?s:se;case"length-percentage":s=B.parseComponentValue();return jA(s)?s:se}}},Tr=function(A,e){this.context=A,this.styles=new Hr(A,window.getComputedStyle(e,null)),this.textNodes=[],this.elements=[],null!==this.styles.transform&&NB(e)&&(e.style.transform="none"),this.bounds=d(this.context,e),this.flags=0},mr=function(A,e,t,r,B){var n="http://www.w3.org/2000/svg",s=document.createElementNS(n,"svg"),n=document.createElementNS(n,"foreignObject");return s.setAttributeNS(null,"width",A.toString()),s.setAttributeNS(null,"height",e.toString()),n.setAttributeNS(null,"width","100%"),n.setAttributeNS(null,"height","100%"),n.setAttributeNS(null,"x",t.toString()),n.setAttributeNS(null,"y",r.toString()),n.setAttributeNS(null,"externalResourcesRequired","true"),s.appendChild(n),n.appendChild(B),s},Nr=function(r){return new Promise(function(A,e){var t=new Image;t.onload=function(){return A(t)},t.onerror=e,t.src="data:image/svg+xml;charset=utf-8,"+encodeURIComponent((new XMLSerializer).serializeToString(r))})},Rr={get SUPPORT_RANGE_BOUNDS(){var A=function(A){if(A.createRange){var e=A.createRange();if(e.getBoundingClientRect){var t=A.createElement("boundtest");t.style.height="123px",t.style.display="block",A.body.appendChild(t),e.selectNode(t);e=e.getBoundingClientRect(),e=Math.round(e.height);if(A.body.removeChild(t),123===e)return!0}}return!1}(document);return Object.defineProperty(Rr,"SUPPORT_RANGE_BOUNDS",{value:A}),A},get SUPPORT_WORD_BREAKING(){var A=Rr.SUPPORT_RANGE_BOUNDS&&function(A){var e=A.createElement("boundtest");e.style.width="50px",e.style.display="block",e.style.fontSize="12px",e.style.letterSpacing="0px",e.style.wordSpacing="0px",A.body.appendChild(e);var r=A.createRange();e.innerHTML="function"==typeof"".repeat?"&#128104;".repeat(10):"";var B=e.firstChild,t=a(B.data).map(function(A){return Q(A)}),n=0,s={},t=t.every(function(A,e){r.setStart(B,n),r.setEnd(B,n+A.length);var t=r.getBoundingClientRect();n+=A.length;A=t.x>s.x||t.y>s.y;return s=t,0===e||A});return A.body.removeChild(e),t}(document);return Object.defineProperty(Rr,"SUPPORT_WORD_BREAKING",{value:A}),A},get SUPPORT_SVG_DRAWING(){var A=function(A){var e=new Image,t=A.createElement("canvas"),A=t.getContext("2d");if(!A)return!1;e.src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg'></svg>";try{A.drawImage(e,0,0),t.toDataURL()}catch(A){return!1}return!0}(document);return Object.defineProperty(Rr,"SUPPORT_SVG_DRAWING",{value:A}),A},get SUPPORT_FOREIGNOBJECT_DRAWING(){var A="function"==typeof Array.from&&"function"==typeof window.fetch?function(t){var A=t.createElement("canvas"),r=100;A.width=r,A.height=r;var B=A.getContext("2d");if(!B)return Promise.reject(!1);B.fillStyle="rgb(0, 255, 0)",B.fillRect(0,0,r,r);var e=new Image,n=A.toDataURL();e.src=n;e=mr(r,r,0,0,e);return B.fillStyle="red",B.fillRect(0,0,r,r),Nr(e).then(function(A){B.drawImage(A,0,0);var e=B.getImageData(0,0,r,r).data;B.fillStyle="red",B.fillRect(0,0,r,r);A=t.createElement("div");return A.style.backgroundImage="url("+n+")",A.style.height="100px",pr(e)?Nr(mr(r,r,0,0,A)):Promise.reject(!1)}).then(function(A){return B.drawImage(A,0,0),pr(B.getImageData(0,0,r,r).data)}).catch(function(){return!1})}(document):Promise.resolve(!1);return Object.defineProperty(Rr,"SUPPORT_FOREIGNOBJECT_DRAWING",{value:A}),A},get SUPPORT_CORS_IMAGES(){var A=void 0!==(new Image).crossOrigin;return Object.defineProperty(Rr,"SUPPORT_CORS_IMAGES",{value:A}),A},get SUPPORT_RESPONSE_TYPE(){var A="string"==typeof(new XMLHttpRequest).responseType;return Object.defineProperty(Rr,"SUPPORT_RESPONSE_TYPE",{value:A}),A},get SUPPORT_CORS_XHR(){var A="withCredentials"in new XMLHttpRequest;return Object.defineProperty(Rr,"SUPPORT_CORS_XHR",{value:A}),A}},Sr=function(A,e){this.text=A,this.bounds=e},Or=function(A,e){var t=e.ownerDocument;if(t){var r=t.createElement("html2canvaswrapper");r.appendChild(e.cloneNode(!0));t=e.parentNode;if(t){t.replaceChild(r,e);A=d(A,r);return r.firstChild&&t.replaceChild(r.firstChild,r),A}}return h.EMPTY},Dr=function(A,e,t){var r=A.ownerDocument;if(!r)throw new Error("Node has no owner document");r=r.createRange();return r.setStart(A,e),r.setEnd(A,e+t),r},br=function(A,e,t,r){return h.fromClientRect(A,Dr(e,t,r).getBoundingClientRect())},Mr=function(A,e){return 0!==e.letterSpacing?a(A).map(function(A){return Q(A)}):_r(A,e)},vr=[32,160,4961,65792,65793,4153,4241],_r=function(A,e){for(var t,r=oA(A,{lineBreak:e.lineBreak,wordBreak:e.overflowWrap===St.BREAK_WORD?"break-word":e.wordBreak}),B=[];!(t=r.next()).done;)!function(){var A,e;t.value&&(A=t.value.slice(),A=a(A),e="",A.forEach(function(A){-1===vr.indexOf(A)?e+=Q(A):(e.length&&B.push(e),B.push(Q(A)),e="")}),e.length&&B.push(e))}();return B},xr=function(A,e,t){var r,B,n,s,o;this.text=Gr(e.data,t.textTransform),this.textBounds=(r=A,A=this.text,n=e,A=Mr(A,B=t),s=[],o=0,A.forEach(function(A){var e;B.textDecorationLine.length||0<A.trim().length?Rr.SUPPORT_RANGE_BOUNDS?Rr.SUPPORT_WORD_BREAKING?s.push(new Sr(A,br(r,n,o,A.length))):s.push(new Sr(A,h.fromDOMRectList(r,Dr(n,o,A.length).getClientRects()))):(e=n.splitText(A.length),s.push(new Sr(A,Or(r,n))),n=e):Rr.SUPPORT_RANGE_BOUNDS||(n=n.splitText(A.length)),o+=A.length}),s)},Gr=function(A,e){switch(e){case Vt.LOWERCASE:return A.toLowerCase();case Vt.CAPITALIZE:return A.replace(Pr,Vr);case Vt.UPPERCASE:return A.toUpperCase();default:return A}},Pr=/(^|\s|:|-|\(|\))([a-z])/g,Vr=function(A,e,t){return 0<A.length?e+t.toUpperCase():A},Xr=(A(kr,Kr=Tr),kr);function kr(A,e){A=Kr.call(this,A,e)||this;return A.src=e.currentSrc||e.src,A.intrinsicWidth=e.naturalWidth,A.intrinsicHeight=e.naturalHeight,A.context.cache.addImage(A.src),A}var Jr,Yr=(A(Wr,Jr=Tr),Wr);function Wr(A,e){A=Jr.call(this,A,e)||this;return A.canvas=e,A.intrinsicWidth=e.width,A.intrinsicHeight=e.height,A}var Zr,qr=(A(jr,Zr=Tr),jr);function jr(A,e){var t=Zr.call(this,A,e)||this,r=new XMLSerializer,A=d(A,e);return e.setAttribute("width",A.width+"px"),e.setAttribute("height",A.height+"px"),t.svg="data:image/svg+xml,"+encodeURIComponent(r.serializeToString(e)),t.intrinsicWidth=e.width.baseVal.value,t.intrinsicHeight=e.height.baseVal.value,t.context.cache.addImage(t.svg),t}var zr,$r=(A(AB,zr=Tr),AB);function AB(A,e){A=zr.call(this,A,e)||this;return A.value=e.value,A}var eB,tB=(A(rB,eB=Tr),rB);function rB(A,e){A=eB.call(this,A,e)||this;return A.start=e.start,A.reversed="boolean"==typeof e.reversed&&!0===e.reversed,A}var BB,nB=[{type:iA.DIMENSION_TOKEN,flags:0,unit:"px",number:3}],sB=[{type:iA.PERCENTAGE_TOKEN,flags:0,number:50}],oB="checkbox",iB="radio",aB="password",cB=707406591,wB=(A(QB,BB=Tr),QB);function QB(A,e){var t=BB.call(this,A,e)||this;switch(t.type=e.type.toLowerCase(),t.checked=e.checked,t.value=0===(e=(A=e).type===aB?new Array(A.value.length+1).join("•"):A.value).length?A.placeholder||"":e,t.type!==oB&&t.type!==iB||(t.styles.backgroundColor=3739148031,t.styles.borderTopColor=t.styles.borderRightColor=t.styles.borderBottomColor=t.styles.borderLeftColor=2779096575,t.styles.borderTopWidth=t.styles.borderRightWidth=t.styles.borderBottomWidth=t.styles.borderLeftWidth=1,t.styles.borderTopStyle=t.styles.borderRightStyle=t.styles.borderBottomStyle=t.styles.borderLeftStyle=We.SOLID,t.styles.backgroundClip=[ge.BORDER_BOX],t.styles.backgroundOrigin=[0],t.bounds=(e=t.bounds).width>e.height?new h(e.left+(e.width-e.height)/2,e.top,e.height,e.height):e.width<e.height?new h(e.left,e.top+(e.height-e.width)/2,e.width,e.width):e),t.type){case oB:t.styles.borderTopRightRadius=t.styles.borderTopLeftRadius=t.styles.borderBottomRightRadius=t.styles.borderBottomLeftRadius=nB;break;case iB:t.styles.borderTopRightRadius=t.styles.borderTopLeftRadius=t.styles.borderBottomRightRadius=t.styles.borderBottomLeftRadius=sB}return t}var uB,lB=(A(EB,uB=Tr),EB);function EB(A,e){A=uB.call(this,A,e)||this,e=e.options[e.selectedIndex||0];return A.value=e&&e.text||"",A}var CB,UB=(A(gB,CB=Tr),gB);function gB(A,e){A=CB.call(this,A,e)||this;return A.value=e.value,A}var FB,hB=(A(dB,FB=Tr),dB);function dB(A,e){var t,r,B=FB.call(this,A,e)||this;B.src=e.src,B.width=parseInt(e.width,10)||0,B.height=parseInt(e.height,10)||0,B.backgroundColor=B.styles.backgroundColor;try{e.contentWindow&&e.contentWindow.document&&e.contentWindow.document.documentElement&&(B.tree=LB(A,e.contentWindow.document.documentElement),t=e.contentWindow.document.documentElement?Ce(A,getComputedStyle(e.contentWindow.document.documentElement).backgroundColor):de.TRANSPARENT,r=e.contentWindow.document.body?Ce(A,getComputedStyle(e.contentWindow.document.body).backgroundColor):de.TRANSPARENT,B.backgroundColor=te(t)?te(r)?B.styles.backgroundColor:r:t)}catch(A){}return B}function HB(A){return"STYLE"===A.tagName}var fB=["OL","UL","MENU"],pB=function(e,A,t,r){for(var B=A.firstChild;B;B=s){var n,s=B.nextSibling;TB(B)&&0<B.data.trim().length?t.textNodes.push(new xr(e,B,t.styles)):mB(B)&&(VB(B)&&B.assignedNodes?B.assignedNodes().forEach(function(A){return pB(e,A,t,r)}):(n=KB(e,B)).styles.isVisible()&&(yB(B,n,r)?n.flags|=4:IB(n.styles)&&(n.flags|=2),-1!==fB.indexOf(B.tagName)&&(n.flags|=8),t.elements.push(n),B.slot,B.shadowRoot?pB(e,B.shadowRoot,n,r):GB(B)||bB(B)||PB(B)||pB(e,B,n,r)))}},KB=function(A,e){return new(_B(e)?Xr:vB(e)?Yr:bB(e)?qr:SB(e)?$r:OB(e)?tB:DB(e)?wB:PB(e)?lB:GB(e)?UB:xB(e)?hB:Tr)(A,e)},LB=function(A,e){var t=KB(A,e);return t.flags|=4,pB(A,e,t,t),t},yB=function(A,e,t){return e.styles.isPositionedWithZIndex()||e.styles.opacity<1||e.styles.isTransformed()||MB(A)&&t.styles.isTransparent()},IB=function(A){return A.isPositioned()||A.isFloating()},TB=function(A){return A.nodeType===Node.TEXT_NODE},mB=function(A){return A.nodeType===Node.ELEMENT_NODE},NB=function(A){return mB(A)&&void 0!==A.style&&!RB(A)},RB=function(A){return"object"==typeof A.className},SB=function(A){return"LI"===A.tagName},OB=function(A){return"OL"===A.tagName},DB=function(A){return"INPUT"===A.tagName},bB=function(A){return"svg"===A.tagName},MB=function(A){return"BODY"===A.tagName},vB=function(A){return"CANVAS"===A.tagName},_B=function(A){return"IMG"===A.tagName},xB=function(A){return"IFRAME"===A.tagName},GB=function(A){return"TEXTAREA"===A.tagName},PB=function(A){return"SELECT"===A.tagName},VB=function(A){return"SLOT"===A.tagName},XB=(kB.prototype.getCounterValue=function(A){A=this.counters[A];return A&&A.length?A[A.length-1]:1},kB.prototype.getCounterValues=function(A){A=this.counters[A];return A||[]},kB.prototype.pop=function(A){var e=this;A.forEach(function(A){return e.counters[A].pop()})},kB.prototype.parse=function(A){var t=this,e=A.counterIncrement,A=A.counterReset,r=!0;null!==e&&e.forEach(function(A){var e=t.counters[A.counter];e&&0!==A.increment&&(r=!1,e.length||e.push(1),e[Math.max(0,e.length-1)]+=A.increment)});var B=[];return r&&A.forEach(function(A){var e=t.counters[A.counter];B.push(A.counter),(e=e||(t.counters[A.counter]=[])).push(A.reset)}),B},kB);function kB(){this.counters={}}function JB(r,A,e,B,t,n){return r<A||e<r?rn(r,t,0<n.length):B.integers.reduce(function(A,e,t){for(;e<=r;)r-=e,A+=B.values[t];return A},"")+n}function YB(A,e,t,r){for(var B="";t||A--,B=r(A)+B,e<=(A/=e)*e;);return B}function WB(A,e,t,r,B){var n=t-e+1;return(A<0?"-":"")+(YB(Math.abs(A),n,r,function(A){return Q(Math.floor(A%n)+e)})+B)}function ZB(A,e,t){void 0===t&&(t=". ");var r=e.length;return YB(Math.abs(A),r,!1,function(A){return e[Math.floor(A%r)]})+t}function qB(A,e,t,r,B,n){if(A<-9999||9999<A)return rn(A,Kt.CJK_DECIMAL,0<B.length);var s=Math.abs(A),o=B;if(0===s)return e[0]+o;for(var i=0;0<s&&i<=4;i++){var a=s%10;0==a&&cr(n,1)&&""!==o?o=e[a]+o:1<a||1==a&&0===i||1==a&&1===i&&cr(n,2)||1==a&&1===i&&cr(n,4)&&100<A||1==a&&1<i&&cr(n,8)?o=e[a]+(0<i?t[i-1]:"")+o:1==a&&0<i&&(o=t[i-1]+o),s=Math.floor(s/10)}return(A<0?r:"")+o}var jB,zB={integers:[1e3,900,500,400,100,90,50,40,10,9,5,4,1],values:["M","CM","D","CD","C","XC","L","XL","X","IX","V","IV","I"]},$B={integers:[9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],values:["Ք","Փ","Ւ","Ց","Ր","Տ","Վ","Ս","Ռ","Ջ","Պ","Չ","Ո","Շ","Ն","Յ","Մ","Ճ","Ղ","Ձ","Հ","Կ","Ծ","Խ","Լ","Ի","Ժ","Թ","Ը","Է","Զ","Ե","Դ","Գ","Բ","Ա"]},An={integers:[1e4,9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,400,300,200,100,90,80,70,60,50,40,30,20,19,18,17,16,15,10,9,8,7,6,5,4,3,2,1],values:["י׳","ט׳","ח׳","ז׳","ו׳","ה׳","ד׳","ג׳","ב׳","א׳","ת","ש","ר","ק","צ","פ","ע","ס","נ","מ","ל","כ","יט","יח","יז","טז","טו","י","ט","ח","ז","ו","ה","ד","ג","ב","א"]},en={integers:[1e4,9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],values:["ჵ","ჰ","ჯ","ჴ","ხ","ჭ","წ","ძ","ც","ჩ","შ","ყ","ღ","ქ","ფ","ჳ","ტ","ს","რ","ჟ","პ","ო","ჲ","ნ","მ","ლ","კ","ი","თ","ჱ","ზ","ვ","ე","დ","გ","ბ","ა"]},tn="마이너스",rn=function(A,e,t){var r=t?". ":"",B=t?"、":"",n=t?", ":"",s=t?" ":"";switch(e){case Kt.DISC:return"•"+s;case Kt.CIRCLE:return"◦"+s;case Kt.SQUARE:return"◾"+s;case Kt.DECIMAL_LEADING_ZERO:var o=WB(A,48,57,!0,r);return o.length<4?"0"+o:o;case Kt.CJK_DECIMAL:return ZB(A,"〇一二三四五六七八九",B);case Kt.LOWER_ROMAN:return JB(A,1,3999,zB,Kt.DECIMAL,r).toLowerCase();case Kt.UPPER_ROMAN:return JB(A,1,3999,zB,Kt.DECIMAL,r);case Kt.LOWER_GREEK:return WB(A,945,969,!1,r);case Kt.LOWER_ALPHA:return WB(A,97,122,!1,r);case Kt.UPPER_ALPHA:return WB(A,65,90,!1,r);case Kt.ARABIC_INDIC:return WB(A,1632,1641,!0,r);case Kt.ARMENIAN:case Kt.UPPER_ARMENIAN:return JB(A,1,9999,$B,Kt.DECIMAL,r);case Kt.LOWER_ARMENIAN:return JB(A,1,9999,$B,Kt.DECIMAL,r).toLowerCase();case Kt.BENGALI:return WB(A,2534,2543,!0,r);case Kt.CAMBODIAN:case Kt.KHMER:return WB(A,6112,6121,!0,r);case Kt.CJK_EARTHLY_BRANCH:return ZB(A,"子丑寅卯辰巳午未申酉戌亥",B);case Kt.CJK_HEAVENLY_STEM:return ZB(A,"甲乙丙丁戊己庚辛壬癸",B);case Kt.CJK_IDEOGRAPHIC:case Kt.TRAD_CHINESE_INFORMAL:return qB(A,"零一二三四五六七八九","十百千萬","負",B,14);case Kt.TRAD_CHINESE_FORMAL:return qB(A,"零壹貳參肆伍陸柒捌玖","拾佰仟萬","負",B,15);case Kt.SIMP_CHINESE_INFORMAL:return qB(A,"零一二三四五六七八九","十百千萬","负",B,14);case Kt.SIMP_CHINESE_FORMAL:return qB(A,"零壹贰叁肆伍陆柒捌玖","拾佰仟萬","负",B,15);case Kt.JAPANESE_INFORMAL:return qB(A,"〇一二三四五六七八九","十百千万","マイナス",B,0);case Kt.JAPANESE_FORMAL:return qB(A,"零壱弐参四伍六七八九","拾百千万","マイナス",B,7);case Kt.KOREAN_HANGUL_FORMAL:return qB(A,"영일이삼사오육칠팔구","십백천만",tn,n,7);case Kt.KOREAN_HANJA_INFORMAL:return qB(A,"零一二三四五六七八九","十百千萬",tn,n,0);case Kt.KOREAN_HANJA_FORMAL:return qB(A,"零壹貳參四五六七八九","拾百千",tn,n,7);case Kt.DEVANAGARI:return WB(A,2406,2415,!0,r);case Kt.GEORGIAN:return JB(A,1,19999,en,Kt.DECIMAL,r);case Kt.GUJARATI:return WB(A,2790,2799,!0,r);case Kt.GURMUKHI:return WB(A,2662,2671,!0,r);case Kt.HEBREW:return JB(A,1,10999,An,Kt.DECIMAL,r);case Kt.HIRAGANA:return ZB(A,"あいうえおかきくけこさしすせそたちつてとなにぬねのはひふへほまみむめもやゆよらりるれろわゐゑをん");case Kt.HIRAGANA_IROHA:return ZB(A,"いろはにほへとちりぬるをわかよたれそつねならむうゐのおくやまけふこえてあさきゆめみしゑひもせす");case Kt.KANNADA:return WB(A,3302,3311,!0,r);case Kt.KATAKANA:return ZB(A,"アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヰヱヲン",B);case Kt.KATAKANA_IROHA:return ZB(A,"イロハニホヘトチリヌルヲワカヨタレソツネナラムウヰノオクヤマケフコエテアサキユメミシヱヒモセス",B);case Kt.LAO:return WB(A,3792,3801,!0,r);case Kt.MONGOLIAN:return WB(A,6160,6169,!0,r);case Kt.MYANMAR:return WB(A,4160,4169,!0,r);case Kt.ORIYA:return WB(A,2918,2927,!0,r);case Kt.PERSIAN:return WB(A,1776,1785,!0,r);case Kt.TAMIL:return WB(A,3046,3055,!0,r);case Kt.TELUGU:return WB(A,3174,3183,!0,r);case Kt.THAI:return WB(A,3664,3673,!0,r);case Kt.TIBETAN:return WB(A,3872,3881,!0,r);default:Kt.DECIMAL;return WB(A,48,57,!0,r)}},Bn="data-html2canvas-ignore",nn=(sn.prototype.toIFrame=function(A,r){var e=this,B=an(A,r);if(!B.contentWindow)return Promise.reject("Unable to find iframe window");var t=A.defaultView.pageXOffset,n=A.defaultView.pageYOffset,s=B.contentWindow,o=s.document,A=wn(B).then(function(){return w(e,void 0,void 0,function(){var e,t;return H(this,function(A){switch(A.label){case 0:return this.scrolledElements.forEach(Cn),s&&(s.scrollTo(r.left,r.top),!/(iPad|iPhone|iPod)/g.test(navigator.userAgent)||s.scrollY===r.top&&s.scrollX===r.left||(this.context.logger.warn("Unable to restore scroll position for cloned document"),this.context.windowBounds=this.context.windowBounds.add(s.scrollX-r.left,s.scrollY-r.top,0,0))),e=this.options.onclone,void 0===(t=this.clonedReferenceElement)?[2,Promise.reject("Error finding the "+this.referenceElement.nodeName+" in the cloned document")]:o.fonts&&o.fonts.ready?[4,o.fonts.ready]:[3,2];case 1:A.sent(),A.label=2;case 2:return/(AppleWebKit)/g.test(navigator.userAgent)?[4,cn(o)]:[3,4];case 3:A.sent(),A.label=4;case 4:return"function"==typeof e?[2,Promise.resolve().then(function(){return e(o,t)}).then(function(){return B})]:[2,B]}})})});return o.open(),o.write(ln(document.doctype)+"<html></html>"),En(this.referenceElement.ownerDocument,t,n),o.replaceChild(o.adoptNode(this.documentElement),o.documentElement),o.close(),A},sn.prototype.createElementClone=function(A){if(vB(A))return this.createCanvasClone(A);if(HB(A))return this.createStyleClone(A);var e=A.cloneNode(!1);return _B(e)&&(_B(A)&&A.currentSrc&&A.currentSrc!==A.src&&(e.src=A.currentSrc,e.srcset=""),"lazy"===e.loading&&(e.loading="eager")),e},sn.prototype.createStyleClone=function(A){try{var e=A.sheet;if(e&&e.cssRules){var t=[].slice.call(e.cssRules,0).reduce(function(A,e){return e&&"string"==typeof e.cssText?A+e.cssText:A},""),r=A.cloneNode(!1);return r.textContent=t,r}}catch(A){if(this.context.logger.error("Unable to access cssRules property",A),"SecurityError"!==A.name)throw A}return A.cloneNode(!1)},sn.prototype.createCanvasClone=function(A){if(this.options.inlineImages&&A.ownerDocument){var e=A.ownerDocument.createElement("img");try{return e.src=A.toDataURL(),e}catch(A){this.context.logger.info("Unable to clone canvas contents, canvas is tainted")}}e=A.cloneNode(!1);try{e.width=A.width,e.height=A.height;var t=A.getContext("2d"),r=e.getContext("2d");return r&&(t?r.putImageData(t.getImageData(0,0,A.width,A.height),0,0):r.drawImage(A,0,0)),e}catch(A){}return e},sn.prototype.cloneNode=function(A){if(TB(A))return document.createTextNode(A.data);if(!A.ownerDocument)return A.cloneNode(!1);var e=A.ownerDocument.defaultView;if(e&&mB(A)&&(NB(A)||RB(A))){var t=this.createElementClone(A),r=e.getComputedStyle(A),B=e.getComputedStyle(A,":before"),n=e.getComputedStyle(A,":after");this.referenceElement===A&&NB(t)&&(this.clonedReferenceElement=t),MB(t)&&hn(t);for(var e=this.counters.parse(new yr(this.context,r)),B=this.resolvePseudoContent(A,t,B,jB.BEFORE),s=A.firstChild;s;s=s.nextSibling)mB(s)&&("SCRIPT"===s.tagName||s.hasAttribute(Bn)||"function"==typeof this.options.ignoreElements&&this.options.ignoreElements(s))||this.options.copyStyles&&mB(s)&&HB(s)||t.appendChild(this.cloneNode(s));B&&t.insertBefore(B,t.firstChild);n=this.resolvePseudoContent(A,t,n,jB.AFTER);return n&&t.appendChild(n),this.counters.pop(e),r&&(this.options.copyStyles||RB(A))&&!xB(A)&&un(r,t),0===A.scrollTop&&0===A.scrollLeft||this.scrolledElements.push([t,A.scrollLeft,A.scrollTop]),(GB(A)||PB(A))&&(GB(t)||PB(t))&&(t.value=A.value),t}return A.cloneNode(!1)},sn.prototype.resolvePseudoContent=function(o,A,e,t){var i=this;if(e){var r=e.content,a=A.ownerDocument;if(a&&r&&"none"!==r&&"-moz-alt-content"!==r&&"none"!==e.display){this.counters.parse(new yr(this.context,e));var c=new Lr(this.context,e),w=a.createElement("html2canvaspseudoelement");un(e,w),c.content.forEach(function(A){if(A.type===iA.STRING_TOKEN)w.appendChild(a.createTextNode(A.value));else if(A.type===iA.URL_TOKEN){var e=a.createElement("img");e.src=A.value,e.style.opacity="1",w.appendChild(e)}else if(A.type===iA.FUNCTION){var t,r,B,n,s;"attr"===A.name?(e=A.values.filter(XA)).length&&w.appendChild(a.createTextNode(o.getAttribute(e[0].value)||"")):"counter"===A.name?(B=(r=A.values.filter(WA))[0],r=r[1],B&&XA(B)&&(t=i.counters.getCounterValue(B.value),s=r&&XA(r)?It.parse(i.context,r.value):Kt.DECIMAL,w.appendChild(a.createTextNode(rn(t,s,!1))))):"counters"===A.name&&(B=(t=A.values.filter(WA))[0],s=t[1],r=t[2],B&&XA(B)&&(B=i.counters.getCounterValues(B.value),n=r&&XA(r)?It.parse(i.context,r.value):Kt.DECIMAL,s=s&&s.type===iA.STRING_TOKEN?s.value:"",s=B.map(function(A){return rn(A,n,!1)}).join(s),w.appendChild(a.createTextNode(s))))}else if(A.type===iA.IDENT_TOKEN)switch(A.value){case"open-quote":w.appendChild(a.createTextNode(wr(c.quotes,i.quoteDepth++,!0)));break;case"close-quote":w.appendChild(a.createTextNode(wr(c.quotes,--i.quoteDepth,!1)));break;default:w.appendChild(a.createTextNode(A.value))}}),w.className=Un+" "+gn;t=t===jB.BEFORE?" "+Un:" "+gn;return RB(A)?A.className.baseValue+=t:A.className+=t,w}}},sn.destroy=function(A){return!!A.parentNode&&(A.parentNode.removeChild(A),!0)},sn);function sn(A,e,t){if(this.context=A,this.options=t,this.scrolledElements=[],this.referenceElement=e,this.counters=new XB,this.quoteDepth=0,!e.ownerDocument)throw new Error("Cloned element does not have an owner document");this.documentElement=this.cloneNode(e.ownerDocument.documentElement)}(be=jB=jB||{})[be.BEFORE=0]="BEFORE",be[be.AFTER=1]="AFTER";function on(e){return new Promise(function(A){!e.complete&&e.src?(e.onload=A,e.onerror=A):A()})}var an=function(A,e){var t=A.createElement("iframe");return t.className="html2canvas-container",t.style.visibility="hidden",t.style.position="fixed",t.style.left="-10000px",t.style.top="0px",t.style.border="0",t.width=e.width.toString(),t.height=e.height.toString(),t.scrolling="no",t.setAttribute(Bn,"true"),A.body.appendChild(t),t},cn=function(A){return Promise.all([].slice.call(A.images,0).map(on))},wn=function(B){return new Promise(function(e,A){var t=B.contentWindow;if(!t)return A("No window assigned for iframe");var r=t.document;t.onload=B.onload=function(){t.onload=B.onload=null;var A=setInterval(function(){0<r.body.childNodes.length&&"complete"===r.readyState&&(clearInterval(A),e(B))},50)}})},Qn=["all","d","content"],un=function(A,e){for(var t=A.length-1;0<=t;t--){var r=A.item(t);-1===Qn.indexOf(r)&&e.style.setProperty(r,A.getPropertyValue(r))}return e},ln=function(A){var e="";return A&&(e+="<!DOCTYPE ",A.name&&(e+=A.name),A.internalSubset&&(e+=A.internalSubset),A.publicId&&(e+='"'+A.publicId+'"'),A.systemId&&(e+='"'+A.systemId+'"'),e+=">"),e},En=function(A,e,t){A&&A.defaultView&&(e!==A.defaultView.pageXOffset||t!==A.defaultView.pageYOffset)&&A.defaultView.scrollTo(e,t)},Cn=function(A){var e=A[0],t=A[1],A=A[2];e.scrollLeft=t,e.scrollTop=A},Un="___html2canvas___pseudoelement_before",gn="___html2canvas___pseudoelement_after",Fn='{\n    content: "" !important;\n    display: none !important;\n}',hn=function(A){dn(A,"."+Un+":before"+Fn+"\n         ."+gn+":after"+Fn)},dn=function(A,e){var t=A.ownerDocument;t&&((t=t.createElement("style")).textContent=e,A.appendChild(t))},Hn=(fn.getOrigin=function(A){var e=fn._link;return e?(e.href=A,e.href=e.href,e.protocol+e.hostname+e.port):"about:blank"},fn.isSameOrigin=function(A){return fn.getOrigin(A)===fn._origin},fn.setContext=function(A){fn._link=A.document.createElement("a"),fn._origin=fn.getOrigin(A.location.href)},fn._origin="about:blank",fn);function fn(){}var pn=(Kn.prototype.addImage=function(A){var e=Promise.resolve();return this.has(A)||(Sn(A)||mn(A))&&(this._cache[A]=this.loadImage(A)).catch(function(){}),e},Kn.prototype.match=function(A){return this._cache[A]},Kn.prototype.loadImage=function(s){return w(this,void 0,void 0,function(){var e,r,t,B,n=this;return H(this,function(A){switch(A.label){case 0:return(e=Hn.isSameOrigin(s),r=!Nn(s)&&!0===this._options.useCORS&&Rr.SUPPORT_CORS_IMAGES&&!e,t=!Nn(s)&&!e&&!Sn(s)&&"string"==typeof this._options.proxy&&Rr.SUPPORT_CORS_XHR&&!r,e||!1!==this._options.allowTaint||Nn(s)||Sn(s)||t||r)?(B=s,t?[4,this.proxy(B)]:[3,2]):[2];case 1:B=A.sent(),A.label=2;case 2:return this.context.logger.debug("Added image "+s.substring(0,256)),[4,new Promise(function(A,e){var t=new Image;t.onload=function(){return A(t)},t.onerror=e,(Rn(B)||r)&&(t.crossOrigin="anonymous"),t.src=B,!0===t.complete&&setTimeout(function(){return A(t)},500),0<n._options.imageTimeout&&setTimeout(function(){return e("Timed out ("+n._options.imageTimeout+"ms) loading image")},n._options.imageTimeout)})];case 3:return[2,A.sent()]}})})},Kn.prototype.has=function(A){return void 0!==this._cache[A]},Kn.prototype.keys=function(){return Promise.resolve(Object.keys(this._cache))},Kn.prototype.proxy=function(s){var o=this,i=this._options.proxy;if(!i)throw new Error("No proxy defined");var a=s.substring(0,256);return new Promise(function(e,t){var r=Rr.SUPPORT_RESPONSE_TYPE?"blob":"text",B=new XMLHttpRequest;B.onload=function(){var A;200===B.status?"text"==r?e(B.response):((A=new FileReader).addEventListener("load",function(){return e(A.result)},!1),A.addEventListener("error",function(A){return t(A)},!1),A.readAsDataURL(B.response)):t("Failed to proxy resource "+a+" with status code "+B.status)},B.onerror=t;var A,n=-1<i.indexOf("?")?"&":"?";B.open("GET",i+n+"url="+encodeURIComponent(s)+"&responseType="+r),"text"!=r&&B instanceof XMLHttpRequest&&(B.responseType=r),o._options.imageTimeout&&(A=o._options.imageTimeout,B.timeout=A,B.ontimeout=function(){return t("Timed out ("+A+"ms) proxying "+a)}),B.send()})},Kn);function Kn(A,e){this.context=A,this._options=e,this._cache={}}var Ln,yn=/^data:image\/svg\+xml/i,In=/^data:image\/.*;base64,/i,Tn=/^data:image\/.*/i,mn=function(A){return Rr.SUPPORT_SVG_DRAWING||!On(A)},Nn=function(A){return Tn.test(A)},Rn=function(A){return In.test(A)},Sn=function(A){return"blob"===A.substr(0,4)},On=function(A){return"svg"===A.substr(-3).toLowerCase()||yn.test(A)};(be=Ln=Ln||{})[be.VECTOR=0]="VECTOR",be[be.BEZIER_CURVE=1]="BEZIER_CURVE";function Dn(A,t){return A.length===t.length&&A.some(function(A,e){return A===t[e]})}var bn=(Mn.prototype.add=function(A,e){return new Mn(this.x+A,this.y+e)},Mn);function Mn(A,e){this.type=Ln.VECTOR,this.x=A,this.y=e}function vn(A,e,t){return new bn(A.x+(e.x-A.x)*t,A.y+(e.y-A.y)*t)}var _n=(xn.prototype.subdivide=function(A,e){var t=vn(this.start,this.startControl,A),r=vn(this.startControl,this.endControl,A),B=vn(this.endControl,this.end,A),n=vn(t,r,A),r=vn(r,B,A),A=vn(n,r,A);return e?new xn(this.start,t,n,A):new xn(A,r,B,this.end)},xn.prototype.add=function(A,e){return new xn(this.start.add(A,e),this.startControl.add(A,e),this.endControl.add(A,e),this.end.add(A,e))},xn.prototype.reverse=function(){return new xn(this.end,this.endControl,this.startControl,this.start)},xn);function xn(A,e,t,r){this.type=Ln.BEZIER_CURVE,this.start=A,this.startControl=e,this.endControl=t,this.end=r}function Gn(A){return A.type===Ln.BEZIER_CURVE}var Pn,Vn=function(A){var e=A.styles,t=A.bounds,r=(C=$A(e.borderTopLeftRadius,t.width,t.height))[0],B=C[1],n=(U=$A(e.borderTopRightRadius,t.width,t.height))[0],s=U[1],o=(g=$A(e.borderBottomRightRadius,t.width,t.height))[0],i=g[1],a=(F=$A(e.borderBottomLeftRadius,t.width,t.height))[0],c=F[1];(h=[]).push((r+n)/t.width),h.push((a+o)/t.width),h.push((B+c)/t.height),h.push((s+i)/t.height),1<(d=Math.max.apply(Math,h))&&(r/=d,B/=d,n/=d,s/=d,o/=d,i/=d,a/=d,c/=d);var w=t.width-n,Q=t.height-i,u=t.width-o,l=t.height-c,E=e.borderTopWidth,C=e.borderRightWidth,U=e.borderBottomWidth,g=e.borderLeftWidth,F=ae(e.paddingTop,A.bounds.width),h=ae(e.paddingRight,A.bounds.width),d=ae(e.paddingBottom,A.bounds.width),A=ae(e.paddingLeft,A.bounds.width);this.topLeftBorderDoubleOuterBox=0<r||0<B?Jn(t.left+g/3,t.top+E/3,r-g/3,B-E/3,Pn.TOP_LEFT):new bn(t.left+g/3,t.top+E/3),this.topRightBorderDoubleOuterBox=0<r||0<B?Jn(t.left+w,t.top+E/3,n-C/3,s-E/3,Pn.TOP_RIGHT):new bn(t.left+t.width-C/3,t.top+E/3),this.bottomRightBorderDoubleOuterBox=0<o||0<i?Jn(t.left+u,t.top+Q,o-C/3,i-U/3,Pn.BOTTOM_RIGHT):new bn(t.left+t.width-C/3,t.top+t.height-U/3),this.bottomLeftBorderDoubleOuterBox=0<a||0<c?Jn(t.left+g/3,t.top+l,a-g/3,c-U/3,Pn.BOTTOM_LEFT):new bn(t.left+g/3,t.top+t.height-U/3),this.topLeftBorderDoubleInnerBox=0<r||0<B?Jn(t.left+2*g/3,t.top+2*E/3,r-2*g/3,B-2*E/3,Pn.TOP_LEFT):new bn(t.left+2*g/3,t.top+2*E/3),this.topRightBorderDoubleInnerBox=0<r||0<B?Jn(t.left+w,t.top+2*E/3,n-2*C/3,s-2*E/3,Pn.TOP_RIGHT):new bn(t.left+t.width-2*C/3,t.top+2*E/3),this.bottomRightBorderDoubleInnerBox=0<o||0<i?Jn(t.left+u,t.top+Q,o-2*C/3,i-2*U/3,Pn.BOTTOM_RIGHT):new bn(t.left+t.width-2*C/3,t.top+t.height-2*U/3),this.bottomLeftBorderDoubleInnerBox=0<a||0<c?Jn(t.left+2*g/3,t.top+l,a-2*g/3,c-2*U/3,Pn.BOTTOM_LEFT):new bn(t.left+2*g/3,t.top+t.height-2*U/3),this.topLeftBorderStroke=0<r||0<B?Jn(t.left+g/2,t.top+E/2,r-g/2,B-E/2,Pn.TOP_LEFT):new bn(t.left+g/2,t.top+E/2),this.topRightBorderStroke=0<r||0<B?Jn(t.left+w,t.top+E/2,n-C/2,s-E/2,Pn.TOP_RIGHT):new bn(t.left+t.width-C/2,t.top+E/2),this.bottomRightBorderStroke=0<o||0<i?Jn(t.left+u,t.top+Q,o-C/2,i-U/2,Pn.BOTTOM_RIGHT):new bn(t.left+t.width-C/2,t.top+t.height-U/2),this.bottomLeftBorderStroke=0<a||0<c?Jn(t.left+g/2,t.top+l,a-g/2,c-U/2,Pn.BOTTOM_LEFT):new bn(t.left+g/2,t.top+t.height-U/2),this.topLeftBorderBox=0<r||0<B?Jn(t.left,t.top,r,B,Pn.TOP_LEFT):new bn(t.left,t.top),this.topRightBorderBox=0<n||0<s?Jn(t.left+w,t.top,n,s,Pn.TOP_RIGHT):new bn(t.left+t.width,t.top),this.bottomRightBorderBox=0<o||0<i?Jn(t.left+u,t.top+Q,o,i,Pn.BOTTOM_RIGHT):new bn(t.left+t.width,t.top+t.height),this.bottomLeftBorderBox=0<a||0<c?Jn(t.left,t.top+l,a,c,Pn.BOTTOM_LEFT):new bn(t.left,t.top+t.height),this.topLeftPaddingBox=0<r||0<B?Jn(t.left+g,t.top+E,Math.max(0,r-g),Math.max(0,B-E),Pn.TOP_LEFT):new bn(t.left+g,t.top+E),this.topRightPaddingBox=0<n||0<s?Jn(t.left+Math.min(w,t.width-C),t.top+E,w>t.width+C?0:Math.max(0,n-C),Math.max(0,s-E),Pn.TOP_RIGHT):new bn(t.left+t.width-C,t.top+E),this.bottomRightPaddingBox=0<o||0<i?Jn(t.left+Math.min(u,t.width-g),t.top+Math.min(Q,t.height-U),Math.max(0,o-C),Math.max(0,i-U),Pn.BOTTOM_RIGHT):new bn(t.left+t.width-C,t.top+t.height-U),this.bottomLeftPaddingBox=0<a||0<c?Jn(t.left+g,t.top+Math.min(l,t.height-U),Math.max(0,a-g),Math.max(0,c-U),Pn.BOTTOM_LEFT):new bn(t.left+g,t.top+t.height-U),this.topLeftContentBox=0<r||0<B?Jn(t.left+g+A,t.top+E+F,Math.max(0,r-(g+A)),Math.max(0,B-(E+F)),Pn.TOP_LEFT):new bn(t.left+g+A,t.top+E+F),this.topRightContentBox=0<n||0<s?Jn(t.left+Math.min(w,t.width+g+A),t.top+E+F,w>t.width+g+A?0:n-g+A,s-(E+F),Pn.TOP_RIGHT):new bn(t.left+t.width-(C+h),t.top+E+F),this.bottomRightContentBox=0<o||0<i?Jn(t.left+Math.min(u,t.width-(g+A)),t.top+Math.min(Q,t.height+E+F),Math.max(0,o-(C+h)),i-(U+d),Pn.BOTTOM_RIGHT):new bn(t.left+t.width-(C+h),t.top+t.height-(U+d)),this.bottomLeftContentBox=0<a||0<c?Jn(t.left+g+A,t.top+l,Math.max(0,a-(g+A)),c-(U+d),Pn.BOTTOM_LEFT):new bn(t.left+g+A,t.top+t.height-(U+d))};(be=Pn=Pn||{})[be.TOP_LEFT=0]="TOP_LEFT",be[be.TOP_RIGHT=1]="TOP_RIGHT",be[be.BOTTOM_RIGHT=2]="BOTTOM_RIGHT",be[be.BOTTOM_LEFT=3]="BOTTOM_LEFT";function Xn(A){return[A.topLeftBorderBox,A.topRightBorderBox,A.bottomRightBorderBox,A.bottomLeftBorderBox]}function kn(A){return[A.topLeftPaddingBox,A.topRightPaddingBox,A.bottomRightPaddingBox,A.bottomLeftPaddingBox]}var Jn=function(A,e,t,r,B){var n=(Math.sqrt(2)-1)/3*4,s=t*n,o=r*n,i=A+t,a=e+r;switch(B){case Pn.TOP_LEFT:return new _n(new bn(A,a),new bn(A,a-o),new bn(i-s,e),new bn(i,e));case Pn.TOP_RIGHT:return new _n(new bn(A,e),new bn(A+s,e),new bn(i,a-o),new bn(i,a));case Pn.BOTTOM_RIGHT:return new _n(new bn(i,e),new bn(i,e+o),new bn(A+s,a),new bn(A,a));default:Pn.BOTTOM_LEFT;return new _n(new bn(i,a),new bn(i-s,a),new bn(A,e+o),new bn(A,e))}},Yn=function(A,e,t){this.type=0,this.target=6,this.offsetX=A,this.offsetY=e,this.matrix=t},Wn=function(A,e){this.type=1,this.target=e,this.path=A},Zn=function(A){this.type=2,this.target=6,this.opacity=A},qn=function(A){this.element=A,this.inlineLevel=[],this.nonInlineLevel=[],this.negativeZIndex=[],this.zeroOrAutoZIndexOrTransformedOrOpacity=[],this.positiveZIndex=[],this.nonPositionedFloats=[],this.nonPositionedInlineLevel=[]},jn=(zn.prototype.getParentEffects=function(){var A,e,t=this.effects.slice(0);return this.container.styles.overflowX!==yt.VISIBLE&&(A=Xn(this.curves),e=kn(this.curves),Dn(A,e)||t.push(new Wn(e,6))),t},zn);function zn(A,e){var t,r;this.container=A,this.effects=e.slice(0),this.curves=new Vn(A),A.styles.opacity<1&&this.effects.push(new Zn(A.styles.opacity)),null!==A.styles.transform&&(t=A.bounds.left+A.styles.transformOrigin[0].number,e=A.bounds.top+A.styles.transformOrigin[1].number,r=A.styles.transform,this.effects.push(new Yn(t,e,r))),A.styles.overflowX!==yt.VISIBLE&&(r=Xn(this.curves),A=kn(this.curves),Dn(r,A)?this.effects.push(new Wn(r,6)):(this.effects.push(new Wn(r,2)),this.effects.push(new Wn(A,4))))}function $n(A,e){switch(e){case 0:return is(A.topLeftBorderBox,A.topLeftPaddingBox,A.topRightBorderBox,A.topRightPaddingBox);case 1:return is(A.topRightBorderBox,A.topRightPaddingBox,A.bottomRightBorderBox,A.bottomRightPaddingBox);case 2:return is(A.bottomRightBorderBox,A.bottomRightPaddingBox,A.bottomLeftBorderBox,A.bottomLeftPaddingBox);default:return is(A.bottomLeftBorderBox,A.bottomLeftPaddingBox,A.topLeftBorderBox,A.topLeftPaddingBox)}}function As(A){var e=A.bounds,A=A.styles;return e.add(A.borderLeftWidth,A.borderTopWidth,-(A.borderRightWidth+A.borderLeftWidth),-(A.borderTopWidth+A.borderBottomWidth))}function es(A){var e=A.styles,t=A.bounds,r=ae(e.paddingLeft,t.width),B=ae(e.paddingRight,t.width),n=ae(e.paddingTop,t.width),A=ae(e.paddingBottom,t.width);return t.add(r+e.borderLeftWidth,n+e.borderTopWidth,-(e.borderRightWidth+e.borderLeftWidth+r+B),-(e.borderTopWidth+e.borderBottomWidth+n+A))}function ts(A,e,t){var r=(B=cs(A.styles.backgroundOrigin,e),n=A,0===B?n.bounds:(2===B?es:As)(n)),B=(s=cs(A.styles.backgroundClip,e),o=A,s===ge.BORDER_BOX?o.bounds:(s===ge.CONTENT_BOX?es:As)(o)),n=as(cs(A.styles.backgroundSize,e),t,r),s=n[0],o=n[1],t=$A(cs(A.styles.backgroundPosition,e),r.width-s,r.height-o);return[ws(cs(A.styles.backgroundRepeat,e),t,n,r,B),Math.round(r.left+t[0]),Math.round(r.top+t[1]),s,o]}function rs(A){return XA(A)&&A.value===ke.AUTO}function Bs(A){return"number"==typeof A}var ns=function(a,c,w,Q){a.container.elements.forEach(function(A){var e=cr(A.flags,4),t=cr(A.flags,2),r=new jn(A,a.getParentEffects());cr(A.styles.display,2048)&&Q.push(r);var B,n,s,o,i=cr(A.flags,8)?[]:Q;e||t?(B=e||A.styles.isPositioned()?w:c,t=new qn(r),A.styles.isPositioned()||A.styles.opacity<1||A.styles.isTransformed()?(n=A.styles.zIndex.order)<0?(s=0,B.negativeZIndex.some(function(A,e){return n>A.element.container.styles.zIndex.order?(s=e,!1):0<s}),B.negativeZIndex.splice(s,0,t)):0<n?(o=0,B.positiveZIndex.some(function(A,e){return n>=A.element.container.styles.zIndex.order?(o=e+1,!1):0<o}),B.positiveZIndex.splice(o,0,t)):B.zeroOrAutoZIndexOrTransformedOrOpacity.push(t):(A.styles.isFloating()?B.nonPositionedFloats:B.nonPositionedInlineLevel).push(t),ns(r,t,e?t:w,i)):((A.styles.isInlineLevel()?c.inlineLevel:c.nonInlineLevel).push(r),ns(r,c,w,i)),cr(A.flags,8)&&ss(A,i)})},ss=function(A,e){for(var t=A instanceof tB?A.start:1,r=A instanceof tB&&A.reversed,B=0;B<e.length;B++){var n=e[B];n.container instanceof $r&&"number"==typeof n.container.value&&0!==n.container.value&&(t=n.container.value),n.listValue=rn(t,n.container.styles.listStyleType,!0),t+=r?-1:1}},os=function(A,e){var t=[];return Gn(A)?t.push(A.subdivide(.5,!1)):t.push(A),Gn(e)?t.push(e.subdivide(.5,!0)):t.push(e),t},is=function(A,e,t,r){var B=[];return Gn(A)?B.push(A.subdivide(.5,!1)):B.push(A),Gn(t)?B.push(t.subdivide(.5,!0)):B.push(t),Gn(r)?B.push(r.subdivide(.5,!0).reverse()):B.push(r),Gn(e)?B.push(e.subdivide(.5,!1).reverse()):B.push(e),B},as=function(A,e,t){var r=e[0],B=e[1],n=e[2],s=A[0],o=A[1];if(!s)return[0,0];if(jA(s)&&o&&jA(o))return[ae(s,t.width),ae(o,t.height)];var i=Bs(n);if(XA(s)&&(s.value===ke.CONTAIN||s.value===ke.COVER))return Bs(n)?t.width/t.height<n!=(s.value===ke.COVER)?[t.width,t.width/n]:[t.height*n,t.height]:[t.width,t.height];var a=Bs(r),e=Bs(B),A=a||e;if(rs(s)&&(!o||rs(o)))return a&&e?[r,B]:i||A?A&&i?[a?r:B*n,e?B:r/n]:[a?r:t.width,e?B:t.height]:[t.width,t.height];if(i){var c=0,w=0;return jA(s)?c=ae(s,t.width):jA(o)&&(w=ae(o,t.height)),rs(s)?c=w*n:o&&!rs(o)||(w=c/n),[c,w]}c=null,w=null;if(jA(s)?c=ae(s,t.width):o&&jA(o)&&(w=ae(o,t.height)),null!==(c=null!==(w=null!==c&&(!o||rs(o))?a&&e?c/r*B:t.height:w)&&rs(s)?a&&e?w/B*r:t.width:c)&&null!==w)return[c,w];throw new Error("Unable to calculate background-size for element")},cs=function(A,e){e=A[e];return void 0===e?A[0]:e},ws=function(A,e,t,r,B){var n=e[0],s=e[1],o=t[0],i=t[1];switch(A){case xe.REPEAT_X:return[new bn(Math.round(r.left),Math.round(r.top+s)),new bn(Math.round(r.left+r.width),Math.round(r.top+s)),new bn(Math.round(r.left+r.width),Math.round(i+r.top+s)),new bn(Math.round(r.left),Math.round(i+r.top+s))];case xe.REPEAT_Y:return[new bn(Math.round(r.left+n),Math.round(r.top)),new bn(Math.round(r.left+n+o),Math.round(r.top)),new bn(Math.round(r.left+n+o),Math.round(r.height+r.top)),new bn(Math.round(r.left+n),Math.round(r.height+r.top))];case xe.NO_REPEAT:return[new bn(Math.round(r.left+n),Math.round(r.top+s)),new bn(Math.round(r.left+n+o),Math.round(r.top+s)),new bn(Math.round(r.left+n+o),Math.round(r.top+s+i)),new bn(Math.round(r.left+n),Math.round(r.top+s+i))];default:return[new bn(Math.round(B.left),Math.round(B.top)),new bn(Math.round(B.left+B.width),Math.round(B.top)),new bn(Math.round(B.left+B.width),Math.round(B.height+B.top)),new bn(Math.round(B.left),Math.round(B.height+B.top))]}},Qs="Hidden Text",us=(ls.prototype.parseMetrics=function(A,e){var t=this._document.createElement("div"),r=this._document.createElement("img"),B=this._document.createElement("span"),n=this._document.body;t.style.visibility="hidden",t.style.fontFamily=A,t.style.fontSize=e,t.style.margin="0",t.style.padding="0",n.appendChild(t),r.src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",r.width=1,r.height=1,r.style.margin="0",r.style.padding="0",r.style.verticalAlign="baseline",B.style.fontFamily=A,B.style.fontSize=e,B.style.margin="0",B.style.padding="0",B.appendChild(this._document.createTextNode(Qs)),t.appendChild(B),t.appendChild(r);e=r.offsetTop-B.offsetTop+2;t.removeChild(B),t.appendChild(this._document.createTextNode(Qs)),t.style.lineHeight="normal",r.style.verticalAlign="super";r=r.offsetTop-t.offsetTop+2;return n.removeChild(t),{baseline:e,middle:r}},ls.prototype.getMetrics=function(A,e){var t=A+" "+e;return void 0===this._data[t]&&(this._data[t]=this.parseMetrics(A,e)),this._data[t]},ls);function ls(A){this._data={},this._document=A}var Es,be=function(A,e){this.context=A,this.options=e},Cs=(A(Us,Es=be),Us.prototype.applyEffects=function(A,e){for(var t=this;this._activeEffects.length;)this.popEffect();A.filter(function(A){return cr(A.target,e)}).forEach(function(A){return t.applyEffect(A)})},Us.prototype.applyEffect=function(A){this.ctx.save(),2===A.type&&(this.ctx.globalAlpha=A.opacity),0===A.type&&(this.ctx.translate(A.offsetX,A.offsetY),this.ctx.transform(A.matrix[0],A.matrix[1],A.matrix[2],A.matrix[3],A.matrix[4],A.matrix[5]),this.ctx.translate(-A.offsetX,-A.offsetY)),1===A.type&&(this.path(A.path),this.ctx.clip()),this._activeEffects.push(A)},Us.prototype.popEffect=function(){this._activeEffects.pop(),this.ctx.restore()},Us.prototype.renderStack=function(e){return w(this,void 0,void 0,function(){return H(this,function(A){switch(A.label){case 0:return e.element.container.styles.isVisible()?[4,this.renderStackContent(e)]:[3,2];case 1:A.sent(),A.label=2;case 2:return[2]}})})},Us.prototype.renderNode=function(e){return w(this,void 0,void 0,function(){return H(this,function(A){switch(A.label){case 0:return e.container.styles.isVisible()?[4,this.renderNodeBackgroundAndBorders(e)]:[3,3];case 1:return A.sent(),[4,this.renderNodeContent(e)];case 2:A.sent(),A.label=3;case 3:return[2]}})})},Us.prototype.renderTextWithLetterSpacing=function(t,A,r){var B=this;0===A?this.ctx.fillText(t.text,t.bounds.left,t.bounds.top+r):a(t.text).map(function(A){return Q(A)}).reduce(function(A,e){return B.ctx.fillText(e,A,t.bounds.top+r),A+B.ctx.measureText(e).width},t.bounds.left)},Us.prototype.createFontStyle=function(A){var e=A.fontVariant.filter(function(A){return"normal"===A||"small-caps"===A}).join(""),t=A.fontFamily.join(", "),r=PA(A.fontSize)?""+A.fontSize.number+A.fontSize.unit:A.fontSize.number+"px";return[[A.fontStyle,e,A.fontWeight,r,t].join(" "),t,r]},Us.prototype.renderTextNode=function(i,a){return w(this,void 0,void 0,function(){var e,t,r,B,n,s,o=this;return H(this,function(A){return r=this.createFontStyle(a),e=r[0],t=r[1],r=r[2],this.ctx.font=e,this.ctx.textBaseline="alphabetic",r=this.fontMetrics.getMetrics(t,r),B=r.baseline,n=r.middle,s=a.paintOrder,i.textBounds.forEach(function(t){s.forEach(function(A){switch(A){case Qr.FILL:o.ctx.fillStyle=re(a.color),o.renderTextWithLetterSpacing(t,a.letterSpacing,B);var e=a.textShadow;e.length&&t.text.trim().length&&(e.slice(0).reverse().forEach(function(A){o.ctx.shadowColor=re(A.color),o.ctx.shadowOffsetX=A.offsetX.number*o.options.scale,o.ctx.shadowOffsetY=A.offsetY.number*o.options.scale,o.ctx.shadowBlur=A.blur.number,o.renderTextWithLetterSpacing(t,a.letterSpacing,B)}),o.ctx.shadowColor="",o.ctx.shadowOffsetX=0,o.ctx.shadowOffsetY=0,o.ctx.shadowBlur=0),a.textDecorationLine.length&&(o.ctx.fillStyle=re(a.textDecorationColor||a.color),a.textDecorationLine.forEach(function(A){switch(A){case 1:o.ctx.fillRect(t.bounds.left,Math.round(t.bounds.top+B),t.bounds.width,1);break;case 2:o.ctx.fillRect(t.bounds.left,Math.round(t.bounds.top),t.bounds.width,1);break;case 3:o.ctx.fillRect(t.bounds.left,Math.ceil(t.bounds.top+n),t.bounds.width,1)}}));break;case Qr.STROKE:a.webkitTextStrokeWidth&&t.text.trim().length&&(o.ctx.strokeStyle=re(a.webkitTextStrokeColor),o.ctx.lineWidth=a.webkitTextStrokeWidth,o.ctx.lineJoin=window.chrome?"miter":"round",o.ctx.strokeText(t.text,t.bounds.left,t.bounds.top+B)),o.ctx.strokeStyle="",o.ctx.lineWidth=0,o.ctx.lineJoin="miter"}})}),[2]})})},Us.prototype.renderReplacedElement=function(A,e,t){var r;t&&0<A.intrinsicWidth&&0<A.intrinsicHeight&&(r=es(A),e=kn(e),this.path(e),this.ctx.save(),this.ctx.clip(),this.ctx.drawImage(t,0,0,A.intrinsicWidth,A.intrinsicHeight,r.left,r.top,r.width,r.height),this.ctx.restore())},Us.prototype.renderNodeContent=function(u){return w(this,void 0,void 0,function(){var e,t,r,B,n,s,o,i,a,c,w,Q;return H(this,function(A){switch(A.label){case 0:this.applyEffects(u.effects,4),e=u.container,t=u.curves,r=e.styles,B=0,n=e.textNodes,A.label=1;case 1:return B<n.length?(s=n[B],[4,this.renderTextNode(s,r)]):[3,4];case 2:A.sent(),A.label=3;case 3:return B++,[3,1];case 4:if(!(e instanceof Xr))return[3,8];A.label=5;case 5:return A.trys.push([5,7,,8]),[4,this.context.cache.match(e.src)];case 6:return a=A.sent(),this.renderReplacedElement(e,t,a),[3,8];case 7:return A.sent(),this.context.logger.error("Error loading image "+e.src),[3,8];case 8:if(e instanceof Yr&&this.renderReplacedElement(e,t,e.canvas),!(e instanceof qr))return[3,12];A.label=9;case 9:return A.trys.push([9,11,,12]),[4,this.context.cache.match(e.svg)];case 10:return a=A.sent(),this.renderReplacedElement(e,t,a),[3,12];case 11:return A.sent(),this.context.logger.error("Error loading svg "+e.svg.substring(0,255)),[3,12];case 12:return e instanceof hB&&e.tree?[4,new Us(this.context,{scale:this.options.scale,backgroundColor:e.backgroundColor,x:0,y:0,width:e.width,height:e.height}).render(e.tree)]:[3,14];case 13:s=A.sent(),e.width&&e.height&&this.ctx.drawImage(s,0,0,e.width,e.height,e.bounds.left,e.bounds.top,e.bounds.width,e.bounds.height),A.label=14;case 14:if(e instanceof wB&&(i=Math.min(e.bounds.width,e.bounds.height),e.type===oB?e.checked&&(this.ctx.save(),this.path([new bn(e.bounds.left+.39363*i,e.bounds.top+.79*i),new bn(e.bounds.left+.16*i,e.bounds.top+.5549*i),new bn(e.bounds.left+.27347*i,e.bounds.top+.44071*i),new bn(e.bounds.left+.39694*i,e.bounds.top+.5649*i),new bn(e.bounds.left+.72983*i,e.bounds.top+.23*i),new bn(e.bounds.left+.84*i,e.bounds.top+.34085*i),new bn(e.bounds.left+.39363*i,e.bounds.top+.79*i)]),this.ctx.fillStyle=re(cB),this.ctx.fill(),this.ctx.restore()):e.type===iB&&e.checked&&(this.ctx.save(),this.ctx.beginPath(),this.ctx.arc(e.bounds.left+i/2,e.bounds.top+i/2,i/4,0,2*Math.PI,!0),this.ctx.fillStyle=re(cB),this.ctx.fill(),this.ctx.restore())),Fs(e)&&e.value.length){switch(c=this.createFontStyle(r),w=c[0],i=c[1],c=this.fontMetrics.getMetrics(w,i).baseline,this.ctx.font=w,this.ctx.fillStyle=re(r.color),this.ctx.textBaseline="alphabetic",this.ctx.textAlign=ds(e.styles.textAlign),Q=es(e),o=0,e.styles.textAlign){case Ot.CENTER:o+=Q.width/2;break;case Ot.RIGHT:o+=Q.width}i=Q.add(o,0,0,-Q.height/2+1),this.ctx.save(),this.path([new bn(Q.left,Q.top),new bn(Q.left+Q.width,Q.top),new bn(Q.left+Q.width,Q.top+Q.height),new bn(Q.left,Q.top+Q.height)]),this.ctx.clip(),this.renderTextWithLetterSpacing(new Sr(e.value,i),r.letterSpacing,c),this.ctx.restore(),this.ctx.textBaseline="alphabetic",this.ctx.textAlign="left"}if(!cr(e.styles.display,2048))return[3,20];if(null===e.styles.listStyleImage)return[3,19];if((c=e.styles.listStyleImage).type!==ye.URL)return[3,18];a=void 0,c=c.url,A.label=15;case 15:return A.trys.push([15,17,,18]),[4,this.context.cache.match(c)];case 16:return a=A.sent(),this.ctx.drawImage(a,e.bounds.left-(a.width+10),e.bounds.top),[3,18];case 17:return A.sent(),this.context.logger.error("Error loading list-style-image "+c),[3,18];case 18:return[3,20];case 19:u.listValue&&e.styles.listStyleType!==Kt.NONE&&(w=this.createFontStyle(r)[0],this.ctx.font=w,this.ctx.fillStyle=re(r.color),this.ctx.textBaseline="middle",this.ctx.textAlign="right",Q=new h(e.bounds.left,e.bounds.top+ae(e.styles.paddingTop,e.bounds.width),e.bounds.width,Ut(r.lineHeight,r.fontSize.number)/2+1),this.renderTextWithLetterSpacing(new Sr(u.listValue,Q),r.letterSpacing,Ut(r.lineHeight,r.fontSize.number)/2+2),this.ctx.textBaseline="bottom",this.ctx.textAlign="left"),A.label=20;case 20:return[2]}})})},Us.prototype.renderStackContent=function(C){return w(this,void 0,void 0,function(){var e,t,r,B,n,s,o,i,a,c,w,Q,u,l,E;return H(this,function(A){switch(A.label){case 0:return[4,this.renderNodeBackgroundAndBorders(C.element)];case 1:A.sent(),e=0,t=C.negativeZIndex,A.label=2;case 2:return e<t.length?(E=t[e],[4,this.renderStack(E)]):[3,5];case 3:A.sent(),A.label=4;case 4:return e++,[3,2];case 5:return[4,this.renderNodeContent(C.element)];case 6:A.sent(),r=0,B=C.nonInlineLevel,A.label=7;case 7:return r<B.length?(E=B[r],[4,this.renderNode(E)]):[3,10];case 8:A.sent(),A.label=9;case 9:return r++,[3,7];case 10:n=0,s=C.nonPositionedFloats,A.label=11;case 11:return n<s.length?(E=s[n],[4,this.renderStack(E)]):[3,14];case 12:A.sent(),A.label=13;case 13:return n++,[3,11];case 14:o=0,i=C.nonPositionedInlineLevel,A.label=15;case 15:return o<i.length?(E=i[o],[4,this.renderStack(E)]):[3,18];case 16:A.sent(),A.label=17;case 17:return o++,[3,15];case 18:a=0,c=C.inlineLevel,A.label=19;case 19:return a<c.length?(E=c[a],[4,this.renderNode(E)]):[3,22];case 20:A.sent(),A.label=21;case 21:return a++,[3,19];case 22:w=0,Q=C.zeroOrAutoZIndexOrTransformedOrOpacity,A.label=23;case 23:return w<Q.length?(E=Q[w],[4,this.renderStack(E)]):[3,26];case 24:A.sent(),A.label=25;case 25:return w++,[3,23];case 26:u=0,l=C.positiveZIndex,A.label=27;case 27:return u<l.length?(E=l[u],[4,this.renderStack(E)]):[3,30];case 28:A.sent(),A.label=29;case 29:return u++,[3,27];case 30:return[2]}})})},Us.prototype.mask=function(A){this.ctx.beginPath(),this.ctx.moveTo(0,0),this.ctx.lineTo(this.canvas.width,0),this.ctx.lineTo(this.canvas.width,this.canvas.height),this.ctx.lineTo(0,this.canvas.height),this.ctx.lineTo(0,0),this.formatPath(A.slice(0).reverse()),this.ctx.closePath()},Us.prototype.path=function(A){this.ctx.beginPath(),this.formatPath(A),this.ctx.closePath()},Us.prototype.formatPath=function(A){var r=this;A.forEach(function(A,e){var t=Gn(A)?A.start:A;0===e?r.ctx.moveTo(t.x,t.y):r.ctx.lineTo(t.x,t.y),Gn(A)&&r.ctx.bezierCurveTo(A.startControl.x,A.startControl.y,A.endControl.x,A.endControl.y,A.end.x,A.end.y)})},Us.prototype.renderRepeat=function(A,e,t,r){this.path(A),this.ctx.fillStyle=e,this.ctx.translate(t,r),this.ctx.fill(),this.ctx.translate(-t,-r)},Us.prototype.resizeImage=function(A,e,t){if(A.width===e&&A.height===t)return A;var r=(null!==(r=this.canvas.ownerDocument)&&void 0!==r?r:document).createElement("canvas");return r.width=Math.max(1,e),r.height=Math.max(1,t),r.getContext("2d").drawImage(A,0,0,A.width,A.height,0,0,e,t),r},Us.prototype.renderBackgroundImage=function(d){return w(this,void 0,void 0,function(){var F,e,h,t,r,B;return H(this,function(A){switch(A.label){case 0:F=d.styles.backgroundImage.length-1,e=function(e){var t,r,B,n,s,o,i,a,c,w,Q,u,l,E,C,U,g;return H(this,function(A){switch(A.label){case 0:if(e.type!==ye.URL)return[3,5];t=void 0,r=e.url,A.label=1;case 1:return A.trys.push([1,3,,4]),[4,h.context.cache.match(r)];case 2:return t=A.sent(),[3,4];case 3:return A.sent(),h.context.logger.error("Error loading background-image "+r),[3,4];case 4:return t&&(B=ts(d,F,[t.width,t.height,t.width/t.height]),o=B[0],Q=B[1],u=B[2],c=B[3],w=B[4],s=h.ctx.createPattern(h.resizeImage(t,c,w),"repeat"),h.renderRepeat(o,s,Q,u)),[3,6];case 5:e.type===ye.LINEAR_GRADIENT?(g=ts(d,F,[null,null,null]),o=g[0],Q=g[1],u=g[2],c=g[3],w=g[4],C=pe(e.angle,c,w),E=C[0],B=C[1],i=C[2],U=C[3],a=C[4],(g=document.createElement("canvas")).width=c,g.height=w,C=g.getContext("2d"),n=C.createLinearGradient(B,U,i,a),fe(e.stops,E).forEach(function(A){return n.addColorStop(A.stop,re(A.color))}),C.fillStyle=n,C.fillRect(0,0,c,w),0<c&&0<w&&(s=h.ctx.createPattern(g,"repeat"),h.renderRepeat(o,s,Q,u))):e.type===ye.RADIAL_GRADIENT&&(U=ts(d,F,[null,null,null]),o=U[0],i=U[1],a=U[2],c=U[3],w=U[4],E=0===e.position.length?[oe]:e.position,Q=ae(E[0],c),u=ae(E[E.length-1],w),C=function(A,e,t,r,B){var n,s,o,i,a=0,c=0;switch(A.size){case ve.CLOSEST_SIDE:A.shape===Me.CIRCLE?a=c=Math.min(Math.abs(e),Math.abs(e-r),Math.abs(t),Math.abs(t-B)):A.shape===Me.ELLIPSE&&(a=Math.min(Math.abs(e),Math.abs(e-r)),c=Math.min(Math.abs(t),Math.abs(t-B)));break;case ve.CLOSEST_CORNER:A.shape===Me.CIRCLE?a=c=Math.min(Ke(e,t),Ke(e,t-B),Ke(e-r,t),Ke(e-r,t-B)):A.shape===Me.ELLIPSE&&(n=Math.min(Math.abs(t),Math.abs(t-B))/Math.min(Math.abs(e),Math.abs(e-r)),o=(s=Le(r,B,e,t,!0))[0],i=s[1],c=n*(a=Ke(o-e,(i-t)/n)));break;case ve.FARTHEST_SIDE:A.shape===Me.CIRCLE?a=c=Math.max(Math.abs(e),Math.abs(e-r),Math.abs(t),Math.abs(t-B)):A.shape===Me.ELLIPSE&&(a=Math.max(Math.abs(e),Math.abs(e-r)),c=Math.max(Math.abs(t),Math.abs(t-B)));break;case ve.FARTHEST_CORNER:A.shape===Me.CIRCLE?a=c=Math.max(Ke(e,t),Ke(e,t-B),Ke(e-r,t),Ke(e-r,t-B)):A.shape===Me.ELLIPSE&&(n=Math.max(Math.abs(t),Math.abs(t-B))/Math.max(Math.abs(e),Math.abs(e-r)),o=(s=Le(r,B,e,t,!1))[0],i=s[1],c=n*(a=Ke(o-e,(i-t)/n)))}return Array.isArray(A.size)&&(a=ae(A.size[0],r),c=2===A.size.length?ae(A.size[1],B):a),[a,c]}(e,Q,u,c,w),g=C[0],U=C[1],0<g&&0<U&&(l=h.ctx.createRadialGradient(i+Q,a+u,0,i+Q,a+u,g),fe(e.stops,2*g).forEach(function(A){return l.addColorStop(A.stop,re(A.color))}),h.path(o),h.ctx.fillStyle=l,g!==U?(E=d.bounds.left+.5*d.bounds.width,C=d.bounds.top+.5*d.bounds.height,g=1/(U=U/g),h.ctx.save(),h.ctx.translate(E,C),h.ctx.transform(1,0,0,U,0,0),h.ctx.translate(-E,-C),h.ctx.fillRect(i,g*(a-C)+C,c,w*g),h.ctx.restore()):h.ctx.fill())),A.label=6;case 6:return F--,[2]}})},h=this,t=0,r=d.styles.backgroundImage.slice(0).reverse(),A.label=1;case 1:return t<r.length?(B=r[t],[5,e(B)]):[3,4];case 2:A.sent(),A.label=3;case 3:return t++,[3,1];case 4:return[2]}})})},Us.prototype.renderSolidBorder=function(e,t,r){return w(this,void 0,void 0,function(){return H(this,function(A){return this.path($n(r,t)),this.ctx.fillStyle=re(e),this.ctx.fill(),[2]})})},Us.prototype.renderDoubleBorder=function(t,r,B,n){return w(this,void 0,void 0,function(){var e;return H(this,function(A){switch(A.label){case 0:return r<3?[4,this.renderSolidBorder(t,B,n)]:[3,2];case 1:return A.sent(),[2];case 2:return e=function(A,e){switch(e){case 0:return is(A.topLeftBorderBox,A.topLeftBorderDoubleOuterBox,A.topRightBorderBox,A.topRightBorderDoubleOuterBox);case 1:return is(A.topRightBorderBox,A.topRightBorderDoubleOuterBox,A.bottomRightBorderBox,A.bottomRightBorderDoubleOuterBox);case 2:return is(A.bottomRightBorderBox,A.bottomRightBorderDoubleOuterBox,A.bottomLeftBorderBox,A.bottomLeftBorderDoubleOuterBox);default:return is(A.bottomLeftBorderBox,A.bottomLeftBorderDoubleOuterBox,A.topLeftBorderBox,A.topLeftBorderDoubleOuterBox)}}(n,B),this.path(e),this.ctx.fillStyle=re(t),this.ctx.fill(),e=function(A,e){switch(e){case 0:return is(A.topLeftBorderDoubleInnerBox,A.topLeftPaddingBox,A.topRightBorderDoubleInnerBox,A.topRightPaddingBox);case 1:return is(A.topRightBorderDoubleInnerBox,A.topRightPaddingBox,A.bottomRightBorderDoubleInnerBox,A.bottomRightPaddingBox);case 2:return is(A.bottomRightBorderDoubleInnerBox,A.bottomRightPaddingBox,A.bottomLeftBorderDoubleInnerBox,A.bottomLeftPaddingBox);default:return is(A.bottomLeftBorderDoubleInnerBox,A.bottomLeftPaddingBox,A.topLeftBorderDoubleInnerBox,A.topLeftPaddingBox)}}(n,B),this.path(e),this.ctx.fill(),[2]}})})},Us.prototype.renderNodeBackgroundAndBorders=function(c){return w(this,void 0,void 0,function(){var e,t,r,B,n,s,o,i,a=this;return H(this,function(A){switch(A.label){case 0:return(this.applyEffects(c.effects,2),e=c.container.styles,t=!te(e.backgroundColor)||e.backgroundImage.length,r=[{style:e.borderTopStyle,color:e.borderTopColor,width:e.borderTopWidth},{style:e.borderRightStyle,color:e.borderRightColor,width:e.borderRightWidth},{style:e.borderBottomStyle,color:e.borderBottomColor,width:e.borderBottomWidth},{style:e.borderLeftStyle,color:e.borderLeftColor,width:e.borderLeftWidth}],B=hs(cs(e.backgroundClip,0),c.curves),t||e.boxShadow.length)?(this.ctx.save(),this.path(B),this.ctx.clip(),te(e.backgroundColor)||(this.ctx.fillStyle=re(e.backgroundColor),this.ctx.fill()),[4,this.renderBackgroundImage(c.container)]):[3,2];case 1:A.sent(),this.ctx.restore(),e.boxShadow.slice(0).reverse().forEach(function(A){a.ctx.save();var t,r,B,n,e=Xn(c.curves),s=A.inset?0:1e4,o=(t=-s+(A.inset?1:-1)*A.spread.number,r=(A.inset?1:-1)*A.spread.number,B=A.spread.number*(A.inset?-2:2),n=A.spread.number*(A.inset?-2:2),e.map(function(A,e){switch(e){case 0:return A.add(t,r);case 1:return A.add(t+B,r);case 2:return A.add(t+B,r+n);case 3:return A.add(t,r+n)}return A}));A.inset?(a.path(e),a.ctx.clip(),a.mask(o)):(a.mask(e),a.ctx.clip(),a.path(o)),a.ctx.shadowOffsetX=A.offsetX.number+s,a.ctx.shadowOffsetY=A.offsetY.number,a.ctx.shadowColor=re(A.color),a.ctx.shadowBlur=A.blur.number,a.ctx.fillStyle=A.inset?re(A.color):"rgba(0,0,0,1)",a.ctx.fill(),a.ctx.restore()}),A.label=2;case 2:s=n=0,o=r,A.label=3;case 3:return s<o.length?(i=o[s]).style!==We.NONE&&!te(i.color)&&0<i.width?i.style!==We.DASHED?[3,5]:[4,this.renderDashedDottedBorder(i.color,i.width,n,c.curves,We.DASHED)]:[3,11]:[3,13];case 4:return A.sent(),[3,11];case 5:return i.style!==We.DOTTED?[3,7]:[4,this.renderDashedDottedBorder(i.color,i.width,n,c.curves,We.DOTTED)];case 6:return A.sent(),[3,11];case 7:return i.style!==We.DOUBLE?[3,9]:[4,this.renderDoubleBorder(i.color,i.width,n,c.curves)];case 8:return A.sent(),[3,11];case 9:return[4,this.renderSolidBorder(i.color,n,c.curves)];case 10:A.sent(),A.label=11;case 11:n++,A.label=12;case 12:return s++,[3,3];case 13:return[2]}})})},Us.prototype.renderDashedDottedBorder=function(Q,u,l,E,C){return w(this,void 0,void 0,function(){var e,t,r,B,n,s,o,i,a,c,w;return H(this,function(A){return this.ctx.save(),a=function(A,e){switch(e){case 0:return os(A.topLeftBorderStroke,A.topRightBorderStroke);case 1:return os(A.topRightBorderStroke,A.bottomRightBorderStroke);case 2:return os(A.bottomRightBorderStroke,A.bottomLeftBorderStroke);default:return os(A.bottomLeftBorderStroke,A.topLeftBorderStroke)}}(E,l),e=$n(E,l),C===We.DASHED&&(this.path(e),this.ctx.clip()),s=Gn(e[0])?(t=e[0].start.x,e[0].start.y):(t=e[0].x,e[0].y),o=Gn(e[1])?(r=e[1].end.x,e[1].end.y):(r=e[1].x,e[1].y),B=0===l||2===l?Math.abs(t-r):Math.abs(s-o),this.ctx.beginPath(),C===We.DOTTED?this.formatPath(a):this.formatPath(e.slice(0,2)),n=u<3?3*u:2*u,s=u<3?2*u:u,C===We.DOTTED&&(s=n=u),o=!0,B<=2*n?o=!1:B<=2*n+s?(n*=i=B/(2*n+s),s*=i):(a=Math.floor((B+s)/(n+s)),i=(B-a*n)/(a-1),s=(a=(B-(a+1)*n)/a)<=0||Math.abs(s-i)<Math.abs(s-a)?i:a),o&&(C===We.DOTTED?this.ctx.setLineDash([0,n+s]):this.ctx.setLineDash([n,s])),C===We.DOTTED?(this.ctx.lineCap="round",this.ctx.lineWidth=u):this.ctx.lineWidth=2*u+1.1,this.ctx.strokeStyle=re(Q),this.ctx.stroke(),this.ctx.setLineDash([]),C===We.DASHED&&(Gn(e[0])&&(c=e[3],w=e[0],this.ctx.beginPath(),this.formatPath([new bn(c.end.x,c.end.y),new bn(w.start.x,w.start.y)]),this.ctx.stroke()),Gn(e[1])&&(c=e[1],w=e[2],this.ctx.beginPath(),this.formatPath([new bn(c.end.x,c.end.y),new bn(w.start.x,w.start.y)]),this.ctx.stroke())),this.ctx.restore(),[2]})})},Us.prototype.render=function(B){return w(this,void 0,void 0,function(){return H(this,function(A){switch(A.label){case 0:return this.options.backgroundColor&&(this.ctx.fillStyle=re(this.options.backgroundColor),this.ctx.fillRect(this.options.x,this.options.y,this.options.width,this.options.height)),t=new jn(e=B,[]),r=new qn(t),ns(t,r,r,e=[]),ss(t.container,e),[4,this.renderStack(r)];case 1:return A.sent(),this.applyEffects([],2),[2,this.canvas]}var e,t,r})})},Us);function Us(A,e){A=Es.call(this,A,e)||this;return A._activeEffects=[],A.canvas=e.canvas||document.createElement("canvas"),A.ctx=A.canvas.getContext("2d"),e.canvas||(A.canvas.width=Math.floor(e.width*e.scale),A.canvas.height=Math.floor(e.height*e.scale),A.canvas.style.width=e.width+"px",A.canvas.style.height=e.height+"px"),A.fontMetrics=new us(document),A.ctx.scale(A.options.scale,A.options.scale),A.ctx.translate(-e.x,-e.y),A.ctx.textBaseline="bottom",A._activeEffects=[],A.context.logger.debug("Canvas renderer initialized ("+e.width+"x"+e.height+") with scale "+e.scale),A}var gs,Fs=function(A){return A instanceof UB||(A instanceof lB||A instanceof wB&&A.type!==iB&&A.type!==oB)},hs=function(A,e){switch(A){case ge.BORDER_BOX:return Xn(e);case ge.CONTENT_BOX:return[e.topLeftContentBox,e.topRightContentBox,e.bottomRightContentBox,e.bottomLeftContentBox];default:ge.PADDING_BOX;return kn(e)}},ds=function(A){switch(A){case Ot.CENTER:return"center";case Ot.RIGHT:return"right";default:Ot.LEFT;return"left"}},Hs=(A(fs,gs=be),fs.prototype.render=function(t){return w(this,void 0,void 0,function(){var e;return H(this,function(A){switch(A.label){case 0:return e=mr(this.options.width*this.options.scale,this.options.height*this.options.scale,this.options.scale,this.options.scale,t),[4,ps(e)];case 1:return e=A.sent(),this.options.backgroundColor&&(this.ctx.fillStyle=re(this.options.backgroundColor),this.ctx.fillRect(0,0,this.options.width*this.options.scale,this.options.height*this.options.scale)),this.ctx.drawImage(e,-this.options.x*this.options.scale,-this.options.y*this.options.scale),[2,this.canvas]}})})},fs);function fs(A,e){A=gs.call(this,A,e)||this;return A.canvas=e.canvas||document.createElement("canvas"),A.ctx=A.canvas.getContext("2d"),A.options=e,A.canvas.width=Math.floor(e.width*e.scale),A.canvas.height=Math.floor(e.height*e.scale),A.canvas.style.width=e.width+"px",A.canvas.style.height=e.height+"px",A.ctx.scale(A.options.scale,A.options.scale),A.ctx.translate(-e.x,-e.y),A.context.logger.debug("EXPERIMENTAL ForeignObject renderer initialized ("+e.width+"x"+e.height+" at "+e.x+","+e.y+") with scale "+e.scale),A}var ps=function(r){return new Promise(function(A,e){var t=new Image;t.onload=function(){A(t)},t.onerror=e,t.src="data:image/svg+xml;charset=utf-8,"+encodeURIComponent((new XMLSerializer).serializeToString(r))})},Ks=(Ls.prototype.debug=function(){for(var A=[],e=0;e<arguments.length;e++)A[e]=arguments[e];this.enabled&&("undefined"!=typeof window&&window.console&&"function"==typeof console.debug?console.debug.apply(console,t([this.id,this.getTime()+"ms"],A)):this.info.apply(this,A))},Ls.prototype.getTime=function(){return Date.now()-this.start},Ls.prototype.info=function(){for(var A=[],e=0;e<arguments.length;e++)A[e]=arguments[e];this.enabled&&"undefined"!=typeof window&&window.console&&"function"==typeof console.info&&console.info.apply(console,t([this.id,this.getTime()+"ms"],A))},Ls.prototype.warn=function(){for(var A=[],e=0;e<arguments.length;e++)A[e]=arguments[e];this.enabled&&("undefined"!=typeof window&&window.console&&"function"==typeof console.warn?console.warn.apply(console,t([this.id,this.getTime()+"ms"],A)):this.info.apply(this,A))},Ls.prototype.error=function(){for(var A=[],e=0;e<arguments.length;e++)A[e]=arguments[e];this.enabled&&("undefined"!=typeof window&&window.console&&"function"==typeof console.error?console.error.apply(console,t([this.id,this.getTime()+"ms"],A)):this.info.apply(this,A))},Ls.instances={},Ls);function Ls(A){var e=A.id,A=A.enabled;this.id=e,this.enabled=A,this.start=Date.now()}var ys=(Is.instanceCount=1,Is);function Is(A,e){this.windowBounds=e,this.instanceName="#"+Is.instanceCount++,this.logger=new Ks({id:this.instanceName,enabled:A.logging}),this.cache=null!==(e=A.cache)&&void 0!==e?e:new pn(this,A)}"undefined"!=typeof window&&Hn.setContext(window);var Ts=function(U,g){return w(void 0,void 0,void 0,function(){var e,t,r,B,n,s,o,i,a,c,w,Q,u,l,E,C;return H(this,function(A){switch(A.label){case 0:if(!U||"object"!=typeof U)return[2,Promise.reject("Invalid element provided as first argument")];if(!(e=U.ownerDocument))throw new Error("Element is not attached to a Document");if(!(t=e.defaultView))throw new Error("Document is not attached to a Window");return l={allowTaint:null!==(c=g.allowTaint)&&void 0!==c&&c,imageTimeout:null!==(w=g.imageTimeout)&&void 0!==w?w:15e3,proxy:g.proxy,useCORS:null!==(Q=g.useCORS)&&void 0!==Q&&Q},c=F({logging:null===(u=g.logging)||void 0===u||u,cache:g.cache},l),w={windowWidth:null!==(w=g.windowWidth)&&void 0!==w?w:t.innerWidth,windowHeight:null!==(Q=g.windowHeight)&&void 0!==Q?Q:t.innerHeight,scrollX:null!==(u=g.scrollX)&&void 0!==u?u:t.pageXOffset,scrollY:null!==(l=g.scrollY)&&void 0!==l?l:t.pageYOffset},Q=new h(w.scrollX,w.scrollY,w.windowWidth,w.windowHeight),u=new ys(c,Q),w=null!==(l=g.foreignObjectRendering)&&void 0!==l&&l,c={onclone:g.onclone,ignoreElements:g.ignoreElements,inlineImages:w,copyStyles:w},u.logger.debug("Starting document clone with size "+Q.width+"x"+Q.height+" scrolled to "+-Q.left+","+-Q.top),l=new nn(u,U,c),(c=l.clonedReferenceElement)?[4,l.toIFrame(e,Q)]:[2,Promise.reject("Unable to find element in cloned iframe")];case 1:return(r=A.sent(),E=MB(c)||"HTML"===c.tagName?function(A){var e=A.body,t=A.documentElement;if(!e||!t)throw new Error("Unable to get document size");A=Math.max(Math.max(e.scrollWidth,t.scrollWidth),Math.max(e.offsetWidth,t.offsetWidth),Math.max(e.clientWidth,t.clientWidth)),t=Math.max(Math.max(e.scrollHeight,t.scrollHeight),Math.max(e.offsetHeight,t.offsetHeight),Math.max(e.clientHeight,t.clientHeight));return new h(0,0,A,t)}(c.ownerDocument):d(u,c),B=E.width,n=E.height,s=E.left,o=E.top,i=ms(u,c,g.backgroundColor),E={canvas:g.canvas,backgroundColor:i,scale:null!==(E=null!==(E=g.scale)&&void 0!==E?E:t.devicePixelRatio)&&void 0!==E?E:1,x:(null!==(E=g.x)&&void 0!==E?E:0)+s,y:(null!==(E=g.y)&&void 0!==E?E:0)+o,width:null!==(E=g.width)&&void 0!==E?E:Math.ceil(B),height:null!==(E=g.height)&&void 0!==E?E:Math.ceil(n)},w)?(u.logger.debug("Document cloned, using foreign object rendering"),[4,new Hs(u,E).render(c)]):[3,3];case 2:return a=A.sent(),[3,5];case 3:return u.logger.debug("Document cloned, element located at "+s+","+o+" with size "+B+"x"+n+" using computed rendering"),u.logger.debug("Starting DOM parsing"),C=LB(u,c),i===C.styles.backgroundColor&&(C.styles.backgroundColor=de.TRANSPARENT),u.logger.debug("Starting renderer for element at "+E.x+","+E.y+" with size "+E.width+"x"+E.height),[4,new Cs(u,E).render(C)];case 4:a=A.sent(),A.label=5;case 5:return null!==(C=g.removeContainer)&&void 0!==C&&!C||nn.destroy(r)||u.logger.error("Cannot detach cloned iframe as it is not in the DOM anymore"),u.logger.debug("Finished rendering"),[2,a]}})})},ms=function(A,e,t){var r=e.ownerDocument,B=r.documentElement?Ce(A,getComputedStyle(r.documentElement).backgroundColor):de.TRANSPARENT,n=r.body?Ce(A,getComputedStyle(r.body).backgroundColor):de.TRANSPARENT,t="string"==typeof t?Ce(A,t):null===t?de.TRANSPARENT:4294967295;return e===r.documentElement?te(B)?te(n)?t:n:B:t};return function(A,e){return Ts(A,e=void 0===e?{}:e)}});