.theme--light.v-application code {
  background-color: unset;
  font-family: <PERSON><PERSON>, sans-serif;
}

pre.hljs {
  padding: 13px;
  border-radius: 5px;
  background-color: rgb(0 255 9 / 3%) !important;
  position: relative;
}

think {
  padding: 3px;
  border-radius: 5px;
  color: #001e67;
  background-color: #fafafa;
  display: block;
}

think p {
  margin-bottom: 3px !important;
}

code {
  /* color: yellow!important; */
  padding: 0 !important;
  white-space: pre-wrap;
  word-wrap: break-word;
}

[v-cloak] {
  display: none;
}

button.runButton {
  display: block;
  background-color: #8ac5ff;
  top: 0;
  right: 0;
  position: absolute;
  padding: 3px;
  border: #2196f3 1px solid;
  border-radius: 3px;
}

.md td,
.md th {
  border: 1px solid #8ac5ff;
  padding: 3px;
}

.md table {
  border-spacing: 0;
  margin: 4px;
  border: 1px solid #8ac5ff;
}

/* .md p {
  margin-bottom: 16px;
  white-space: pre-wrap;
} */
toolcall,
toolresponse {
  padding: 11px;
  border-radius: 5px;
  color: #4caf50;
  background-color: #8ac5ff24;
  display: block;
  width: 100%;
  white-space: nowrap;
}

toolcall p {
  margin-bottom: 0 !important;
}

toolcall name,
toolcall function {
  display: block;
}

toolcall name:before,
toolcall function:before {
  content: "工具： ";
  color: black
}

toolcall arguments {
  display: block;
  white-space: pre-line;
}


toolcall arguments:before {
  color: black
}

toolresponse:before {
  content: "结果： ";
  color: black
}

header .v-btn {
  margin: 3px;
}


.data-table-content-counter {
  text-align: center;
  color: gray;
  margin-bottom: 0 !important;
}

.data-table-content-counter::before {
  content: "(";
}

.data-table-content-counter::after {
  content: ")";
}


.v-data-table__wrapper {
  overflow-x: hidden;
}


/* 渐变动画 */
.slide-fade-enter-active {
  transition: all 0.5s ease;
}

.slide-fade-leave-active {
  transition: all .3s cubic-bezier(1.0, 0.5, 0.8, 1.0);
}

.slide-fade-enter,
.slide-fade-leave-to {
  transform: translateX(10px);
  opacity: 0;
}

.delete {
  background-color: #ff0000;
}

.insert {
  background-color: #00ab00
}

.textarea-container textarea {
  width: -webkit-fill-available;
  height: 10em;
}

/* textarea后的白色渐变 */
.textarea-container {
  position: relative;
  display: inline-block;

  width: -webkit-fill-available;
}

.textarea-container:after {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 20px;
  background: linear-gradient(to top, rgb(255, 255, 255), transparent);
  pointer-events: none;
  z-index: 1;
}

/* input后的白色渐变 */
.input-container {
  position: relative;
  display: inline-block;
}

.input-container:after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 15px;
  background: linear-gradient(to left, rgb(255, 255, 255), transparent);
  pointer-events: none;
  z-index: 1;
}