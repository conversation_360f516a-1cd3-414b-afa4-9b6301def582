<!DOCTYPE html>
<html>

<head>
  <title>对话定制</title>
  <meta charset="utf-8">
  <meta content="yes" name="mobile-web-app-capable" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=650,  user-scalable=no">
  <link rel="shortcut icon" href="#" />
  <link href="static/mdi_font/css/materialdesignicons.min.css" rel="stylesheet">
  <link href="static/3/vuetify.min.css" rel="stylesheet">
  <script src="static/3/vue.global.prod.js"></script>
  <script src="static/3/vuetify.min.js"></script>

  <style>
    iframe {
      border: 0;
      width: 50%;
      height: 100%;
      display: inline-block;
      position: absolute;
    }

    #app {
      height: 100%;
      width: 50%;
      display: inline-block;
    }
  </style>
</head>

<body>
  <div id="app" v-cloak>
    <v-app class="ma-5">
      <div>
        <v-text-field autofocus v-model="名称" label="名称" density="compact" variant="underlined" hide-details
          @change="f生成"></v-text-field>
        <v-textarea class="mt-2" v-model="系统提示词" label="系统提示词" rows="2" auto-grow variant="underlined" @change="f生成"
          density="compact" hide-details="auto" clearable></v-textarea>

        <v-btn @click="f生成()" class="ma-5">生成对话</v-btn>
        <v-btn @click="在独立窗口打开()" class="ma-5">在独立窗口打开</v-btn>
        <v-btn @click="保存至首页()" class="ma-5">保存至首页</v-btn>
      </div>
      <v-snackbar v-model="b显示提示文本" :timeout="1300" style="white-space: pre-line">{{s提示文本}}</v-snackbar>
    </v-app>

  </div>
  <iframe src="" class=""></iframe>
  <script type="text/javascript">
    const vuetify = Vuetify.createVuetify({
      defaults: {
        VBtn: {
          color: 'primary',
          variant: 'tonal'
        }
      }
    })
    init = async () => {
      let data = {
        名称: "",
        系统提示词: "",
        show_dialog: false,
        dialog_input: "",
      }
      function throttle(func, delay) {
        let timer = null;
        let lastArgs = null;
        return function (...args) {
          lastArgs = args;
          if (timer) {
            return;
          }
          timer = setTimeout(() => {
            func.apply(this, lastArgs);
            timer = null;
          }, delay);
        }
      }
      function mount() {
        iframe = document.querySelector('iframe')
        iframe.src = ''
        setTimeout(() => iframe.src = make_src(), 20)
      }
      app = await Vue.createApp({
        data() {
          return data
        },
        methods: {
          f生成: throttle(mount, 100),
          在独立窗口打开() {
            window.open(make_src(), "_blank")
          },
          保存至首页() {
            pages = JSON.parse(localStorage["rzyzzgjj_zdrw"] || "[]")
            pages.push({ src: make_src(), title: app.名称, icon: 'chat-processing' })
            localStorage["rzyzzgjj_zdrw"] = JSON.stringify(pages)
            alert('完成！请刷新首页查看')
          },

        },
        computed: {
        },
        watch: {
        }
      }).use(vuetify).mount('#app')
    }
    init()

    _alert = alert
    // alert = (text) => {
    //   app.s提示文本 = text; //.replace(/\n/g,"<br>")
    //   app.b显示提示文本 = true;
    // }
    function Uint8ArrayToString(fileData, encoding = 'utf-8') {
      return new TextDecoder(encoding).decode(fileData);
    }
    make_src = () => {
      src = './chat.html#' + btoa(encodeURIComponent(toEval.toString().replace(/^.+\n/, '').replace(/\n.+$/, '')
        .replaceAll("CHAT_NAME", app.名称)
        .replaceAll("SYSTEM_PROMPT", app.系统提示词)
      ))
      return src
    }
    function toEval() {
      chat_name = 'CHAT_NAME';
      name_space = 'CHAT_NAME';
      get_system_prompt = function () {
        return `SYSTEM_PROMPT`
      }
    }
  </script>
</body>

</html>