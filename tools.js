tools = [
    {
        "name": "get_articles_by_name",
        "display_name": "读取资料",
        "group": "资料管理",
        "distribution": `从知识库中,选择资料阅读。注意：应先使用list_articles获取资料名`,
        "arguments": "<names>xxx\nyyy（如果需要同时读取多个资料，不同资料名使用换行分隔）</names>"
    },
    {
        "name": "edit_article_by_name",
        "display_name": "编辑资料",
        "group": "资料管理",
        "distribution": `向知识库保存资料。注意：只在用户明确要求保存时才使用本工具`,
        "arguments": "<article_name>资料名</article_name><content>xxx</content>}"
    },
    {
        "name": "list_articles",
        "display_name": "列出资料",
        "group": "资料管理",
        "distribution": `列出知识库所有资料名`,
        "arguments": ""
    },
    {
        "name": "get_time",
        "display_name": "获取时间",
        "distribution": `获取时间`,
        "arguments": ""
    },
    {
        "name": "select_next_step",
        "display_name": "选择下步操作",
        "distribution": `如果需要提供给用户不同选择，使用本工具`,
        "arguments": "<steps>选项a\n选项b（使用换行分隔）</steps>"
    },
    {
        "name": "math_calc",
        "display_name": "数值计算",
        "distribution": `调用math.js进行数值计算`,
        "arguments": "<evaluate>sin(45deg)+1</evaluate>"
    },
]
argument_name_dict = {
    names: "资料",
    article_name: "资料名",
    content: "内容",
    evaluate: "表达式",
}
get_articles_by_name = (arguments) => {
    let names = arguments.names.split('\n').filter(i => i != "")
    let result = {}
    make_article = (article) => {
        return '# ' + article.title + '\n\n' + article.content
        // return make_prompt('《' + article.title + '》', article.content) + `<|im_end|>\n`
    }
    let article = app.articles.filter(i => (names.findIndex(s => s == i.title) > -1))
    result.sources = article
    if (article.length > 0) {

        result.content = article.map(make_article).join(`\n\n`)
        result.text = '读取成功'
    }
    else {
        result.content = '读取失败,没有找到'
        result.text = '读取失败,没有找到'
    }
    return result
}
edit_article_by_name = async (arguments) => {
    let article_name = arguments("article_name")
    let content = arguments("content")
    let result = {}
    let index = app.articles.findIndex(i => i.title == article_name)
    let source = {
        content: content,
        title: article_name,
        use: false
    }
    if (index == -1) {
        if (await confirm(`是否新建资料[${article_name}]?\n\n${content}`)) {
            app.articles.unshift(source)
            result.sources = []
            result.content = '新建资料成功'
            result.text = '新建资料成功'
        } else {
            result.sources = []
            result.content = '新建资料失败,用户拒绝'
            result.text = '已拒绝新建资料'
            return result
        }
    } else {

        if (await confirm(`是否修改资料[${article_name}]?\n\n${content}`)) {
            app.articles[index].content = content
            result.sources = []
            result.content = '修改资料成功'
            result.text = '修改资料成功'
        } else {
            result.sources = []
            result.content = '修改资料失败,用户拒绝'
            result.text = '已拒绝修改资料'

            return result
        }
    }
    let articles = app.articles
    await (articlesStorage["main"] = JSON.stringify(articles))
    on_articles_change_lock = true
    on_articles_change.postMessage('1');
    setTimeout(() => { on_articles_change_lock = false }, 500)
    return result
}
list_articles = (arguments) => {
    let result = {}
    result.sources = []
    if (app.articlesNames.length == 0) {

        result.content = '知识库为空'
        result.text = '知识库为空'
    } else {

        result.content = JSON.stringify(app.articlesNames)
        result.text = '知识库存在以下资料：\n```\n' + app.articlesNames.join("\n") + "\n```"
    }
    return result
}

get_time = (arguments) => {
    let result = {}
    result.sources = []
    result.content = new Date().toLocaleString()
    result.text = '当前时间：' + result.content
    return result
}
math_calc = (arguments) => {
    let evaluate = arguments("evaluate")
    let result = {}
    result.sources = []
    result.content = math.evaluate(evaluate);
    result.text = '计算结果：' + result.content
    return result
}
const fileSystemTools = [
    {
        "name": "list_files",
        "display_name": "列出文件",
        "group": "文件管理",
        "distribution": "列出当前工作目录中的所有文件名",
        "arguments": ""
    },
    {
        "name": "delete_file",
        "display_name": "删除文件",
        "group": "文件管理",
        "distribution": "从当前工作目录中删除指定文件。注意：应先使用list_files获取文件名",
        "arguments": "<filename>要删除的文件名</filename>"
    },
    {
        "name": "modify_file",
        "display_name": "编辑文件",
        "group": "文件管理",
        "distribution": "向工作目录保存文件内容。注意：只在用户明确要求保存时才使用本工具",
        "arguments": "<filename>文件名</filename><content>文件内容</content>"
    },
    {
        "name": "get_file_content",
        "display_name": "读取文件",
        "group": "文件管理",
        "distribution": "从工作目录中读取指定文件的内容",
        "arguments": "<filename>要读取的文件名</filename>"
    }
];
argument_name_dict['filename']='文件名'
tools = [...tools, ...fileSystemTools]

let directoryHandle = null;

async function initFileSystem() {
    if (directoryHandle) {
        return true
    }
    let success = false
    while (!success)
        try {
            directoryHandle = await window.showDirectoryPicker({
                id: 'workspace',
                mode: 'readwrite'
            });
            success = true
        } catch (err) {
            success = false
            if (!await confirm(`初始化文件系统失败，是否重试？`))
                break
        }
    return success
}


async function list_files() {
    if (! await initFileSystem()) {
        return {
            sources: [],
            content: "初始化文件系统失败",
            text: "初始化文件系统失败"
        };
    }

    try {
        const fileNames = [];
        for await (const entry of directoryHandle.values()) {
            if (entry.kind === 'file') {
                fileNames.push(entry.name);
            }
        }

        return {
            sources: fileNames,
            content: JSON.stringify(fileNames),
            text: fileNames.length > 0
                ? `目录包含以下文件：\n\`\`\`\n${fileNames.join("\n")}\n\`\`\``
                : "目录为空"
        };
    } catch (err) {
        return {
            sources: [],
            content: `列出文件失败: ${err.message}`,
            text: `列出文件失败: ${err.message}`
        };
    }
}

async function delete_file(arguments) {
    const filename = arguments("filename");

    if (! await initFileSystem()) {
        return {
            sources: [],
            content: "初始化文件系统失败",
            text: "初始化文件系统失败"
        };
    }

    if (!await confirm(`是否删除文件: ${filename}`)) {
        result.sources = []
        result.content = '操作失败,用户拒绝'
        result.text = '已拒绝'
        return result
    }
    try {
        await directoryHandle.removeEntry(filename);
        return {
            sources: [],
            content: `已删除文件: ${filename}`,
            text: `已成功删除文件: ${filename}`
        };
    } catch (err) {
        return {
            sources: [],
            content: `删除文件失败: ${err.message}`,
            text: `删除文件 ${filename} 失败: ${err.message}`
        };
    }
}

async function modify_file(arguments) {
    const filename = arguments("filename");
    const content = arguments("content");

    if (! await initFileSystem()) {
        return {
            sources: [],
            content: "初始化文件系统失败",
            text: "初始化文件系统失败"
        };
    }

    if (!await confirm(`是否修改文件: ${filename}`)) {
        result.sources = []
        result.content = '操作失败,用户拒绝'
        result.text = '已拒绝'
        return result
    }
    try {
        const fileHandle = await directoryHandle.getFileHandle(filename, { create: true });
        const writable = await fileHandle.createWritable();
        await writable.write(content);
        await writable.close();

        return {
            sources: [],
            content: `已保存文件: ${filename}`,
            text: `已成功保存文件: ${filename}`
        };
    } catch (err) {
        return {
            sources: [],
            content: `保存文件失败: ${err.message}`,
            text: `保存文件 ${filename} 失败: ${err.message}`
        };
    }
}

async function get_file_content(arguments) {
    const filename = arguments("filename");

    if (! await initFileSystem()) {
        return {
            sources: [],
            content: "初始化文件系统失败",
            text: "初始化文件系统失败"
        };
    }
    try {
        const fileHandle = await directoryHandle.getFileHandle(filename);
        const file = await fileHandle.getFile();
        const content = await file.text();

        return {
            sources: [],
            content: content,
            text: `读取成功`
        };
    } catch (err) {
        return {
            sources: [],
            content: `读取文件失败: ${err.message}`,
            text: `读取文件 ${filename} 失败: ${err.message}`
        };
    }
}
