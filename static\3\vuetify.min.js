/*!
* Vuetify v3.8.8
* Forged by <PERSON>
* Released under the MIT License.
*/
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("vue")):"function"==typeof define&&define.amd?define(["exports","vue"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).Vuetify={},e.Vue)}(this,(function(e,t){"use strict"
const a="undefined"!=typeof window,l=a&&"IntersectionObserver"in window,o=a&&("ontouchstart"in window||window.navigator.maxTouchPoints>0),n=a&&"EyeDropper"in window
function r(e,t,a){(function(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")})(e,t),t.set(e,a)}function i(e,t,a){return e.set(u(e,t),a),a}function s(e,t){return e.get(u(e,t))}function u(e,t,a){if("function"==typeof e?e===t:e.has(t))return arguments.length<3?t:a
throw new TypeError("Private element is not present on this object")}function c(e,t,a){const l=t.length-1
if(l<0)return void 0===e?a:e
for(let o=0;o<l;o++){if(null==e)return a
e=e[t[o]]}return null==e||void 0===e[t[l]]?a:e[t[l]]}function d(e,t){if(e===t)return!0
if(e instanceof Date&&t instanceof Date&&e.getTime()!==t.getTime())return!1
if(e!==Object(e)||t!==Object(t))return!1
const a=Object.keys(e)
return a.length===Object.keys(t).length&&a.every((a=>d(e[a],t[a])))}function v(e,t,a){return null!=e&&t&&"string"==typeof t?void 0!==e[t]?e[t]:c(e,(t=(t=t.replace(/\[(\w+)\]/g,".$1")).replace(/^\./,"")).split("."),a):a}function p(e,t,a){if(!0===t)return void 0===e?a:e
if(null==t||"boolean"==typeof t)return a
if(e!==Object(e)){if("function"!=typeof t)return a
const l=t(e,a)
return void 0===l?a:l}if("string"==typeof t)return v(e,t,a)
if(Array.isArray(t))return c(e,t,a)
if("function"!=typeof t)return a
const l=t(e,a)
return void 0===l?a:l}function m(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0
return Array.from({length:e},((e,a)=>t+a))}function f(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"px"
if(null==e||""===e)return
const a=Number(e)
return isNaN(a)?String(e):isFinite(a)?`${a}${t}`:void 0}function g(e){return null!==e&&"object"==typeof e&&!Array.isArray(e)}function h(e){let t
return null!==e&&"object"==typeof e&&((t=Object.getPrototypeOf(e))===Object.prototype||null===t)}function y(e){if(e&&"$el"in e){const t=e.$el
return t?.nodeType===Node.TEXT_NODE?t.nextElementSibling:t}return e}const b=Object.freeze({enter:13,tab:9,delete:46,esc:27,space:32,up:38,down:40,left:37,right:39,end:35,home:36,del:46,backspace:8,insert:45,pageup:33,pagedown:34,shift:16}),V=Object.freeze({enter:"Enter",tab:"Tab",delete:"Delete",esc:"Escape",space:"Space",up:"ArrowUp",down:"ArrowDown",left:"ArrowLeft",right:"ArrowRight",end:"End",home:"Home",del:"Delete",backspace:"Backspace",insert:"Insert",pageup:"PageUp",pagedown:"PageDown",shift:"Shift"})
function w(e){return Object.keys(e)}function S(e,t){return t.every((t=>e.hasOwnProperty(t)))}function k(e,t){const a={}
for(const l of t)Object.prototype.hasOwnProperty.call(e,l)&&(a[l]=e[l])
return a}function x(e,t,a){const l=Object.create(null),o=Object.create(null)
for(const a in e)t.some((e=>e instanceof RegExp?e.test(a):e===a))?l[a]=e[a]:o[a]=e[a]
return[l,o]}function C(e,t){const a={...e}
return t.forEach((e=>delete a[e])),a}const N=/^on[^a-z]/,E=e=>N.test(e),I=["onAfterscriptexecute","onAnimationcancel","onAnimationend","onAnimationiteration","onAnimationstart","onAuxclick","onBeforeinput","onBeforescriptexecute","onChange","onClick","onCompositionend","onCompositionstart","onCompositionupdate","onContextmenu","onCopy","onCut","onDblclick","onFocusin","onFocusout","onFullscreenchange","onFullscreenerror","onGesturechange","onGestureend","onGesturestart","onGotpointercapture","onInput","onKeydown","onKeypress","onKeyup","onLostpointercapture","onMousedown","onMousemove","onMouseout","onMouseover","onMouseup","onMousewheel","onPaste","onPointercancel","onPointerdown","onPointerenter","onPointerleave","onPointermove","onPointerout","onPointerover","onPointerup","onReset","onSelect","onSubmit","onTouchcancel","onTouchend","onTouchmove","onTouchstart","onTransitioncancel","onTransitionend","onTransitionrun","onTransitionstart","onWheel"],_=["ArrowUp","ArrowDown","ArrowRight","ArrowLeft","Enter","Escape","Tab"," "]
function P(e){const[t,a]=x(e,[N]),l=C(t,I),[o,n]=x(a,["class","style","id",/^data-/])
return Object.assign(o,t),Object.assign(n,l),[o,n]}function B(e){return null==e?[]:Array.isArray(e)?e:[e]}function R(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1
return Math.max(t,Math.min(a,e))}function A(e){const t=e.toString().trim()
return t.includes(".")?t.length-t.indexOf(".")-1:0}function T(e,t){return e+(arguments.length>2&&void 0!==arguments[2]?arguments[2]:"0").repeat(Math.max(0,t-e.length))}function D(e,t){return(arguments.length>2&&void 0!==arguments[2]?arguments[2]:"0").repeat(Math.max(0,t-e.length))+e}function F(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1e3
if(e<t)return`${e} B`
const a=1024===t?["Ki","Mi","Gi"]:["k","M","G"]
let l=-1
for(;Math.abs(e)>=t&&l<a.length-1;)e/=t,++l
return`${e.toFixed(1)} ${a[l]}B`}function z(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=arguments.length>2?arguments[2]:void 0
const l={}
for(const t in e)l[t]=e[t]
for(const o in t){const n=e[o],r=t[o]
h(n)&&h(r)?l[o]=z(n,r,a):a&&Array.isArray(n)&&Array.isArray(r)?l[o]=a(n,r):l[o]=r}return l}function $(e){return e.map((e=>e.type===t.Fragment?$(e.children):e)).flat()}function M(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:""
if(M.cache.has(e))return M.cache.get(e)
const t=e.replace(/[^a-z]/gi,"-").replace(/\B([A-Z])/g,"-$1").toLowerCase()
return M.cache.set(e,t),t}function O(e,t){if(!t||"object"!=typeof t)return[]
if(Array.isArray(t))return t.map((t=>O(e,t))).flat(1)
if(t.suspense)return O(e,t.ssContent)
if(Array.isArray(t.children))return t.children.map((t=>O(e,t))).flat(1)
if(t.component){if(Object.getOwnPropertySymbols(t.component.provides).includes(e))return[t.component]
if(t.component.subTree)return O(e,t.component.subTree).flat(1)}return[]}M.cache=new Map
var L=new WeakMap,j=new WeakMap
class H{constructor(e){r(this,L,[]),r(this,j,0),this.size=e}get isFull(){return s(L,this).length===this.size}push(e){s(L,this)[s(j,this)]=e,i(j,this,(s(j,this)+1)%this.size)}values(){return s(L,this).slice(s(j,this)).concat(s(L,this).slice(0,s(j,this)))}clear(){s(L,this).length=0,i(j,this,0)}}function W(e){const a=t.reactive({})
t.watchEffect((()=>{const t=e()
for(const e in t)a[e]=t[e]}),{flush:"sync"})
const l={}
for(const e in a)l[e]=t.toRef((()=>a[e]))
return l}function U(e,t){return e.includes(t)}function Y(e){return e[2].toLowerCase()+e.slice(3)}const G=()=>[Function,Array]
function q(e,a){return!!(e[a="on"+t.capitalize(a)]||e[`${a}Once`]||e[`${a}Capture`]||e[`${a}OnceCapture`]||e[`${a}CaptureOnce`])}function K(e){for(var t=arguments.length,a=new Array(t>1?t-1:0),l=1;l<t;l++)a[l-1]=arguments[l]
if(Array.isArray(e))for(const t of e)t(...a)
else"function"==typeof e&&e(...a)}function X(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1]
const a=["button","[href]",'input:not([type="hidden"])',"select","textarea","[tabindex]"].map((e=>`${e}${t?':not([tabindex="-1"])':""}:not([disabled])`)).join(", ")
return[...e.querySelectorAll(a)]}function Z(e,t,a){let l,o=e.indexOf(document.activeElement)
const n="next"===t?1:-1
do{o+=n,l=e[o]}while((!l||null==l.offsetParent||!(a?.(l)??1))&&o<e.length&&o>=0)
return l}function Q(e,t){const a=X(e)
if(null==t)e!==document.activeElement&&e.contains(document.activeElement)||a[0]?.focus()
else if("first"===t)a[0]?.focus()
else if("last"===t)a.at(-1)?.focus()
else if("number"==typeof t)a[t]?.focus()
else{const l=Z(a,t)
l?l.focus():Q(e,"next"===t?"first":"last")}}function J(e){return null==e||"string"==typeof e&&""===e.trim()}function ee(){}function te(e,t){if(!(a&&"undefined"!=typeof CSS&&void 0!==CSS.supports&&CSS.supports(`selector(${t})`)))return null
try{return!!e&&e.matches(t)}catch(e){return null}}function ae(e){return e.some((e=>!t.isVNode(e)||e.type!==t.Comment&&(e.type!==t.Fragment||ae(e.children))))?e:null}function le(){const e=t.shallowRef(),a=t=>{e.value=t}
return Object.defineProperty(a,"value",{enumerable:!0,get:()=>e.value,set:t=>e.value=t}),Object.defineProperty(a,"el",{enumerable:!0,get:()=>y(e.value)}),a}function oe(e){const t=1===e.key.length,a=!e.ctrlKey&&!e.metaKey&&!e.altKey
return t&&a}function ne(e){return"string"==typeof e||"number"==typeof e||"boolean"==typeof e||"bigint"==typeof e}function re(e){if(!e)return
const a={}
for(const l in e)a[t.camelize(l)]=e[l]
return a}const ie=["top","bottom"],se=["start","end","left","right"]
function ue(e,t){let[a,l]=e.split(" ")
return l||(l=U(ie,a)?"start":U(se,a)?"top":"center"),{side:ce(a,t),align:ce(l,t)}}function ce(e,t){return"start"===e?t?"right":"left":"end"===e?t?"left":"right":e}function de(e){return{side:{center:"center",top:"bottom",bottom:"top",left:"right",right:"left"}[e.side],align:e.align}}function ve(e){return{side:e.side,align:{center:"center",top:"bottom",bottom:"top",left:"right",right:"left"}[e.align]}}function pe(e){return{side:e.align,align:e.side}}function me(e){return U(ie,e.side)?"y":"x"}class fe{constructor(e){let{x:t,y:a,width:l,height:o}=e
this.x=t,this.y=a,this.width=l,this.height=o}get top(){return this.y}get bottom(){return this.y+this.height}get left(){return this.x}get right(){return this.x+this.width}}function ge(e,t){return{x:{before:Math.max(0,t.left-e.left),after:Math.max(0,e.right-t.right)},y:{before:Math.max(0,t.top-e.top),after:Math.max(0,e.bottom-t.bottom)}}}function he(e){return Array.isArray(e)?new fe({x:e[0],y:e[1],width:0,height:0}):e.getBoundingClientRect()}function ye(e){const t=e.getBoundingClientRect(),a=getComputedStyle(e),l=a.transform
if(l){let o,n,r,i,s
if(l.startsWith("matrix3d("))o=l.slice(9,-1).split(/, /),n=Number(o[0]),r=Number(o[5]),i=Number(o[12]),s=Number(o[13])
else{if(!l.startsWith("matrix("))return new fe(t)
o=l.slice(7,-1).split(/, /),n=Number(o[0]),r=Number(o[3]),i=Number(o[4]),s=Number(o[5])}const u=a.transformOrigin,c=t.x-i-(1-n)*parseFloat(u),d=t.y-s-(1-r)*parseFloat(u.slice(u.indexOf(" ")+1)),v=n?t.width/n:e.offsetWidth+1,p=r?t.height/r:e.offsetHeight+1
return new fe({x:c,y:d,width:v,height:p})}return new fe(t)}function be(e,t,a){if(void 0===e.animate)return{finished:Promise.resolve()}
let l
try{l=e.animate(t,a)}catch(e){return{finished:Promise.resolve()}}return void 0===l.finished&&(l.finished=new Promise((e=>{l.onfinish=()=>{e(l)}}))),l}const Ve=new WeakMap
const we=2.4,Se=.2126729,ke=.7151522,xe=.072175,Ce=.55,Ne=.58,Ee=.57,Ie=.62,_e=.03,Pe=1.45,Be=5e-4,Re=1.25,Ae=1.25,Te=.078,De=12.82051282051282,Fe=.06,ze=.001
function $e(e,t){const a=(e.r/255)**we,l=(e.g/255)**we,o=(e.b/255)**we,n=(t.r/255)**we,r=(t.g/255)**we,i=(t.b/255)**we
let s,u=a*Se+l*ke+o*xe,c=n*Se+r*ke+i*xe
if(u<=_e&&(u+=(_e-u)**Pe),c<=_e&&(c+=(_e-c)**Pe),Math.abs(c-u)<Be)return 0
if(c>u){const e=(c**Ce-u**Ne)*Re
s=e<ze?0:e<Te?e-e*De*Fe:e-Fe}else{const e=(c**Ie-u**Ee)*Ae
s=e>-.001?0:e>-.078?e-e*De*Fe:e+Fe}return 100*s}function Me(e){t.warn(`Vuetify: ${e}`)}function Oe(e){t.warn(`Vuetify error: ${e}`)}const Le=.20689655172413793,je=e=>e>Le**3?Math.cbrt(e):e/(3*Le**2)+4/29,He=e=>e>Le?e**3:3*Le**2*(e-4/29)
function We(e){const t=je,a=t(e[1])
return[116*a-16,500*(t(e[0]/.95047)-a),200*(a-t(e[2]/1.08883))]}function Ue(e){const t=He,a=(e[0]+16)/116
return[.95047*t(a+e[1]/500),t(a),1.08883*t(a-e[2]/200)]}const Ye=[[3.2406,-1.5372,-.4986],[-.9689,1.8758,.0415],[.0557,-.204,1.057]],Ge=e=>e<=.0031308?12.92*e:1.055*e**(1/2.4)-.055,qe=[[.4124,.3576,.1805],[.2126,.7152,.0722],[.0193,.1192,.9505]],Ke=e=>e<=.04045?e/12.92:((e+.055)/1.055)**2.4
function Xe(e){const t=Array(3),a=Ge,l=Ye
for(let o=0;o<3;++o)t[o]=Math.round(255*R(a(l[o][0]*e[0]+l[o][1]*e[1]+l[o][2]*e[2])))
return{r:t[0],g:t[1],b:t[2]}}function Ze(e){let{r:t,g:a,b:l}=e
const o=[0,0,0],n=Ke,r=qe
t=n(t/255),a=n(a/255),l=n(l/255)
for(let e=0;e<3;++e)o[e]=r[e][0]*t+r[e][1]*a+r[e][2]*l
return o}function Qe(e){return!!e&&/^(#|var\(--|(rgb|hsl)a?\()/.test(e)}const Je=/^(?<fn>(?:rgb|hsl)a?)\((?<values>.+)\)/,et={rgb:(e,t,a,l)=>({r:e,g:t,b:a,a:l}),rgba:(e,t,a,l)=>({r:e,g:t,b:a,a:l}),hsl:(e,t,a,l)=>lt({h:e,s:t,l:a,a:l}),hsla:(e,t,a,l)=>lt({h:e,s:t,l:a,a:l}),hsv:(e,t,a,l)=>at({h:e,s:t,v:a,a:l}),hsva:(e,t,a,l)=>at({h:e,s:t,v:a,a:l})}
function tt(e){if("number"==typeof e)return(isNaN(e)||e<0||e>16777215)&&Me(`'${e}' is not a valid hex color`),{r:(16711680&e)>>16,g:(65280&e)>>8,b:255&e}
if("string"==typeof e&&Je.test(e)){const{groups:t}=e.match(Je),{fn:a,values:l}=t,o=l.split(/,\s*|\s*\/\s*|\s+/).map(((e,t)=>e.endsWith("%")||t>0&&t<3&&["hsl","hsla","hsv","hsva"].includes(a)?parseFloat(e)/100:parseFloat(e)))
return et[a](...o)}if("string"==typeof e){let t=e.startsWith("#")?e.slice(1):e;[3,4].includes(t.length)?t=t.split("").map((e=>e+e)).join(""):[6,8].includes(t.length)||Me(`'${e}' is not a valid hex(a) color`)
const a=parseInt(t,16)
return(isNaN(a)||a<0||a>4294967295)&&Me(`'${e}' is not a valid hex(a) color`),dt(t)}if("object"==typeof e){if(S(e,["r","g","b"]))return e
if(S(e,["h","s","l"]))return at(rt(e))
if(S(e,["h","s","v"]))return at(e)}throw new TypeError(`Invalid color: ${null==e?e:String(e)||e.constructor.name}\nExpected #hex, #hexa, rgb(), rgba(), hsl(), hsla(), object or number`)}function at(e){const{h:t,s:a,v:l,a:o}=e,n=e=>{const o=(e+t/60)%6
return l-l*a*Math.max(Math.min(o,4-o,1),0)},r=[n(5),n(3),n(1)].map((e=>Math.round(255*e)))
return{r:r[0],g:r[1],b:r[2],a:o}}function lt(e){return at(rt(e))}function ot(e){if(!e)return{h:0,s:1,v:1,a:1}
const t=e.r/255,a=e.g/255,l=e.b/255,o=Math.max(t,a,l),n=Math.min(t,a,l)
let r=0
o!==n&&(o===t?r=60*(0+(a-l)/(o-n)):o===a?r=60*(2+(l-t)/(o-n)):o===l&&(r=60*(4+(t-a)/(o-n)))),r<0&&(r+=360)
const i=[r,0===o?0:(o-n)/o,o]
return{h:i[0],s:i[1],v:i[2],a:e.a}}function nt(e){const{h:t,s:a,v:l,a:o}=e,n=l-l*a/2
return{h:t,s:1===n||0===n?0:(l-n)/Math.min(n,1-n),l:n,a:o}}function rt(e){const{h:t,s:a,l,a:o}=e,n=l+a*Math.min(l,1-l)
return{h:t,s:0===n?0:2-2*l/n,v:n,a:o}}function it(e){let{r:t,g:a,b:l,a:o}=e
return void 0===o?`rgb(${t}, ${a}, ${l})`:`rgba(${t}, ${a}, ${l}, ${o})`}function st(e){return it(at(e))}function ut(e){const t=Math.round(e).toString(16)
return("00".substr(0,2-t.length)+t).toUpperCase()}function ct(e){let{r:t,g:a,b:l,a:o}=e
return`#${[ut(t),ut(a),ut(l),void 0!==o?ut(Math.round(255*o)):""].join("")}`}function dt(e){e=function(e){e.startsWith("#")&&(e=e.slice(1))
e=e.replace(/([^0-9a-f])/gi,"F"),(3===e.length||4===e.length)&&(e=e.split("").map((e=>e+e)).join(""))
6!==e.length&&(e=T(T(e,6),8,"F"))
return e}(e)
let[t,a,l,o]=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1
const a=[]
let l=0
for(;l<e.length;)a.push(e.substr(l,t)),l+=t
return a}(e,2).map((e=>parseInt(e,16)))
return o=void 0===o?o:o/255,{r:t,g:a,b:l,a:o}}function vt(e){return ct(at(e))}function pt(e,t){const a=We(Ze(e))
return a[0]=a[0]+10*t,Xe(Ue(a))}function mt(e,t){const a=We(Ze(e))
return a[0]=a[0]-10*t,Xe(Ue(a))}function ft(e){return Ze(tt(e))[1]}function gt(e,t){const a=ft(e),l=ft(t)
return(Math.max(a,l)+.05)/(Math.min(a,l)+.05)}function ht(e){const t=Math.abs($e(tt(0),tt(e)))
return Math.abs($e(tt(16777215),tt(e)))>Math.min(t,50)?"#fff":"#000"}function yt(e,t){return a=>Object.keys(e).reduce(((l,o)=>{const n="object"==typeof e[o]&&null!=e[o]&&!Array.isArray(e[o])?e[o]:{type:e[o]}
return l[o]=a&&o in a?{...n,default:a[o]}:n,t&&!l[o].source&&(l[o].source=t),l}),{})}const bt=yt({class:[String,Array,Object],style:{type:[String,Array,Object],default:null}},"component")
function Vt(e,a){const l=t.getCurrentInstance()
if(!l)throw new Error(`[Vuetify] ${e} must be called from inside a setup function`)
return l}function wt(){const e=Vt(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"composables").type
return M(e?.aliasName||e?.name)}const St=Symbol.for("vuetify:defaults")
function kt(){const e=t.inject(St)
if(!e)throw new Error("[Vuetify] Could not find defaults instance")
return e}function xt(e,a){const l=kt(),o=t.ref(e),n=t.computed((()=>{if(t.unref(a?.disabled))return l.value
const e=t.unref(a?.scoped),n=t.unref(a?.reset),r=t.unref(a?.root)
if(null==o.value&&!(e||n||r))return l.value
let i=z(o.value,{prev:l.value})
if(e)return i
if(n||r){const e=Number(n||1/0)
for(let t=0;t<=e&&(i&&"prev"in i);t++)i=i.prev
return i&&"string"==typeof r&&r in i&&(i=z(z(i,{prev:i}),i[r])),i}return i.prev?z(i.prev,i):i}))
return t.provide(St,n),n}function Ct(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=arguments.length>1?arguments[1]:void 0,l=arguments.length>2&&void 0!==arguments[2]?arguments[2]:kt()
const o=Vt("useDefaults")
if(a=a??o.type.name??o.type.__name,!a)throw new Error("[Vuetify] Could not determine component name")
const n=t.computed((()=>l.value?.[e._as??a])),r=new Proxy(e,{get(e,t){const a=Reflect.get(e,t)
if("class"===t||"style"===t)return[n.value?.[t],a].filter((e=>null!=e))
if(function(e,t){return e.props&&(void 0!==e.props[t]||void 0!==e.props[M(t)])}(o.vnode,t))return a
const r=n.value?.[t]
if(void 0!==r)return r
const i=l.value?.global?.[t]
return void 0!==i?i:a}}),i=t.shallowRef()
return t.watchEffect((()=>{if(n.value){const e=Object.entries(n.value).filter((e=>{let[t]=e
return t.startsWith(t[0].toUpperCase())}))
i.value=e.length?Object.fromEntries(e):void 0}else i.value=void 0})),{props:r,provideSubDefaults:function(){const e=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Vt("injectSelf")
const{provides:a}=t
if(a&&e in a)return a[e]}(St,o)
t.provide(St,t.computed((()=>i.value?z(e?.value??{},i.value):e?.value)))}}}function Nt(e){if(e._setup=e._setup??e.setup,!e.name)return Me("The component is missing an explicit name, unable to generate default prop value"),e
if(e._setup){e.props=yt(e.props??{},e.name)()
const t=Object.keys(e.props).filter((e=>"class"!==e&&"style"!==e))
e.filterProps=function(e){return k(e,t)},e.props._as=String,e.setup=function(t,a){const l=kt()
if(!l.value)return e._setup(t,a)
const{props:o,provideSubDefaults:n}=Ct(t,t._as??e.name,l),r=e._setup(o,a)
return n(),r}}return e}function Et(){let e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0]
return a=>(e?Nt:t.defineComponent)(a)}function It(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"div",l=arguments.length>2?arguments[2]:void 0
return Et()({name:l??t.capitalize(t.camelize(e.replace(/__/g,"-"))),props:{tag:{type:String,default:a},...bt()},setup(a,l){let{slots:o}=l
return()=>t.h(a.tag,{class:[e,a.class],style:a.style},o.default?.())}})}function _t(e){if("function"!=typeof e.getRootNode){for(;e.parentNode;)e=e.parentNode
return e!==document?null:document}const t=e.getRootNode()
return t!==document&&t.getRootNode({composed:!0})!==document?null:t}const Pt="cubic-bezier(0.4, 0, 0.2, 1)"
function Bt(e,t,a){return Object.keys(e).filter((e=>E(e)&&e.endsWith(t))).reduce(((l,o)=>(l[o.slice(0,-t.length)]=t=>e[o](t,a(t)),l)),{})}function Rt(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1]
for(;e;){if(t?Dt(e):Tt(e))return e
e=e.parentElement}return document.scrollingElement}function At(e,t){const a=[]
if(t&&e&&!t.contains(e))return a
for(;e&&(Tt(e)&&a.push(e),e!==t);)e=e.parentElement
return a}function Tt(e){if(!e||e.nodeType!==Node.ELEMENT_NODE)return!1
const t=window.getComputedStyle(e)
return"scroll"===t.overflowY||"auto"===t.overflowY&&e.scrollHeight>e.clientHeight}function Dt(e){if(!e||e.nodeType!==Node.ELEMENT_NODE)return!1
const t=window.getComputedStyle(e)
return["scroll","auto"].includes(t.overflowY)}function Ft(e){Vt("useRender").render=e}const zt=[String,Function,Object,Array],$t=Symbol.for("vuetify:icons"),Mt=yt({icon:{type:zt},tag:{type:[String,Object,Function],required:!0}},"icon"),Ot=Et()({name:"VComponentIcon",props:Mt(),setup(e,a){let{slots:l}=a
return()=>{const a=e.icon
return t.createVNode(e.tag,null,{default:()=>[e.icon?t.createVNode(a,null,null):l.default?.()]})}}}),Lt=Nt({name:"VSvgIcon",inheritAttrs:!1,props:Mt(),setup(e,a){let{attrs:l}=a
return()=>t.createVNode(e.tag,t.mergeProps(l,{style:null}),{default:()=>[t.createElementVNode("svg",{class:"v-icon__svg",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",role:"img","aria-hidden":"true"},[Array.isArray(e.icon)?e.icon.map((e=>Array.isArray(e)?t.createElementVNode("path",{d:e[0],"fill-opacity":e[1]},null):t.createElementVNode("path",{d:e},null))):t.createElementVNode("path",{d:e.icon},null)])]})}}),jt=Nt({name:"VLigatureIcon",props:Mt(),setup:e=>()=>t.createVNode(e.tag,null,{default:()=>[e.icon]})}),Ht=Nt({name:"VClassIcon",props:Mt(),setup:e=>()=>t.createVNode(e.tag,{class:t.normalizeClass(e.icon)},null)})
function Wt(e){const t={svg:{component:Lt},class:{component:Ht}},a=e?.defaultSet??"mdi"
return"mdi"!==a||t.mdi||(t.mdi=Yt),z({defaultSet:a,sets:t,aliases:{...Ut,vuetify:["M8.2241 14.2009L12 21L22 3H14.4459L8.2241 14.2009Z",["M7.26303 12.4733L7.00113 12L2 3H12.5261C12.5261 3 12.5261 3 12.5261 3L7.26303 12.4733Z",.6]],"vuetify-outline":"svg:M7.26 12.47 12.53 3H2L7.26 12.47ZM14.45 3 8.22 14.2 12 21 22 3H14.45ZM18.6 5 12 16.88 10.51 14.2 15.62 5ZM7.26 8.35 5.4 5H9.13L7.26 8.35Z","vuetify-play":["m6.376 13.184-4.11-7.192C1.505 4.66 2.467 3 4.003 3h8.532l-.953 1.576-.006.01-.396.677c-.429.732-.214 1.507.194 2.015.404.503 1.092.878 1.869.806a3.72 3.72 0 0 1 1.005.022c.276.053.434.143.523.237.138.146.38.635-.25 2.09-.893 1.63-1.553 1.722-1.847 1.677-.213-.033-.468-.158-.756-.406a4.95 4.95 0 0 1-.8-.927c-.39-.564-1.04-.84-1.66-.846-.625-.006-1.316.27-1.693.921l-.478.826-.911 1.506Z",["M9.093 11.552c.046-.079.144-.15.32-.148a.53.53 0 0 1 .43.207c.285.414.636.847 1.046 1.2.405.35.914.662 1.516.754 1.334.205 2.502-.698 3.48-2.495l.014-.028.013-.03c.687-1.574.774-2.852-.005-3.675-.37-.391-.861-.586-1.333-.676a5.243 5.243 0 0 0-1.447-.044c-.173.016-.393-.073-.54-.257-.145-.18-.127-.316-.082-.392l.393-.672L14.287 3h5.71c1.536 0 2.499 1.659 1.737 2.992l-7.997 13.996c-.768 1.344-2.706 1.344-3.473 0l-3.037-5.314 1.377-2.278.004-.006.004-.007.481-.831Z",.6]]}},e)}const Ut={collapse:"mdi-chevron-up",complete:"mdi-check",cancel:"mdi-close-circle",close:"mdi-close",delete:"mdi-close-circle",clear:"mdi-close-circle",success:"mdi-check-circle",info:"mdi-information",warning:"mdi-alert-circle",error:"mdi-close-circle",prev:"mdi-chevron-left",next:"mdi-chevron-right",checkboxOn:"mdi-checkbox-marked",checkboxOff:"mdi-checkbox-blank-outline",checkboxIndeterminate:"mdi-minus-box",delimiter:"mdi-circle",sortAsc:"mdi-arrow-up",sortDesc:"mdi-arrow-down",expand:"mdi-chevron-down",menu:"mdi-menu",subgroup:"mdi-menu-down",dropdown:"mdi-menu-down",radioOn:"mdi-radiobox-marked",radioOff:"mdi-radiobox-blank",edit:"mdi-pencil",ratingEmpty:"mdi-star-outline",ratingFull:"mdi-star",ratingHalf:"mdi-star-half-full",loading:"mdi-cached",first:"mdi-page-first",last:"mdi-page-last",unfold:"mdi-unfold-more-horizontal",file:"mdi-paperclip",plus:"mdi-plus",minus:"mdi-minus",calendar:"mdi-calendar",treeviewCollapse:"mdi-menu-down",treeviewExpand:"mdi-menu-right",eyeDropper:"mdi-eyedropper",upload:"mdi-cloud-upload",color:"mdi-palette"},Yt={component:e=>t.h(Ht,{...e,class:"mdi"})},Gt={defaults:{global:{rounded:"sm"},VAvatar:{rounded:"circle"},VAutocomplete:{variant:"underlined"},VBanner:{color:"primary"},VBtn:{color:"primary",rounded:0},VCheckbox:{color:"secondary"},VCombobox:{variant:"underlined"},VDatePicker:{color:"primary",controlHeight:44,elevation:1,rounded:0,VBtn:{color:"high-emphasis",rounded:"circle"}},VSelect:{variant:"underlined"},VSlider:{color:"primary"},VTabs:{color:"primary"},VTextarea:{variant:"underlined"},VTextField:{variant:"underlined"},VToolbar:{VBtn:{color:null}}},icons:{defaultSet:"mdi",sets:{mdi:Yt}},theme:{themes:{light:{colors:{primary:"#3F51B5","primary-darken-1":"#303F9F","primary-lighten-1":"#C5CAE9",secondary:"#FF4081","secondary-darken-1":"#F50057","secondary-lighten-1":"#FF80AB",accent:"#009688"}}}}},qt={defaults:{global:{rounded:"md"},VAvatar:{rounded:"circle"},VAutocomplete:{variant:"filled"},VBanner:{color:"primary"},VBtn:{color:"primary"},VCheckbox:{color:"secondary"},VCombobox:{variant:"filled"},VDatePicker:{color:"primary",controlHeight:56,elevation:2,rounded:"md",VBtn:{color:"high-emphasis",rounded:"circle"}},VSelect:{variant:"filled"},VSlider:{color:"primary"},VTabs:{color:"primary"},VTextarea:{variant:"filled"},VTextField:{variant:"filled"},VToolbar:{VBtn:{color:null}}},icons:{defaultSet:"mdi",sets:{mdi:Yt}},theme:{themes:{light:{colors:{primary:"#6200EE","primary-darken-1":"#3700B3",secondary:"#03DAC6","secondary-darken-1":"#018786",error:"#B00020"}}}}},Kt={defaults:{VAppBar:{flat:!0},VAutocomplete:{variant:"outlined"},VBanner:{color:"primary"},VBottomSheet:{contentClass:"rounded-t-xl overflow-hidden"},VBtn:{color:"primary",rounded:"xl"},VBtnGroup:{rounded:"xl",VBtn:{rounded:null}},VCard:{rounded:"lg"},VCheckbox:{color:"secondary",inset:!0},VChip:{rounded:"sm"},VCombobox:{variant:"outlined"},VDateInput:{variant:"outlined"},VDatePicker:{controlHeight:48,color:"primary",divided:!0,headerColor:"",elevation:3,rounded:"xl",VBtn:{color:"high-emphasis",rounded:"circle"}},VFileInput:{variant:"outlined"},VNavigationDrawer:{},VNumberInput:{variant:"outlined",VBtn:{color:void 0,rounded:void 0}},VSelect:{variant:"outlined"},VSlider:{color:"primary"},VTabs:{color:"primary"},VTextarea:{variant:"outlined"},VTextField:{variant:"outlined"},VToolbar:{VBtn:{color:null}}},icons:{defaultSet:"mdi",sets:{mdi:Yt}},theme:{themes:{light:{colors:{primary:"#6750a4",secondary:"#b4b0bb",tertiary:"#7d5260",error:"#b3261e",surface:"#fffbfe"}}}}}
var Xt=Object.freeze({__proto__:null,md1:Gt,md2:qt,md3:Kt})
function Zt(e){let l=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"content"
const o=le(),n=t.ref()
if(a){const a=new ResizeObserver((t=>{e?.(t,a),t.length&&(n.value="content"===l?t[0].contentRect:t[0].target.getBoundingClientRect())}))
t.onBeforeUnmount((()=>{a.disconnect()})),t.watch((()=>o.el),((e,t)=>{t&&(a.unobserve(t),n.value=void 0),e&&a.observe(e)}),{flush:"post"})}return{resizeRef:o,contentRect:t.readonly(n)}}const Qt=Symbol.for("vuetify:layout"),Jt=Symbol.for("vuetify:layout-item"),ea=yt({overlaps:{type:Array,default:()=>[]},fullHeight:Boolean},"layout"),ta=yt({name:{type:String},order:{type:[Number,String],default:0},absolute:Boolean},"layout-item")
function aa(){const e=t.inject(Qt)
if(!e)throw new Error("[Vuetify] Could not find injected layout")
return{getLayoutItem:e.getLayoutItem,mainRect:e.mainRect,mainStyles:e.mainStyles}}function la(e){const a=t.inject(Qt)
if(!a)throw new Error("[Vuetify] Could not find injected layout")
const l=e.id??`layout-item-${t.useId()}`,o=Vt("useLayoutItem")
t.provide(Jt,{id:l})
const n=t.shallowRef(!1)
t.onDeactivated((()=>n.value=!0)),t.onActivated((()=>n.value=!1))
const{layoutItemStyles:r,layoutItemScrimStyles:i}=a.register(o,{...e,active:t.computed((()=>!n.value&&e.active.value)),id:l})
return t.onBeforeUnmount((()=>a.unregister(l))),{layoutItemStyles:r,layoutRect:a.layoutRect,layoutItemScrimStyles:i}}function oa(e){const a=t.inject(Qt,null),l=t.computed((()=>a?a.rootZIndex.value-100:1e3)),o=t.ref([]),n=t.reactive(new Map),r=t.reactive(new Map),i=t.reactive(new Map),s=t.reactive(new Map),u=t.reactive(new Map),{resizeRef:c,contentRect:d}=Zt(),v=t.computed((()=>{const t=new Map,a=e.overlaps??[]
for(const e of a.filter((e=>e.includes(":")))){const[a,l]=e.split(":")
if(!o.value.includes(a)||!o.value.includes(l))continue
const i=n.get(a),s=n.get(l),u=r.get(a),c=r.get(l)
i&&s&&u&&c&&(t.set(l,{position:i.value,amount:parseInt(u.value,10)}),t.set(a,{position:s.value,amount:-parseInt(c.value,10)}))}return t})),p=t.computed((()=>{const e=[...new Set([...i.values()].map((e=>e.value)))].sort(((e,t)=>e-t)),t=[]
for(const a of e){const e=o.value.filter((e=>i.get(e)?.value===a))
t.push(...e)}return((e,t,a,l)=>{let o={top:0,left:0,right:0,bottom:0}
const n=[{id:"",layer:{...o}}]
for(const r of e){const e=t.get(r),i=a.get(r),s=l.get(r)
if(!e||!i||!s)continue
const u={...o,[e.value]:parseInt(o[e.value],10)+(s.value?parseInt(i.value,10):0)}
n.push({id:r,layer:u}),o=u}return n})(t,n,r,s)})),m=t.computed((()=>!Array.from(u.values()).some((e=>e.value)))),g=t.computed((()=>p.value[p.value.length-1].layer)),h=t.toRef((()=>({"--v-layout-left":f(g.value.left),"--v-layout-right":f(g.value.right),"--v-layout-top":f(g.value.top),"--v-layout-bottom":f(g.value.bottom),...m.value?void 0:{transition:"none"}}))),y=t.computed((()=>p.value.slice(1).map(((e,t)=>{let{id:a}=e
const{layer:l}=p.value[t],o=r.get(a),i=n.get(a)
return{id:a,...l,size:Number(o.value),position:i.value}})))),b=e=>y.value.find((t=>t.id===e)),V=Vt("createLayout"),w=t.shallowRef(!1)
t.onMounted((()=>{w.value=!0})),t.provide(Qt,{register:(e,a)=>{let{id:c,order:d,position:f,layoutSize:g,elementSize:h,active:b,disableTransitions:S,absolute:k}=a
i.set(c,d),n.set(c,f),r.set(c,g),s.set(c,b),S&&u.set(c,S)
const x=O(Jt,V?.vnode).indexOf(e)
x>-1?o.value.splice(x,0,c):o.value.push(c)
const C=t.computed((()=>y.value.findIndex((e=>e.id===c)))),N=t.computed((()=>l.value+2*p.value.length-2*C.value))
return{layoutItemStyles:t.computed((()=>{const e="left"===f.value||"right"===f.value,t="right"===f.value,a="bottom"===f.value,o=h.value??g.value,n=0===o?"%":"px",r={[f.value]:0,zIndex:N.value,transform:`translate${e?"X":"Y"}(${(b.value?0:-(0===o?100:o))*(t||a?-1:1)}${n})`,position:k.value||1e3!==l.value?"absolute":"fixed",...m.value?void 0:{transition:"none"}}
if(!w.value)return r
const i=y.value[C.value]
if(!i)throw new Error(`[Vuetify] Could not find layout item "${c}"`)
const s=v.value.get(c)
return s&&(i[s.position]+=s.amount),{...r,height:e?`calc(100% - ${i.top}px - ${i.bottom}px)`:h.value?`${h.value}px`:void 0,left:t?void 0:`${i.left}px`,right:t?`${i.right}px`:void 0,top:"bottom"!==f.value?`${i.top}px`:void 0,bottom:"top"!==f.value?`${i.bottom}px`:void 0,width:e?h.value?`${h.value}px`:void 0:`calc(100% - ${i.left}px - ${i.right}px)`}})),layoutItemScrimStyles:t.computed((()=>({zIndex:N.value-1}))),zIndex:N}},unregister:e=>{i.delete(e),n.delete(e),r.delete(e),s.delete(e),u.delete(e),o.value=o.value.filter((t=>t!==e))},mainRect:g,mainStyles:h,getLayoutItem:b,items:y,layoutRect:d,rootZIndex:l})
return{layoutClasses:t.toRef((()=>["v-layout",{"v-layout--full-height":e.fullHeight}])),layoutStyles:t.toRef((()=>({zIndex:a?l.value:void 0,position:a?"relative":void 0,overflow:a?"hidden":void 0}))),getLayoutItem:b,items:y,layoutRect:d,layoutRef:c}}function na(e,a){let l
function o(){l=t.effectScope(),l.run((()=>a.length?a((()=>{l?.stop(),o()})):a()))}t.watch(e,(e=>{e&&!l?o():e||(l?.stop(),l=void 0)}),{immediate:!0}),t.onScopeDispose((()=>{l?.stop()}))}function ra(e,a,l){let o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:e=>e,n=arguments.length>4&&void 0!==arguments[4]?arguments[4]:e=>e
const r=Vt("useProxiedModel"),i=t.ref(void 0!==e[a]?e[a]:l),s=M(a),u=s!==a?t.computed((()=>(e[a],!(!r.vnode.props?.hasOwnProperty(a)&&!r.vnode.props?.hasOwnProperty(s)||!r.vnode.props?.hasOwnProperty(`onUpdate:${a}`)&&!r.vnode.props?.hasOwnProperty(`onUpdate:${s}`))))):t.computed((()=>(e[a],!(!r.vnode.props?.hasOwnProperty(a)||!r.vnode.props?.hasOwnProperty(`onUpdate:${a}`)))))
na((()=>!u.value),(()=>{t.watch((()=>e[a]),(e=>{i.value=e}))}))
const c=t.computed({get(){const t=e[a]
return o(u.value?t:i.value)},set(l){const s=n(l),c=t.toRaw(u.value?e[a]:i.value)
c!==s&&o(c)!==l&&(i.value=s,r?.emit(`update:${a}`,s))}})
return Object.defineProperty(c,"externalValue",{get:()=>u.value?e[a]:i.value}),c}var ia={badge:"Badge",open:"Open",close:"Close",dismiss:"Dismiss",confirmEdit:{ok:"OK",cancel:"Cancel"},dataIterator:{noResultsText:"No matching records found",loadingText:"Loading items..."},dataTable:{itemsPerPageText:"Rows per page:",ariaLabel:{sortDescending:"Sorted descending.",sortAscending:"Sorted ascending.",sortNone:"Not sorted.",activateNone:"Activate to remove sorting.",activateDescending:"Activate to sort descending.",activateAscending:"Activate to sort ascending."},sortBy:"Sort by"},dataFooter:{itemsPerPageText:"Items per page:",itemsPerPageAll:"All",nextPage:"Next page",prevPage:"Previous page",firstPage:"First page",lastPage:"Last page",pageText:"{0}-{1} of {2}"},dateRangeInput:{divider:"to"},datePicker:{itemsSelected:"{0} selected",range:{title:"Select dates",header:"Enter dates"},title:"Select date",header:"Enter date",input:{placeholder:"Enter date"}},noDataText:"No data available",carousel:{prev:"Previous visual",next:"Next visual",ariaLabel:{delimiter:"Carousel slide {0} of {1}"}},calendar:{moreEvents:"{0} more",today:"Today"},input:{clear:"Clear {0}",prependAction:"{0} prepended action",appendAction:"{0} appended action",otp:"Please enter OTP character {0}"},fileInput:{counter:"{0} files",counterSize:"{0} files ({1} in total)"},fileUpload:{title:"Drag and drop files here",divider:"or",browse:"Browse Files"},timePicker:{am:"AM",pm:"PM",title:"Select Time"},pagination:{ariaLabel:{root:"Pagination Navigation",next:"Next page",previous:"Previous page",page:"Go to page {0}",currentPage:"Page {0}, Current page",first:"First page",last:"Last page"}},stepper:{next:"Next",prev:"Previous"},rating:{ariaLabel:{item:"Rating {0} of {1}"}},loading:"Loading...",infiniteScroll:{loadMore:"Load more",empty:"No more"},rules:{required:"This field is required",email:"Please enter a valid email",number:"This field can only contain numbers",integer:"This field can only contain integer values",capital:"This field can only contain uppercase letters",maxLength:"You must enter a maximum of {0} characters",minLength:"You must enter a minimum of {0} characters",strictLength:"The length of the entered field is invalid",exclude:"The {0} character is not allowed",notEmpty:"Please choose at least one value",pattern:"Invalid format"}}
const sa="$vuetify.",ua=(e,t)=>e.replace(/\{(\d+)\}/g,((e,a)=>String(t[Number(a)]))),ca=(e,t,a)=>function(l){for(var o=arguments.length,n=new Array(o>1?o-1:0),r=1;r<o;r++)n[r-1]=arguments[r]
if(!l.startsWith(sa))return ua(l,n)
const i=l.replace(sa,""),s=e.value&&a.value[e.value],u=t.value&&a.value[t.value]
let c=v(s,i,null)
return c||(Me(`Translation key "${l}" not found in "${e.value}", trying fallback locale`),c=v(u,i,null)),c||(Oe(`Translation key "${l}" not found in fallback`),c=l),"string"!=typeof c&&(Oe(`Translation key "${l}" has a non-string value`),c=l),ua(c,n)}
function da(e,t){return(a,l)=>new Intl.NumberFormat([e.value,t.value],l).format(a)}function va(e,a,l){const o=ra(e,a,e[a]??l.value)
return o.value=e[a]??l.value,t.watch(l,(t=>{null==e[a]&&(o.value=l.value)})),o}function pa(e){return t=>{const a=va(t,"locale",e.current),l=va(t,"fallback",e.fallback),o=va(t,"messages",e.messages)
return{name:"vuetify",current:a,fallback:l,messages:o,t:ca(a,l,o),n:da(a,l),provide:pa({current:a,fallback:l,messages:o})}}}const ma=Symbol.for("vuetify:locale")
function fa(e){const a=e?.adapter&&(l=e?.adapter,null!=l.name)?e?.adapter:function(e){const a=t.shallowRef(e?.locale??"en"),l=t.shallowRef(e?.fallback??"en"),o=t.ref({en:ia,...e?.messages})
return{name:"vuetify",current:a,fallback:l,messages:o,t:ca(a,l,o),n:da(a,l),provide:pa({current:a,fallback:l,messages:o})}}(e)
var l
const o=function(e,a){const l=t.ref(a?.rtl??{af:!1,ar:!0,bg:!1,ca:!1,ckb:!1,cs:!1,de:!1,el:!1,en:!1,es:!1,et:!1,fa:!0,fi:!1,fr:!1,hr:!1,hu:!1,he:!0,id:!1,it:!1,ja:!1,km:!1,ko:!1,lv:!1,lt:!1,nl:!1,no:!1,pl:!1,pt:!1,ro:!1,ru:!1,sk:!1,sl:!1,srCyrl:!1,srLatn:!1,sv:!1,th:!1,tr:!1,az:!1,uk:!1,vi:!1,zhHans:!1,zhHant:!1}),o=t.computed((()=>l.value[e.current.value]??!1))
return{isRtl:o,rtl:l,rtlClasses:t.toRef((()=>"v-locale--is-"+(o.value?"rtl":"ltr")))}}(a,e)
return{...a,...o}}function ga(){const e=t.inject(ma)
if(!e)throw new Error("[Vuetify] Could not find injected locale instance")
return e}function ha(e){const a=t.inject(ma)
if(!a)throw new Error("[Vuetify] Could not find injected locale instance")
const l=a.provide(e),o=function(e,a,l){const o=t.computed((()=>l.rtl??a.value[e.current.value]??!1))
return{isRtl:o,rtl:a,rtlClasses:t.toRef((()=>"v-locale--is-"+(o.value?"rtl":"ltr")))}}(l,a.rtl,e),n={...l,...o}
return t.provide(ma,n),n}function ya(){const e=t.inject(ma)
if(!e)throw new Error("[Vuetify] Could not find injected rtl instance")
return{isRtl:e.isRtl,rtlClasses:e.rtlClasses}}const ba=Symbol.for("vuetify:theme"),Va=yt({theme:String},"theme")
function wa(e,t,a,l){e.push(`${function(e,t){if(!t)return e
const a=`:where(${t})`
return":root"===e?a:`${a} ${e}`}(t,l)} {\n`,...a.map((e=>`  ${e};\n`)),"}\n")}function Sa(e){const t=e.dark?2:1,a=e.dark?1:2,l=[]
for(const[o,n]of Object.entries(e.colors)){const e=tt(n)
l.push(`--v-theme-${o}: ${e.r},${e.g},${e.b}`),o.startsWith("on-")||l.push(`--v-theme-${o}-overlay-multiplier: ${ft(n)>.18?t:a}`)}for(const[t,a]of Object.entries(e.variables)){const e="string"==typeof a&&a.startsWith("#")?tt(a):void 0,o=e?`${e.r}, ${e.g}, ${e.b}`:void 0
l.push(`--v-${t}: ${o??a}`)}return l}function ka(e,t,a){const l={}
if(a)for(const o of["lighten","darken"]){const n="lighten"===o?pt:mt
for(const r of m(a[o],1))l[`${e}-${o}-${r}`]=ct(n(tt(t),r))}return l}function xa(e,t){if(!t)return{}
let a={}
for(const l of t.colors){const o=e[l]
o&&(a={...a,...ka(l,o,t)})}return a}function Ca(e){const t={}
for(const a of Object.keys(e)){if(a.startsWith("on-")||e[`on-${a}`])continue
const l=`on-${a}`,o=tt(e[a])
t[l]=ht(o)}return t}function Na(e){const l=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{defaultTheme:"light",variations:{colors:[],lighten:0,darken:0},themes:{light:{dark:!1,colors:{background:"#FFFFFF",surface:"#FFFFFF","surface-bright":"#FFFFFF","surface-light":"#EEEEEE","surface-variant":"#424242","on-surface-variant":"#EEEEEE",primary:"#1867C0","primary-darken-1":"#1F5592",secondary:"#48A9A6","secondary-darken-1":"#018786",error:"#B00020",info:"#2196F3",success:"#4CAF50",warning:"#FB8C00"},variables:{"border-color":"#000000","border-opacity":.12,"high-emphasis-opacity":.87,"medium-emphasis-opacity":.6,"disabled-opacity":.38,"idle-opacity":.04,"hover-opacity":.04,"focus-opacity":.12,"selected-opacity":.08,"activated-opacity":.12,"pressed-opacity":.12,"dragged-opacity":.08,"theme-kbd":"#212529","theme-on-kbd":"#FFFFFF","theme-code":"#F5F5F5","theme-on-code":"#000000"}},dark:{dark:!0,colors:{background:"#121212",surface:"#212121","surface-bright":"#ccbfd6","surface-light":"#424242","surface-variant":"#c8c8c8","on-surface-variant":"#000000",primary:"#2196F3","primary-darken-1":"#277CC1",secondary:"#54B6B2","secondary-darken-1":"#48A9A6",error:"#CF6679",info:"#2196F3",success:"#4CAF50",warning:"#FB8C00"},variables:{"border-color":"#FFFFFF","border-opacity":.12,"high-emphasis-opacity":1,"medium-emphasis-opacity":.7,"disabled-opacity":.5,"idle-opacity":.1,"hover-opacity":.04,"focus-opacity":.12,"selected-opacity":.08,"activated-opacity":.12,"pressed-opacity":.16,"dragged-opacity":.08,"theme-kbd":"#212529","theme-on-kbd":"#FFFFFF","theme-code":"#343434","theme-on-code":"#CCCCCC"}}},stylesheetId:"vuetify-theme-stylesheet"}
const t={defaultTheme:"light",variations:{colors:[],lighten:0,darken:0},themes:{light:{dark:!1,colors:{background:"#FFFFFF",surface:"#FFFFFF","surface-bright":"#FFFFFF","surface-light":"#EEEEEE","surface-variant":"#424242","on-surface-variant":"#EEEEEE",primary:"#1867C0","primary-darken-1":"#1F5592",secondary:"#48A9A6","secondary-darken-1":"#018786",error:"#B00020",info:"#2196F3",success:"#4CAF50",warning:"#FB8C00"},variables:{"border-color":"#000000","border-opacity":.12,"high-emphasis-opacity":.87,"medium-emphasis-opacity":.6,"disabled-opacity":.38,"idle-opacity":.04,"hover-opacity":.04,"focus-opacity":.12,"selected-opacity":.08,"activated-opacity":.12,"pressed-opacity":.12,"dragged-opacity":.08,"theme-kbd":"#212529","theme-on-kbd":"#FFFFFF","theme-code":"#F5F5F5","theme-on-code":"#000000"}},dark:{dark:!0,colors:{background:"#121212",surface:"#212121","surface-bright":"#ccbfd6","surface-light":"#424242","surface-variant":"#c8c8c8","on-surface-variant":"#000000",primary:"#2196F3","primary-darken-1":"#277CC1",secondary:"#54B6B2","secondary-darken-1":"#48A9A6",error:"#CF6679",info:"#2196F3",success:"#4CAF50",warning:"#FB8C00"},variables:{"border-color":"#FFFFFF","border-opacity":.12,"high-emphasis-opacity":1,"medium-emphasis-opacity":.7,"disabled-opacity":.5,"idle-opacity":.1,"hover-opacity":.04,"focus-opacity":.12,"selected-opacity":.08,"activated-opacity":.12,"pressed-opacity":.16,"dragged-opacity":.08,"theme-kbd":"#212529","theme-on-kbd":"#FFFFFF","theme-code":"#343434","theme-on-code":"#CCCCCC"}}},stylesheetId:"vuetify-theme-stylesheet"}
if(!e)return{...t,isDisabled:!0}
const a={}
for(const[l,o]of Object.entries(e.themes??{})){const e=o.dark||"dark"===l?t.themes?.dark:t.themes?.light
a[l]=z(e,o)}return z(t,{...e,themes:a})}(e),o=t.shallowRef(l.defaultTheme),n=t.ref(l.themes),r=t.computed((()=>{const e={}
for(const[t,a]of Object.entries(n.value)){const o={...a.colors,...xa(a.colors,l.variations)}
e[t]={...a,colors:{...o,...Ca(o)}}}return e})),i=t.toRef((()=>r.value[o.value])),s=t.computed((()=>{const e=[]
i.value?.dark&&wa(e,":root",["color-scheme: dark"],l.scope),wa(e,":root",Sa(i.value),l.scope)
for(const[t,a]of Object.entries(r.value))wa(e,`.v-theme--${t}`,["color-scheme: "+(a.dark?"dark":"normal"),...Sa(a)],l.scope)
const t=[],a=[],o=new Set(Object.values(r.value).flatMap((e=>Object.keys(e.colors))))
for(const e of o)e.startsWith("on-")?wa(a,`.${e}`,[`color: rgb(var(--v-theme-${e})) !important`],l.scope):(wa(t,`.bg-${e}`,[`--v-theme-overlay-multiplier: var(--v-theme-${e}-overlay-multiplier)`,`background-color: rgb(var(--v-theme-${e})) !important`,`color: rgb(var(--v-theme-on-${e})) !important`],l.scope),wa(a,`.text-${e}`,[`color: rgb(var(--v-theme-${e})) !important`],l.scope),wa(a,`.border-${e}`,[`--v-border-color: var(--v-theme-${e})`],l.scope))
return e.push(...t,...a),e.map(((e,t)=>0===t?e:`    ${e}`)).join("")}))
const u=t.toRef((()=>l.isDisabled?void 0:`v-theme--${o.value}`))
return{install:function(e){if(l.isDisabled)return
const o=e._context.provides.usehead
if(o){function n(){return{style:[{textContent:s.value,id:l.stylesheetId,nonce:l.cspNonce||!1}]}}if(o.push){const r=o.push(n)
a&&t.watch(s,(()=>{r.patch(n)}))}else a?(o.addHeadObjs(t.toRef(n)),t.watchEffect((()=>o.updateDOM()))):o.addHeadObjs(n())}else{function i(){!function(e,t){e&&(e.innerHTML=t)}(function(e,t){if(!a)return null
let l=document.getElementById(e)
return l||(l=document.createElement("style"),l.id=e,l.type="text/css",t&&l.setAttribute("nonce",t),document.head.appendChild(l)),l}(l.stylesheetId,l.cspNonce),s.value)}a?t.watch(s,i,{immediate:!0}):i()}},isDisabled:l.isDisabled,name:o,themes:n,current:i,computedThemes:r,themeClasses:u,styles:s,global:{name:o,current:i}}}function Ea(e){Vt("provideTheme")
const a=t.inject(ba,null)
if(!a)throw new Error("Could not find Vuetify theme injection")
const l=t.toRef((()=>e.theme??a.name.value)),o=t.toRef((()=>a.themes.value[l.value])),n=t.toRef((()=>a.isDisabled?void 0:`v-theme--${l.value}`)),r={...a,name:l,current:o,themeClasses:n}
return t.provide(ba,r),r}function Ia(){Vt("useTheme")
const e=t.inject(ba,null)
if(!e)throw new Error("Could not find Vuetify theme injection")
return e}const _a=yt({...bt(),...ea({fullHeight:!0}),...Va()},"VApp"),Pa=Et()({name:"VApp",props:_a(),setup(e,a){let{slots:l}=a
const o=Ea(e),{layoutClasses:n,getLayoutItem:r,items:i,layoutRef:s}=oa(e),{rtlClasses:u}=ya()
return Ft((()=>t.createElementVNode("div",{ref:s,class:t.normalizeClass(["v-application",o.themeClasses.value,n.value,u.value,e.class]),style:t.normalizeStyle([e.style])},[t.createElementVNode("div",{class:"v-application__wrap"},[l.default?.()])]))),{getLayoutItem:r,items:i,theme:o}}}),Ba=yt({tag:{type:[String,Object,Function],default:"div"}},"tag"),Ra=yt({text:String,...bt(),...Ba()},"VToolbarTitle"),Aa=Et()({name:"VToolbarTitle",props:Ra(),setup(e,a){let{slots:l}=a
return Ft((()=>{const a=!!(l.default||l.text||e.text)
return t.createVNode(e.tag,{class:t.normalizeClass(["v-toolbar-title",e.class]),style:t.normalizeStyle(e.style)},{default:()=>[a&&t.createElementVNode("div",{class:"v-toolbar-title__placeholder"},[l.text?l.text():e.text,l.default?.()])]})})),{}}}),Ta=yt({disabled:Boolean,group:Boolean,hideOnLeave:Boolean,leaveAbsolute:Boolean,mode:String,origin:String},"transition")
function Da(e,a,l){return Et()({name:e,props:Ta({mode:l,origin:a}),setup(a,l){let{slots:o}=l
const n={onBeforeEnter(e){a.origin&&(e.style.transformOrigin=a.origin)},onLeave(e){if(a.leaveAbsolute){const{offsetTop:t,offsetLeft:a,offsetWidth:l,offsetHeight:o}=e
e._transitionInitialStyles={position:e.style.position,top:e.style.top,left:e.style.left,width:e.style.width,height:e.style.height},e.style.position="absolute",e.style.top=`${t}px`,e.style.left=`${a}px`,e.style.width=`${l}px`,e.style.height=`${o}px`}a.hideOnLeave&&e.style.setProperty("display","none","important")},onAfterLeave(e){if(a.leaveAbsolute&&e?._transitionInitialStyles){const{position:t,top:a,left:l,width:o,height:n}=e._transitionInitialStyles
delete e._transitionInitialStyles,e.style.position=t||"",e.style.top=a||"",e.style.left=l||"",e.style.width=o||"",e.style.height=n||""}}}
return()=>{const l=a.group?t.TransitionGroup:t.Transition
return t.h(l,{name:a.disabled?"":e,css:!a.disabled,...a.group?void 0:{mode:a.mode},...a.disabled?{}:n},o.default)}}})}function Fa(e,a){let l=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"in-out"
return Et()({name:e,props:{mode:{type:String,default:l},disabled:Boolean,group:Boolean},setup(l,o){let{slots:n}=o
const r=l.group?t.TransitionGroup:t.Transition
return()=>t.h(r,{name:l.disabled?"":e,css:!l.disabled,...l.disabled?{}:a},n.default)}})}function za(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:""
const a=arguments.length>1&&void 0!==arguments[1]&&arguments[1]?"width":"height",l=t.camelize(`offset-${a}`)
return{onBeforeEnter(e){e._parent=e.parentNode,e._initialStyle={transition:e.style.transition,overflow:e.style.overflow,[a]:e.style[a]}},onEnter(t){const o=t._initialStyle
if(!o)return
t.style.setProperty("transition","none","important"),t.style.overflow="hidden"
const n=`${t[l]}px`
t.style[a]="0",t.offsetHeight,t.style.transition=o.transition,e&&t._parent&&t._parent.classList.add(e),requestAnimationFrame((()=>{t.style[a]=n}))},onAfterEnter:n,onEnterCancelled:n,onLeave(e){e._initialStyle={transition:"",overflow:e.style.overflow,[a]:e.style[a]},e.style.overflow="hidden",e.style[a]=`${e[l]}px`,e.offsetHeight,requestAnimationFrame((()=>e.style[a]="0"))},onAfterLeave:o,onLeaveCancelled:o}
function o(t){e&&t._parent&&t._parent.classList.remove(e),n(t)}function n(e){if(!e._initialStyle)return
const t=e._initialStyle[a]
e.style.overflow=e._initialStyle.overflow,null!=t&&(e.style[a]=t),delete e._initialStyle}}const $a=yt({target:[Object,Array]},"v-dialog-transition"),Ma=new WeakMap,Oa=Et()({name:"VDialogTransition",props:$a(),setup(e,a){let{slots:l}=a
const o={onBeforeEnter(e){e.style.pointerEvents="none",e.style.visibility="hidden"},async onEnter(t,a){await new Promise((e=>requestAnimationFrame(e))),await new Promise((e=>requestAnimationFrame(e))),t.style.visibility=""
const l=ja(e.target,t),{x:o,y:n,sx:r,sy:i,speed:s}=l
Ma.set(t,l)
const u=be(t,[{transform:`translate(${o}px, ${n}px) scale(${r}, ${i})`,opacity:0},{}],{duration:225*s,easing:"cubic-bezier(0.0, 0, 0.2, 1)"})
La(t)?.forEach((e=>{be(e,[{opacity:0},{opacity:0,offset:.33},{}],{duration:450*s,easing:Pt})})),u.finished.then((()=>a()))},onAfterEnter(e){e.style.removeProperty("pointer-events")},onBeforeLeave(e){e.style.pointerEvents="none"},async onLeave(t,a){let l
await new Promise((e=>requestAnimationFrame(e))),l=!Ma.has(t)||Array.isArray(e.target)||e.target.offsetParent||e.target.getClientRects().length?ja(e.target,t):Ma.get(t)
const{x:o,y:n,sx:r,sy:i,speed:s}=l
be(t,[{},{transform:`translate(${o}px, ${n}px) scale(${r}, ${i})`,opacity:0}],{duration:125*s,easing:"cubic-bezier(0.4, 0, 1, 1)"}).finished.then((()=>a())),La(t)?.forEach((e=>{be(e,[{},{opacity:0,offset:.2},{opacity:0}],{duration:250*s,easing:Pt})}))},onAfterLeave(e){e.style.removeProperty("pointer-events")}}
return()=>e.target?t.createVNode(t.Transition,t.mergeProps({name:"dialog-transition"},o,{css:!1}),l):t.createVNode(t.Transition,{name:"dialog-transition"},l)}})
function La(e){const t=e.querySelector(":scope > .v-card, :scope > .v-sheet, :scope > .v-list")?.children
return t&&[...t]}function ja(e,t){const a=he(e),l=ye(t),[o,n]=getComputedStyle(t).transformOrigin.split(" ").map((e=>parseFloat(e))),[r,i]=getComputedStyle(t).getPropertyValue("--v-overlay-anchor-origin").split(" ")
let s=a.left+a.width/2
"left"===r||"left"===i?s-=a.width/2:"right"!==r&&"right"!==i||(s+=a.width/2)
let u=a.top+a.height/2
"top"===r||"top"===i?u-=a.height/2:"bottom"!==r&&"bottom"!==i||(u+=a.height/2)
const c=a.width/l.width,d=a.height/l.height,v=Math.max(1,c,d),p=c/v||0,m=d/v||0,f=l.width*l.height/(window.innerWidth*window.innerHeight),g=f>.12?Math.min(1.5,10*(f-.12)+1):1
return{x:s-(o+l.left),y:u-(n+l.top),sx:p,sy:m,speed:g}}const Ha=Da("fab-transition","center center","out-in"),Wa=Da("dialog-bottom-transition"),Ua=Da("dialog-top-transition"),Ya=Da("fade-transition"),Ga=Da("scale-transition"),qa=Da("scroll-x-transition"),Ka=Da("scroll-x-reverse-transition"),Xa=Da("scroll-y-transition"),Za=Da("scroll-y-reverse-transition"),Qa=Da("slide-x-transition"),Ja=Da("slide-x-reverse-transition"),el=Da("slide-y-transition"),tl=Da("slide-y-reverse-transition"),al=Fa("expand-transition",za()),ll=Fa("expand-x-transition",za("",!0)),ol=yt({defaults:Object,disabled:Boolean,reset:[Number,String],root:[Boolean,String],scoped:Boolean},"VDefaultsProvider"),nl=Et(!1)({name:"VDefaultsProvider",props:ol(),setup(e,a){let{slots:l}=a
const{defaults:o,disabled:n,reset:r,root:i,scoped:s}=t.toRefs(e)
return xt(o,{reset:r,root:i,scoped:s,disabled:n}),()=>l.default?.()}}),rl=yt({height:[Number,String],maxHeight:[Number,String],maxWidth:[Number,String],minHeight:[Number,String],minWidth:[Number,String],width:[Number,String]},"dimension")
function il(e){return{dimensionStyles:t.computed((()=>{const t={},a=f(e.height),l=f(e.maxHeight),o=f(e.maxWidth),n=f(e.minHeight),r=f(e.minWidth),i=f(e.width)
return null!=a&&(t.height=a),null!=l&&(t.maxHeight=l),null!=o&&(t.maxWidth=o),null!=n&&(t.minHeight=n),null!=r&&(t.minWidth=r),null!=i&&(t.width=i),t}))}}const sl=yt({aspectRatio:[String,Number],contentClass:null,inline:Boolean,...bt(),...rl()},"VResponsive"),ul=Et()({name:"VResponsive",props:sl(),setup(e,a){let{slots:l}=a
const{aspectStyles:o}=function(e){return{aspectStyles:t.computed((()=>{const t=Number(e.aspectRatio)
return t?{paddingBottom:String(1/t*100)+"%"}:void 0}))}}(e),{dimensionStyles:n}=il(e)
return Ft((()=>t.createElementVNode("div",{class:t.normalizeClass(["v-responsive",{"v-responsive--inline":e.inline},e.class]),style:t.normalizeStyle([n.value,e.style])},[t.createElementVNode("div",{class:"v-responsive__sizer",style:t.normalizeStyle(o.value)},null),l.additional?.(),l.default&&t.createElementVNode("div",{class:t.normalizeClass(["v-responsive__content",e.contentClass])},[l.default()])]))),{}}})
function cl(e){return W((()=>{const a=t.toValue(e),l=[],o={}
if(a.background)if(Qe(a.background)){if(o.backgroundColor=a.background,!a.text&&(Qe(n=a.background)&&!/^((rgb|hsl)a?\()?var\(--/.test(n))){const e=tt(a.background)
if(null==e.a||1===e.a){const t=ht(e)
o.color=t,o.caretColor=t}}}else l.push(`bg-${a.background}`)
var n
return a.text&&(Qe(a.text)?(o.color=a.text,o.caretColor=a.text):l.push(`text-${a.text}`)),{colorClasses:l,colorStyles:o}}))}function dl(e){const{colorClasses:a,colorStyles:l}=cl((()=>({text:t.toValue(e)})))
return{textColorClasses:a,textColorStyles:l}}function vl(e){const{colorClasses:a,colorStyles:l}=cl((()=>({background:t.toValue(e)})))
return{backgroundColorClasses:a,backgroundColorStyles:l}}const pl=yt({rounded:{type:[Boolean,Number,String],default:void 0},tile:Boolean},"rounded")
function ml(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:wt()
return{roundedClasses:t.computed((()=>{const l=t.isRef(e)?e.value:e.rounded,o=t.isRef(e)?e.value:e.tile,n=[]
if(!0===l||""===l)n.push(`${a}--rounded`)
else if("string"==typeof l||0===l)for(const e of String(l).split(" "))n.push(`rounded-${e}`)
else(o||!1===l)&&n.push("rounded-0")
return n}))}}const fl=yt({transition:{type:null,default:"fade-transition",validator:e=>!0!==e}},"transition"),gl=(e,a)=>{let{slots:l}=a
const{transition:o,disabled:n,group:r,...i}=e,{component:s=(r?t.TransitionGroup:t.Transition),...u}=g(o)?o:{}
let c
return c=g(o)?t.mergeProps(u,JSON.parse(JSON.stringify({disabled:n,group:r})),i):t.mergeProps({name:n||!o?"":o},i),t.h(s,c,l)}
function hl(e,t){const a=e._observe?.[t.instance.$.uid]
a&&(a.observer.unobserve(e),delete e._observe[t.instance.$.uid])}const yl={mounted:function(e,t){if(!l)return
const a=t.modifiers||{},o=t.value,{handler:n,options:r}="object"==typeof o?o:{handler:o,options:{}},i=new IntersectionObserver((function(){let l=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],o=arguments.length>1?arguments[1]:void 0
const r=e._observe?.[t.instance.$.uid]
if(!r)return
const i=l.some((e=>e.isIntersecting))
!n||a.quiet&&!r.init||a.once&&!i&&!r.init||n(i,l,o),i&&a.once?hl(e,t):r.init=!0}),r)
e._observe=Object(e._observe),e._observe[t.instance.$.uid]={init:!1,observer:i},i.observe(e)},unmounted:hl},bl=yt({absolute:Boolean,alt:String,cover:Boolean,color:String,draggable:{type:[Boolean,String],default:void 0},eager:Boolean,gradient:String,lazySrc:String,options:{type:Object,default:()=>({root:void 0,rootMargin:void 0,threshold:void 0})},sizes:String,src:{type:[String,Object],default:""},crossorigin:String,referrerpolicy:String,srcset:String,position:String,...sl(),...bt(),...pl(),...fl()},"VImg"),Vl=Et()({name:"VImg",directives:{vIntersect:yl},props:bl(),emits:{loadstart:e=>!0,load:e=>!0,error:e=>!0},setup(e,a){let{emit:o,slots:n}=a
const{backgroundColorClasses:r,backgroundColorStyles:i}=vl((()=>e.color)),{roundedClasses:s}=ml(e),u=Vt("VImg"),c=t.shallowRef(""),d=t.ref(),v=t.shallowRef(e.eager?"loading":"idle"),p=t.shallowRef(),m=t.shallowRef(),g=t.computed((()=>e.src&&"object"==typeof e.src?{src:e.src.src,srcset:e.srcset||e.src.srcset,lazySrc:e.lazySrc||e.src.lazySrc,aspect:Number(e.aspectRatio||e.src.aspect||0)}:{src:e.src,srcset:e.srcset,lazySrc:e.lazySrc,aspect:Number(e.aspectRatio||0)})),h=t.computed((()=>g.value.aspect||p.value/m.value||0))
function y(a){if((!e.eager||!a)&&(!l||a||e.eager)){if(v.value="loading",g.value.lazySrc){const e=new Image
e.src=g.value.lazySrc,k(e,null)}g.value.src&&t.nextTick((()=>{o("loadstart",d.value?.currentSrc||g.value.src),setTimeout((()=>{if(!u.isUnmounted)if(d.value?.complete){if(d.value.naturalWidth||V(),"error"===v.value)return
h.value||k(d.value,null),"loading"===v.value&&b()}else h.value||k(d.value),w()}))}))}}function b(){u.isUnmounted||(w(),k(d.value),v.value="loaded",o("load",d.value?.currentSrc||g.value.src))}function V(){u.isUnmounted||(v.value="error",o("error",d.value?.currentSrc||g.value.src))}function w(){const e=d.value
e&&(c.value=e.currentSrc||e.src)}t.watch((()=>e.src),(()=>{y("idle"!==v.value)})),t.watch(h,((e,t)=>{!e&&t&&d.value&&k(d.value)})),t.onBeforeMount((()=>y()))
let S=-1
function k(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:100
const a=()=>{if(clearTimeout(S),u.isUnmounted)return
const{naturalHeight:l,naturalWidth:o}=e
l||o?(p.value=o,m.value=l):e.complete||"loading"!==v.value||null==t?(e.currentSrc.endsWith(".svg")||e.currentSrc.startsWith("data:image/svg+xml"))&&(p.value=1,m.value=1):S=window.setTimeout(a,t)}
a()}t.onBeforeUnmount((()=>{clearTimeout(S)}))
const x=t.toRef((()=>({"v-img__img--cover":e.cover,"v-img__img--contain":!e.cover}))),C=()=>{if(!g.value.src||"idle"===v.value)return null
const a=t.createElementVNode("img",{class:t.normalizeClass(["v-img__img",x.value]),style:{objectPosition:e.position},crossorigin:e.crossorigin,src:g.value.src,srcset:g.value.srcset,alt:e.alt,referrerpolicy:e.referrerpolicy,draggable:e.draggable,sizes:e.sizes,ref:d,onLoad:b,onError:V},null),l=n.sources?.()
return t.createVNode(gl,{transition:e.transition,appear:!0},{default:()=>[t.withDirectives(l?t.createElementVNode("picture",{class:"v-img__picture"},[l,a]):a,[[t.vShow,"loaded"===v.value]])]})},N=()=>t.createVNode(gl,{transition:e.transition},{default:()=>[g.value.lazySrc&&"loaded"!==v.value&&t.createElementVNode("img",{class:t.normalizeClass(["v-img__img","v-img__img--preload",x.value]),style:{objectPosition:e.position},crossorigin:e.crossorigin,src:g.value.lazySrc,alt:e.alt,referrerpolicy:e.referrerpolicy,draggable:e.draggable},null)]}),E=()=>n.placeholder?t.createVNode(gl,{transition:e.transition,appear:!0},{default:()=>[("loading"===v.value||"error"===v.value&&!n.error)&&t.createElementVNode("div",{class:"v-img__placeholder"},[n.placeholder()])]}):null,I=()=>n.error?t.createVNode(gl,{transition:e.transition,appear:!0},{default:()=>["error"===v.value&&t.createElementVNode("div",{class:"v-img__error"},[n.error()])]}):null,_=()=>e.gradient?t.createElementVNode("div",{class:"v-img__gradient",style:{backgroundImage:`linear-gradient(${e.gradient})`}},null):null,P=t.shallowRef(!1)
{const e=t.watch(h,(t=>{t&&(requestAnimationFrame((()=>{requestAnimationFrame((()=>{P.value=!0}))})),e())}))}return Ft((()=>{const a=ul.filterProps(e)
return t.withDirectives(t.createVNode(ul,t.mergeProps({class:["v-img",{"v-img--absolute":e.absolute,"v-img--booting":!P.value},r.value,s.value,e.class],style:[{width:f("auto"===e.width?p.value:e.width)},i.value,e.style]},a,{aspectRatio:h.value,"aria-label":e.alt,role:e.alt?"img":void 0}),{additional:()=>t.createElementVNode(t.Fragment,null,[t.createVNode(C,null,null),t.createVNode(N,null,null),t.createVNode(_,null,null),t.createVNode(E,null,null),t.createVNode(I,null,null)]),default:n.default}),[[yl,{handler:y,options:e.options},null,{once:!0}]])})),{currentSrc:c,image:d,state:v,naturalWidth:p,naturalHeight:m}}}),wl=yt({border:[Boolean,Number,String]},"border")
function Sl(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:wt()
return{borderClasses:t.computed((()=>{const t=e.border
return!0===t||""===t?`${a}--border`:"string"==typeof t||0===t?String(t).split(" ").map((e=>`border-${e}`)):[]}))}}const kl=yt({elevation:{type:[Number,String],validator(e){const t=parseInt(e)
return!isNaN(t)&&t>=0&&t<=24}}},"elevation")
function xl(e){return{elevationClasses:t.toRef((()=>{const a=t.isRef(e)?e.value:e.elevation
return null==a?[]:[`elevation-${a}`]}))}}const Cl=[null,"prominent","default","comfortable","compact"],Nl=yt({absolute:Boolean,collapse:Boolean,color:String,density:{type:String,default:"default",validator:e=>Cl.includes(e)},extended:Boolean,extensionHeight:{type:[Number,String],default:48},flat:Boolean,floating:Boolean,height:{type:[Number,String],default:64},image:String,title:String,...wl(),...bt(),...kl(),...pl(),...Ba({tag:"header"}),...Va()},"VToolbar"),El=Et()({name:"VToolbar",props:Nl(),setup(e,a){let{slots:l}=a
const{backgroundColorClasses:o,backgroundColorStyles:n}=vl((()=>e.color)),{borderClasses:r}=Sl(e),{elevationClasses:i}=xl(e),{roundedClasses:s}=ml(e),{themeClasses:u}=Ea(e),{rtlClasses:c}=ya(),d=t.shallowRef(!(!e.extended&&!l.extension?.())),v=t.computed((()=>parseInt(Number(e.height)+("prominent"===e.density?Number(e.height):0)-("comfortable"===e.density?8:0)-("compact"===e.density?16:0),10))),p=t.computed((()=>d.value?parseInt(Number(e.extensionHeight)+("prominent"===e.density?Number(e.extensionHeight):0)-("comfortable"===e.density?4:0)-("compact"===e.density?8:0),10):0))
return xt({VBtn:{variant:"text"}}),Ft((()=>{const a=!(!e.title&&!l.title),m=!(!l.image&&!e.image),g=l.extension?.()
return d.value=!(!e.extended&&!g),t.createVNode(e.tag,{class:t.normalizeClass(["v-toolbar",{"v-toolbar--absolute":e.absolute,"v-toolbar--collapse":e.collapse,"v-toolbar--flat":e.flat,"v-toolbar--floating":e.floating,[`v-toolbar--density-${e.density}`]:!0},o.value,r.value,i.value,s.value,u.value,c.value,e.class]),style:t.normalizeStyle([n.value,e.style])},{default:()=>[m&&t.createElementVNode("div",{key:"image",class:"v-toolbar__image"},[l.image?t.createVNode(nl,{key:"image-defaults",disabled:!e.image,defaults:{VImg:{cover:!0,src:e.image}}},l.image):t.createVNode(Vl,{key:"image-img",cover:!0,src:e.image},null)]),t.createVNode(nl,{defaults:{VTabs:{height:f(v.value)}}},{default:()=>[t.createElementVNode("div",{class:"v-toolbar__content",style:{height:f(v.value)}},[l.prepend&&t.createElementVNode("div",{class:"v-toolbar__prepend"},[l.prepend?.()]),a&&t.createVNode(Aa,{key:"title",text:e.title},{text:l.title}),l.default?.(),l.append&&t.createElementVNode("div",{class:"v-toolbar__append"},[l.append?.()])])]}),t.createVNode(nl,{defaults:{VTabs:{height:f(p.value)}}},{default:()=>[t.createVNode(al,null,{default:()=>[d.value&&t.createElementVNode("div",{class:"v-toolbar__extension",style:{height:f(p.value)}},[g])]})]})]})})),{contentHeight:v,extensionHeight:p}}}),Il=yt({scrollTarget:{type:String},scrollThreshold:{type:[String,Number],default:300}},"scroll")
function _l(){const e=t.shallowRef(!1)
t.onMounted((()=>{window.requestAnimationFrame((()=>{e.value=!0}))}))
return{ssrBootStyles:t.toRef((()=>e.value?void 0:{transition:"none !important"})),isBooted:t.readonly(e)}}const Pl=yt({scrollBehavior:String,modelValue:{type:Boolean,default:!0},location:{type:String,default:"top",validator:e=>["top","bottom"].includes(e)},...Nl(),...ta(),...Il(),height:{type:[Number,String],default:64}},"VAppBar"),Bl=Et()({name:"VAppBar",props:Pl(),emits:{"update:modelValue":e=>!0},setup(e,a){let{slots:l}=a
const o=t.ref(),n=ra(e,"modelValue"),r=t.computed((()=>{const t=new Set(e.scrollBehavior?.split(" ")??[])
return{hide:t.has("hide"),fullyHide:t.has("fully-hide"),inverted:t.has("inverted"),collapse:t.has("collapse"),elevate:t.has("elevate"),fadeImage:t.has("fade-image")}})),i=t.computed((()=>{const e=r.value
return e.hide||e.fullyHide||e.inverted||e.collapse||e.elevate||e.fadeImage||!n.value})),{currentScroll:s,scrollThreshold:u,isScrollingUp:c,scrollRatio:d}=function(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}
const{canScroll:l}=a
let o=0,n=0
const r=t.ref(null),i=t.shallowRef(0),s=t.shallowRef(0),u=t.shallowRef(0),c=t.shallowRef(!1),d=t.shallowRef(!1),v=t.computed((()=>Number(e.scrollThreshold))),p=t.computed((()=>R((v.value-i.value)/v.value||0))),m=()=>{const e=r.value
if(!e||l&&!l.value)return
o=i.value,i.value="window"in e?e.pageYOffset:e.scrollTop
const t=e instanceof Window?document.documentElement.scrollHeight:e.scrollHeight
n===t?(d.value=i.value<o,u.value=Math.abs(i.value-v.value)):n=t}
return t.watch(d,(()=>{s.value=s.value||i.value})),t.watch(c,(()=>{s.value=0})),t.onMounted((()=>{t.watch((()=>e.scrollTarget),(e=>{const t=e?document.querySelector(e):window
t?t!==r.value&&(r.value?.removeEventListener("scroll",m),r.value=t,r.value.addEventListener("scroll",m,{passive:!0})):Me(`Unable to locate element with identifier ${e}`)}),{immediate:!0})})),t.onBeforeUnmount((()=>{r.value?.removeEventListener("scroll",m)})),l&&t.watch(l,m,{immediate:!0}),{scrollThreshold:v,currentScroll:i,currentThreshold:u,isScrollActive:c,scrollRatio:p,isScrollingUp:d,savedScroll:s}}(e,{canScroll:i}),v=t.toRef((()=>r.value.hide||r.value.fullyHide)),p=t.computed((()=>e.collapse||r.value.collapse&&(r.value.inverted?d.value>0:0===d.value))),m=t.computed((()=>e.flat||r.value.fullyHide&&!n.value||r.value.elevate&&(r.value.inverted?s.value>0:0===s.value))),f=t.computed((()=>r.value.fadeImage?r.value.inverted?1-d.value:d.value:void 0)),g=t.computed((()=>{if(r.value.hide&&r.value.inverted)return 0
const e=o.value?.contentHeight??0,t=o.value?.extensionHeight??0
return v.value?s.value<u.value||r.value.fullyHide?e+t:e:e+t}))
na((()=>!!e.scrollBehavior),(()=>{t.watchEffect((()=>{v.value?r.value.inverted?n.value=s.value>u.value:n.value=c.value||s.value<u.value:n.value=!0}))}))
const{ssrBootStyles:h}=_l(),{layoutItemStyles:y}=la({id:e.name,order:t.computed((()=>parseInt(e.order,10))),position:t.toRef((()=>e.location)),layoutSize:g,elementSize:t.shallowRef(void 0),active:n,absolute:t.toRef((()=>e.absolute))})
return Ft((()=>{const a=El.filterProps(e)
return t.createVNode(El,t.mergeProps({ref:o,class:["v-app-bar",{"v-app-bar--bottom":"bottom"===e.location},e.class],style:[{...y.value,"--v-toolbar-image-opacity":f.value,height:void 0,...h.value},e.style]},a,{collapse:p.value,flat:m.value}),l)})),{}}}),Rl=[null,"default","comfortable","compact"],Al=yt({density:{type:String,default:"default",validator:e=>Rl.includes(e)}},"density")
function Tl(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:wt()
return{densityClasses:t.toRef((()=>`${a}--density-${e.density}`))}}const Dl=["elevated","flat","tonal","outlined","text","plain"]
function Fl(e,a){return t.createElementVNode(t.Fragment,null,[e&&t.createElementVNode("span",{key:"overlay",class:t.normalizeClass(`${a}__overlay`)},null),t.createElementVNode("span",{key:"underlay",class:t.normalizeClass(`${a}__underlay`)},null)])}const zl=yt({color:String,variant:{type:String,default:"elevated",validator:e=>Dl.includes(e)}},"variant")
function $l(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:wt()
const l=t.toRef((()=>{const{variant:l}=t.toValue(e)
return`${a}--variant-${l}`})),{colorClasses:o,colorStyles:n}=cl((()=>{const{variant:a,color:l}=t.toValue(e)
return{[["elevated","flat"].includes(a)?"background":"text"]:l}}))
return{colorClasses:o,colorStyles:n,variantClasses:l}}const Ml=yt({baseColor:String,divided:Boolean,...wl(),...bt(),...Al(),...kl(),...pl(),...Ba(),...Va(),...zl()},"VBtnGroup"),Ol=Et()({name:"VBtnGroup",props:Ml(),setup(e,a){let{slots:l}=a
const{themeClasses:o}=Ea(e),{densityClasses:n}=Tl(e),{borderClasses:r}=Sl(e),{elevationClasses:i}=xl(e),{roundedClasses:s}=ml(e)
xt({VBtn:{height:"auto",baseColor:t.toRef((()=>e.baseColor)),color:t.toRef((()=>e.color)),density:t.toRef((()=>e.density)),flat:!0,variant:t.toRef((()=>e.variant))}}),Ft((()=>t.createVNode(e.tag,{class:t.normalizeClass(["v-btn-group",{"v-btn-group--divided":e.divided},o.value,r.value,n.value,i.value,s.value,e.class]),style:t.normalizeStyle(e.style)},l)))}}),Ll=yt({modelValue:{type:null,default:void 0},multiple:Boolean,mandatory:[Boolean,String],max:Number,selectedClass:String,disabled:Boolean},"group"),jl=yt({value:null,disabled:Boolean,selectedClass:String},"group-item")
function Hl(e,a){let l=!(arguments.length>2&&void 0!==arguments[2])||arguments[2]
const o=Vt("useGroupItem")
if(!o)throw new Error("[Vuetify] useGroupItem composable must be used inside a component setup function")
const n=t.useId()
t.provide(Symbol.for(`${a.description}:id`),n)
const r=t.inject(a,null)
if(!r){if(!l)return r
throw new Error(`[Vuetify] Could not find useGroup injection with symbol ${a.description}`)}const i=t.toRef((()=>e.value)),s=t.computed((()=>!(!r.disabled.value&&!e.disabled)))
r.register({id:n,value:i,disabled:s},o),t.onBeforeUnmount((()=>{r.unregister(n)}))
const u=t.computed((()=>r.isSelected(n))),c=t.computed((()=>r.items.value[0].id===n)),d=t.computed((()=>r.items.value[r.items.value.length-1].id===n)),v=t.computed((()=>u.value&&[r.selectedClass.value,e.selectedClass]))
return t.watch(u,(e=>{o.emit("group:selected",{value:e})}),{flush:"sync"}),{id:n,isSelected:u,isFirst:c,isLast:d,toggle:()=>r.select(n,!u.value),select:e=>r.select(n,e),selectedClass:v,value:i,disabled:s,group:r}}function Wl(e,a){let l=!1
const o=t.reactive([]),n=ra(e,"modelValue",[],(e=>null==e?[]:Ul(o,B(e))),(t=>{const a=function(e,t){const a=[]
return t.forEach((t=>{const l=e.findIndex((e=>e.id===t))
if(~l){const t=e[l]
a.push(null!=t.value?t.value:l)}})),a}(o,t)
return e.multiple?a:a[0]})),r=Vt("useGroup")
function i(){const t=o.find((e=>!e.disabled))
t&&"force"===e.mandatory&&!n.value.length&&(n.value=[t.id])}function s(t){if(e.multiple&&Me('This method is not supported when using "multiple" prop'),n.value.length){const e=n.value[0],a=o.findIndex((t=>t.id===e))
let l=(a+t)%o.length,r=o[l]
for(;r.disabled&&l!==a;)l=(l+t)%o.length,r=o[l]
if(r.disabled)return
n.value=[o[l].id]}else{const e=o.find((e=>!e.disabled))
e&&(n.value=[e.id])}}t.onMounted((()=>{i()})),t.onBeforeUnmount((()=>{l=!0})),t.onUpdated((()=>{for(let e=0;e<o.length;e++)o[e].useIndexAsValue&&(o[e].value=e)}))
const u={register:function(e,l){const n=e,i=O(Symbol.for(`${a.description}:id`),r?.vnode).indexOf(l)
null==t.unref(n.value)&&(n.value=i,n.useIndexAsValue=!0),i>-1?o.splice(i,0,n):o.push(n)},unregister:function(e){if(l)return
i()
const t=o.findIndex((t=>t.id===e))
o.splice(t,1)},selected:n,select:function(t,a){const l=o.find((e=>e.id===t))
if(!a||!l?.disabled)if(e.multiple){const l=n.value.slice(),o=l.findIndex((e=>e===t)),r=~o
if(a=a??!r,r&&e.mandatory&&l.length<=1)return
if(!r&&null!=e.max&&l.length+1>e.max)return
o<0&&a?l.push(t):o>=0&&!a&&l.splice(o,1),n.value=l}else{const l=n.value.includes(t)
if(e.mandatory&&l)return
n.value=a??!l?[t]:[]}},disabled:t.toRef((()=>e.disabled)),prev:()=>s(o.length-1),next:()=>s(1),isSelected:e=>n.value.includes(e),selectedClass:t.toRef((()=>e.selectedClass)),items:t.toRef((()=>o)),getItemIndex:e=>function(e,t){const a=Ul(e,[t])
return a.length?e.findIndex((e=>e.id===a[0])):-1}(o,e)}
return t.provide(a,u),u}function Ul(e,t){const a=[]
return t.forEach((t=>{const l=e.find((e=>d(t,e.value))),o=e[t]
null!=l?.value?a.push(l.id):null!=o&&a.push(o.id)})),a}const Yl=Symbol.for("vuetify:v-btn-toggle"),Gl=yt({...Ml(),...Ll()},"VBtnToggle"),ql=Et()({name:"VBtnToggle",props:Gl(),emits:{"update:modelValue":e=>!0},setup(e,a){let{slots:l}=a
const{isSelected:o,next:n,prev:r,select:i,selected:s}=Wl(e,Yl)
return Ft((()=>{const a=Ol.filterProps(e)
return t.createVNode(Ol,t.mergeProps({class:["v-btn-toggle",e.class]},a,{style:e.style}),{default:()=>[l.default?.({isSelected:o,next:n,prev:r,select:i,selected:s})]})})),{next:n,prev:r,select:i}}}),Kl=["x-small","small","default","large","x-large"],Xl=yt({size:{type:[String,Number],default:"default"}},"size")
function Zl(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:wt()
return W((()=>{const a=e.size
let l,o
return U(Kl,a)?l=`${t}--size-${a}`:a&&(o={width:f(a),height:f(a)}),{sizeClasses:l,sizeStyles:o}}))}const Ql=yt({color:String,disabled:Boolean,start:Boolean,end:Boolean,icon:zt,opacity:[String,Number],...bt(),...Xl(),...Ba({tag:"i"}),...Va()},"VIcon"),Jl=Et()({name:"VIcon",props:Ql(),setup(e,a){let{attrs:l,slots:o}=a
const n=t.shallowRef(),{themeClasses:r}=Ia(),{iconData:i}=(e=>{const a=t.inject($t)
if(!a)throw new Error("Missing Vuetify Icons provide!")
return{iconData:t.computed((()=>{const l=t.toValue(e)
if(!l)return{component:Ot}
let o=l
if("string"==typeof o&&(o=o.trim(),o.startsWith("$")&&(o=a.aliases?.[o.slice(1)])),o||Me(`Could not find aliased icon "${l}"`),Array.isArray(o))return{component:Lt,icon:o}
if("string"!=typeof o)return{component:Ot,icon:o}
const n=Object.keys(a.sets).find((e=>"string"==typeof o&&o.startsWith(`${e}:`))),r=n?o.slice(n.length+1):o
return{component:a.sets[n??a.defaultSet].component,icon:r}}))}})((()=>n.value||e.icon)),{sizeClasses:s}=Zl(e),{textColorClasses:u,textColorStyles:c}=dl((()=>e.color))
return Ft((()=>{const a=o.default?.()
a&&(n.value=$(a).filter((e=>e.type===t.Text&&e.children&&"string"==typeof e.children))[0]?.children)
const d=!(!l.onClick&&!l.onClickOnce)
return t.createVNode(i.value.component,{tag:e.tag,icon:i.value.icon,class:t.normalizeClass(["v-icon","notranslate",r.value,s.value,u.value,{"v-icon--clickable":d,"v-icon--disabled":e.disabled,"v-icon--start":e.start,"v-icon--end":e.end},e.class]),style:t.normalizeStyle([{"--v-icon-opacity":e.opacity},s.value?void 0:{fontSize:f(e.size),height:f(e.size),width:f(e.size)},c.value,e.style]),role:d?"button":void 0,"aria-hidden":!d,tabindex:d?e.disabled?-1:0:void 0},{default:()=>[a]})})),{}}})
function eo(e,a){const o=t.ref(),n=t.shallowRef(!1)
if(l){const e=new IntersectionObserver((e=>{n.value=!!e.find((e=>e.isIntersecting))}),a)
t.onBeforeUnmount((()=>{e.disconnect()})),t.watch(o,((t,a)=>{a&&(e.unobserve(a),n.value=!1),t&&e.observe(t)}),{flush:"post"})}return{intersectionRef:o,isIntersecting:n}}const to=yt({bgColor:String,color:String,indeterminate:[Boolean,String],modelValue:{type:[Number,String],default:0},rotate:{type:[Number,String],default:0},width:{type:[Number,String],default:4},...bt(),...Xl(),...Ba({tag:"div"}),...Va()},"VProgressCircular"),ao=Et()({name:"VProgressCircular",props:to(),setup(e,a){let{slots:l}=a
const o=2*Math.PI*20,n=t.ref(),{themeClasses:r}=Ea(e),{sizeClasses:i,sizeStyles:s}=Zl(e),{textColorClasses:u,textColorStyles:c}=dl((()=>e.color)),{textColorClasses:d,textColorStyles:v}=dl((()=>e.bgColor)),{intersectionRef:p,isIntersecting:m}=eo(),{resizeRef:g,contentRect:h}=Zt(),y=t.toRef((()=>R(parseFloat(e.modelValue),0,100))),b=t.toRef((()=>Number(e.width))),V=t.toRef((()=>s.value?Number(e.size):h.value?h.value.width:Math.max(b.value,32))),w=t.toRef((()=>20/(1-b.value/V.value)*2)),S=t.toRef((()=>b.value/V.value*w.value)),k=t.toRef((()=>f((100-y.value)/100*o)))
return t.watchEffect((()=>{p.value=n.value,g.value=n.value})),Ft((()=>t.createVNode(e.tag,{ref:n,class:t.normalizeClass(["v-progress-circular",{"v-progress-circular--indeterminate":!!e.indeterminate,"v-progress-circular--visible":m.value,"v-progress-circular--disable-shrink":"disable-shrink"===e.indeterminate},r.value,i.value,u.value,e.class]),style:t.normalizeStyle([s.value,c.value,e.style]),role:"progressbar","aria-valuemin":"0","aria-valuemax":"100","aria-valuenow":e.indeterminate?void 0:y.value},{default:()=>[t.createElementVNode("svg",{style:{transform:`rotate(calc(-90deg + ${Number(e.rotate)}deg))`},xmlns:"http://www.w3.org/2000/svg",viewBox:`0 0 ${w.value} ${w.value}`},[t.createElementVNode("circle",{class:t.normalizeClass(["v-progress-circular__underlay",d.value]),style:t.normalizeStyle(v.value),fill:"transparent",cx:"50%",cy:"50%",r:20,"stroke-width":S.value,"stroke-dasharray":o,"stroke-dashoffset":0},null),t.createElementVNode("circle",{class:"v-progress-circular__overlay",fill:"transparent",cx:"50%",cy:"50%",r:20,"stroke-width":S.value,"stroke-dasharray":o,"stroke-dashoffset":k.value},null)]),l.default&&t.createElementVNode("div",{class:"v-progress-circular__content"},[l.default({value:y.value})])]}))),{}}}),lo={center:"center",top:"bottom",bottom:"top",left:"right",right:"left"},oo=yt({location:String},"location")
function no(e){let a=arguments.length>1&&void 0!==arguments[1]&&arguments[1],l=arguments.length>2?arguments[2]:void 0
const{isRtl:o}=ya(),n=t.computed((()=>{if(!e.location)return{}
const{side:t,align:n}=ue(e.location.split(" ").length>1?e.location:`${e.location} center`,o.value)
function r(e){return l?l(e):0}const i={}
return"center"!==t&&(a?i[lo[t]]=`calc(100% - ${r(t)}px)`:i[t]=0),"center"!==n?a?i[lo[n]]=`calc(100% - ${r(n)}px)`:i[n]=0:("center"===t?i.top=i.left="50%":i[{top:"left",bottom:"left",left:"top",right:"top"}[t]]="50%",i.transform={top:"translateX(-50%)",bottom:"translateX(-50%)",left:"translateY(-50%)",right:"translateY(-50%)",center:"translate(-50%, -50%)"}[t]),i}))
return{locationStyles:n}}const ro=yt({absolute:Boolean,active:{type:Boolean,default:!0},bgColor:String,bgOpacity:[Number,String],bufferValue:{type:[Number,String],default:0},bufferColor:String,bufferOpacity:[Number,String],clickable:Boolean,color:String,height:{type:[Number,String],default:4},indeterminate:Boolean,max:{type:[Number,String],default:100},modelValue:{type:[Number,String],default:0},opacity:[Number,String],reverse:Boolean,stream:Boolean,striped:Boolean,roundedBar:Boolean,...bt(),...oo({location:"top"}),...pl(),...Ba(),...Va()},"VProgressLinear"),io=Et()({name:"VProgressLinear",props:ro(),emits:{"update:modelValue":e=>!0},setup(e,l){let{slots:o}=l
const n=ra(e,"modelValue"),{isRtl:r,rtlClasses:i}=ya(),{themeClasses:s}=Ea(e),{locationStyles:u}=no(e),{textColorClasses:c,textColorStyles:d}=dl((()=>e.color)),{backgroundColorClasses:v,backgroundColorStyles:p}=vl((()=>e.bgColor||e.color)),{backgroundColorClasses:m,backgroundColorStyles:g}=vl((()=>e.bufferColor||e.bgColor||e.color)),{backgroundColorClasses:h,backgroundColorStyles:y}=vl((()=>e.color)),{roundedClasses:b}=ml(e),{intersectionRef:V,isIntersecting:w}=eo(),S=t.computed((()=>parseFloat(e.max))),k=t.computed((()=>parseFloat(e.height))),x=t.computed((()=>R(parseFloat(e.bufferValue)/S.value*100,0,100))),C=t.computed((()=>R(parseFloat(n.value)/S.value*100,0,100))),N=t.computed((()=>r.value!==e.reverse)),E=t.computed((()=>e.indeterminate?"fade-transition":"slide-x-transition")),I=a&&window.matchMedia?.("(forced-colors: active)").matches
function _(e){if(!V.value)return
const{left:t,right:a,width:l}=V.value.getBoundingClientRect(),o=N.value?l-e.clientX+(a-l):e.clientX-t
n.value=Math.round(o/l*S.value)}return Ft((()=>t.createVNode(e.tag,{ref:V,class:t.normalizeClass(["v-progress-linear",{"v-progress-linear--absolute":e.absolute,"v-progress-linear--active":e.active&&w.value,"v-progress-linear--reverse":N.value,"v-progress-linear--rounded":e.rounded,"v-progress-linear--rounded-bar":e.roundedBar,"v-progress-linear--striped":e.striped},b.value,s.value,i.value,e.class]),style:t.normalizeStyle([{bottom:"bottom"===e.location?0:void 0,top:"top"===e.location?0:void 0,height:e.active?f(k.value):0,"--v-progress-linear-height":f(k.value),...e.absolute?u.value:{}},e.style]),role:"progressbar","aria-hidden":e.active?"false":"true","aria-valuemin":"0","aria-valuemax":e.max,"aria-valuenow":e.indeterminate?void 0:Math.min(parseFloat(n.value),S.value),onClick:e.clickable&&_},{default:()=>[e.stream&&t.createElementVNode("div",{key:"stream",class:t.normalizeClass(["v-progress-linear__stream",c.value]),style:{...d.value,[N.value?"left":"right"]:f(-k.value),borderTop:`${f(k.value/2)} dotted`,opacity:parseFloat(e.bufferOpacity),top:`calc(50% - ${f(k.value/4)})`,width:f(100-x.value,"%"),"--v-progress-linear-stream-to":f(k.value*(N.value?1:-1))}},null),t.createElementVNode("div",{class:t.normalizeClass(["v-progress-linear__background",I?void 0:v.value]),style:t.normalizeStyle([p.value,{opacity:parseFloat(e.bgOpacity),width:e.stream?0:void 0}])},null),t.createElementVNode("div",{class:t.normalizeClass(["v-progress-linear__buffer",I?void 0:m.value]),style:t.normalizeStyle([g.value,{opacity:parseFloat(e.bufferOpacity),width:f(x.value,"%")}])},null),t.createVNode(t.Transition,{name:E.value},{default:()=>[e.indeterminate?t.createElementVNode("div",{class:"v-progress-linear__indeterminate"},[["long","short"].map((e=>t.createElementVNode("div",{key:e,class:t.normalizeClass(["v-progress-linear__indeterminate",e,I?void 0:h.value]),style:t.normalizeStyle(y.value)},null)))]):t.createElementVNode("div",{class:t.normalizeClass(["v-progress-linear__determinate",I?void 0:h.value]),style:t.normalizeStyle([y.value,{width:f(C.value,"%")}])},null)]}),o.default&&t.createElementVNode("div",{class:"v-progress-linear__content"},[o.default({value:C.value,buffer:x.value})])]}))),{}}}),so=yt({loading:[Boolean,String]},"loader")
function uo(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:wt()
return{loaderClasses:t.toRef((()=>({[`${a}--loading`]:e.loading})))}}function co(e,a){let{slots:l}=a
return t.createElementVNode("div",{class:t.normalizeClass(`${e.name}__loader`)},[l.default?.({color:e.color,isActive:e.active})||t.createVNode(io,{absolute:e.absolute,active:e.active,color:e.color,height:"2",indeterminate:!0},null)])}const vo=["static","relative","fixed","absolute","sticky"],po=yt({position:{type:String,validator:e=>vo.includes(e)}},"position")
function mo(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:wt()
return{positionClasses:t.toRef((()=>e.position?`${a}--${e.position}`:void 0))}}function fo(){return Vt("useRouter")?.proxy?.$router}function go(e,a){const l=t.resolveDynamicComponent("RouterLink"),o=t.toRef((()=>!(!e.href&&!e.to))),n=t.computed((()=>o?.value||q(a,"click")||q(e,"click")))
if("string"==typeof l||!("useLink"in l)){const a=t.toRef((()=>e.href))
return{isLink:o,isClickable:n,href:a,linkProps:t.reactive({href:a})}}const r=l.useLink({to:t.toRef((()=>e.to||"")),replace:t.toRef((()=>e.replace))}),i=t.computed((()=>e.to?r:void 0)),s=function(){const e=Vt("useRoute")
return t.computed((()=>e?.proxy?.$route))}(),u=t.computed((()=>!!i.value&&(e.exact?s.value?i.value.isExactActive?.value&&d(i.value.route.value.query,s.value.query):i.value.isExactActive?.value??!1:i.value.isActive?.value??!1))),c=t.computed((()=>e.to?i.value?.route.value.href:e.href))
return{isLink:o,isClickable:n,isActive:u,route:i.value?.route,navigate:i.value?.navigate,href:c,linkProps:t.reactive({href:c,"aria-current":t.toRef((()=>u.value?"page":void 0))})}}const ho=yt({href:String,replace:Boolean,to:[String,Object],exact:Boolean},"router")
let yo=!1
const bo=Symbol("rippleStop"),Vo=80
function wo(e,t){e.style.transform=t,e.style.webkitTransform=t}function So(e){return"TouchEvent"===e.constructor.name}function ko(e){return"KeyboardEvent"===e.constructor.name}const xo={show(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{}
if(!t?._ripple?.enabled)return
const l=document.createElement("span"),o=document.createElement("span")
l.appendChild(o),l.className="v-ripple__container",a.class&&(l.className+=` ${a.class}`)
const{radius:n,scale:r,x:i,y:s,centerX:u,centerY:c}=function(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},l=0,o=0
if(!ko(e)){const a=t.getBoundingClientRect(),n=So(e)?e.touches[e.touches.length-1]:e
l=n.clientX-a.left,o=n.clientY-a.top}let n=0,r=.3
t._ripple?.circle?(r=.15,n=t.clientWidth/2,n=a.center?n:n+Math.sqrt((l-n)**2+(o-n)**2)/4):n=Math.sqrt(t.clientWidth**2+t.clientHeight**2)/2
const i=(t.clientWidth-2*n)/2+"px",s=(t.clientHeight-2*n)/2+"px"
return{radius:n,scale:r,x:a.center?i:l-n+"px",y:a.center?s:o-n+"px",centerX:i,centerY:s}}(e,t,a),d=2*n+"px"
o.className="v-ripple__animation",o.style.width=d,o.style.height=d,t.appendChild(l)
const v=window.getComputedStyle(t)
v&&"static"===v.position&&(t.style.position="relative",t.dataset.previousPosition="static"),o.classList.add("v-ripple__animation--enter"),o.classList.add("v-ripple__animation--visible"),wo(o,`translate(${i}, ${s}) scale3d(${r},${r},${r})`),o.dataset.activated=String(performance.now()),requestAnimationFrame((()=>{requestAnimationFrame((()=>{o.classList.remove("v-ripple__animation--enter"),o.classList.add("v-ripple__animation--in"),wo(o,`translate(${u}, ${c}) scale3d(1,1,1)`)}))}))},hide(e){if(!e?._ripple?.enabled)return
const t=e.getElementsByClassName("v-ripple__animation")
if(0===t.length)return
const a=t[t.length-1]
if(a.dataset.isHiding)return
a.dataset.isHiding="true"
const l=performance.now()-Number(a.dataset.activated),o=Math.max(250-l,0)
setTimeout((()=>{a.classList.remove("v-ripple__animation--in"),a.classList.add("v-ripple__animation--out"),setTimeout((()=>{1===e.getElementsByClassName("v-ripple__animation").length&&e.dataset.previousPosition&&(e.style.position=e.dataset.previousPosition,delete e.dataset.previousPosition),a.parentNode?.parentNode===e&&e.removeChild(a.parentNode)}),300)}),o)}}
function Co(e){return void 0===e||!!e}function No(e){const t={},a=e.currentTarget
if(a?._ripple&&!a._ripple.touched&&!e[bo]){if(e[bo]=!0,So(e))a._ripple.touched=!0,a._ripple.isTouch=!0
else if(a._ripple.isTouch)return
if(t.center=a._ripple.centered||ko(e),a._ripple.class&&(t.class=a._ripple.class),So(e)){if(a._ripple.showTimerCommit)return
a._ripple.showTimerCommit=()=>{xo.show(e,a,t)},a._ripple.showTimer=window.setTimeout((()=>{a?._ripple?.showTimerCommit&&(a._ripple.showTimerCommit(),a._ripple.showTimerCommit=null)}),Vo)}else xo.show(e,a,t)}}function Eo(e){e[bo]=!0}function Io(e){const t=e.currentTarget
if(t?._ripple){if(window.clearTimeout(t._ripple.showTimer),"touchend"===e.type&&t._ripple.showTimerCommit)return t._ripple.showTimerCommit(),t._ripple.showTimerCommit=null,void(t._ripple.showTimer=window.setTimeout((()=>{Io(e)})))
window.setTimeout((()=>{t._ripple&&(t._ripple.touched=!1)})),xo.hide(t)}}function _o(e){const t=e.currentTarget
t?._ripple&&(t._ripple.showTimerCommit&&(t._ripple.showTimerCommit=null),window.clearTimeout(t._ripple.showTimer))}let Po=!1
function Bo(e){Po||e.keyCode!==b.enter&&e.keyCode!==b.space||(Po=!0,No(e))}function Ro(e){Po=!1,Io(e)}function Ao(e){Po&&(Po=!1,Io(e))}function To(e,t,a){const{value:l,modifiers:o}=t,n=Co(l)
if(n||xo.hide(e),e._ripple=e._ripple??{},e._ripple.enabled=n,e._ripple.centered=o.center,e._ripple.circle=o.circle,g(l)&&l.class&&(e._ripple.class=l.class),n&&!a){if(o.stop)return e.addEventListener("touchstart",Eo,{passive:!0}),void e.addEventListener("mousedown",Eo)
e.addEventListener("touchstart",No,{passive:!0}),e.addEventListener("touchend",Io,{passive:!0}),e.addEventListener("touchmove",_o,{passive:!0}),e.addEventListener("touchcancel",Io),e.addEventListener("mousedown",No),e.addEventListener("mouseup",Io),e.addEventListener("mouseleave",Io),e.addEventListener("keydown",Bo),e.addEventListener("keyup",Ro),e.addEventListener("blur",Ao),e.addEventListener("dragstart",Io,{passive:!0})}else!n&&a&&Do(e)}function Do(e){e.removeEventListener("mousedown",No),e.removeEventListener("touchstart",No),e.removeEventListener("touchend",Io),e.removeEventListener("touchmove",_o),e.removeEventListener("touchcancel",Io),e.removeEventListener("mouseup",Io),e.removeEventListener("mouseleave",Io),e.removeEventListener("keydown",Bo),e.removeEventListener("keyup",Ro),e.removeEventListener("dragstart",Io),e.removeEventListener("blur",Ao)}const Fo={mounted:function(e,t){To(e,t,!1)},unmounted:function(e){delete e._ripple,Do(e)},updated:function(e,t){if(t.value===t.oldValue)return
To(e,t,Co(t.oldValue))}},zo=yt({active:{type:Boolean,default:void 0},activeColor:String,baseColor:String,symbol:{type:null,default:Yl},flat:Boolean,icon:[Boolean,String,Function,Object],prependIcon:zt,appendIcon:zt,block:Boolean,readonly:Boolean,slim:Boolean,stacked:Boolean,ripple:{type:[Boolean,Object],default:!0},text:{type:[String,Number,Boolean],default:void 0},...wl(),...bt(),...Al(),...rl(),...kl(),...jl(),...so(),...oo(),...po(),...pl(),...ho(),...Xl(),...Ba({tag:"button"}),...Va(),...zl({variant:"elevated"})},"VBtn"),$o=Et()({name:"VBtn",props:zo(),emits:{"group:selected":e=>!0},setup(e,a){let{attrs:l,slots:o}=a
const{themeClasses:n}=Ea(e),{borderClasses:r}=Sl(e),{densityClasses:i}=Tl(e),{dimensionStyles:s}=il(e),{elevationClasses:u}=xl(e),{loaderClasses:c}=uo(e),{locationStyles:d}=no(e),{positionClasses:v}=mo(e),{roundedClasses:p}=ml(e),{sizeClasses:m,sizeStyles:f}=Zl(e),g=Hl(e,e.symbol,!1),h=go(e,l),y=t.computed((()=>void 0!==e.active?e.active:h.isLink.value?h.isActive?.value:g?.isSelected.value)),b=t.toRef((()=>y.value?e.activeColor??e.color:e.color)),V=t.computed((()=>({color:g?.isSelected.value&&(!h.isLink.value||h.isActive?.value)||!g||h.isActive?.value?b.value??e.baseColor:e.baseColor,variant:e.variant}))),{colorClasses:w,colorStyles:S,variantClasses:k}=$l(V),x=t.computed((()=>g?.disabled.value||e.disabled)),C=t.toRef((()=>"elevated"===e.variant&&!(e.disabled||e.flat||e.border))),N=t.computed((()=>{if(void 0!==e.value&&"symbol"!=typeof e.value)return Object(e.value)===e.value?JSON.stringify(e.value,null,0):e.value}))
function E(e){x.value||h.isLink.value&&(e.metaKey||e.ctrlKey||e.shiftKey||0!==e.button||"_blank"===l.target)||(h.navigate?.(e),g?.toggle())}return function(e,a){t.watch((()=>e.isActive?.value),(l=>{e.isLink.value&&l&&a&&t.nextTick((()=>{a(!0)}))}),{immediate:!0})}(h,g?.select),Ft((()=>{const a=h.isLink.value?"a":e.tag,l=!(!e.prependIcon&&!o.prepend),b=!(!e.appendIcon&&!o.append),V=!(!e.icon||!0===e.icon)
return t.withDirectives(t.createVNode(a,t.mergeProps({type:"a"===a?void 0:"button",class:["v-btn",g?.selectedClass.value,{"v-btn--active":y.value,"v-btn--block":e.block,"v-btn--disabled":x.value,"v-btn--elevated":C.value,"v-btn--flat":e.flat,"v-btn--icon":!!e.icon,"v-btn--loading":e.loading,"v-btn--readonly":e.readonly,"v-btn--slim":e.slim,"v-btn--stacked":e.stacked},n.value,r.value,w.value,i.value,u.value,c.value,v.value,p.value,m.value,k.value,e.class],style:[S.value,s.value,d.value,f.value,e.style],"aria-busy":!!e.loading||void 0,disabled:x.value||void 0,tabindex:e.loading||e.readonly?-1:void 0,onClick:E,value:N.value},h.linkProps),{default:()=>[Fl(!0,"v-btn"),!e.icon&&l&&t.createElementVNode("span",{key:"prepend",class:"v-btn__prepend"},[o.prepend?t.createVNode(nl,{key:"prepend-defaults",disabled:!e.prependIcon,defaults:{VIcon:{icon:e.prependIcon}}},o.prepend):t.createVNode(Jl,{key:"prepend-icon",icon:e.prependIcon},null)]),t.createElementVNode("span",{class:"v-btn__content","data-no-activator":""},[!o.default&&V?t.createVNode(Jl,{key:"content-icon",icon:e.icon},null):t.createVNode(nl,{key:"content-defaults",disabled:!V,defaults:{VIcon:{icon:e.icon}}},{default:()=>[o.default?.()??t.toDisplayString(e.text)]})]),!e.icon&&b&&t.createElementVNode("span",{key:"append",class:"v-btn__append"},[o.append?t.createVNode(nl,{key:"append-defaults",disabled:!e.appendIcon,defaults:{VIcon:{icon:e.appendIcon}}},o.append):t.createVNode(Jl,{key:"append-icon",icon:e.appendIcon},null)]),!!e.loading&&t.createElementVNode("span",{key:"loader",class:"v-btn__loader"},[o.loader?.()??t.createVNode(ao,{color:"boolean"==typeof e.loading?void 0:e.loading,indeterminate:!0,width:"2"},null)])]}),[[Fo,!x.value&&e.ripple,"",{center:!!e.icon}]])})),{group:g}}}),Mo=yt({...zo({icon:"$menu",variant:"text"})},"VAppBarNavIcon"),Oo=Et()({name:"VAppBarNavIcon",props:Mo(),setup(e,a){let{slots:l}=a
return Ft((()=>t.createVNode($o,t.mergeProps(e,{class:["v-app-bar-nav-icon"]}),l))),{}}}),Lo=Et()({name:"VAppBarTitle",props:Ra(),setup(e,a){let{slots:l}=a
return Ft((()=>t.createVNode(Aa,t.mergeProps(e,{class:"v-app-bar-title"}),l))),{}}}),jo=It("v-alert-title"),Ho=["success","info","warning","error"],Wo=yt({border:{type:[Boolean,String],validator:e=>"boolean"==typeof e||["top","end","bottom","start"].includes(e)},borderColor:String,closable:Boolean,closeIcon:{type:zt,default:"$close"},closeLabel:{type:String,default:"$vuetify.close"},icon:{type:[Boolean,String,Function,Object],default:null},modelValue:{type:Boolean,default:!0},prominent:Boolean,title:String,text:String,type:{type:String,validator:e=>Ho.includes(e)},...bt(),...Al(),...rl(),...kl(),...oo(),...po(),...pl(),...Ba(),...Va(),...zl({variant:"flat"})},"VAlert"),Uo=Et()({name:"VAlert",props:Wo(),emits:{"click:close":e=>!0,"update:modelValue":e=>!0},setup(e,a){let{emit:l,slots:o}=a
const n=ra(e,"modelValue"),r=t.toRef((()=>{if(!1!==e.icon)return e.type?e.icon??`$${e.type}`:e.icon})),{themeClasses:i}=Ea(e),{colorClasses:s,colorStyles:u,variantClasses:c}=$l((()=>({color:e.color??e.type,variant:e.variant}))),{densityClasses:d}=Tl(e),{dimensionStyles:v}=il(e),{elevationClasses:p}=xl(e),{locationStyles:m}=no(e),{positionClasses:f}=mo(e),{roundedClasses:g}=ml(e),{textColorClasses:h,textColorStyles:y}=dl((()=>e.borderColor)),{t:b}=ga(),V=t.toRef((()=>({"aria-label":b(e.closeLabel),onClick(e){n.value=!1,l("click:close",e)}})))
return()=>{const a=!(!o.prepend&&!r.value),l=!(!o.title&&!e.title),b=!(!o.close&&!e.closable)
return n.value&&t.createVNode(e.tag,{class:t.normalizeClass(["v-alert",e.border&&{"v-alert--border":!!e.border,[`v-alert--border-${!0===e.border?"start":e.border}`]:!0},{"v-alert--prominent":e.prominent},i.value,s.value,d.value,p.value,f.value,g.value,c.value,e.class]),style:t.normalizeStyle([u.value,v.value,m.value,e.style]),role:"alert"},{default:()=>[Fl(!1,"v-alert"),e.border&&t.createElementVNode("div",{key:"border",class:t.normalizeClass(["v-alert__border",h.value]),style:t.normalizeStyle(y.value)},null),a&&t.createElementVNode("div",{key:"prepend",class:"v-alert__prepend"},[o.prepend?t.createVNode(nl,{key:"prepend-defaults",disabled:!r.value,defaults:{VIcon:{density:e.density,icon:r.value,size:e.prominent?44:28}}},o.prepend):t.createVNode(Jl,{key:"prepend-icon",density:e.density,icon:r.value,size:e.prominent?44:28},null)]),t.createElementVNode("div",{class:"v-alert__content"},[l&&t.createVNode(jo,{key:"title"},{default:()=>[o.title?.()??e.title]}),o.text?.()??e.text,o.default?.()]),o.append&&t.createElementVNode("div",{key:"append",class:"v-alert__append"},[o.append()]),b&&t.createElementVNode("div",{key:"close",class:"v-alert__close"},[o.close?t.createVNode(nl,{key:"close-defaults",defaults:{VBtn:{icon:e.closeIcon,size:"x-small",variant:"text"}}},{default:()=>[o.close?.({props:V.value})]}):t.createVNode($o,t.mergeProps({key:"close-btn",icon:e.closeIcon,size:"x-small",variant:"text"},V.value),null)])]})}}}),Yo=yt({start:Boolean,end:Boolean,icon:zt,image:String,text:String,...wl(),...bt(),...Al(),...pl(),...Xl(),...Ba(),...Va(),...zl({variant:"flat"})},"VAvatar"),Go=Et()({name:"VAvatar",props:Yo(),setup(e,a){let{slots:l}=a
const{themeClasses:o}=Ea(e),{borderClasses:n}=Sl(e),{colorClasses:r,colorStyles:i,variantClasses:s}=$l(e),{densityClasses:u}=Tl(e),{roundedClasses:c}=ml(e),{sizeClasses:d,sizeStyles:v}=Zl(e)
return Ft((()=>t.createVNode(e.tag,{class:t.normalizeClass(["v-avatar",{"v-avatar--start":e.start,"v-avatar--end":e.end},o.value,n.value,r.value,u.value,c.value,d.value,s.value,e.class]),style:t.normalizeStyle([i.value,v.value,e.style])},{default:()=>[l.default?t.createVNode(nl,{key:"content-defaults",defaults:{VImg:{cover:!0,src:e.image},VIcon:{icon:e.icon}}},{default:()=>[l.default()]}):e.image?t.createVNode(Vl,{key:"image",src:e.image,alt:"",cover:!0},null):e.icon?t.createVNode(Jl,{key:"icon",icon:e.icon},null):e.text,Fl(!1,"v-avatar")]}))),{}}}),qo=yt({text:String,onClick:G(),...bt(),...Va()},"VLabel"),Ko=Et()({name:"VLabel",props:qo(),setup(e,a){let{slots:l}=a
return Ft((()=>t.createElementVNode("label",{class:t.normalizeClass(["v-label",{"v-label--clickable":!!e.onClick},e.class]),style:t.normalizeStyle(e.style),onClick:e.onClick},[e.text,l.default?.()]))),{}}}),Xo=Symbol.for("vuetify:selection-control-group"),Zo=yt({color:String,disabled:{type:Boolean,default:null},defaultsTarget:String,error:Boolean,id:String,inline:Boolean,falseIcon:zt,trueIcon:zt,ripple:{type:[Boolean,Object],default:!0},multiple:{type:Boolean,default:null},name:String,readonly:{type:Boolean,default:null},modelValue:null,type:String,valueComparator:{type:Function,default:d},...bt(),...Al(),...Va()},"SelectionControlGroup"),Qo=yt({...Zo({defaultsTarget:"VSelectionControl"})},"VSelectionControlGroup"),Jo=Et()({name:"VSelectionControlGroup",props:Qo(),emits:{"update:modelValue":e=>!0},setup(e,a){let{slots:l}=a
const o=ra(e,"modelValue"),n=t.useId(),r=t.toRef((()=>e.id||`v-selection-control-group-${n}`)),i=t.toRef((()=>e.name||r.value)),s=new Set
return t.provide(Xo,{modelValue:o,forceUpdate:()=>{s.forEach((e=>e()))},onForceUpdate:e=>{s.add(e),t.onScopeDispose((()=>{s.delete(e)}))}}),xt({[e.defaultsTarget]:{color:t.toRef((()=>e.color)),disabled:t.toRef((()=>e.disabled)),density:t.toRef((()=>e.density)),error:t.toRef((()=>e.error)),inline:t.toRef((()=>e.inline)),modelValue:o,multiple:t.toRef((()=>!!e.multiple||null==e.multiple&&Array.isArray(o.value))),name:i,falseIcon:t.toRef((()=>e.falseIcon)),trueIcon:t.toRef((()=>e.trueIcon)),readonly:t.toRef((()=>e.readonly)),ripple:t.toRef((()=>e.ripple)),type:t.toRef((()=>e.type)),valueComparator:t.toRef((()=>e.valueComparator))}}),Ft((()=>t.createElementVNode("div",{class:t.normalizeClass(["v-selection-control-group",{"v-selection-control-group--inline":e.inline},e.class]),style:t.normalizeStyle(e.style),role:"radio"===e.type?"radiogroup":void 0},[l.default?.()]))),{}}}),en=yt({label:String,baseColor:String,trueValue:null,falseValue:null,value:null,...bt(),...Zo()},"VSelectionControl")
const tn=Et()({name:"VSelectionControl",directives:{vRipple:Fo},inheritAttrs:!1,props:en(),emits:{"update:modelValue":e=>!0},setup(e,a){let{attrs:l,slots:o}=a
const{group:n,densityClasses:r,icon:i,model:s,textColorClasses:u,textColorStyles:c,backgroundColorClasses:d,backgroundColorStyles:v,trueValue:p}=function(e){const a=t.inject(Xo,void 0),{densityClasses:l}=Tl(e),o=ra(e,"modelValue"),n=t.computed((()=>void 0!==e.trueValue?e.trueValue:void 0===e.value||e.value)),r=t.computed((()=>void 0!==e.falseValue&&e.falseValue)),i=t.computed((()=>!!e.multiple||null==e.multiple&&Array.isArray(o.value))),s=t.computed({get(){const t=a?a.modelValue.value:o.value
return i.value?B(t).some((t=>e.valueComparator(t,n.value))):e.valueComparator(t,n.value)},set(t){if(e.readonly)return
const l=t?n.value:r.value
let s=l
i.value&&(s=t?[...B(o.value),l]:B(o.value).filter((t=>!e.valueComparator(t,n.value)))),a?a.modelValue.value=s:o.value=s}}),{textColorClasses:u,textColorStyles:c}=dl((()=>{if(!e.error&&!e.disabled)return s.value?e.color:e.baseColor})),{backgroundColorClasses:d,backgroundColorStyles:v}=vl((()=>!s.value||e.error||e.disabled?e.baseColor:e.color)),p=t.computed((()=>s.value?e.trueIcon:e.falseIcon))
return{group:a,densityClasses:l,trueValue:n,falseValue:r,model:s,textColorClasses:u,textColorStyles:c,backgroundColorClasses:d,backgroundColorStyles:v,icon:p}}(e),m=t.useId(),f=t.shallowRef(!1),g=t.shallowRef(!1),h=t.ref(),y=t.toRef((()=>e.id||`input-${m}`)),b=t.toRef((()=>!e.disabled&&!e.readonly))
function V(e){b.value&&(f.value=!0,!1!==te(e.target,":focus-visible")&&(g.value=!0))}function w(){f.value=!1,g.value=!1}function S(e){e.stopPropagation()}function k(a){b.value?(e.readonly&&n&&t.nextTick((()=>n.forceUpdate())),s.value=a.target.checked):h.value&&(h.value.checked=s.value)}return n?.onForceUpdate((()=>{h.value&&(h.value.checked=s.value)})),Ft((()=>{const a=o.label?o.label({label:e.label,props:{for:y.value}}):e.label,[n,m]=P(l),b=t.createElementVNode("input",t.mergeProps({ref:h,checked:s.value,disabled:!!e.disabled,id:y.value,onBlur:w,onFocus:V,onInput:k,"aria-disabled":!!e.disabled,"aria-label":e.label,type:e.type,value:p.value,name:e.name,"aria-checked":"checkbox"===e.type?s.value:void 0},m),null)
return t.createElementVNode("div",t.mergeProps({class:["v-selection-control",{"v-selection-control--dirty":s.value,"v-selection-control--disabled":e.disabled,"v-selection-control--error":e.error,"v-selection-control--focused":f.value,"v-selection-control--focus-visible":g.value,"v-selection-control--inline":e.inline},r.value,e.class]},n,{style:e.style}),[t.createElementVNode("div",{class:t.normalizeClass(["v-selection-control__wrapper",u.value]),style:t.normalizeStyle(c.value)},[o.default?.({backgroundColorClasses:d,backgroundColorStyles:v}),t.withDirectives(t.createElementVNode("div",{class:t.normalizeClass(["v-selection-control__input"])},[o.input?.({model:s,textColorClasses:u,textColorStyles:c,backgroundColorClasses:d,backgroundColorStyles:v,inputNode:b,icon:i.value,props:{onFocus:V,onBlur:w,id:y.value}})??t.createElementVNode(t.Fragment,null,[i.value&&t.createVNode(Jl,{key:"icon",icon:i.value},null),b])]),[[Fo,e.ripple&&[!e.disabled&&!e.readonly,null,["center","circle"]]]])]),a&&t.createVNode(Ko,{for:y.value,onClick:S},{default:()=>[a]})])})),{isFocused:f,input:h}}}),an=yt({indeterminate:Boolean,indeterminateIcon:{type:zt,default:"$checkboxIndeterminate"},...en({falseIcon:"$checkboxOff",trueIcon:"$checkboxOn"})},"VCheckboxBtn"),ln=Et()({name:"VCheckboxBtn",props:an(),emits:{"update:modelValue":e=>!0,"update:indeterminate":e=>!0},setup(e,a){let{slots:l}=a
const o=ra(e,"indeterminate"),n=ra(e,"modelValue")
function r(e){o.value&&(o.value=!1)}const i=t.toRef((()=>o.value?e.indeterminateIcon:e.falseIcon)),s=t.toRef((()=>o.value?e.indeterminateIcon:e.trueIcon))
return Ft((()=>{const a=C(tn.filterProps(e),["modelValue"])
return t.createVNode(tn,t.mergeProps(a,{modelValue:n.value,"onUpdate:modelValue":[e=>n.value=e,r],class:["v-checkbox-btn",e.class],style:e.style,type:"checkbox",falseIcon:i.value,trueIcon:s.value,"aria-checked":o.value?"mixed":void 0}),l)})),{}}})
function on(e){const{t:a}=ga()
return{InputIcon:function(l){let{name:o,color:n,...r}=l
const i={prepend:"prependAction",prependInner:"prependAction",append:"appendAction",appendInner:"appendAction",clear:"clear"}[o],s=e[`onClick:${o}`],u=s&&i?a(`$vuetify.input.${i}`,e.label??""):void 0
return t.createVNode(Jl,t.mergeProps({icon:e[`${o}Icon`],"aria-label":u,onClick:s,onKeydown:function(e){"Enter"!==e.key&&" "!==e.key||(e.preventDefault(),e.stopPropagation(),K(s,new PointerEvent("click",e)))},color:n},r),null)}}}const nn=yt({active:Boolean,color:String,messages:{type:[Array,String],default:()=>[]},...bt(),...fl({transition:{component:el,leaveAbsolute:!0,group:!0}})},"VMessages"),rn=Et()({name:"VMessages",props:nn(),setup(e,a){let{slots:l}=a
const o=t.computed((()=>B(e.messages))),{textColorClasses:n,textColorStyles:r}=dl((()=>e.color))
return Ft((()=>t.createVNode(gl,{transition:e.transition,tag:"div",class:t.normalizeClass(["v-messages",n.value,e.class]),style:t.normalizeStyle([r.value,e.style])},{default:()=>[e.active&&o.value.map(((e,a)=>t.createElementVNode("div",{class:"v-messages__message",key:`${a}-${o.value}`},[l.message?l.message({message:e}):e])))]}))),{}}}),sn=yt({focused:Boolean,"onUpdate:focused":G()},"focus")
function un(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:wt()
const l=ra(e,"focused")
return{focusClasses:t.toRef((()=>({[`${a}--focused`]:l.value}))),isFocused:l,focus:function(){l.value=!0},blur:function(){l.value=!1}}}const cn=Symbol.for("vuetify:form"),dn=yt({disabled:Boolean,fastFail:Boolean,readonly:Boolean,modelValue:{type:Boolean,default:null},validateOn:{type:String,default:"input"}},"form")
function vn(e){const a=t.inject(cn,null)
return{...a,isReadonly:t.computed((()=>!!(e?.readonly??a?.isReadonly.value))),isDisabled:t.computed((()=>!!(e?.disabled??a?.isDisabled.value)))}}const pn=Symbol.for("vuetify:rules")
const mn=yt({disabled:{type:Boolean,default:null},error:Boolean,errorMessages:{type:[Array,String],default:()=>[]},maxErrors:{type:[Number,String],default:1},name:String,label:String,readonly:{type:Boolean,default:null},rules:{type:Array,default:()=>[]},modelValue:null,validateOn:String,validationValue:null,...sn()},"validation")
function fn(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:wt(),l=arguments.length>2&&void 0!==arguments[2]?arguments[2]:t.useId()
const o=ra(e,"modelValue"),n=t.computed((()=>void 0===e.validationValue?o.value:e.validationValue)),r=vn(e),i=function(e){const a=t.inject(pn,null)
return a?a(e):t.toRef(e)}((()=>e.rules)),s=t.ref([]),u=t.shallowRef(!0),c=t.computed((()=>!(!B(""===o.value?null:o.value).length&&!B(""===n.value?null:n.value).length))),d=t.computed((()=>e.errorMessages?.length?B(e.errorMessages).concat(s.value).slice(0,Math.max(0,Number(e.maxErrors))):s.value)),v=t.computed((()=>{let t=(e.validateOn??r.validateOn?.value)||"input"
"lazy"===t&&(t="input lazy"),"eager"===t&&(t="input eager")
const a=new Set(t?.split(" ")??[])
return{input:a.has("input"),blur:a.has("blur")||a.has("input")||a.has("invalid-input"),invalidInput:a.has("invalid-input"),lazy:a.has("lazy"),eager:a.has("eager")}})),p=t.computed((()=>!e.error&&!e.errorMessages?.length&&(!e.rules.length||(u.value?!s.value.length&&!v.value.lazy||null:!s.value.length)))),m=t.shallowRef(!1),f=t.computed((()=>({[`${a}--error`]:!1===p.value,[`${a}--dirty`]:c.value,[`${a}--disabled`]:r.isDisabled.value,[`${a}--readonly`]:r.isReadonly.value}))),g=Vt("validation"),h=t.computed((()=>e.name??t.unref(l)))
async function y(){o.value=null,await t.nextTick(),await b()}async function b(){u.value=!0,v.value.lazy?s.value=[]:await V(!v.value.eager)}async function V(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0]
const a=[]
m.value=!0
for(const t of i.value){if(a.length>=Number(e.maxErrors??1))break
const l="function"==typeof t?t:()=>t,o=await l(n.value)
!0!==o&&(!1===o||"string"==typeof o?a.push(o||""):console.warn(`${o} is not a valid value. Rule functions must return boolean true or a string.`))}return s.value=a,m.value=!1,u.value=t,s.value}return t.onBeforeMount((()=>{r.register?.({id:h.value,vm:g,validate:V,reset:y,resetValidation:b})})),t.onBeforeUnmount((()=>{r.unregister?.(h.value)})),t.onMounted((async()=>{v.value.lazy||await V(!v.value.eager),r.update?.(h.value,p.value,d.value)})),na((()=>v.value.input||v.value.invalidInput&&!1===p.value),(()=>{t.watch(n,(()=>{if(null!=n.value)V()
else if(e.focused){const a=t.watch((()=>e.focused),(e=>{e||V(),a()}))}}))})),na((()=>v.value.blur),(()=>{t.watch((()=>e.focused),(e=>{e||V()}))})),t.watch([p,d],(()=>{r.update?.(h.value,p.value,d.value)})),{errorMessages:d,isDirty:c,isDisabled:r.isDisabled,isReadonly:r.isReadonly,isPristine:u,isValid:p,isValidating:m,reset:y,resetValidation:b,validate:V,validationClasses:f}}const gn=yt({id:String,appendIcon:zt,baseColor:String,centerAffix:{type:Boolean,default:!0},color:String,glow:Boolean,iconColor:[Boolean,String],prependIcon:zt,hideDetails:[Boolean,String],hideSpinButtons:Boolean,hint:String,persistentHint:Boolean,messages:{type:[Array,String],default:()=>[]},direction:{type:String,default:"horizontal",validator:e=>["horizontal","vertical"].includes(e)},"onClick:prepend":G(),"onClick:append":G(),...bt(),...Al(),...k(rl(),["maxWidth","minWidth","width"]),...Va(),...mn()},"VInput"),hn=Et()({name:"VInput",props:{...gn()},emits:{"update:modelValue":e=>!0},setup(e,a){let{attrs:l,slots:o,emit:n}=a
const{densityClasses:r}=Tl(e),{dimensionStyles:i}=il(e),{themeClasses:s}=Ea(e),{rtlClasses:u}=ya(),{InputIcon:c}=on(e),d=t.useId(),v=t.computed((()=>e.id||`input-${d}`)),p=t.computed((()=>`${v.value}-messages`)),{errorMessages:m,isDirty:f,isDisabled:g,isReadonly:h,isPristine:y,isValid:b,isValidating:V,reset:w,resetValidation:S,validate:k,validationClasses:x}=fn(e,"v-input",v),C=t.computed((()=>({id:v,messagesId:p,isDirty:f,isDisabled:g,isReadonly:h,isPristine:y,isValid:b,isValidating:V,reset:w,resetValidation:S,validate:k}))),N=t.toRef((()=>e.error||e.disabled?void 0:e.focused?e.color:e.baseColor)),E=t.toRef((()=>{if(e.iconColor)return!0===e.iconColor?N.value:e.iconColor})),I=t.computed((()=>e.errorMessages?.length||!y.value&&m.value.length?m.value:e.hint&&(e.persistentHint||e.focused)?e.hint:e.messages))
return Ft((()=>{const a=!(!o.prepend&&!e.prependIcon),l=!(!o.append&&!e.appendIcon),n=I.value.length>0,d=!e.hideDetails||"auto"===e.hideDetails&&(n||!!o.details)
return t.createElementVNode("div",{class:t.normalizeClass(["v-input",`v-input--${e.direction}`,{"v-input--center-affix":e.centerAffix,"v-input--focused":e.focused,"v-input--glow":e.glow,"v-input--hide-spin-buttons":e.hideSpinButtons},r.value,s.value,u.value,x.value,e.class]),style:t.normalizeStyle([i.value,e.style])},[a&&t.createElementVNode("div",{key:"prepend",class:"v-input__prepend"},[o.prepend?.(C.value),e.prependIcon&&t.createVNode(c,{key:"prepend-icon",name:"prepend",color:E.value},null)]),o.default&&t.createElementVNode("div",{class:"v-input__control"},[o.default?.(C.value)]),l&&t.createElementVNode("div",{key:"append",class:"v-input__append"},[e.appendIcon&&t.createVNode(c,{key:"append-icon",name:"append",color:E.value},null),o.append?.(C.value)]),d&&t.createElementVNode("div",{id:p.value,class:"v-input__details",role:"alert","aria-live":"polite"},[t.createVNode(rn,{active:n,messages:I.value},{message:o.message}),o.details?.(C.value)])])})),{reset:w,resetValidation:S,validate:k,isValid:b,errorMessages:m}}}),yn=yt({...gn(),...C(an(),["inline"])},"VCheckbox"),bn=Et()({name:"VCheckbox",inheritAttrs:!1,props:yn(),emits:{"update:modelValue":e=>!0,"update:focused":e=>!0},setup(e,a){let{attrs:l,slots:o}=a
const n=ra(e,"modelValue"),{isFocused:r,focus:i,blur:s}=un(e),u=t.useId()
return Ft((()=>{const[a,c]=P(l),d=hn.filterProps(e),v=ln.filterProps(e)
return t.createVNode(hn,t.mergeProps({class:["v-checkbox",e.class]},a,d,{modelValue:n.value,"onUpdate:modelValue":e=>n.value=e,id:e.id||`checkbox-${u}`,focused:r.value,style:e.style}),{...o,default:e=>{let{id:a,messagesId:l,isDisabled:r,isReadonly:u,isValid:d}=e
return t.createVNode(ln,t.mergeProps(v,{id:a.value,"aria-describedby":l.value,disabled:r.value,readonly:u.value},c,{error:!1===d.value,modelValue:n.value,"onUpdate:modelValue":e=>n.value=e,onFocus:i,onBlur:s}),o)}})})),{}}}),Vn=["sm","md","lg","xl","xxl"],wn=Symbol.for("vuetify:display"),Sn={mobileBreakpoint:"lg",thresholds:{xs:0,sm:600,md:960,lg:1280,xl:1920,xxl:2560}},kn=function(){return z(Sn,arguments.length>0&&void 0!==arguments[0]?arguments[0]:Sn)}
function xn(e){return a&&!e?window.innerWidth:"object"==typeof e&&e.clientWidth||0}function Cn(e){return a&&!e?window.innerHeight:"object"==typeof e&&e.clientHeight||0}function Nn(e){const t=a&&!e?window.navigator.userAgent:"ssr"
function l(e){return Boolean(t.match(e))}return{android:l(/android/i),ios:l(/iphone|ipad|ipod/i),cordova:l(/cordova/i),electron:l(/electron/i),chrome:l(/chrome/i),edge:l(/edge/i),firefox:l(/firefox/i),opera:l(/opera/i),win:l(/win/i),mac:l(/mac/i),linux:l(/linux/i),touch:o,ssr:"ssr"===t}}const En=yt({mobile:{type:Boolean,default:!1},mobileBreakpoint:[Number,String]},"display")
function In(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{mobile:null},a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:wt()
const l=t.inject(wn)
if(!l)throw new Error("Could not find Vuetify display injection")
const o=t.computed((()=>!!e.mobile||("number"==typeof e.mobileBreakpoint?l.width.value<e.mobileBreakpoint:e.mobileBreakpoint?l.width.value<l.thresholds.value[e.mobileBreakpoint]:null===e.mobile&&l.mobile.value))),n=t.toRef((()=>a?{[`${a}--mobile`]:o.value}:{}))
return{...l,displayClasses:n,mobile:o}}const _n=Symbol.for("vuetify:goto")
function Pn(e){return"string"==typeof e?document.querySelector(e):y(e)}function Bn(e,t,a){if("number"==typeof e)return t&&a?-e:e
let l=Pn(e),o=0
for(;l;)o+=t?l.offsetLeft:l.offsetTop,l=l.offsetParent
return o}async function Rn(e,t,a,l){const o=a?"scrollLeft":"scrollTop",n=z(l?.options??{container:void 0,duration:300,layout:!1,offset:0,easing:"easeInOutCubic",patterns:{linear:e=>e,easeInQuad:e=>e**2,easeOutQuad:e=>e*(2-e),easeInOutQuad:e=>e<.5?2*e**2:(4-2*e)*e-1,easeInCubic:e=>e**3,easeOutCubic:e=>--e**3+1,easeInOutCubic:e=>e<.5?4*e**3:(e-1)*(2*e-2)*(2*e-2)+1,easeInQuart:e=>e**4,easeOutQuart:e=>1- --e**4,easeInOutQuart:e=>e<.5?8*e**4:1-8*--e**4,easeInQuint:e=>e**5,easeOutQuint:e=>1+--e**5,easeInOutQuint:e=>e<.5?16*e**5:1+16*--e**5}},t),r=l?.rtl.value,i=("number"==typeof e?e:Pn(e))??0,s="parent"===n.container&&i instanceof HTMLElement?i.parentElement:Pn(n.container)??(document.scrollingElement||document.body)
const u="function"==typeof n.easing?n.easing:n.patterns[n.easing]
if(!u)throw new TypeError(`Easing function "${n.easing}" not found.`)
let c
if("number"==typeof i)c=Bn(i,a,r)
else if(c=Bn(i,a,r)-Bn(s,a,r),n.layout){const e=window.getComputedStyle(i).getPropertyValue("--v-layout-top")
e&&(c-=parseInt(e,10))}c+=n.offset,c=function(e,t,a,l){const{scrollWidth:o,scrollHeight:n}=e,[r,i]=e===document.scrollingElement?[window.innerWidth,window.innerHeight]:[e.offsetWidth,e.offsetHeight]
let s,u
l?a?(s=-(o-r),u=0):(s=0,u=o-r):(s=0,u=n+-i)
return R(t,s,u)}(s,c,!!r,!!a)
const d=s[o]??0
if(c===d)return Promise.resolve(c)
const v=performance.now()
return new Promise((e=>requestAnimationFrame((function t(a){const l=(a-v)/n.duration,r=Math.floor(d+(c-d)*u(R(l,0,1)))
return s[o]=r,l>=1&&Math.abs(r-s[o])<10?e(c):l>2?(Me("Scroll target is not reachable"),e(s[o])):void requestAnimationFrame(t)}))))}function An(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}
const a=t.inject(_n),{isRtl:l}=ya()
if(!a)throw new Error("[Vuetify] Could not find injected goto instance")
const o={...a,rtl:t.toRef((()=>a.rtl.value||l.value))}
async function n(t,a){return Rn(t,z(e,a),!1,o)}return n.horizontal=async(t,a)=>Rn(t,z(e,a),!0,o),n}function Tn(e,t){const a=e?"scrollWidth":"scrollHeight"
return t?.[a]||0}function Dn(e,t,a){if(!a)return 0
const{scrollLeft:l,offsetWidth:o,scrollWidth:n}=a
return e?t?n-o+l:l:a.scrollTop}function Fn(e,t){const a=e?"offsetWidth":"offsetHeight"
return t?.[a]||0}function zn(e,t){const a=e?"offsetLeft":"offsetTop"
return t?.[a]||0}const $n=Symbol.for("vuetify:v-slide-group"),Mn=yt({centerActive:Boolean,direction:{type:String,default:"horizontal"},symbol:{type:null,default:$n},nextIcon:{type:zt,default:"$next"},prevIcon:{type:zt,default:"$prev"},showArrows:{type:[Boolean,String],validator:e=>"boolean"==typeof e||["always","desktop","mobile"].includes(e)},...bt(),...En({mobile:null}),...Ba(),...Ll({selectedClass:"v-slide-group-item--active"})},"VSlideGroup"),On=Et()({name:"VSlideGroup",props:Mn(),emits:{"update:modelValue":e=>!0},setup(e,l){let{slots:o}=l
const{isRtl:n}=ya(),{displayClasses:r,mobile:i}=In(e),s=Wl(e,e.symbol),u=t.shallowRef(!1),c=t.shallowRef(0),d=t.shallowRef(0),v=t.shallowRef(0),p=t.computed((()=>"horizontal"===e.direction)),{resizeRef:m,contentRect:f}=Zt(),{resizeRef:g,contentRect:h}=Zt(),y=An(),b=t.computed((()=>({container:m.el,duration:200,easing:"easeOutQuart"}))),V=t.computed((()=>s.selected.value.length?s.items.value.findIndex((e=>e.id===s.selected.value[0])):-1)),w=t.computed((()=>s.selected.value.length?s.items.value.findIndex((e=>e.id===s.selected.value[s.selected.value.length-1])):-1))
if(a){let a=-1
t.watch((()=>[s.selected.value,f.value,h.value,p.value]),(()=>{cancelAnimationFrame(a),a=requestAnimationFrame((()=>{if(f.value&&h.value){const e=p.value?"width":"height"
d.value=f.value[e],v.value=h.value[e],u.value=d.value+1<v.value}if(V.value>=0&&g.el){k(g.el.children[w.value],e.centerActive)}}))}))}const S=t.shallowRef(!1)
function k(e,t){let a=0
a=t?function(e){let{selectedElement:t,containerElement:a,isHorizontal:l}=e
const o=Fn(l,a)
return zn(l,t)-o/2+Fn(l,t)/2}({containerElement:m.el,isHorizontal:p.value,selectedElement:e}):function(e){let{selectedElement:t,containerElement:a,isRtl:l,isHorizontal:o}=e
const n=Fn(o,a),r=Dn(o,l,a),i=Fn(o,t),s=zn(o,t),u=.4*i
return r>s?s-u:r+n<s+i?s-n+i+u:r}({containerElement:m.el,isHorizontal:p.value,isRtl:n.value,selectedElement:e}),x(a)}function x(e){if(!a||!m.el)return
const t=Fn(p.value,m.el),l=Dn(p.value,n.value,m.el)
if(!(Tn(p.value,m.el)<=t||Math.abs(e-l)<16)){if(p.value&&n.value&&m.el){const{scrollWidth:t,offsetWidth:a}=m.el
e=t-a-e}p.value?y.horizontal(e,b.value):y(e,b.value)}}function C(e){const{scrollTop:t,scrollLeft:a}=e.target
c.value=p.value?a:t}function N(e){if(S.value=!0,u.value&&g.el)for(const t of e.composedPath())for(const e of g.el.children)if(e===t)return void k(e)}function E(e){S.value=!1}let I=!1
function _(e){I||S.value||e.relatedTarget&&g.el?.contains(e.relatedTarget)||A(),I=!1}function P(){I=!0}function B(e){function t(t){e.preventDefault(),A(t)}g.el&&(p.value?"ArrowRight"===e.key?t(n.value?"prev":"next"):"ArrowLeft"===e.key&&t(n.value?"next":"prev"):"ArrowDown"===e.key?t("next"):"ArrowUp"===e.key&&t("prev"),"Home"===e.key?t("first"):"End"===e.key&&t("last"))}function R(e,t){if(!e)return
let a=e
do{a=a?.["next"===t?"nextElementSibling":"previousElementSibling"]}while(a?.hasAttribute("disabled"))
return a}function A(e){if(!g.el)return
let t
if(e)if("next"===e){if(t=R(g.el.querySelector(":focus"),e),!t)return A("first")}else if("prev"===e){if(t=R(g.el.querySelector(":focus"),e),!t)return A("last")}else"first"===e?(t=g.el.firstElementChild,t?.hasAttribute("disabled")&&(t=R(t,"next"))):"last"===e&&(t=g.el.lastElementChild,t?.hasAttribute("disabled")&&(t=R(t,"prev")))
else{t=X(g.el)[0]}t&&t.focus({preventScroll:!0})}function T(e){const t=p.value&&n.value?-1:1,a=("prev"===e?-t:t)*d.value
let l=c.value+a
if(p.value&&n.value&&m.el){const{scrollWidth:e,offsetWidth:t}=m.el
l+=e-t}x(l)}const D=t.computed((()=>({next:s.next,prev:s.prev,select:s.select,isSelected:s.isSelected}))),F=t.computed((()=>{switch(e.showArrows){case"always":return!0
case"desktop":return!i.value
case!0:return u.value||Math.abs(c.value)>0
case"mobile":return i.value||u.value||Math.abs(c.value)>0
default:return!i.value&&(u.value||Math.abs(c.value)>0)}})),z=t.computed((()=>Math.abs(c.value)>1)),$=t.computed((()=>{if(!m.value)return!1
const e=Tn(p.value,m.el),t=function(e,t){const a=e?"clientWidth":"clientHeight"
return t?.[a]||0}(p.value,m.el)
return e-t-Math.abs(c.value)>1}))
return Ft((()=>t.createVNode(e.tag,{class:t.normalizeClass(["v-slide-group",{"v-slide-group--vertical":!p.value,"v-slide-group--has-affixes":F.value,"v-slide-group--is-overflowing":u.value},r.value,e.class]),style:t.normalizeStyle(e.style),tabindex:S.value||s.selected.value.length?-1:0,onFocus:_},{default:()=>[F.value&&t.createElementVNode("div",{key:"prev",class:t.normalizeClass(["v-slide-group__prev",{"v-slide-group__prev--disabled":!z.value}]),onMousedown:P,onClick:()=>z.value&&T("prev")},[o.prev?.(D.value)??t.createVNode(Ya,null,{default:()=>[t.createVNode(Jl,{icon:n.value?e.nextIcon:e.prevIcon},null)]})]),t.createElementVNode("div",{key:"container",ref:m,class:"v-slide-group__container",onScroll:C},[t.createElementVNode("div",{ref:g,class:"v-slide-group__content",onFocusin:N,onFocusout:E,onKeydown:B},[o.default?.(D.value)])]),F.value&&t.createElementVNode("div",{key:"next",class:t.normalizeClass(["v-slide-group__next",{"v-slide-group__next--disabled":!$.value}]),onMousedown:P,onClick:()=>$.value&&T("next")},[o.next?.(D.value)??t.createVNode(Ya,null,{default:()=>[t.createVNode(Jl,{icon:n.value?e.prevIcon:e.nextIcon},null)]})])]}))),{selected:s.selected,scrollTo:T,scrollOffset:c,focus:A,hasPrev:z,hasNext:$}}}),Ln=Symbol.for("vuetify:v-chip-group"),jn=yt({baseColor:String,column:Boolean,filter:Boolean,valueComparator:{type:Function,default:d},...Mn(),...bt(),...Ll({selectedClass:"v-chip--selected"}),...Ba(),...Va(),...zl({variant:"tonal"})},"VChipGroup"),Hn=Et()({name:"VChipGroup",props:jn(),emits:{"update:modelValue":e=>!0},setup(e,a){let{slots:l}=a
const{themeClasses:o}=Ea(e),{isSelected:n,select:r,next:i,prev:s,selected:u}=Wl(e,Ln)
return xt({VChip:{baseColor:t.toRef((()=>e.baseColor)),color:t.toRef((()=>e.color)),disabled:t.toRef((()=>e.disabled)),filter:t.toRef((()=>e.filter)),variant:t.toRef((()=>e.variant))}}),Ft((()=>{const a=On.filterProps(e)
return t.createVNode(On,t.mergeProps(a,{class:["v-chip-group",{"v-chip-group--column":e.column},o.value,e.class],style:e.style}),{default:()=>[l.default?.({isSelected:n,select:r,next:i,prev:s,selected:u.value})]})})),{}}}),Wn=yt({activeClass:String,appendAvatar:String,appendIcon:zt,baseColor:String,closable:Boolean,closeIcon:{type:zt,default:"$delete"},closeLabel:{type:String,default:"$vuetify.close"},draggable:Boolean,filter:Boolean,filterIcon:{type:zt,default:"$complete"},label:Boolean,link:{type:Boolean,default:void 0},pill:Boolean,prependAvatar:String,prependIcon:zt,ripple:{type:[Boolean,Object],default:!0},text:{type:[String,Number,Boolean],default:void 0},modelValue:{type:Boolean,default:!0},onClick:G(),onClickOnce:G(),...wl(),...bt(),...Al(),...kl(),...jl(),...pl(),...ho(),...Xl(),...Ba({tag:"span"}),...Va(),...zl({variant:"tonal"})},"VChip"),Un=Et()({name:"VChip",directives:{vRipple:Fo},props:Wn(),emits:{"click:close":e=>!0,"update:modelValue":e=>!0,"group:selected":e=>!0,click:e=>!0},setup(e,a){let{attrs:l,emit:o,slots:n}=a
const{t:r}=ga(),{borderClasses:i}=Sl(e),{densityClasses:s}=Tl(e),{elevationClasses:u}=xl(e),{roundedClasses:c}=ml(e),{sizeClasses:d}=Zl(e),{themeClasses:v}=Ea(e),p=ra(e,"modelValue"),m=Hl(e,Ln,!1),f=go(e,l),g=t.toRef((()=>!1!==e.link&&f.isLink.value)),h=t.computed((()=>!e.disabled&&!1!==e.link&&(!!m||e.link||f.isClickable.value))),y=t.toRef((()=>({"aria-label":r(e.closeLabel),disabled:e.disabled,onClick(e){e.preventDefault(),e.stopPropagation(),p.value=!1,o("click:close",e)}}))),{colorClasses:b,colorStyles:V,variantClasses:w}=$l((()=>({color:!m||m.isSelected.value?e.color??e.baseColor:e.baseColor,variant:e.variant})))
function S(e){o("click",e),h.value&&(f.navigate?.(e),m?.toggle())}function k(e){"Enter"!==e.key&&" "!==e.key||(e.preventDefault(),S(e))}return()=>{const a=f.isLink.value?"a":e.tag,l=!(!e.appendIcon&&!e.appendAvatar),o=!(!l&&!n.append),r=!(!n.close&&!e.closable),x=!(!n.filter&&!e.filter)&&m,C=!(!e.prependIcon&&!e.prependAvatar),N=!(!C&&!n.prepend)
return p.value&&t.withDirectives(t.createVNode(a,t.mergeProps({class:["v-chip",{"v-chip--disabled":e.disabled,"v-chip--label":e.label,"v-chip--link":h.value,"v-chip--filter":x,"v-chip--pill":e.pill,[`${e.activeClass}`]:e.activeClass&&f.isActive?.value},v.value,i.value,b.value,s.value,u.value,c.value,d.value,w.value,m?.selectedClass.value,e.class],style:[V.value,e.style],disabled:e.disabled||void 0,draggable:e.draggable,tabindex:h.value?0:void 0,onClick:S,onKeydown:h.value&&!g.value&&k},f.linkProps),{default:()=>[Fl(h.value,"v-chip"),x&&t.createVNode(ll,{key:"filter"},{default:()=>[t.withDirectives(t.createElementVNode("div",{class:"v-chip__filter"},[n.filter?t.createVNode(nl,{key:"filter-defaults",disabled:!e.filterIcon,defaults:{VIcon:{icon:e.filterIcon}}},n.filter):t.createVNode(Jl,{key:"filter-icon",icon:e.filterIcon},null)]),[[t.vShow,m.isSelected.value]])]}),N&&t.createElementVNode("div",{key:"prepend",class:"v-chip__prepend"},[n.prepend?t.createVNode(nl,{key:"prepend-defaults",disabled:!C,defaults:{VAvatar:{image:e.prependAvatar,start:!0},VIcon:{icon:e.prependIcon,start:!0}}},n.prepend):t.createElementVNode(t.Fragment,null,[e.prependIcon&&t.createVNode(Jl,{key:"prepend-icon",icon:e.prependIcon,start:!0},null),e.prependAvatar&&t.createVNode(Go,{key:"prepend-avatar",image:e.prependAvatar,start:!0},null)])]),t.createElementVNode("div",{class:"v-chip__content","data-no-activator":""},[n.default?.({isSelected:m?.isSelected.value,selectedClass:m?.selectedClass.value,select:m?.select,toggle:m?.toggle,value:m?.value.value,disabled:e.disabled})??t.toDisplayString(e.text)]),o&&t.createElementVNode("div",{key:"append",class:"v-chip__append"},[n.append?t.createVNode(nl,{key:"append-defaults",disabled:!l,defaults:{VAvatar:{end:!0,image:e.appendAvatar},VIcon:{end:!0,icon:e.appendIcon}}},n.append):t.createElementVNode(t.Fragment,null,[e.appendIcon&&t.createVNode(Jl,{key:"append-icon",end:!0,icon:e.appendIcon},null),e.appendAvatar&&t.createVNode(Go,{key:"append-avatar",end:!0,image:e.appendAvatar},null)])]),r&&t.createElementVNode("button",t.mergeProps({key:"close",class:"v-chip__close",type:"button","data-testid":"close-chip"},y.value),[n.close?t.createVNode(nl,{key:"close-defaults",defaults:{VIcon:{icon:e.closeIcon,size:"x-small"}}},n.close):t.createVNode(Jl,{key:"close-icon",icon:e.closeIcon,size:"x-small"},null)])]}),[[Fo,h.value&&e.ripple,null]])}}}),Yn=Symbol.for("vuetify:list")
function Gn(){const e=t.inject(Yn,{hasPrepend:t.shallowRef(!1),updateHasPrepend:()=>null}),a={hasPrepend:t.shallowRef(!1),updateHasPrepend:e=>{e&&(a.hasPrepend.value=e)}}
return t.provide(Yn,a),e}function qn(){return t.inject(Yn,null)}const Kn=e=>{const a={activate:a=>{let{id:l,value:o,activated:n}=a
return l=t.toRaw(l),e&&!o&&1===n.size&&n.has(l)||(o?n.add(l):n.delete(l)),n},in:(e,t,l)=>{let o=new Set
if(null!=e)for(const n of B(e))o=a.activate({id:n,value:!0,activated:new Set(o),children:t,parents:l})
return o},out:e=>Array.from(e)}
return a},Xn=e=>{const a=Kn(e)
return{activate:e=>{let{activated:l,id:o,...n}=e
o=t.toRaw(o)
const r=l.has(o)?new Set([o]):new Set
return a.activate({...n,id:o,activated:r})},in:(e,t,l)=>{let o=new Set
if(null!=e){const n=B(e)
n.length&&(o=a.in(n.slice(0,1),t,l))}return o},out:(e,t,l)=>a.out(e,t,l)}},Zn={open:e=>{let{id:t,value:a,opened:l,parents:o}=e
if(a){const e=new Set
e.add(t)
let a=o.get(t)
for(;null!=a;)e.add(a),a=o.get(a)
return e}return l.delete(t),l},select:()=>null},Qn={open:e=>{let{id:t,value:a,opened:l,parents:o}=e
if(a){let e=o.get(t)
for(l.add(t);null!=e&&e!==t;)l.add(e),e=o.get(e)
return l}return l.delete(t),l},select:()=>null},Jn={open:Qn.open,select:e=>{let{id:t,value:a,opened:l,parents:o}=e
if(!a)return l
const n=[]
let r=o.get(t)
for(;null!=r;)n.push(r),r=o.get(r)
return new Set(n)}},er=e=>{const a={select:a=>{let{id:l,value:o,selected:n}=a
if(l=t.toRaw(l),e&&!o){const e=Array.from(n.entries()).reduce(((e,t)=>{let[a,l]=t
return"on"===l&&e.push(a),e}),[])
if(1===e.length&&e[0]===l)return n}return n.set(l,o?"on":"off"),n},in:(e,t,l)=>{const o=new Map
for(const n of e||[])a.select({id:n,value:!0,selected:o,children:t,parents:l})
return o},out:e=>{const t=[]
for(const[a,l]of e.entries())"on"===l&&t.push(a)
return t}}
return a},tr=e=>{const a=er(e)
return{select:e=>{let{selected:l,id:o,...n}=e
o=t.toRaw(o)
const r=l.has(o)?new Map([[o,l.get(o)]]):new Map
return a.select({...n,id:o,selected:r})},in:(e,t,l)=>e?.length?a.in(e.slice(0,1),t,l):new Map,out:(e,t,l)=>a.out(e,t,l)}},ar=e=>{const a={select:a=>{let{id:l,value:o,selected:n,children:r,parents:i}=a
l=t.toRaw(l)
const s=new Map(n),u=[l]
for(;u.length;){const e=u.shift()
n.set(t.toRaw(e),o?"on":"off"),r.has(e)&&u.push(...r.get(e))}let c=t.toRaw(i.get(l))
for(;c;){const e=r.get(c),a=e.every((e=>"on"===n.get(t.toRaw(e)))),l=e.every((e=>!n.has(t.toRaw(e))||"off"===n.get(t.toRaw(e))))
n.set(c,a?"on":l?"off":"indeterminate"),c=t.toRaw(i.get(c))}if(e&&!o){const e=Array.from(n.entries()).reduce(((e,t)=>{let[a,l]=t
return"on"===l&&e.push(a),e}),[])
if(0===e.length)return s}return n},in:(e,t,l)=>{let o=new Map
for(const n of e||[])o=a.select({id:n,value:!0,selected:o,children:t,parents:l})
return o},out:(e,t)=>{const a=[]
for(const[l,o]of e.entries())"on"!==o||t.has(l)||a.push(l)
return a}}
return a},lr=Symbol.for("vuetify:nested"),or={id:t.shallowRef(),root:{register:()=>null,unregister:()=>null,parents:t.ref(new Map),children:t.ref(new Map),open:()=>null,openOnSelect:()=>null,activate:()=>null,select:()=>null,activatable:t.ref(!1),selectable:t.ref(!1),opened:t.ref(new Set),activated:t.ref(new Set),selected:t.ref(new Map),selectedValues:t.ref([]),getPath:()=>[]}},nr=yt({activatable:Boolean,selectable:Boolean,activeStrategy:[String,Function,Object],selectStrategy:[String,Function,Object],openStrategy:[String,Object],opened:null,activated:null,selected:null,mandatory:Boolean},"nested"),rr=e=>{let a=!1
const l=t.ref(new Map),o=t.ref(new Map),n=ra(e,"opened",e.opened,(e=>new Set(e)),(e=>[...e.values()])),r=t.computed((()=>{if("object"==typeof e.activeStrategy)return e.activeStrategy
if("function"==typeof e.activeStrategy)return e.activeStrategy(e.mandatory)
switch(e.activeStrategy){case"leaf":return(e=>{const a=Kn(e)
return{activate:e=>{let{id:l,activated:o,children:n,...r}=e
return l=t.toRaw(l),n.has(l)?o:a.activate({id:l,activated:o,children:n,...r})},in:a.in,out:a.out}})(e.mandatory)
case"single-leaf":return(e=>{const a=Xn(e)
return{activate:e=>{let{id:l,activated:o,children:n,...r}=e
return l=t.toRaw(l),n.has(l)?o:a.activate({id:l,activated:o,children:n,...r})},in:a.in,out:a.out}})(e.mandatory)
case"independent":return Kn(e.mandatory)
default:return Xn(e.mandatory)}})),i=t.computed((()=>{if("object"==typeof e.selectStrategy)return e.selectStrategy
if("function"==typeof e.selectStrategy)return e.selectStrategy(e.mandatory)
switch(e.selectStrategy){case"single-leaf":return(e=>{const a=tr(e)
return{select:e=>{let{id:l,selected:o,children:n,...r}=e
return l=t.toRaw(l),n.has(l)?o:a.select({id:l,selected:o,children:n,...r})},in:a.in,out:a.out}})(e.mandatory)
case"leaf":return(e=>{const a=er(e)
return{select:e=>{let{id:l,selected:o,children:n,...r}=e
return l=t.toRaw(l),n.has(l)?o:a.select({id:l,selected:o,children:n,...r})},in:a.in,out:a.out}})(e.mandatory)
case"independent":return er(e.mandatory)
case"single-independent":return tr(e.mandatory)
case"trunk":return(e=>{const t=ar(e)
return{select:t.select,in:t.in,out:(e,t,a)=>{const l=[]
for(const[t,o]of e.entries())if("on"===o){if(a.has(t)){const l=a.get(t)
if("on"===e.get(l))continue}l.push(t)}return l}}})(e.mandatory)
default:return ar(e.mandatory)}})),s=t.computed((()=>{if("object"==typeof e.openStrategy)return e.openStrategy
switch(e.openStrategy){case"list":return Jn
case"single":return Zn
default:return Qn}})),u=ra(e,"activated",e.activated,(e=>r.value.in(e,l.value,o.value)),(e=>r.value.out(e,l.value,o.value))),c=ra(e,"selected",e.selected,(e=>i.value.in(e,l.value,o.value)),(e=>i.value.out(e,l.value,o.value)))
function d(e){const t=[]
let a=e
for(;null!=a;)t.unshift(a),a=o.value.get(a)
return t}t.onBeforeUnmount((()=>{a=!0}))
const v=Vt("nested"),p=new Set,m={id:t.shallowRef(),root:{opened:n,activatable:t.toRef((()=>e.activatable)),selectable:t.toRef((()=>e.selectable)),activated:u,selected:c,selectedValues:t.computed((()=>{const e=[]
for(const[t,a]of c.value.entries())"on"===a&&e.push(t)
return e})),register:(e,t,a)=>{if(p.has(e)){Oe(`Multiple nodes with the same ID\n\t${d(e).map(String).join(" -> ")}\n\t${d(t).concat(e).map(String).join(" -> ")}`)}else p.add(e),t&&e!==t&&o.value.set(e,t),a&&l.value.set(e,[]),null!=t&&l.value.set(t,[...l.value.get(t)||[],e])},unregister:e=>{if(a)return
p.delete(e),l.value.delete(e)
const t=o.value.get(e)
if(t){const a=l.value.get(t)??[]
l.value.set(t,a.filter((t=>t!==e)))}o.value.delete(e)},open:(e,t,a)=>{v.emit("click:open",{id:e,value:t,path:d(e),event:a})
const r=s.value.open({id:e,value:t,opened:new Set(n.value),children:l.value,parents:o.value,event:a})
r&&(n.value=r)},openOnSelect:(e,t,a)=>{const r=s.value.select({id:e,value:t,selected:new Map(c.value),opened:new Set(n.value),children:l.value,parents:o.value,event:a})
r&&(n.value=r)},select:(e,t,a)=>{v.emit("click:select",{id:e,value:t,path:d(e),event:a})
const n=i.value.select({id:e,value:t,selected:new Map(c.value),children:l.value,parents:o.value,event:a})
n&&(c.value=n),m.root.openOnSelect(e,t,a)},activate:(t,a,n)=>{if(!e.activatable)return m.root.select(t,!0,n)
v.emit("click:activate",{id:t,value:a,path:d(t),event:n})
const i=r.value.activate({id:t,value:a,activated:new Set(u.value),children:l.value,parents:o.value,event:n})
if(i.size!==u.value.size)u.value=i
else{for(const e of i)if(!u.value.has(e))return void(u.value=i)
for(const e of u.value)if(!i.has(e))return void(u.value=i)}},children:l,parents:o,getPath:d}}
return t.provide(lr,m),m.root},ir=(e,a)=>{const l=t.inject(lr,or),o=Symbol("nested item"),n=t.computed((()=>t.toValue(e)??o)),r={...l,id:n,open:(e,t)=>l.root.open(n.value,e,t),openOnSelect:(e,t)=>l.root.openOnSelect(n.value,e,t),isOpen:t.computed((()=>l.root.opened.value.has(n.value))),parent:t.computed((()=>l.root.parents.value.get(n.value))),activate:(e,t)=>l.root.activate(n.value,e,t),isActivated:t.computed((()=>l.root.activated.value.has(t.toRaw(n.value)))),select:(e,t)=>l.root.select(n.value,e,t),isSelected:t.computed((()=>"on"===l.root.selected.value.get(t.toRaw(n.value)))),isIndeterminate:t.computed((()=>"indeterminate"===l.root.selected.value.get(t.toRaw(n.value)))),isLeaf:t.computed((()=>!l.root.children.value.get(n.value))),isGroupActivator:l.isGroupActivator}
return t.onBeforeMount((()=>{!l.isGroupActivator&&l.root.register(n.value,l.id.value,a)})),t.onBeforeUnmount((()=>{!l.isGroupActivator&&l.root.unregister(n.value)})),a&&t.provide(lr,r),r},sr=Nt({name:"VListGroupActivator",setup(e,a){let{slots:l}=a
return(()=>{const e=t.inject(lr,or)
t.provide(lr,{...e,isGroupActivator:!0})})(),()=>l.default?.()}}),ur=yt({activeColor:String,baseColor:String,color:String,collapseIcon:{type:zt,default:"$collapse"},expandIcon:{type:zt,default:"$expand"},prependIcon:zt,appendIcon:zt,fluid:Boolean,subgroup:Boolean,title:String,value:null,...bt(),...Ba()},"VListGroup"),cr=Et()({name:"VListGroup",props:ur(),setup(e,a){let{slots:l}=a
const{isOpen:o,open:n,id:r}=ir((()=>e.value),!0),i=t.computed((()=>`v-list-group--id-${String(r.value)}`)),s=qn(),{isBooted:u}=_l()
function c(e){e.stopPropagation(),["INPUT","TEXTAREA"].includes(e.target?.tagName)||n(!o.value,e)}const d=t.computed((()=>({onClick:c,class:"v-list-group__header",id:i.value}))),v=t.computed((()=>o.value?e.collapseIcon:e.expandIcon)),p=t.computed((()=>({VListItem:{active:o.value,activeColor:e.activeColor,baseColor:e.baseColor,color:e.color,prependIcon:e.prependIcon||e.subgroup&&v.value,appendIcon:e.appendIcon||!e.subgroup&&v.value,title:e.title,value:e.value}})))
return Ft((()=>t.createVNode(e.tag,{class:t.normalizeClass(["v-list-group",{"v-list-group--prepend":s?.hasPrepend.value,"v-list-group--fluid":e.fluid,"v-list-group--subgroup":e.subgroup,"v-list-group--open":o.value},e.class]),style:t.normalizeStyle(e.style)},{default:()=>[l.activator&&t.createVNode(nl,{defaults:p.value},{default:()=>[t.createVNode(sr,null,{default:()=>[l.activator({props:d.value,isOpen:o.value})]})]}),t.createVNode(gl,{transition:{component:al},disabled:!u.value},{default:()=>[t.withDirectives(t.createElementVNode("div",{class:"v-list-group__items",role:"group","aria-labelledby":i.value},[l.default?.()]),[[t.vShow,o.value]])]})]}))),{isOpen:o}}}),dr=yt({opacity:[Number,String],...bt(),...Ba()},"VListItemSubtitle"),vr=Et()({name:"VListItemSubtitle",props:dr(),setup(e,a){let{slots:l}=a
return Ft((()=>t.createVNode(e.tag,{class:t.normalizeClass(["v-list-item-subtitle",e.class]),style:t.normalizeStyle([{"--v-list-item-subtitle-opacity":e.opacity},e.style])},l))),{}}}),pr=It("v-list-item-title"),mr=yt({active:{type:Boolean,default:void 0},activeClass:String,activeColor:String,appendAvatar:String,appendIcon:zt,baseColor:String,disabled:Boolean,lines:[Boolean,String],link:{type:Boolean,default:void 0},nav:Boolean,prependAvatar:String,prependIcon:zt,ripple:{type:[Boolean,Object],default:!0},slim:Boolean,subtitle:{type:[String,Number,Boolean],default:void 0},title:{type:[String,Number,Boolean],default:void 0},value:null,onClick:G(),onClickOnce:G(),...wl(),...bt(),...Al(),...rl(),...kl(),...pl(),...ho(),...Ba(),...Va(),...zl({variant:"text"})},"VListItem"),fr=Et()({name:"VListItem",directives:{vRipple:Fo},props:mr(),emits:{click:e=>!0},setup(e,a){let{attrs:l,slots:o,emit:n}=a
const r=go(e,l),i=t.computed((()=>void 0===e.value?r.href.value:e.value)),{activate:s,isActivated:u,select:c,isOpen:d,isSelected:v,isIndeterminate:p,isGroupActivator:m,root:f,parent:g,openOnSelect:h,id:y}=ir(i,!1),b=qn(),V=t.computed((()=>!1!==e.active&&(e.active||r.isActive?.value||(f.activatable.value?u.value:v.value)))),w=t.toRef((()=>!1!==e.link&&r.isLink.value)),S=t.computed((()=>!!b&&(f.selectable.value||f.activatable.value||null!=e.value))),k=t.computed((()=>!e.disabled&&!1!==e.link&&(e.link||r.isClickable.value||S.value))),x=t.toRef((()=>e.rounded||e.nav)),C=t.toRef((()=>e.color??e.activeColor)),N=t.toRef((()=>({color:V.value?C.value??e.baseColor:e.baseColor,variant:e.variant})))
function E(){null!=g.value&&f.open(g.value,!0),h(!0)}t.watch((()=>r.isActive?.value),(e=>{e&&E()})),t.onBeforeMount((()=>{r.isActive?.value&&E()}))
const{themeClasses:I}=Ea(e),{borderClasses:_}=Sl(e),{colorClasses:P,colorStyles:B,variantClasses:R}=$l(N),{densityClasses:A}=Tl(e),{dimensionStyles:T}=il(e),{elevationClasses:D}=xl(e),{roundedClasses:F}=ml(x),z=t.toRef((()=>e.lines?`v-list-item--${e.lines}-line`:void 0)),$=t.computed((()=>({isActive:V.value,select:c,isOpen:d.value,isSelected:v.value,isIndeterminate:p.value})))
function M(t){n("click",t),["INPUT","TEXTAREA"].includes(t.target?.tagName)||k.value&&(r.navigate?.(t),m||(f.activatable.value?s(!u.value,t):(f.selectable.value||null!=e.value)&&c(!v.value,t)))}function O(e){const t=e.target;["INPUT","TEXTAREA"].includes(t.tagName)||"Enter"!==e.key&&" "!==e.key||(e.preventDefault(),e.target.dispatchEvent(new MouseEvent("click",e)))}return Ft((()=>{const a=w.value?"a":e.tag,l=o.title||null!=e.title,n=o.subtitle||null!=e.subtitle,i=!(!e.appendAvatar&&!e.appendIcon),s=!(!i&&!o.append),c=!(!e.prependAvatar&&!e.prependIcon),d=!(!c&&!o.prepend)
var p,m
return b?.updateHasPrepend(d),e.activeColor&&(p="active-color",m=["color","base-color"],m=Array.isArray(m)?m.slice(0,-1).map((e=>`'${e}'`)).join(", ")+` or '${m.at(-1)}'`:`'${m}'`,t.warn(`[Vuetify UPGRADE] '${p}' is deprecated, use ${m} instead.`)),t.withDirectives(t.createVNode(a,t.mergeProps({class:["v-list-item",{"v-list-item--active":V.value,"v-list-item--disabled":e.disabled,"v-list-item--link":k.value,"v-list-item--nav":e.nav,"v-list-item--prepend":!d&&b?.hasPrepend.value,"v-list-item--slim":e.slim,[`${e.activeClass}`]:e.activeClass&&V.value},I.value,_.value,P.value,A.value,D.value,z.value,F.value,R.value,e.class],style:[B.value,T.value,e.style],tabindex:k.value?b?-2:0:void 0,"aria-selected":S.value?f.activatable.value?u.value:f.selectable.value?v.value:V.value:void 0,onClick:M,onKeydown:k.value&&!w.value&&O},r.linkProps),{default:()=>[Fl(k.value||V.value,"v-list-item"),d&&t.createElementVNode("div",{key:"prepend",class:"v-list-item__prepend"},[o.prepend?t.createVNode(nl,{key:"prepend-defaults",disabled:!c,defaults:{VAvatar:{density:e.density,image:e.prependAvatar},VIcon:{density:e.density,icon:e.prependIcon},VListItemAction:{start:!0}}},{default:()=>[o.prepend?.($.value)]}):t.createElementVNode(t.Fragment,null,[e.prependAvatar&&t.createVNode(Go,{key:"prepend-avatar",density:e.density,image:e.prependAvatar},null),e.prependIcon&&t.createVNode(Jl,{key:"prepend-icon",density:e.density,icon:e.prependIcon},null)]),t.createElementVNode("div",{class:"v-list-item__spacer"},null)]),t.createElementVNode("div",{class:"v-list-item__content","data-no-activator":""},[l&&t.createVNode(pr,{key:"title"},{default:()=>[o.title?.({title:e.title})??t.toDisplayString(e.title)]}),n&&t.createVNode(vr,{key:"subtitle"},{default:()=>[o.subtitle?.({subtitle:e.subtitle})??t.toDisplayString(e.subtitle)]}),o.default?.($.value)]),s&&t.createElementVNode("div",{key:"append",class:"v-list-item__append"},[o.append?t.createVNode(nl,{key:"append-defaults",disabled:!i,defaults:{VAvatar:{density:e.density,image:e.appendAvatar},VIcon:{density:e.density,icon:e.appendIcon},VListItemAction:{end:!0}}},{default:()=>[o.append?.($.value)]}):t.createElementVNode(t.Fragment,null,[e.appendIcon&&t.createVNode(Jl,{key:"append-icon",density:e.density,icon:e.appendIcon},null),e.appendAvatar&&t.createVNode(Go,{key:"append-avatar",density:e.density,image:e.appendAvatar},null)]),t.createElementVNode("div",{class:"v-list-item__spacer"},null)])]}),[[Fo,k.value&&e.ripple]])})),{activate:s,isActivated:u,isGroupActivator:m,isSelected:v,list:b,select:c,root:f,id:y,link:r}}}),gr=yt({color:String,inset:Boolean,sticky:Boolean,title:String,...bt(),...Ba()},"VListSubheader"),hr=Et()({name:"VListSubheader",props:gr(),setup(e,a){let{slots:l}=a
const{textColorClasses:o,textColorStyles:n}=dl((()=>e.color))
return Ft((()=>{const a=!(!l.default&&!e.title)
return t.createVNode(e.tag,{class:t.normalizeClass(["v-list-subheader",{"v-list-subheader--inset":e.inset,"v-list-subheader--sticky":e.sticky},o.value,e.class]),style:t.normalizeStyle([{textColorStyles:n},e.style])},{default:()=>[a&&t.createElementVNode("div",{class:"v-list-subheader__text"},[l.default?.()??e.title])]})})),{}}}),yr=yt({color:String,inset:Boolean,length:[Number,String],opacity:[Number,String],thickness:[Number,String],vertical:Boolean,...bt(),...Va()},"VDivider"),br=Et()({name:"VDivider",props:yr(),setup(e,a){let{attrs:l,slots:o}=a
const{themeClasses:n}=Ea(e),{textColorClasses:r,textColorStyles:i}=dl((()=>e.color)),s=t.computed((()=>{const t={}
return e.length&&(t[e.vertical?"height":"width"]=f(e.length)),e.thickness&&(t[e.vertical?"borderRightWidth":"borderTopWidth"]=f(e.thickness)),t}))
return Ft((()=>{const a=t.createElementVNode("hr",{class:t.normalizeClass([{"v-divider":!0,"v-divider--inset":e.inset,"v-divider--vertical":e.vertical},n.value,r.value,e.class]),style:t.normalizeStyle([s.value,i.value,{"--v-border-opacity":e.opacity},e.style]),"aria-orientation":l.role&&"separator"!==l.role?void 0:e.vertical?"vertical":"horizontal",role:`${l.role||"separator"}`},null)
return o.default?t.createElementVNode("div",{class:t.normalizeClass(["v-divider__wrapper",{"v-divider__wrapper--vertical":e.vertical,"v-divider__wrapper--inset":e.inset}])},[a,t.createElementVNode("div",{class:"v-divider__content"},[o.default()]),a]):a})),{}}}),Vr=yt({items:Array,returnObject:Boolean},"VListChildren"),wr=Et()({name:"VListChildren",props:Vr(),setup(e,a){let{slots:l}=a
return Gn(),()=>l.default?.()??e.items?.map((a=>{let{children:o,props:n,type:r,raw:i}=a
if("divider"===r)return l.divider?.({props:n})??t.createVNode(br,n,null)
if("subheader"===r)return l.subheader?.({props:n})??t.createVNode(hr,n,null)
const s={subtitle:l.subtitle?e=>l.subtitle?.({...e,item:i}):void 0,prepend:l.prepend?e=>l.prepend?.({...e,item:i}):void 0,append:l.append?e=>l.append?.({...e,item:i}):void 0,title:l.title?e=>l.title?.({...e,item:i}):void 0},u=cr.filterProps(n)
return o?t.createVNode(cr,t.mergeProps({value:n?.value},u),{activator:a=>{let{props:o}=a
const r={...n,...o,value:e.returnObject?i:n.value}
return l.header?l.header({props:r}):t.createVNode(fr,r,s)},default:()=>t.createVNode(wr,{items:o,returnObject:e.returnObject},l)}):l.item?l.item({props:n}):t.createVNode(fr,t.mergeProps(n,{value:e.returnObject?i:n.value}),s)}))}}),Sr=yt({items:{type:Array,default:()=>[]},itemTitle:{type:[String,Array,Function],default:"title"},itemValue:{type:[String,Array,Function],default:"value"},itemChildren:{type:[Boolean,String,Array,Function],default:"children"},itemProps:{type:[Boolean,String,Array,Function],default:"props"},returnObject:Boolean,valueComparator:Function},"list-items")
function kr(e,t){const a=p(t,e.itemTitle,t),l=p(t,e.itemValue,a),o=p(t,e.itemChildren),n={title:a,value:l,...re(!0===e.itemProps?"object"!=typeof t||null==t||Array.isArray(t)?void 0:"children"in t?C(t,["children"]):t:p(t,e.itemProps))}
return{title:String(n.title??""),value:n.value,props:n,children:Array.isArray(o)?xr(e,o):void 0,raw:t}}function xr(e,t){const a=k(e,["itemTitle","itemValue","itemChildren","itemProps","returnObject","valueComparator"]),l=[]
for(const e of t)l.push(kr(a,e))
return l}function Cr(e){const a=t.computed((()=>xr(e,e.items))),l=t.computed((()=>a.value.some((e=>null===e.value)))),o=t.shallowRef(new Map),n=t.shallowRef([])
return t.watchEffect((()=>{const e=a.value,t=new Map,l=[]
for(let a=0;a<e.length;a++){const o=e[a]
if(ne(o.value)||null===o.value){let e=t.get(o.value)
e||(e=[],t.set(o.value,e)),e.push(o)}else l.push(o)}o.value=t,n.value=l})),{items:a,transformIn:function(t){const r=o.value,i=a.value,s=n.value,u=l.value,c=e.returnObject,v=!!e.valueComparator,p=e.valueComparator||d,m=k(e,["itemTitle","itemValue","itemChildren","itemProps","returnObject","valueComparator"]),f=[]
e:for(const e of t){if(!u&&null===e)continue
if(c&&"string"==typeof e){f.push(kr(m,e))
continue}const t=r.get(e)
if(!v&&t)f.push(...t)
else{for(const t of v?i:s)if(p(e,t.value)){f.push(t)
continue e}f.push(kr(m,e))}}return f},transformOut:function(t){return e.returnObject?t.map((e=>{let{raw:t}=e
return t})):t.map((e=>{let{value:t}=e
return t}))}}}function Nr(e,t){const a=p(t,e.itemType,"item"),l=ne(t)?t:p(t,e.itemTitle),o=p(t,e.itemValue,void 0),n=p(t,e.itemChildren),r={title:l,value:o,...!0===e.itemProps?C(t,["children"]):p(t,e.itemProps)}
return{type:a,title:r.title,value:r.value,props:r,children:"item"===a&&n?Er(e,n):void 0,raw:t}}function Er(e,t){const a=[]
for(const l of t)a.push(Nr(e,l))
return a}const Ir=yt({baseColor:String,activeColor:String,activeClass:String,bgColor:String,disabled:Boolean,expandIcon:zt,collapseIcon:zt,lines:{type:[Boolean,String],default:"one"},slim:Boolean,nav:Boolean,"onClick:open":G(),"onClick:select":G(),"onUpdate:opened":G(),...nr({selectStrategy:"single-leaf",openStrategy:"list"}),...wl(),...bt(),...Al(),...rl(),...kl(),itemType:{type:String,default:"type"},...Sr(),...pl(),...Ba(),...Va(),...zl({variant:"text"})},"VList"),_r=Et()({name:"VList",props:Ir(),emits:{"update:selected":e=>!0,"update:activated":e=>!0,"update:opened":e=>!0,"click:open":e=>!0,"click:activate":e=>!0,"click:select":e=>!0},setup(e,a){let{slots:l}=a
const{items:o}=function(e){return{items:t.computed((()=>Er(e,e.items)))}}(e),{themeClasses:n}=Ea(e),{backgroundColorClasses:r,backgroundColorStyles:i}=vl((()=>e.bgColor)),{borderClasses:s}=Sl(e),{densityClasses:u}=Tl(e),{dimensionStyles:c}=il(e),{elevationClasses:d}=xl(e),{roundedClasses:v}=ml(e),{children:p,open:m,parents:f,select:g,getPath:h}=rr(e),y=t.toRef((()=>e.lines?`v-list--${e.lines}-line`:void 0)),b=t.toRef((()=>e.activeColor)),V=t.toRef((()=>e.baseColor)),w=t.toRef((()=>e.color))
Gn(),xt({VListGroup:{activeColor:b,baseColor:V,color:w,expandIcon:t.toRef((()=>e.expandIcon)),collapseIcon:t.toRef((()=>e.collapseIcon))},VListItem:{activeClass:t.toRef((()=>e.activeClass)),activeColor:b,baseColor:V,color:w,density:t.toRef((()=>e.density)),disabled:t.toRef((()=>e.disabled)),lines:t.toRef((()=>e.lines)),nav:t.toRef((()=>e.nav)),slim:t.toRef((()=>e.slim)),variant:t.toRef((()=>e.variant))}})
const S=t.shallowRef(!1),k=t.ref()
function x(e){S.value=!0}function C(e){S.value=!1}function N(e){S.value||e.relatedTarget&&k.value?.contains(e.relatedTarget)||_()}function E(e){const t=e.target
if(k.value&&!["INPUT","TEXTAREA"].includes(t.tagName)){if("ArrowDown"===e.key)_("next")
else if("ArrowUp"===e.key)_("prev")
else if("Home"===e.key)_("first")
else{if("End"!==e.key)return
_("last")}e.preventDefault()}}function I(e){S.value=!0}function _(e){if(k.value)return Q(k.value,e)}return Ft((()=>t.createVNode(e.tag,{ref:k,class:t.normalizeClass(["v-list",{"v-list--disabled":e.disabled,"v-list--nav":e.nav,"v-list--slim":e.slim},n.value,r.value,s.value,u.value,d.value,y.value,v.value,e.class]),style:t.normalizeStyle([i.value,c.value,e.style]),tabindex:e.disabled?-1:0,role:"listbox","aria-activedescendant":void 0,onFocusin:x,onFocusout:C,onFocus:N,onKeydown:E,onMousedown:I},{default:()=>[t.createVNode(wr,{items:o.value,returnObject:e.returnObject},l)]}))),{open:m,select:g,focus:_,children:p,parents:f,getPath:h}}}),Pr=It("v-list-img"),Br=yt({start:Boolean,end:Boolean,...bt(),...Ba()},"VListItemAction"),Rr=Et()({name:"VListItemAction",props:Br(),setup(e,a){let{slots:l}=a
return Ft((()=>t.createVNode(e.tag,{class:t.normalizeClass(["v-list-item-action",{"v-list-item-action--start":e.start,"v-list-item-action--end":e.end},e.class]),style:t.normalizeStyle(e.style)},l))),{}}}),Ar=yt({start:Boolean,end:Boolean,...bt(),...Ba()},"VListItemMedia"),Tr=Et()({name:"VListItemMedia",props:Ar(),setup(e,a){let{slots:l}=a
return Ft((()=>t.createVNode(e.tag,{class:t.normalizeClass(["v-list-item-media",{"v-list-item-media--start":e.start,"v-list-item-media--end":e.end},e.class]),style:t.normalizeStyle(e.style)},l))),{}}})
function Dr(e,t){return{x:e.x+t.x,y:e.y+t.y}}function Fr(e,t){if("top"===e.side||"bottom"===e.side){const{side:a,align:l}=e
return Dr({x:"left"===l?0:"center"===l?t.width/2:"right"===l?t.width:l,y:"top"===a?0:"bottom"===a?t.height:a},t)}if("left"===e.side||"right"===e.side){const{side:a,align:l}=e
return Dr({x:"left"===a?0:"right"===a?t.width:a,y:"top"===l?0:"center"===l?t.height/2:"bottom"===l?t.height:l},t)}return Dr({x:t.width/2,y:t.height/2},t)}const zr={static:function(){},connected:function(e,a,l){(Array.isArray(e.target.value)||function(e){for(;e;){if("fixed"===window.getComputedStyle(e).position)return!0
e=e.offsetParent}return!1}(e.target.value))&&Object.assign(l.value,{position:"fixed",top:0,[e.isRtl.value?"right":"left"]:0})
const{preferredAnchor:o,preferredOrigin:n}=W((()=>{const t=ue(a.location,e.isRtl.value),l="overlap"===a.origin?t:"auto"===a.origin?de(t):ue(a.origin,e.isRtl.value)
return t.side===l.side&&t.align===ve(l).align?{preferredAnchor:pe(t),preferredOrigin:pe(l)}:{preferredAnchor:t,preferredOrigin:l}})),[r,i,s,u]=["minWidth","minHeight","maxWidth","maxHeight"].map((e=>t.computed((()=>{const t=parseFloat(a[e])
return isNaN(t)?1/0:t})))),c=t.computed((()=>{if(Array.isArray(a.offset))return a.offset
if("string"==typeof a.offset){const e=a.offset.split(" ").map(parseFloat)
return e.length<2&&e.push(0),e}return"number"==typeof a.offset?[a.offset,0]:[0,0]}))
let v=!1,p=-1
const m=new H(4),g=new ResizeObserver((()=>{if(!v)return
if(requestAnimationFrame((e=>{e!==p&&m.clear(),requestAnimationFrame((e=>{p=e}))})),m.isFull){const e=m.values()
if(d(e.at(-1),e.at(-3)))return}const e=y()
e&&m.push(e.flipped)}))
t.watch([e.target,e.contentEl],((e,t)=>{let[a,l]=e,[o,n]=t
o&&!Array.isArray(o)&&g.unobserve(o),a&&!Array.isArray(a)&&g.observe(a),n&&g.unobserve(n),l&&g.observe(l)}),{immediate:!0}),t.onScopeDispose((()=>{g.disconnect()}))
let h=new fe({x:0,y:0,width:0,height:0})
function y(){if(v=!1,requestAnimationFrame((()=>v=!0)),!e.target.value||!e.contentEl.value)return;(Array.isArray(e.target.value)||e.target.value.offsetParent||e.target.value.getClientRects().length)&&(h=he(e.target.value))
const t=function(e,t){const a=ye(e)
t?a.x+=parseFloat(e.style.right||0):a.x-=parseFloat(e.style.left||0)
return a.y-=parseFloat(e.style.top||0),a}(e.contentEl.value,e.isRtl.value),a=At(e.contentEl.value)
a.length||(a.push(document.documentElement),e.contentEl.value.style.top&&e.contentEl.value.style.left||(t.x-=parseFloat(document.documentElement.style.getPropertyValue("--v-body-scroll-x")||0),t.y-=parseFloat(document.documentElement.style.getPropertyValue("--v-body-scroll-y")||0)))
const d=a.reduce(((e,t)=>{const a=function(e){if(e===document.documentElement)return visualViewport?new fe({x:visualViewport.scale>1?0:visualViewport.offsetLeft,y:visualViewport.scale>1?0:visualViewport.offsetTop,width:visualViewport.width*visualViewport.scale,height:visualViewport.height*visualViewport.scale}):new fe({x:0,y:0,width:document.documentElement.clientWidth,height:document.documentElement.clientHeight})
{const t=e.getBoundingClientRect()
return new fe({x:t.x,y:t.y,width:e.clientWidth,height:e.clientHeight})}}(t)
return e?new fe({x:Math.max(e.left,a.left),y:Math.max(e.top,a.top),width:Math.min(e.right,a.right)-Math.max(e.left,a.left),height:Math.min(e.bottom,a.bottom)-Math.max(e.top,a.top)}):a}),void 0)
d.x+=12,d.y+=12,d.width-=24,d.height-=24
let p={anchor:o.value,origin:n.value}
function m(e){const a=new fe(t),l=Fr(e.anchor,h),o=Fr(e.origin,a)
let{x:n,y:r}=(v=o,{x:(i=l).x-v.x,y:i.y-v.y})
var i,v
switch(e.anchor.side){case"top":r-=c.value[0]
break
case"bottom":r+=c.value[0]
break
case"left":n-=c.value[0]
break
case"right":n+=c.value[0]}switch(e.anchor.align){case"top":r-=c.value[1]
break
case"bottom":r+=c.value[1]
break
case"left":n-=c.value[1]
break
case"right":n+=c.value[1]}a.x+=n,a.y+=r,a.width=Math.min(a.width,s.value),a.height=Math.min(a.height,u.value)
return{overflows:ge(a,d),x:n,y:r}}let g=0,y=0
const b={x:0,y:0},V={x:!1,y:!1}
let w=-1
for(;;){if(w++>10){Oe("Infinite loop detected in connectedLocationStrategy")
break}const{x:e,y:a,overflows:l}=m(p)
g+=e,y+=a,t.x+=e,t.y+=a
{const e=me(p.anchor),t=l.x.before||l.x.after,a=l.y.before||l.y.after
let o=!1
if(["x","y"].forEach((n=>{if("x"===n&&t&&!V.x||"y"===n&&a&&!V.y){const t={anchor:{...p.anchor},origin:{...p.origin}},a="x"===n?"y"===e?ve:de:"y"===e?de:ve
t.anchor=a(t.anchor),t.origin=a(t.origin)
const{overflows:r}=m(t);(r[n].before<=l[n].before&&r[n].after<=l[n].after||r[n].before+r[n].after<(l[n].before+l[n].after)/2)&&(p=t,o=V[n]=!0)}})),o)continue}l.x.before&&(g+=l.x.before,t.x+=l.x.before),l.x.after&&(g-=l.x.after,t.x-=l.x.after),l.y.before&&(y+=l.y.before,t.y+=l.y.before),l.y.after&&(y-=l.y.after,t.y-=l.y.after)
{const e=ge(t,d)
b.x=d.width-e.x.before-e.x.after,b.y=d.height-e.y.before-e.y.after,g+=e.x.before,t.x+=e.x.before,y+=e.y.before,t.y+=e.y.before}break}const S=me(p.anchor)
return Object.assign(l.value,{"--v-overlay-anchor-origin":`${p.anchor.side} ${p.anchor.align}`,transformOrigin:`${p.origin.side} ${p.origin.align}`,top:f(Mr(y)),left:e.isRtl.value?void 0:f(Mr(g)),right:e.isRtl.value?f(Mr(-g)):void 0,minWidth:f("y"===S?Math.min(r.value,h.width):r.value),maxWidth:f(Or(R(b.x,r.value===1/0?0:r.value,s.value))),maxHeight:f(Or(R(b.y,i.value===1/0?0:i.value,u.value)))}),{available:b,contentBox:t,flipped:V}}return t.watch((()=>[o.value,n.value,a.offset,a.minWidth,a.minHeight,a.maxWidth,a.maxHeight]),(()=>y())),t.nextTick((()=>{const e=y()
if(!e)return
const{available:t,contentBox:a}=e
a.height>t.y&&requestAnimationFrame((()=>{y(),requestAnimationFrame((()=>{y()}))}))})),{updateLocation:y}}},$r=yt({locationStrategy:{type:[String,Function],default:"static",validator:e=>"function"==typeof e||e in zr},location:{type:String,default:"bottom"},origin:{type:String,default:"auto"},offset:[Number,String,Array]},"VOverlay-location-strategies")
function Mr(e){return Math.round(e*devicePixelRatio)/devicePixelRatio}function Or(e){return Math.ceil(e*devicePixelRatio)/devicePixelRatio}let Lr=!0
const jr=[]
let Hr=-1
function Wr(){cancelAnimationFrame(Hr),Hr=requestAnimationFrame((()=>{const e=jr.shift()
e&&e(),jr.length?Wr():Lr=!0}))}const Ur={none:null,close:function(e){Gr(e.targetEl.value??e.contentEl.value,(function(t){e.isActive.value=!1}))},block:function(e,a){const l=e.root.value?.offsetParent,o=[...new Set([...At(e.targetEl.value,a.contained?l:void 0),...At(e.contentEl.value,a.contained?l:void 0)])].filter((e=>!e.classList.contains("v-overlay-scroll-blocked"))),n=window.innerWidth-document.documentElement.offsetWidth,r=(i=l||document.documentElement,Tt(i)&&i)
var i
r&&e.root.value.classList.add("v-overlay--scroll-blocked")
o.forEach(((e,t)=>{e.style.setProperty("--v-body-scroll-x",f(-e.scrollLeft)),e.style.setProperty("--v-body-scroll-y",f(-e.scrollTop)),e!==document.documentElement&&e.style.setProperty("--v-scrollbar-offset",f(n)),e.classList.add("v-overlay-scroll-blocked")})),t.onScopeDispose((()=>{o.forEach(((e,t)=>{const a=parseFloat(e.style.getPropertyValue("--v-body-scroll-x")),l=parseFloat(e.style.getPropertyValue("--v-body-scroll-y")),o=e.style.scrollBehavior
e.style.scrollBehavior="auto",e.style.removeProperty("--v-body-scroll-x"),e.style.removeProperty("--v-body-scroll-y"),e.style.removeProperty("--v-scrollbar-offset"),e.classList.remove("v-overlay-scroll-blocked"),e.scrollLeft=-a,e.scrollTop=-l,e.style.scrollBehavior=o})),r&&e.root.value.classList.remove("v-overlay--scroll-blocked")}))},reposition:function(e,a,l){let o=!1,n=-1,r=-1
function i(t){var a
a=()=>{const a=performance.now()
e.updateLocation.value?.(t)
const l=performance.now()-a
o=l/(1e3/60)>2},!Lr||jr.length?(jr.push(a),Wr()):(Lr=!1,a(),Wr())}r=("undefined"==typeof requestIdleCallback?e=>e():requestIdleCallback)((()=>{l.run((()=>{Gr(e.targetEl.value??e.contentEl.value,(e=>{o?(cancelAnimationFrame(n),n=requestAnimationFrame((()=>{n=requestAnimationFrame((()=>{i(e)}))}))):i(e)}))}))})),t.onScopeDispose((()=>{"undefined"!=typeof cancelIdleCallback&&cancelIdleCallback(r),cancelAnimationFrame(n)}))}},Yr=yt({scrollStrategy:{type:[String,Function],default:"block",validator:e=>"function"==typeof e||e in Ur}},"VOverlay-scroll-strategies")
function Gr(e,a){const l=[document,...At(e)]
l.forEach((e=>{e.addEventListener("scroll",a,{passive:!0})})),t.onScopeDispose((()=>{l.forEach((e=>{e.removeEventListener("scroll",a)}))}))}const qr=Symbol.for("vuetify:v-menu"),Kr=yt({closeDelay:[Number,String],openDelay:[Number,String]},"delay")
function Xr(e,t){let l=()=>{}
function o(o){l?.()
const n=Number(o?e.openDelay:e.closeDelay)
return new Promise((e=>{l=function(e,t){if(!a||0===e)return t(),()=>{}
const l=window.setTimeout(t,e)
return()=>window.clearTimeout(l)}(n,(()=>{t?.(o),e(o)}))}))}return{clearDelay:l,runOpenDelay:function(){return o(!0)},runCloseDelay:function(){return o(!1)}}}const Zr=yt({target:[String,Object],activator:[String,Object],activatorProps:{type:Object,default:()=>({})},openOnClick:{type:Boolean,default:void 0},openOnHover:Boolean,openOnFocus:{type:Boolean,default:void 0},closeOnContentClick:Boolean,...Kr()},"VOverlay-activator")
function Qr(e,l){let{isActive:o,isTop:n,contentEl:r}=l
const i=Vt("useActivator"),s=t.ref()
let u=!1,c=!1,d=!0
const v=t.computed((()=>e.openOnFocus||null==e.openOnFocus&&e.openOnHover)),p=t.computed((()=>e.openOnClick||null==e.openOnClick&&!e.openOnHover&&!v.value)),{runOpenDelay:m,runCloseDelay:f}=Xr(e,(t=>{t!==(e.openOnHover&&u||v.value&&c)||e.openOnHover&&o.value&&!n.value||(o.value!==t&&(d=!0),o.value=t)})),g=t.ref(),h=e=>{e.stopPropagation(),s.value=e.currentTarget||e.target,o.value||(g.value=[e.clientX,e.clientY]),o.value=!o.value},y=e=>{e.sourceCapabilities?.firesTouchEvents||(u=!0,s.value=e.currentTarget||e.target,m())},b=e=>{u=!1,f()},V=e=>{!1!==te(e.target,":focus-visible")&&(c=!0,e.stopPropagation(),s.value=e.currentTarget||e.target,m())},w=e=>{c=!1,e.stopPropagation(),f()},S=t.computed((()=>{const t={}
return p.value&&(t.onClick=h),e.openOnHover&&(t.onMouseenter=y,t.onMouseleave=b),v.value&&(t.onFocus=V,t.onBlur=w),t})),k=t.computed((()=>{const a={}
if(e.openOnHover&&(a.onMouseenter=()=>{u=!0,m()},a.onMouseleave=()=>{u=!1,f()}),v.value&&(a.onFocusin=()=>{c=!0,m()},a.onFocusout=()=>{c=!1,f()}),e.closeOnContentClick){const e=t.inject(qr,null)
a.onClick=()=>{o.value=!1,e?.closeParents()}}return a})),x=t.computed((()=>{const t={}
return e.openOnHover&&(t.onMouseenter=()=>{d&&(u=!0,d=!1,m())},t.onMouseleave=()=>{u=!1,f()}),t}))
t.watch(n,(t=>{!t||(!e.openOnHover||u||v.value&&c)&&(!v.value||c||e.openOnHover&&u)||r.value?.contains(document.activeElement)||(o.value=!1)})),t.watch(o,(e=>{e||setTimeout((()=>{g.value=void 0}))}),{flush:"post"})
const C=le()
t.watchEffect((()=>{C.value&&t.nextTick((()=>{s.value=C.el}))}))
const N=le(),I=t.computed((()=>"cursor"===e.target&&g.value?g.value:N.value?N.el:Jr(e.target,i)||s.value)),_=t.computed((()=>Array.isArray(I.value)?void 0:I.value))
let P
return t.watch((()=>!!e.activator),(l=>{l&&a?(P=t.effectScope(),P.run((()=>{!function(e,a,l){let{activatorEl:o,activatorEvents:n}=l
function r(){let a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:s(),l=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e.activatorProps
a&&function(e,t){Object.keys(t).forEach((a=>{if(E(a)){const l=Y(a),o=Ve.get(e)
if(null==t[a])o?.forEach((t=>{const[a,n]=t
a===l&&(e.removeEventListener(l,n),o.delete(t))}))
else if(!o||![...o]?.some((e=>e[0]===l&&e[1]===t[a]))){e.addEventListener(l,t[a])
const n=o||new Set
n.add([l,t[a]]),Ve.has(e)||Ve.set(e,n)}}else null==t[a]?e.removeAttribute(a):e.setAttribute(a,t[a])}))}(a,t.mergeProps(n.value,l))}function i(){let a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:s(),l=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e.activatorProps
a&&function(e,t){Object.keys(t).forEach((t=>{if(E(t)){const a=Y(t),l=Ve.get(e)
l?.forEach((t=>{const[o,n]=t
o===a&&(e.removeEventListener(a,n),l.delete(t))}))}else e.removeAttribute(t)}))}(a,t.mergeProps(n.value,l))}function s(){const t=Jr(arguments.length>0&&void 0!==arguments[0]?arguments[0]:e.activator,a)
return o.value=t?.nodeType===Node.ELEMENT_NODE?t:void 0,o.value}t.watch((()=>e.activator),((e,a)=>{if(a&&e!==a){const e=s(a)
e&&i(e)}e&&t.nextTick((()=>r()))}),{immediate:!0}),t.watch((()=>e.activatorProps),(()=>{r()})),t.onScopeDispose((()=>{i()}))}(e,i,{activatorEl:s,activatorEvents:S})}))):P&&P.stop()}),{flush:"post",immediate:!0}),t.onScopeDispose((()=>{P?.stop()})),{activatorEl:s,activatorRef:C,target:I,targetEl:_,targetRef:N,activatorEvents:S,contentEvents:k,scrimEvents:x}}function Jr(e,t){if(!e)return
let a
if("parent"===e){let e=t?.proxy?.$el?.parentNode
for(;e?.hasAttribute("data-no-activator");)e=e.parentNode
a=e}else a="string"==typeof e?document.querySelector(e):"$el"in e?e.$el:e
return a}function ei(){if(!a)return t.shallowRef(!1)
const{ssr:e}=In()
if(e){const e=t.shallowRef(!1)
return t.onMounted((()=>{e.value=!0})),e}return t.shallowRef(!0)}const ti=yt({eager:Boolean},"lazy")
function ai(e,a){const l=t.shallowRef(!1),o=t.toRef((()=>l.value||e.eager||a.value))
return t.watch(a,(()=>l.value=!0)),{isBooted:l,hasContent:o,onAfterLeave:function(){e.eager||(l.value=!1)}}}function li(){const e=Vt("useScopeId").vnode.scopeId
return{scopeId:e?{[e]:""}:void 0}}const oi=Symbol.for("vuetify:stack"),ni=t.reactive([])
function ri(){return!0}function ii(e,t,a){if(!e||!1===si(e,a))return!1
const l=_t(t)
if("undefined"!=typeof ShadowRoot&&l instanceof ShadowRoot&&l.host===e.target)return!1
const o=("object"==typeof a.value&&a.value.include||(()=>[]))()
return o.push(t),!o.some((t=>t?.contains(e.target)))}function si(e,t){return("object"==typeof t.value&&t.value.closeConditional||ri)(e)}function ui(e,t){const a=_t(e)
t(document),"undefined"!=typeof ShadowRoot&&a instanceof ShadowRoot&&t(a)}const ci={mounted(e,t){const a=a=>function(e,t,a){const l="function"==typeof a.value?a.value:a.value.handler
e.shadowTarget=e.target,t._clickOutside.lastMousedownWasOutside&&ii(e,t,a)&&setTimeout((()=>{si(e,a)&&l&&l(e)}),0)}(a,e,t),l=a=>{e._clickOutside.lastMousedownWasOutside=ii(a,e,t)}
ui(e,(e=>{e.addEventListener("click",a,!0),e.addEventListener("mousedown",l,!0)})),e._clickOutside||(e._clickOutside={lastMousedownWasOutside:!1}),e._clickOutside[t.instance.$.uid]={onClick:a,onMousedown:l}},beforeUnmount(e,t){e._clickOutside&&(ui(e,(a=>{if(!a||!e._clickOutside?.[t.instance.$.uid])return
const{onClick:l,onMousedown:o}=e._clickOutside[t.instance.$.uid]
a.removeEventListener("click",l,!0),a.removeEventListener("mousedown",o,!0)})),delete e._clickOutside[t.instance.$.uid])}}
function di(e){const{modelValue:a,color:l,...o}=e
return t.createVNode(t.Transition,{name:"fade-transition",appear:!0},{default:()=>[e.modelValue&&t.createElementVNode("div",t.mergeProps({class:["v-overlay__scrim",e.color.backgroundColorClasses.value],style:e.color.backgroundColorStyles.value},o),null)]})}const vi=yt({absolute:Boolean,attach:[Boolean,String,Object],closeOnBack:{type:Boolean,default:!0},contained:Boolean,contentClass:null,contentProps:null,disabled:Boolean,opacity:[Number,String],noClickAnimation:Boolean,modelValue:Boolean,persistent:Boolean,scrim:{type:[Boolean,String],default:!0},zIndex:{type:[Number,String],default:2e3},...Zr(),...bt(),...rl(),...ti(),...$r(),...Yr(),...Va(),...fl()},"VOverlay"),pi=Et()({name:"VOverlay",directives:{vClickOutside:ci},inheritAttrs:!1,props:{_disableGlobalStack:Boolean,...vi()},emits:{"click:outside":e=>!0,"update:modelValue":e=>!0,keydown:e=>!0,afterEnter:()=>!0,afterLeave:()=>!0},setup(e,l){let{slots:o,attrs:n,emit:r}=l
const i=Vt("VOverlay"),s=t.ref(),u=t.ref(),c=t.ref(),d=ra(e,"modelValue"),v=t.computed({get:()=>d.value,set:t=>{t&&e.disabled||(d.value=t)}}),{themeClasses:p}=Ea(e),{rtlClasses:m,isRtl:g}=ya(),{hasContent:h,onAfterLeave:y}=ai(e,v),b=vl((()=>"string"==typeof e.scrim?e.scrim:null)),{globalTop:V,localTop:w,stackStyles:S}=function(e,a,l){const o=Vt("useStack"),n=!l,r=t.inject(oi,void 0),i=t.reactive({activeChildren:new Set})
t.provide(oi,i)
const s=t.shallowRef(Number(t.toValue(a)))
na(e,(()=>{const e=ni.at(-1)?.[1]
s.value=e?e+10:Number(t.toValue(a)),n&&ni.push([o.uid,s.value]),r?.activeChildren.add(o.uid),t.onScopeDispose((()=>{if(n){const e=t.toRaw(ni).findIndex((e=>e[0]===o.uid))
ni.splice(e,1)}r?.activeChildren.delete(o.uid)}))}))
const u=t.shallowRef(!0)
n&&t.watchEffect((()=>{const e=ni.at(-1)?.[0]===o.uid
setTimeout((()=>u.value=e))}))
const c=t.toRef((()=>!i.activeChildren.size))
return{globalTop:t.readonly(u),localTop:c,stackStyles:t.toRef((()=>({zIndex:s.value})))}}(v,(()=>e.zIndex),e._disableGlobalStack),{activatorEl:k,activatorRef:x,target:C,targetEl:N,targetRef:E,activatorEvents:I,contentEvents:_,scrimEvents:P}=Qr(e,{isActive:v,isTop:w,contentEl:c}),{teleportTarget:B}=function(e){return{teleportTarget:t.computed((()=>{const l=e()
if(!0===l||!a)return
const o=!1===l?document.body:"string"==typeof l?document.querySelector(l):l
if(null==o)return void t.warn(`Unable to locate target ${l}`)
let n=[...o.children].find((e=>e.matches(".v-overlay-container")))
return n||(n=document.createElement("div"),n.className="v-overlay-container",o.appendChild(n)),n}))}}((()=>{const t=e.attach||e.contained
if(t)return t
const a=k?.value?.getRootNode()||i.proxy?.$el?.getRootNode()
return a instanceof ShadowRoot&&a})),{dimensionStyles:R}=il(e),A=ei(),{scopeId:T}=li()
t.watch((()=>e.disabled),(e=>{e&&(v.value=!1)}))
const{contentStyles:D,updateLocation:F}=function(e,l){const o=t.ref({}),n=t.ref()
function r(e){n.value?.(e)}function i(e){n.value?.(e)}function s(e){n.value?.(e)}return a&&na((()=>!(!l.isActive.value||!e.locationStrategy)),(a=>{t.watch((()=>e.locationStrategy),a),t.onScopeDispose((()=>{window.removeEventListener("resize",r),visualViewport?.removeEventListener("resize",i),visualViewport?.removeEventListener("scroll",s),n.value=void 0})),window.addEventListener("resize",r,{passive:!0}),visualViewport?.addEventListener("resize",i,{passive:!0}),visualViewport?.addEventListener("scroll",s,{passive:!0}),"function"==typeof e.locationStrategy?n.value=e.locationStrategy(l,e,o)?.updateLocation:n.value=zr[e.locationStrategy](l,e,o)?.updateLocation})),{contentStyles:o,updateLocation:n}}(e,{isRtl:g,contentEl:c,target:C,isActive:v})
function z(t){r("click:outside",t),e.persistent?H():v.value=!1}function $(t){return v.value&&V.value&&(!e.scrim||t.target===u.value||t instanceof MouseEvent&&t.shadowTarget===u.value)}function M(t){"Escape"===t.key&&V.value&&(c.value?.contains(document.activeElement)||r("keydown",t),e.persistent?H():(v.value=!1,c.value?.contains(document.activeElement)&&k.value?.focus()))}function O(e){("Escape"!==e.key||V.value)&&r("keydown",e)}!function(e,l){if(!a)return
let o
t.watchEffect((async()=>{o?.stop(),l.isActive.value&&e.scrollStrategy&&(o=t.effectScope(),await new Promise((e=>setTimeout(e))),o.active&&o.run((()=>{"function"==typeof e.scrollStrategy?e.scrollStrategy(l,e,o):Ur[e.scrollStrategy]?.(l,e,o)})))})),t.onScopeDispose((()=>{o?.stop()}))}(e,{root:s,contentEl:c,targetEl:N,isActive:v,updateLocation:F}),a&&t.watch(v,(e=>{e?window.addEventListener("keydown",M):window.removeEventListener("keydown",M)}),{immediate:!0}),t.onBeforeUnmount((()=>{a&&window.removeEventListener("keydown",M)}))
const L=fo()
na((()=>e.closeOnBack),(()=>{!function(e,l){let o,n,r=!1
function i(e){e.state?.replaced||(r=!0,setTimeout((()=>r=!1)))}a&&e?.beforeEach&&(t.nextTick((()=>{window.addEventListener("popstate",i),o=e.beforeEach(((e,t,a)=>{yo?r?l(a):a():setTimeout((()=>r?l(a):a())),yo=!0})),n=e?.afterEach((()=>{yo=!1}))})),t.onScopeDispose((()=>{window.removeEventListener("popstate",i),o?.(),n?.()})))}(L,(t=>{V.value&&v.value?(t(!1),e.persistent?H():v.value=!1):t()}))}))
const j=t.ref()
function H(){e.noClickAnimation||c.value&&be(c.value,[{transformOrigin:"center"},{transform:"scale(1.03)"},{transformOrigin:"center"}],{duration:150,easing:Pt})}function W(){r("afterEnter")}function U(){y(),r("afterLeave")}return t.watch((()=>v.value&&(e.absolute||e.contained)&&null==B.value),(e=>{if(e){const e=Rt(s.value)
e&&e!==document.scrollingElement&&(j.value=e.scrollTop)}})),Ft((()=>t.createElementVNode(t.Fragment,null,[o.activator?.({isActive:v.value,targetRef:E,props:t.mergeProps({ref:x},I.value,e.activatorProps)}),A.value&&h.value&&t.createVNode(t.Teleport,{disabled:!B.value,to:B.value},{default:()=>[t.createElementVNode("div",t.mergeProps({class:["v-overlay",{"v-overlay--absolute":e.absolute||e.contained,"v-overlay--active":v.value,"v-overlay--contained":e.contained},p.value,m.value,e.class],style:[S.value,{"--v-overlay-opacity":e.opacity,top:f(j.value)},e.style],ref:s,onKeydown:O},T,n),[t.createVNode(di,t.mergeProps({color:b,modelValue:v.value&&!!e.scrim,ref:u},P.value),null),t.createVNode(gl,{appear:!0,persisted:!0,transition:e.transition,target:C.value,onAfterEnter:W,onAfterLeave:U},{default:()=>[t.withDirectives(t.createElementVNode("div",t.mergeProps({ref:c,class:["v-overlay__content",e.contentClass],style:[R.value,D.value]},_.value,e.contentProps),[o.default?.({isActive:v})]),[[t.vShow,v.value],[ci,{handler:z,closeConditional:$,include:()=>[k.value]}]])]})])]})]))),{activatorEl:k,scrimEl:u,target:C,animateClick:H,contentEl:c,globalTop:V,localTop:w,updateLocation:F}}}),mi=Symbol("Forwarded refs")
function fi(e,t){let a=e
for(;a;){const e=Reflect.getOwnPropertyDescriptor(a,t)
if(e)return e
a=Object.getPrototypeOf(a)}}function gi(e){for(var t=arguments.length,a=new Array(t>1?t-1:0),l=1;l<t;l++)a[l-1]=arguments[l]
return e[mi]=a,new Proxy(e,{get(e,t){if(Reflect.has(e,t))return Reflect.get(e,t)
if("symbol"!=typeof t&&!t.startsWith("$")&&!t.startsWith("__"))for(const e of a)if(e.value&&Reflect.has(e.value,t)){const a=Reflect.get(e.value,t)
return"function"==typeof a?a.bind(e.value):a}},has(e,t){if(Reflect.has(e,t))return!0
if("symbol"==typeof t||t.startsWith("$")||t.startsWith("__"))return!1
for(const e of a)if(e.value&&Reflect.has(e.value,t))return!0
return!1},set(e,t,l){if(Reflect.has(e,t))return Reflect.set(e,t,l)
if("symbol"==typeof t||t.startsWith("$")||t.startsWith("__"))return!1
for(const e of a)if(e.value&&Reflect.has(e.value,t))return Reflect.set(e.value,t,l)
return!1},getOwnPropertyDescriptor(e,t){const l=Reflect.getOwnPropertyDescriptor(e,t)
if(l)return l
if("symbol"!=typeof t&&!t.startsWith("$")&&!t.startsWith("__")){for(const e of a){if(!e.value)continue
const a=fi(e.value,t)??("_"in e.value?fi(e.value._?.setupState,t):void 0)
if(a)return a}for(const e of a){const a=e.value&&e.value[mi]
if(!a)continue
const l=a.slice()
for(;l.length;){const e=l.shift(),a=fi(e.value,t)
if(a)return a
const o=e.value&&e.value[mi]
o&&l.push(...o)}}}}})}const hi=yt({id:String,submenu:Boolean,...C(vi({closeDelay:250,closeOnContentClick:!0,locationStrategy:"connected",location:void 0,openDelay:300,scrim:!1,scrollStrategy:"reposition",transition:{component:Oa}}),["absolute"])},"VMenu"),yi=Et()({name:"VMenu",props:hi(),emits:{"update:modelValue":e=>!0},setup(e,l){let{slots:o}=l
const n=ra(e,"modelValue"),{scopeId:r}=li(),{isRtl:i}=ya(),s=t.useId(),u=t.toRef((()=>e.id||`v-menu-${s}`)),c=t.ref(),d=t.inject(qr,null),v=t.shallowRef(new Set)
async function p(e){const a=e.relatedTarget,l=e.target
if(await t.nextTick(),n.value&&a!==l&&c.value?.contentEl&&c.value?.globalTop&&![document,c.value.contentEl].includes(l)&&!c.value.contentEl.contains(l)){const e=X(c.value.contentEl)
e[0]?.focus()}}function m(e){d?.closeParents(e)}function f(t){if(!e.disabled)if("Tab"===t.key||"Enter"===t.key&&!e.closeOnContentClick){if("Enter"===t.key&&(t.target instanceof HTMLTextAreaElement||t.target instanceof HTMLInputElement&&t.target.closest("form")))return
"Enter"===t.key&&t.preventDefault()
Z(X(c.value?.contentEl,!1),t.shiftKey?"prev":"next",(e=>e.tabIndex>=0))||(n.value=!1,c.value?.activatorEl?.focus())}else e.submenu&&t.key===(i.value?"ArrowRight":"ArrowLeft")&&(n.value=!1,c.value?.activatorEl?.focus())}function g(t){if(e.disabled)return
const a=c.value?.contentEl
a&&n.value?"ArrowDown"===t.key?(t.preventDefault(),t.stopImmediatePropagation(),Q(a,"next")):"ArrowUp"===t.key?(t.preventDefault(),t.stopImmediatePropagation(),Q(a,"prev")):e.submenu&&(t.key===(i.value?"ArrowRight":"ArrowLeft")?n.value=!1:t.key===(i.value?"ArrowLeft":"ArrowRight")&&(t.preventDefault(),Q(a,"first"))):(e.submenu?t.key===(i.value?"ArrowLeft":"ArrowRight"):["ArrowDown","ArrowUp"].includes(t.key))&&(n.value=!0,t.preventDefault(),setTimeout((()=>setTimeout((()=>g(t))))))}t.provide(qr,{register(){v.value.add(s)},unregister(){v.value.delete(s)},closeParents(t){setTimeout((()=>{v.value.size||e.persistent||null!=t&&(!c.value?.contentEl||function(e,t){const a=e.clientX,l=e.clientY,o=t.getBoundingClientRect(),n=o.left,r=o.top,i=o.right,s=o.bottom
return a>=n&&a<=i&&l>=r&&l<=s}(t,c.value.contentEl))||(n.value=!1,d?.closeParents())}),40)}}),t.onBeforeUnmount((()=>{d?.unregister(),document.removeEventListener("focusin",p)})),t.onDeactivated((()=>n.value=!1)),t.watch(n,(e=>{e?(d?.register(),a&&document.addEventListener("focusin",p,{once:!0})):(d?.unregister(),a&&document.removeEventListener("focusin",p))}),{immediate:!0})
const h=t.computed((()=>t.mergeProps({"aria-haspopup":"menu","aria-expanded":String(n.value),"aria-controls":u.value,onKeydown:g},e.activatorProps)))
return Ft((()=>{const a=pi.filterProps(e)
return t.createVNode(pi,t.mergeProps({ref:c,id:u.value,class:["v-menu",e.class],style:e.style},a,{modelValue:n.value,"onUpdate:modelValue":e=>n.value=e,absolute:!0,activatorProps:h.value,location:e.location??(e.submenu?"end":"bottom"),"onClick:outside":m,onKeydown:f},r),{activator:o.activator,default:function(){for(var e=arguments.length,a=new Array(e),l=0;l<e;l++)a[l]=arguments[l]
return t.createVNode(nl,{root:"VMenu"},{default:()=>[o.default?.(...a)]})}})})),gi({id:u,ΨopenChildren:v},c)}}),bi=yt({active:Boolean,disabled:Boolean,max:[Number,String],value:{type:[Number,String],default:0},...bt(),...fl({transition:{component:el}})},"VCounter"),Vi=Et()({name:"VCounter",functional:!0,props:bi(),setup(e,a){let{slots:l}=a
const o=t.toRef((()=>e.max?`${e.value} / ${e.max}`:String(e.value)))
return Ft((()=>t.createVNode(gl,{transition:e.transition},{default:()=>[t.withDirectives(t.createElementVNode("div",{class:t.normalizeClass(["v-counter",{"text-error":e.max&&!e.disabled&&parseFloat(e.value)>parseFloat(e.max)},e.class]),style:t.normalizeStyle(e.style)},[l.default?l.default({counter:o.value,max:e.max,value:e.value}):o.value]),[[t.vShow,e.active]])]}))),{}}}),wi=yt({floating:Boolean,...bt()},"VFieldLabel"),Si=Et()({name:"VFieldLabel",props:wi(),setup(e,a){let{slots:l}=a
return Ft((()=>t.createVNode(Ko,{class:t.normalizeClass(["v-field-label",{"v-field-label--floating":e.floating},e.class]),style:t.normalizeStyle(e.style),"aria-hidden":e.floating||void 0},l))),{}}}),ki=["underlined","outlined","filled","solo","solo-inverted","solo-filled","plain"],xi=yt({appendInnerIcon:zt,bgColor:String,clearable:Boolean,clearIcon:{type:zt,default:"$clear"},active:Boolean,centerAffix:{type:Boolean,default:void 0},color:String,baseColor:String,dirty:Boolean,disabled:{type:Boolean,default:null},glow:Boolean,error:Boolean,flat:Boolean,iconColor:[Boolean,String],label:String,persistentClear:Boolean,prependInnerIcon:zt,reverse:Boolean,singleLine:Boolean,variant:{type:String,default:"filled",validator:e=>ki.includes(e)},"onClick:clear":G(),"onClick:appendInner":G(),"onClick:prependInner":G(),...bt(),...so(),...pl(),...Va()},"VField"),Ci=Et()({name:"VField",inheritAttrs:!1,props:{id:String,...sn(),...xi()},emits:{"update:focused":e=>!0,"update:modelValue":e=>!0},setup(e,a){let{attrs:l,emit:o,slots:n}=a
const{themeClasses:r}=Ea(e),{loaderClasses:i}=uo(e),{focusClasses:s,isFocused:u,focus:c,blur:d}=un(e),{InputIcon:v}=on(e),{roundedClasses:p}=ml(e),{rtlClasses:m}=ya(),g=t.toRef((()=>e.dirty||e.active)),h=t.toRef((()=>!(!e.label&&!n.label))),y=t.toRef((()=>!e.singleLine&&h.value)),b=t.useId(),V=t.computed((()=>e.id||`input-${b}`)),w=t.toRef((()=>`${V.value}-messages`)),S=t.ref(),k=t.ref(),x=t.ref(),C=t.computed((()=>["plain","underlined"].includes(e.variant))),N=t.computed((()=>e.error||e.disabled?void 0:g.value&&u.value?e.color:e.baseColor)),E=t.computed((()=>{if(e.iconColor&&(!e.glow||u.value))return!0===e.iconColor?N.value:e.iconColor})),{backgroundColorClasses:I,backgroundColorStyles:_}=vl((()=>e.bgColor)),{textColorClasses:P,textColorStyles:B}=dl(N)
t.watch(g,(e=>{if(y.value){const t=S.value.$el,a=k.value.$el
requestAnimationFrame((()=>{const l=ye(t),o=a.getBoundingClientRect(),n=o.x-l.x,r=o.y-l.y-(l.height/2-o.height/2),i=o.width/.75,s=Math.abs(i-l.width)>1?{maxWidth:f(i)}:void 0,u=getComputedStyle(t),c=getComputedStyle(a),d=1e3*parseFloat(u.transitionDuration)||150,v=parseFloat(c.getPropertyValue("--v-field-label-scale")),p=c.getPropertyValue("color")
t.style.visibility="visible",a.style.visibility="hidden",be(t,{transform:`translate(${n}px, ${r}px) scale(${v})`,color:p,...s},{duration:d,easing:Pt,direction:e?"normal":"reverse"}).finished.then((()=>{t.style.removeProperty("visibility"),a.style.removeProperty("visibility")}))}))}}),{flush:"post"})
const R=t.computed((()=>({isActive:g,isFocused:u,controlRef:x,blur:d,focus:c})))
function A(e){e.target!==document.activeElement&&e.preventDefault()}return Ft((()=>{const a="outlined"===e.variant,o=!(!n["prepend-inner"]&&!e.prependInnerIcon),u=!(!e.clearable&&!n.clear||e.disabled),f=!!(n["append-inner"]||e.appendInnerIcon||u),b=()=>n.label?n.label({...R.value,label:e.label,props:{for:V.value}}):e.label
return t.createElementVNode("div",t.mergeProps({class:["v-field",{"v-field--active":g.value,"v-field--appended":f,"v-field--center-affix":e.centerAffix??!C.value,"v-field--disabled":e.disabled,"v-field--dirty":e.dirty,"v-field--error":e.error,"v-field--glow":e.glow,"v-field--flat":e.flat,"v-field--has-background":!!e.bgColor,"v-field--persistent-clear":e.persistentClear,"v-field--prepended":o,"v-field--reverse":e.reverse,"v-field--single-line":e.singleLine,"v-field--no-label":!b(),[`v-field--variant-${e.variant}`]:!0},r.value,I.value,s.value,i.value,p.value,m.value,e.class],style:[_.value,e.style],onClick:A},l),[t.createElementVNode("div",{class:"v-field__overlay"},null),t.createVNode(co,{name:"v-field",active:!!e.loading,color:e.error?"error":"string"==typeof e.loading?e.loading:e.color},{default:n.loader}),o&&t.createElementVNode("div",{key:"prepend",class:"v-field__prepend-inner"},[e.prependInnerIcon&&t.createVNode(v,{key:"prepend-icon",name:"prependInner",color:E.value},null),n["prepend-inner"]?.(R.value)]),t.createElementVNode("div",{class:"v-field__field","data-no-activator":""},[["filled","solo","solo-inverted","solo-filled"].includes(e.variant)&&y.value&&t.createVNode(Si,{key:"floating-label",ref:k,class:t.normalizeClass([P.value]),floating:!0,for:V.value,style:t.normalizeStyle(B.value)},{default:()=>[b()]}),h.value&&t.createVNode(Si,{key:"label",ref:S,for:V.value},{default:()=>[b()]}),n.default?.({...R.value,props:{id:V.value,class:"v-field__input","aria-describedby":w.value},focus:c,blur:d})??t.createElementVNode("div",{id:V.value,class:"v-field__input","aria-describedby":w.value},null)]),u&&t.createVNode(ll,{key:"clear"},{default:()=>[t.withDirectives(t.createElementVNode("div",{class:"v-field__clearable",onMousedown:e=>{e.preventDefault(),e.stopPropagation()}},[t.createVNode(nl,{defaults:{VIcon:{icon:e.clearIcon}}},{default:()=>[n.clear?n.clear({...R.value,props:{onFocus:c,onBlur:d,onClick:e["onClick:clear"],tabindex:-1}}):t.createVNode(v,{name:"clear",onFocus:c,onBlur:d,tabindex:-1},null)]})]),[[t.vShow,e.dirty]])]}),f&&t.createElementVNode("div",{key:"append",class:"v-field__append-inner"},[n["append-inner"]?.(R.value),e.appendInnerIcon&&t.createVNode(v,{key:"append-icon",name:"appendInner",color:E.value},null)]),t.createElementVNode("div",{class:t.normalizeClass(["v-field__outline",P.value]),style:t.normalizeStyle(B.value)},[a&&t.createElementVNode(t.Fragment,null,[t.createElementVNode("div",{class:"v-field__outline__start"},null),y.value&&t.createElementVNode("div",{class:"v-field__outline__notch"},[t.createVNode(Si,{ref:k,floating:!0,for:V.value},{default:()=>[b()]})]),t.createElementVNode("div",{class:"v-field__outline__end"},null)]),C.value&&y.value&&t.createVNode(Si,{ref:k,floating:!0,for:V.value},{default:()=>[b()]})])])})),{controlRef:x,fieldIconColor:E}}}),Ni=["color","file","time","date","datetime-local","week","month"],Ei=yt({autofocus:Boolean,counter:[Boolean,Number,String],counterValue:[Number,Function],prefix:String,placeholder:String,persistentPlaceholder:Boolean,persistentCounter:Boolean,suffix:String,role:String,type:{type:String,default:"text"},modelModifiers:Object,...gn(),...xi()},"VTextField"),Ii=Et()({name:"VTextField",directives:{vIntersect:yl},inheritAttrs:!1,props:Ei(),emits:{"click:control":e=>!0,"mousedown:control":e=>!0,"update:focused":e=>!0,"update:modelValue":e=>!0},setup(e,a){let{attrs:l,emit:o,slots:n}=a
const r=ra(e,"modelValue"),{isFocused:i,focus:s,blur:u}=un(e),c=t.computed((()=>"function"==typeof e.counterValue?e.counterValue(r.value):"number"==typeof e.counterValue?e.counterValue:(r.value??"").toString().length)),d=t.computed((()=>l.maxlength?l.maxlength:!e.counter||"number"!=typeof e.counter&&"string"!=typeof e.counter?void 0:e.counter)),v=t.computed((()=>["plain","underlined"].includes(e.variant)))
function p(t,a){e.autofocus&&t&&a[0].target?.focus?.()}const m=t.ref(),f=t.ref(),g=t.ref(),h=t.computed((()=>Ni.includes(e.type)||e.persistentPlaceholder||i.value||e.active))
function y(){g.value!==document.activeElement&&g.value?.focus(),i.value||s()}function b(e){o("mousedown:control",e),e.target!==g.value&&(y(),e.preventDefault())}function V(e){y(),o("click:control",e)}function w(a){const l=a.target
if(r.value=l.value,e.modelModifiers?.trim&&["text","search","password","tel","url"].includes(e.type)){const e=[l.selectionStart,l.selectionEnd]
t.nextTick((()=>{l.selectionStart=e[0],l.selectionEnd=e[1]}))}}return Ft((()=>{const a=!!(n.counter||!1!==e.counter&&null!=e.counter),o=!(!a&&!n.details),[s,S]=P(l),{modelValue:k,...x}=hn.filterProps(e),C=Ci.filterProps(e)
return t.createVNode(hn,t.mergeProps({ref:m,modelValue:r.value,"onUpdate:modelValue":e=>r.value=e,class:["v-text-field",{"v-text-field--prefixed":e.prefix,"v-text-field--suffixed":e.suffix,"v-input--plain-underlined":v.value},e.class],style:e.style},s,x,{centerAffix:!v.value,focused:i.value}),{...n,default:a=>{let{id:l,isDisabled:o,isDirty:s,isReadonly:c,isValid:d,reset:v}=a
return t.createVNode(Ci,t.mergeProps({ref:f,onMousedown:b,onClick:V,"onClick:clear":a=>function(a,l){a.stopPropagation(),y(),t.nextTick((()=>{r.value=null,l(),K(e["onClick:clear"],a)}))}(a,v),"onClick:prependInner":e["onClick:prependInner"],"onClick:appendInner":e["onClick:appendInner"],role:e.role},C,{id:l.value,active:h.value||s.value,dirty:s.value||e.dirty,disabled:o.value,focused:i.value,error:!1===d.value}),{...n,default:a=>{let{props:{class:l,...i}}=a
const s=t.withDirectives(t.createElementVNode("input",t.mergeProps({ref:g,value:r.value,onInput:w,autofocus:e.autofocus,readonly:c.value,disabled:o.value,name:e.name,placeholder:e.placeholder,size:1,type:e.type,onFocus:y,onBlur:u},i,S),null),[[yl,{handler:p},null,{once:!0}]])
return t.createElementVNode(t.Fragment,null,[e.prefix&&t.createElementVNode("span",{class:"v-text-field__prefix"},[t.createElementVNode("span",{class:"v-text-field__prefix__text"},[e.prefix])]),n.default?t.createElementVNode("div",{class:t.normalizeClass(l),"data-no-activator":""},[n.default(),s]):t.cloneVNode(s,{class:l}),e.suffix&&t.createElementVNode("span",{class:"v-text-field__suffix"},[t.createElementVNode("span",{class:"v-text-field__suffix__text"},[e.suffix])])])}})},details:o?l=>t.createElementVNode(t.Fragment,null,[n.details?.(l),a&&t.createElementVNode(t.Fragment,null,[t.createElementVNode("span",null,null),t.createVNode(Vi,{active:e.persistentCounter||i.value,value:c.value,max:d.value,disabled:e.disabled},n.counter)])]):void 0})})),gi({},m,f,g)}}),_i=yt({renderless:Boolean,...bt()},"VVirtualScrollItem"),Pi=Et()({name:"VVirtualScrollItem",inheritAttrs:!1,props:_i(),emits:{"update:height":e=>!0},setup(e,a){let{attrs:l,emit:o,slots:n}=a
const{resizeRef:r,contentRect:i}=Zt(void 0,"border")
t.watch((()=>i.value?.height),(e=>{null!=e&&o("update:height",e)})),Ft((()=>e.renderless?t.createElementVNode(t.Fragment,null,[n.default?.({itemRef:r})]):t.createElementVNode("div",t.mergeProps({ref:r,class:["v-virtual-scroll__item",e.class],style:e.style},l),[n.default?.()])))}}),Bi=yt({itemHeight:{type:[Number,String],default:null},itemKey:{type:[String,Array,Function],default:null},height:[Number,String]},"virtual")
function Ri(e,l){const o=In(),n=t.shallowRef(0)
t.watchEffect((()=>{n.value=parseFloat(e.itemHeight||0)}))
const r=t.shallowRef(0),i=t.shallowRef(Math.ceil((parseInt(e.height)||o.height.value)/(n.value||16))||1),s=t.shallowRef(0),u=t.shallowRef(0),c=t.ref(),d=t.ref()
let v=0
const{resizeRef:m,contentRect:f}=Zt()
t.watchEffect((()=>{m.value=c.value}))
const g=t.computed((()=>c.value===document.documentElement?o.height.value:f.value?.height||parseInt(e.height)||0)),h=t.computed((()=>!!(c.value&&d.value&&g.value&&n.value)))
let y=Array.from({length:l.value.length}),b=Array.from({length:l.value.length})
const V=t.shallowRef(0)
let w=-1
function S(e){return y[e]||n.value}const k=function(e,a){let l=0
const o=function(){for(var o=arguments.length,n=new Array(o),r=0;r<o;r++)n[r]=arguments[r]
clearTimeout(l),l=setTimeout((()=>e(...n)),t.unref(a))}
return o.clear=()=>{clearTimeout(l)},o.immediate=e,o}((()=>{const e=performance.now()
b[0]=0
const t=l.value.length
for(let e=1;e<=t-1;e++)b[e]=(b[e-1]||0)+S(e-1)
V.value=Math.max(V.value,performance.now()-e)}),V),x=t.watch(h,(e=>{e&&(x(),v=d.value.offsetTop,k.immediate(),T(),~w&&t.nextTick((()=>{a&&window.requestAnimationFrame((()=>{F(w),w=-1}))})))}))
function C(e){return e=R(e,0,l.value.length-1),b[e]||0}function N(e){return function(e,t){let a=e.length-1,l=0,o=0,n=null,r=-1
if(e[a]<t)return a
for(;l<=a;)if(o=l+a>>1,n=e[o],n>t)a=o-1
else{if(!(n<t))return n===t?o:l
r=o,l=o+1}return r}(b,e)}t.onScopeDispose((()=>{k.clear()}))
let E=0,I=0,_=0
t.watch(g,((e,t)=>{t&&(T(),e<t&&requestAnimationFrame((()=>{I=0,T()})))}))
let P=-1
function B(){c.value&&d.value&&(I=0,_=0,window.clearTimeout(P),T())}let A=-1
function T(){cancelAnimationFrame(A),A=requestAnimationFrame(D)}function D(){if(!c.value||!g.value)return
const e=E-v,t=Math.sign(I),a=R(N(Math.max(0,e-100)),0,l.value.length),o=R(N(e+g.value+100)+1,a+1,l.value.length)
if((-1!==t||a<r.value)&&(1!==t||o>i.value)){const e=C(r.value)-C(a),t=C(o)-C(i.value)
Math.max(e,t)>100?(r.value=a,i.value=o):(a<=0&&(r.value=a),o>=l.value.length&&(i.value=o))}s.value=C(r.value),u.value=C(l.value.length)-C(i.value)}function F(e){const t=C(e)
!c.value||e&&!t?w=e:c.value.scrollTop=t}const z=t.computed((()=>l.value.slice(r.value,i.value).map(((t,a)=>{const l=a+r.value
return{raw:t,index:l,key:p(t,e.itemKey,l)}}))))
return t.watch(l,(()=>{y=Array.from({length:l.value.length}),b=Array.from({length:l.value.length}),k.immediate(),T()}),{deep:1}),{calculateVisibleItems:T,containerRef:c,markerRef:d,computedItems:z,paddingTop:s,paddingBottom:u,scrollToIndex:F,handleScroll:function(){if(!c.value||!d.value)return
const e=c.value.scrollTop,t=performance.now()
t-_>500?(I=Math.sign(e-E),v=d.value.offsetTop):I=e-E,E=e,_=t,window.clearTimeout(P),P=window.setTimeout(B,500),T()},handleScrollend:B,handleItemResize:function(e,t){const a=y[e],l=n.value
n.value=l?Math.min(n.value,t):t,a===t&&l===n.value||(y[e]=t,k())}}}const Ai=yt({items:{type:Array,default:()=>[]},renderless:Boolean,...Bi(),...bt(),...rl()},"VVirtualScroll"),Ti=Et()({name:"VVirtualScroll",props:Ai(),setup(e,a){let{slots:l}=a
const o=Vt("VVirtualScroll"),{dimensionStyles:n}=il(e),{calculateVisibleItems:r,containerRef:i,markerRef:s,handleScroll:u,handleScrollend:c,handleItemResize:d,scrollToIndex:v,paddingTop:p,paddingBottom:m,computedItems:g}=Ri(e,t.toRef((()=>e.items)))
return na((()=>e.renderless),(()=>{function e(){const e=arguments.length>0&&void 0!==arguments[0]&&arguments[0]?"addEventListener":"removeEventListener"
i.value===document.documentElement?(document[e]("scroll",u,{passive:!0}),document[e]("scrollend",c)):(i.value?.[e]("scroll",u,{passive:!0}),i.value?.[e]("scrollend",c))}t.onMounted((()=>{i.value=Rt(o.vnode.el,!0),e(!0)})),t.onScopeDispose(e)})),Ft((()=>{const a=g.value.map((a=>t.createVNode(Pi,{key:a.key,renderless:e.renderless,"onUpdate:height":e=>d(a.index,e)},{default:e=>l.default?.({item:a.raw,index:a.index,...e})})))
return e.renderless?t.createElementVNode(t.Fragment,null,[t.createElementVNode("div",{ref:s,class:"v-virtual-scroll__spacer",style:{paddingTop:f(p.value)}},null),a,t.createElementVNode("div",{class:"v-virtual-scroll__spacer",style:{paddingBottom:f(m.value)}},null)]):t.createElementVNode("div",{ref:i,class:t.normalizeClass(["v-virtual-scroll",e.class]),onScrollPassive:u,onScrollend:c,style:t.normalizeStyle([n.value,e.style])},[t.createElementVNode("div",{ref:s,class:"v-virtual-scroll__container",style:{paddingTop:f(p.value),paddingBottom:f(m.value)}},[a])])})),{calculateVisibleItems:r,scrollToIndex:v}}})
function Di(e,a){const l=t.shallowRef(!1)
let o
return{onScrollPassive:function(e){cancelAnimationFrame(o),l.value=!0,o=requestAnimationFrame((()=>{o=requestAnimationFrame((()=>{l.value=!1}))}))},onKeydown:async function(o){if("Tab"===o.key&&a.value?.focus(),!["PageDown","PageUp","Home","End"].includes(o.key))return
const n=e.value?.$el
if(!n)return
"Home"!==o.key&&"End"!==o.key||n.scrollTo({top:"Home"===o.key?0:n.scrollHeight,behavior:"smooth"}),await async function(){await new Promise((e=>requestAnimationFrame(e))),await new Promise((e=>requestAnimationFrame(e))),await new Promise((e=>requestAnimationFrame(e))),await new Promise((e=>{if(l.value){const a=t.watch(l,(()=>{a(),e()}))}else e()}))}()
const r=n.querySelectorAll(":scope > :not(.v-virtual-scroll__spacer)")
if("PageDown"===o.key||"Home"===o.key){const e=n.getBoundingClientRect().top
for(const t of r)if(t.getBoundingClientRect().top>=e){t.focus()
break}}else{const e=n.getBoundingClientRect().bottom
for(const t of[...r].reverse())if(t.getBoundingClientRect().bottom<=e){t.focus()
break}}}}}const Fi=yt({chips:Boolean,closableChips:Boolean,closeText:{type:String,default:"$vuetify.close"},openText:{type:String,default:"$vuetify.open"},eager:Boolean,hideNoData:Boolean,hideSelected:Boolean,listProps:{type:Object},menu:Boolean,menuIcon:{type:zt,default:"$dropdown"},menuProps:{type:Object},multiple:Boolean,noDataText:{type:String,default:"$vuetify.noDataText"},openOnClear:Boolean,itemColor:String,...Sr({itemChildren:!1})},"Select"),zi=yt({...Fi(),...C(Ei({modelValue:null,role:"combobox"}),["validationValue","dirty","appendInnerIcon"]),...fl({transition:{component:Oa}})},"VSelect"),$i=Et()({name:"VSelect",props:zi(),emits:{"update:focused":e=>!0,"update:modelValue":e=>!0,"update:menu":e=>!0},setup(e,l){let{slots:o}=l
const{t:n}=ga(),r=t.ref(),i=t.ref(),s=t.ref(),{items:u,transformIn:c,transformOut:v}=Cr(e),p=ra(e,"modelValue",[],(e=>c(null===e?[null]:B(e))),(t=>{const a=v(t)
return e.multiple?a:a[0]??null})),m=t.computed((()=>"function"==typeof e.counterValue?e.counterValue(p.value):"number"==typeof e.counterValue?e.counterValue:p.value.length)),f=vn(e),g=t.computed((()=>p.value.map((e=>e.value)))),h=t.shallowRef(!1)
let y,b="",V=-1
const w=t.computed((()=>e.hideSelected?u.value.filter((t=>!p.value.some((a=>(e.valueComparator||d)(a,t))))):u.value)),S=t.computed((()=>e.hideNoData&&!w.value.length||f.isReadonly.value||f.isDisabled.value)),k=ra(e,"menu"),x=t.computed({get:()=>k.value,set:e=>{k.value&&!e&&i.value?.ΨopenChildren.size||e&&S.value||(k.value=e)}}),C=t.toRef((()=>x.value?e.closeText:e.openText)),N=t.computed((()=>({...e.menuProps,activatorProps:{...e.menuProps?.activatorProps||{},"aria-haspopup":"listbox"}}))),E=t.ref(),I=Di(E,r)
function _(t){e.openOnClear&&(x.value=!0)}function P(){S.value||(x.value=!x.value)}function R(e){oe(e)&&A(e)}function A(t){if(!t.key||f.isReadonly.value)return;["Enter"," ","ArrowDown","ArrowUp","Home","End"].includes(t.key)&&t.preventDefault(),["Enter","ArrowDown"," "].includes(t.key)&&(x.value=!0),["Escape","Tab"].includes(t.key)&&(x.value=!1),"Home"===t.key?E.value?.focus("first"):"End"===t.key&&E.value?.focus("last")
if(!oe(t))return
const a=performance.now()
a-y>1e3&&(b="",V=-1),b+=t.key.toLowerCase(),y=a
const l=w.value
function o(){for(let e=V+1;e<l.length;e++){const t=l[e]
if(t.title.toLowerCase().startsWith(b))return[t,e]}}const n=function(){let e=o()
return e||(b.at(-1)===b.at(-2)&&(b=b.slice(0,-1),e=o(),e)?e:(V=-1,e=o(),e||(b=t.key.toLowerCase(),o())))}()
if(!n)return
const[r,i]=n
V=i,E.value?.focus(i),e.multiple||(p.value=[r])}function T(a){let l=!(arguments.length>1&&void 0!==arguments[1])||arguments[1]
if(!a.props.disabled)if(e.multiple){const t=p.value.findIndex((t=>(e.valueComparator||d)(t.value,a.value))),o=null==l?!~t:l
if(~t){const e=o?[...p.value,a]:[...p.value]
e.splice(t,1),p.value=e}else o&&(p.value=[...p.value,a])}else{const e=!1!==l
p.value=e?[a]:[],t.nextTick((()=>{x.value=!1}))}}function D(e){E.value?.$el.contains(e.relatedTarget)||(x.value=!1)}function F(){e.eager&&s.value?.calculateVisibleItems()}function z(){h.value&&r.value?.focus()}function $(e){h.value=!0}function M(e){if(null==e)p.value=[]
else if(te(r.value,":autofill")||te(r.value,":-webkit-autofill")){const t=u.value.find((t=>t.title===e))
t&&T(t)}else r.value&&(r.value.value="")}return t.watch(x,(()=>{if(!e.hideSelected&&x.value&&p.value.length){const t=w.value.findIndex((t=>p.value.some((a=>(e.valueComparator||d)(a.value,t.value)))))
a&&window.requestAnimationFrame((()=>{t>=0&&s.value?.scrollToIndex(t)}))}})),t.watch((()=>e.items),((e,t)=>{x.value||h.value&&!t.length&&e.length&&(x.value=!0)})),Ft((()=>{const a=!(!e.chips&&!o.chip),l=!!(!e.hideNoData||w.value.length||o["prepend-item"]||o["append-item"]||o["no-data"]),u=p.value.length>0,c=Ii.filterProps(e),d=u||!h.value&&e.label&&!e.persistentPlaceholder?void 0:e.placeholder
return t.createVNode(Ii,t.mergeProps({ref:r},c,{modelValue:p.value.map((e=>e.props.value)).join(", "),"onUpdate:modelValue":M,focused:h.value,"onUpdate:focused":e=>h.value=e,validationValue:p.externalValue,counterValue:m.value,dirty:u,class:["v-select",{"v-select--active-menu":x.value,"v-select--chips":!!e.chips,["v-select--"+(e.multiple?"multiple":"single")]:!0,"v-select--selected":p.value.length,"v-select--selection-slot":!!o.selection},e.class],style:e.style,inputmode:"none",placeholder:d,"onClick:clear":_,"onMousedown:control":P,onBlur:D,onKeydown:A,"aria-label":n(C.value),title:n(C.value)}),{...o,default:()=>t.createElementVNode(t.Fragment,null,[t.createVNode(yi,t.mergeProps({ref:i,modelValue:x.value,"onUpdate:modelValue":e=>x.value=e,activator:"parent",contentClass:"v-select__content",disabled:S.value,eager:e.eager,maxHeight:310,openOnClick:!1,closeOnContentClick:!1,transition:e.transition,onAfterEnter:F,onAfterLeave:z},N.value),{default:()=>[l&&t.createVNode(_r,t.mergeProps({ref:E,selected:g.value,selectStrategy:e.multiple?"independent":"single-independent",onMousedown:e=>e.preventDefault(),onKeydown:R,onFocusin:$,tabindex:"-1","aria-live":"polite","aria-label":`${e.label}-list`,color:e.itemColor??e.color},I,e.listProps),{default:()=>[o["prepend-item"]?.(),!w.value.length&&!e.hideNoData&&(o["no-data"]?.()??t.createVNode(fr,{key:"no-data",title:n(e.noDataText)},null)),t.createVNode(Ti,{ref:s,renderless:!0,items:w.value,itemKey:"value"},{default:a=>{let{item:l,index:n,itemRef:r}=a
const i=t.mergeProps(l.props,{ref:r,key:l.value,onClick:()=>T(l,null)})
return o.item?.({item:l,index:n,props:i})??t.createVNode(fr,t.mergeProps(i,{role:"option"}),{prepend:a=>{let{isSelected:o}=a
return t.createElementVNode(t.Fragment,null,[e.multiple&&!e.hideSelected?t.createVNode(ln,{key:l.value,modelValue:o,ripple:!1,tabindex:"-1"},null):void 0,l.props.prependAvatar&&t.createVNode(Go,{image:l.props.prependAvatar},null),l.props.prependIcon&&t.createVNode(Jl,{icon:l.props.prependIcon},null)])}})}}),o["append-item"]?.()]})]}),p.value.map(((l,n)=>{function r(e){e.stopPropagation(),e.preventDefault(),T(l,!1)}const i={"onClick:close":r,onKeydown(e){"Enter"!==e.key&&" "!==e.key||(e.preventDefault(),e.stopPropagation(),r(e))},onMousedown(e){e.preventDefault(),e.stopPropagation()},modelValue:!0,"onUpdate:modelValue":void 0},s=a?!!o.chip:!!o.selection,u=s?ae(a?o.chip({item:l,index:n,props:i}):o.selection({item:l,index:n})):void 0
if(!s||u)return t.createElementVNode("div",{key:l.value,class:"v-select__selection"},[a?o.chip?t.createVNode(nl,{key:"chip-defaults",defaults:{VChip:{closable:e.closableChips,size:"small",text:l.title}}},{default:()=>[u]}):t.createVNode(Un,t.mergeProps({key:"chip",closable:e.closableChips,size:"small",text:l.title,disabled:l.props.disabled},i),null):u??t.createElementVNode("span",{class:"v-select__selection-text"},[l.title,e.multiple&&n<p.value.length-1&&t.createElementVNode("span",{class:"v-select__selection-comma"},[t.createTextVNode(",")])])])}))]),"append-inner":function(){for(var a=arguments.length,l=new Array(a),n=0;n<a;n++)l[n]=arguments[n]
return t.createElementVNode(t.Fragment,null,[o["append-inner"]?.(...l),e.menuIcon?t.createVNode(Jl,{class:"v-select__menu-icon",color:r.value?.fieldIconColor,icon:e.menuIcon},null):void 0])}})})),gi({isFocused:h,menu:x,select:T},r)}}),Mi=(e,t,a)=>{if(null==e||null==t)return-1
if(!t.length)return 0
e=e.toString().toLocaleLowerCase(),t=t.toString().toLocaleLowerCase()
const l=[]
let o=e.indexOf(t)
for(;~o;)l.push([o,o+t.length]),o=e.indexOf(t,o+t.length)
return l.length?l:-1}
function Oi(e,t){if(null!=e&&"boolean"!=typeof e&&-1!==e)return"number"==typeof e?[[e,e+t.length]]:Array.isArray(e[0])?e:[e]}const Li=yt({customFilter:Function,customKeyFilter:Object,filterKeys:[Array,String],filterMode:{type:String,default:"intersection"},noFilter:Boolean},"filter")
function ji(e,a,l,o){const n=t.shallowRef([]),r=t.shallowRef(new Map),i=t.computed((()=>o?.transform?t.unref(a).map((e=>[e,o.transform(e)])):t.unref(a)))
return t.watchEffect((()=>{const s="function"==typeof l?l():t.unref(l),u="string"!=typeof s&&"number"!=typeof s?"":String(s),c=function(e,t,a){const l=[],o=a?.default??Mi,n=!!a?.filterKeys&&B(a.filterKeys),r=Object.keys(a?.customKeyFilter??{}).length
if(!e?.length)return l
e:for(let i=0;i<e.length;i++){const[s,u=s]=B(e[i]),c={},d={}
let v=-1
if((t||r>0)&&!a?.noFilter){if("object"==typeof s){const e=n||Object.keys(u)
for(const l of e){const e=p(u,l),n=a?.customKeyFilter?.[l]
if(v=n?n(e,t,s):o(e,t,s),-1!==v&&!1!==v)n?c[l]=Oi(v,t):d[l]=Oi(v,t)
else if("every"===a?.filterMode)continue e}}else v=o(s,t,s),-1!==v&&!1!==v&&(d.title=Oi(v,t))
const e=Object.keys(d).length,l=Object.keys(c).length
if(!e&&!l)continue
if("union"===a?.filterMode&&l!==r&&!e)continue
if("intersection"===a?.filterMode&&(l!==r||!e))continue}l.push({index:i,matches:{...d,...c}})}return l}(i.value,u,{customKeyFilter:{...e.customKeyFilter,...t.unref(o?.customKeyFilter)},default:e.customFilter,filterKeys:e.filterKeys,filterMode:e.filterMode,noFilter:e.noFilter}),d=t.unref(a),v=[],m=new Map
c.forEach((e=>{let{index:t,matches:a}=e
const l=d[t]
v.push(l),m.set(l.value,a)})),n.value=v,r.value=m})),{filteredItems:n,filteredMatches:r,getMatches:function(e){return r.value.get(e.value)}}}function Hi(e,a,l){return null!=l&&l.length?l.map(((o,n)=>{const r=0===n?0:l[n-1][1],i=[t.createElementVNode("span",{class:t.normalizeClass(`${e}__unmask`)},[a.slice(r,o[0])]),t.createElementVNode("span",{class:t.normalizeClass(`${e}__mask`)},[a.slice(o[0],o[1])])]
return n===l.length-1&&i.push(t.createElementVNode("span",{class:t.normalizeClass(`${e}__unmask`)},[a.slice(o[1])])),t.createElementVNode(t.Fragment,null,[i])})):a}const Wi=yt({autoSelectFirst:{type:[Boolean,String]},clearOnSelect:Boolean,search:String,...Li({filterKeys:["title"]}),...Fi(),...C(Ei({modelValue:null,role:"combobox"}),["validationValue","dirty","appendInnerIcon"]),...fl({transition:!1})},"VAutocomplete"),Ui=Et()({name:"VAutocomplete",props:Wi(),emits:{"update:focused":e=>!0,"update:search":e=>!0,"update:modelValue":e=>!0,"update:menu":e=>!0},setup(e,l){let{slots:o}=l
const{t:n}=ga(),r=t.ref(),i=t.shallowRef(!1),s=t.shallowRef(!0),u=t.shallowRef(!1),c=t.ref(),v=t.ref(),p=t.shallowRef(-1),{items:m,transformIn:f,transformOut:g}=Cr(e),{textColorClasses:h,textColorStyles:y}=dl((()=>r.value?.color)),b=ra(e,"search",""),V=ra(e,"modelValue",[],(e=>f(null===e?[null]:B(e))),(t=>{const a=g(t)
return e.multiple?a:a[0]??null})),w=t.computed((()=>"function"==typeof e.counterValue?e.counterValue(V.value):"number"==typeof e.counterValue?e.counterValue:V.value.length)),S=vn(e),{filteredItems:k,getMatches:x}=ji(e,m,(()=>s.value?"":b.value)),C=t.computed((()=>e.hideSelected?k.value.filter((e=>!V.value.some((t=>t.value===e.value)))):k.value)),N=t.computed((()=>!(!e.chips&&!o.chip))),E=t.computed((()=>N.value||!!o.selection)),I=t.computed((()=>V.value.map((e=>e.props.value)))),_=t.computed((()=>(!0===e.autoSelectFirst||"exact"===e.autoSelectFirst&&b.value===C.value[0]?.title)&&C.value.length>0&&!s.value&&!u.value)),P=t.computed((()=>e.hideNoData&&!C.value.length||S.isReadonly.value||S.isDisabled.value)),R=ra(e,"menu"),A=t.computed({get:()=>R.value,set:e=>{R.value&&!e&&c.value?.ΨopenChildren.size||e&&P.value||(R.value=e)}}),T=t.computed((()=>A.value?e.closeText:e.openText)),D=t.ref(),F=Di(D,r)
function z(t){e.openOnClear&&(A.value=!0),b.value=""}function $(){P.value||(A.value=!0)}function M(e){P.value||(i.value&&(e.preventDefault(),e.stopPropagation()),A.value=!A.value)}function O(e){" "!==e.key&&oe(e)&&r.value?.focus()}function L(t){if(S.isReadonly.value)return
const a=r.value?.selectionStart,l=V.value.length
if(["Enter","ArrowDown","ArrowUp"].includes(t.key)&&t.preventDefault(),["Enter","ArrowDown"].includes(t.key)&&(A.value=!0),["Escape"].includes(t.key)&&(A.value=!1),_.value&&["Enter","Tab"].includes(t.key)&&!V.value.some((e=>{let{value:t}=e
return t===C.value[0].value}))&&K(C.value[0]),"ArrowDown"===t.key&&_.value&&D.value?.focus("next"),["Backspace","Delete"].includes(t.key)){if(!e.multiple&&E.value&&V.value.length>0&&!b.value)return K(V.value[0],!1)
if(~p.value){t.preventDefault()
const e=p.value
K(V.value[p.value],!1),p.value=e>=l-1?l-2:e}else"Backspace"!==t.key||b.value||(p.value=l-1)}else if(e.multiple)if("ArrowLeft"===t.key){if(p.value<0&&a&&a>0)return
const e=p.value>-1?p.value-1:l-1
if(V.value[e])p.value=e
else{const e=b.value?.length??null
p.value=-1,r.value?.setSelectionRange(e,e)}}else if("ArrowRight"===t.key){if(p.value<0)return
const e=p.value+1
V.value[e]?p.value=e:(p.value=-1,r.value?.setSelectionRange(0,0))}else~p.value&&oe(t)&&(p.value=-1)}function j(e){if(te(r.value,":autofill")||te(r.value,":-webkit-autofill")){const t=m.value.find((t=>t.title===e.target.value))
t&&K(t)}}function H(){e.eager&&v.value?.calculateVisibleItems()}function W(){i.value&&(s.value=!0,r.value?.focus())}function U(e){i.value=!0,setTimeout((()=>{u.value=!0}))}function Y(e){u.value=!1}function G(t){null!=t&&(""!==t||e.multiple||E.value)||(V.value=[])}const q=t.shallowRef(!1)
function K(a){let l=!(arguments.length>1&&void 0!==arguments[1])||arguments[1]
if(a&&!a.props.disabled)if(e.multiple){const t=V.value.findIndex((t=>(e.valueComparator||d)(t.value,a.value))),o=null==l?!~t:l
if(~t){const e=o?[...V.value,a]:[...V.value]
e.splice(t,1),V.value=e}else o&&(V.value=[...V.value,a])
e.clearOnSelect&&(b.value="")}else{const e=!1!==l
V.value=e?[a]:[],b.value=e&&!E.value?a.title:"",t.nextTick((()=>{A.value=!1,s.value=!0}))}}return t.watch(i,((a,l)=>{a!==l&&(a?(q.value=!0,b.value=e.multiple||E.value?"":String(V.value.at(-1)?.props.title??""),s.value=!0,t.nextTick((()=>q.value=!1))):(e.multiple||null!=b.value||(V.value=[]),A.value=!1,(e.multiple||E.value)&&(b.value=""),p.value=-1))})),t.watch(b,(e=>{i.value&&!q.value&&(e&&(A.value=!0),s.value=!e)})),t.watch(A,(()=>{if(!e.hideSelected&&A.value&&V.value.length){const e=C.value.findIndex((e=>V.value.some((t=>e.value===t.value))))
a&&window.requestAnimationFrame((()=>{e>=0&&v.value?.scrollToIndex(e)}))}})),t.watch((()=>e.items),((e,t)=>{A.value||i.value&&!t.length&&e.length&&(A.value=!0)})),Ft((()=>{const a=!!(!e.hideNoData||C.value.length||o["prepend-item"]||o["append-item"]||o["no-data"]),l=V.value.length>0,u=Ii.filterProps(e)
return t.createVNode(Ii,t.mergeProps({ref:r},u,{modelValue:b.value,"onUpdate:modelValue":[e=>b.value=e,G],focused:i.value,"onUpdate:focused":e=>i.value=e,validationValue:V.externalValue,counterValue:w.value,dirty:l,onChange:j,class:["v-autocomplete","v-autocomplete--"+(e.multiple?"multiple":"single"),{"v-autocomplete--active-menu":A.value,"v-autocomplete--chips":!!e.chips,"v-autocomplete--selection-slot":!!E.value,"v-autocomplete--selecting-index":p.value>-1},e.class],style:e.style,readonly:S.isReadonly.value,placeholder:l?void 0:e.placeholder,"onClick:clear":z,"onMousedown:control":$,onKeydown:L}),{...o,default:()=>t.createElementVNode(t.Fragment,null,[t.createVNode(yi,t.mergeProps({ref:c,modelValue:A.value,"onUpdate:modelValue":e=>A.value=e,activator:"parent",contentClass:"v-autocomplete__content",disabled:P.value,eager:e.eager,maxHeight:310,openOnClick:!1,closeOnContentClick:!1,transition:e.transition,onAfterEnter:H,onAfterLeave:W},e.menuProps),{default:()=>[a&&t.createVNode(_r,t.mergeProps({ref:D,selected:I.value,selectStrategy:e.multiple?"independent":"single-independent",onMousedown:e=>e.preventDefault(),onKeydown:O,onFocusin:U,onFocusout:Y,tabindex:"-1","aria-live":"polite",color:e.itemColor??e.color},F,e.listProps),{default:()=>[o["prepend-item"]?.(),!C.value.length&&!e.hideNoData&&(o["no-data"]?.()??t.createVNode(fr,{key:"no-data",title:n(e.noDataText)},null)),t.createVNode(Ti,{ref:v,renderless:!0,items:C.value,itemKey:"value"},{default:a=>{let{item:l,index:n,itemRef:r}=a
const i=t.mergeProps(l.props,{ref:r,key:l.value,active:!(!_.value||0!==n)||void 0,onClick:()=>K(l,null)})
return o.item?.({item:l,index:n,props:i})??t.createVNode(fr,t.mergeProps(i,{role:"option"}),{prepend:a=>{let{isSelected:o}=a
return t.createElementVNode(t.Fragment,null,[e.multiple&&!e.hideSelected?t.createVNode(ln,{key:l.value,modelValue:o,ripple:!1,tabindex:"-1"},null):void 0,l.props.prependAvatar&&t.createVNode(Go,{image:l.props.prependAvatar},null),l.props.prependIcon&&t.createVNode(Jl,{icon:l.props.prependIcon},null)])},title:()=>s.value?l.title:Hi("v-autocomplete",l.title,x(l)?.title)})}}),o["append-item"]?.()]})]}),V.value.map(((a,l)=>{function n(e){e.stopPropagation(),e.preventDefault(),K(a,!1)}const r={"onClick:close":n,onKeydown(e){"Enter"!==e.key&&" "!==e.key||(e.preventDefault(),e.stopPropagation(),n(e))},onMousedown(e){e.preventDefault(),e.stopPropagation()},modelValue:!0,"onUpdate:modelValue":void 0},i=N.value?!!o.chip:!!o.selection,s=i?ae(N.value?o.chip({item:a,index:l,props:r}):o.selection({item:a,index:l})):void 0
if(!i||s)return t.createElementVNode("div",{key:a.value,class:t.normalizeClass(["v-autocomplete__selection",l===p.value&&["v-autocomplete__selection--selected",h.value]]),style:t.normalizeStyle(l===p.value?y.value:{})},[N.value?o.chip?t.createVNode(nl,{key:"chip-defaults",defaults:{VChip:{closable:e.closableChips,size:"small",text:a.title}}},{default:()=>[s]}):t.createVNode(Un,t.mergeProps({key:"chip",closable:e.closableChips,size:"small",text:a.title,disabled:a.props.disabled},r),null):s??t.createElementVNode("span",{class:"v-autocomplete__selection-text"},[a.title,e.multiple&&l<V.value.length-1&&t.createElementVNode("span",{class:"v-autocomplete__selection-comma"},[t.createTextVNode(",")])])])}))]),"append-inner":function(){for(var a=arguments.length,l=new Array(a),i=0;i<a;i++)l[i]=arguments[i]
return t.createElementVNode(t.Fragment,null,[o["append-inner"]?.(...l),e.menuIcon?t.createVNode(Jl,{class:"v-autocomplete__menu-icon",color:r.value?.fieldIconColor,icon:e.menuIcon,onMousedown:M,onClick:ee,"aria-label":n(T.value),title:n(T.value),tabindex:"-1"},null):void 0])}})})),gi({isFocused:i,isPristine:s,menu:A,search:b,filteredItems:k,select:K},r)}}),Yi=yt({bordered:Boolean,color:String,content:[Number,String],dot:Boolean,floating:Boolean,icon:zt,inline:Boolean,label:{type:String,default:"$vuetify.badge"},max:[Number,String],modelValue:{type:Boolean,default:!0},offsetX:[Number,String],offsetY:[Number,String],textColor:String,...bt(),...oo({location:"top end"}),...pl(),...Ba(),...Va(),...fl({transition:"scale-rotate-transition"})},"VBadge"),Gi=Et()({name:"VBadge",inheritAttrs:!1,props:Yi(),setup(e,a){const{backgroundColorClasses:l,backgroundColorStyles:o}=vl((()=>e.color)),{roundedClasses:n}=ml(e),{t:r}=ga(),{textColorClasses:i,textColorStyles:s}=dl((()=>e.textColor)),{themeClasses:u}=Ia(),{locationStyles:c}=no(e,!0,(t=>(e.floating?e.dot?2:4:e.dot?8:12)+(["top","bottom"].includes(t)?Number(e.offsetY??0):["left","right"].includes(t)?Number(e.offsetX??0):0)))
return Ft((()=>{const d=Number(e.content),v=!e.max||isNaN(d)?e.content:d<=Number(e.max)?d:`${e.max}+`,[p,m]=x(a.attrs,["aria-atomic","aria-label","aria-live","role","title"])
return t.createVNode(e.tag,t.mergeProps({class:["v-badge",{"v-badge--bordered":e.bordered,"v-badge--dot":e.dot,"v-badge--floating":e.floating,"v-badge--inline":e.inline},e.class]},m,{style:e.style}),{default:()=>[t.createElementVNode("div",{class:"v-badge__wrapper"},[a.slots.default?.(),t.createVNode(gl,{transition:e.transition},{default:()=>[t.withDirectives(t.createElementVNode("span",t.mergeProps({class:["v-badge__badge",u.value,l.value,n.value,i.value],style:[o.value,s.value,e.inline?{}:c.value],"aria-atomic":"true","aria-label":r(e.label,d),"aria-live":"polite",role:"status"},p),[e.dot?void 0:a.slots.badge?a.slots.badge?.():e.icon?t.createVNode(Jl,{icon:e.icon},null):v]),[[t.vShow,e.modelValue]])]})])]})})),{}}}),qi=yt({color:String,density:String,...bt()},"VBannerActions"),Ki=Et()({name:"VBannerActions",props:qi(),setup(e,a){let{slots:l}=a
return xt({VBtn:{color:e.color,density:e.density,slim:!0,variant:"text"}}),Ft((()=>t.createElementVNode("div",{class:t.normalizeClass(["v-banner-actions",e.class]),style:t.normalizeStyle(e.style)},[l.default?.()]))),{}}}),Xi=It("v-banner-text"),Zi=yt({avatar:String,bgColor:String,color:String,icon:zt,lines:String,stacked:Boolean,sticky:Boolean,text:String,...wl(),...bt(),...Al(),...rl(),...En({mobile:null}),...kl(),...oo(),...po(),...pl(),...Ba(),...Va()},"VBanner"),Qi=Et()({name:"VBanner",props:Zi(),setup(e,a){let{slots:l}=a
const{backgroundColorClasses:o,backgroundColorStyles:n}=vl((()=>e.bgColor)),{borderClasses:r}=Sl(e),{densityClasses:i}=Tl(e),{displayClasses:s,mobile:u}=In(e),{dimensionStyles:c}=il(e),{elevationClasses:d}=xl(e),{locationStyles:v}=no(e),{positionClasses:p}=mo(e),{roundedClasses:m}=ml(e),{themeClasses:f}=Ea(e),g=t.toRef((()=>e.color)),h=t.toRef((()=>e.density))
xt({VBannerActions:{color:g,density:h}}),Ft((()=>{const a=!(!e.text&&!l.text),y=!(!e.avatar&&!e.icon),b=!(!y&&!l.prepend)
return t.createVNode(e.tag,{class:t.normalizeClass(["v-banner",{"v-banner--stacked":e.stacked||u.value,"v-banner--sticky":e.sticky,[`v-banner--${e.lines}-line`]:!!e.lines},f.value,o.value,r.value,i.value,s.value,d.value,p.value,m.value,e.class]),style:t.normalizeStyle([n.value,c.value,v.value,e.style]),role:"banner"},{default:()=>[b&&t.createElementVNode("div",{key:"prepend",class:"v-banner__prepend"},[l.prepend?t.createVNode(nl,{key:"prepend-defaults",disabled:!y,defaults:{VAvatar:{color:g.value,density:h.value,icon:e.icon,image:e.avatar}}},l.prepend):t.createVNode(Go,{key:"prepend-avatar",color:g.value,density:h.value,icon:e.icon,image:e.avatar},null)]),t.createElementVNode("div",{class:"v-banner__content"},[a&&t.createVNode(Xi,{key:"text"},{default:()=>[l.text?.()??e.text]}),l.default?.()]),l.actions&&t.createVNode(Ki,{key:"actions"},l.actions)]})}))}}),Ji=yt({baseColor:String,bgColor:String,color:String,grow:Boolean,mode:{type:String,validator:e=>!e||["horizontal","shift"].includes(e)},height:{type:[Number,String],default:56},active:{type:Boolean,default:!0},...wl(),...bt(),...Al(),...kl(),...pl(),...ta({name:"bottom-navigation"}),...Ba({tag:"header"}),...Ll({selectedClass:"v-btn--selected"}),...Va()},"VBottomNavigation"),es=Et()({name:"VBottomNavigation",props:Ji(),emits:{"update:active":e=>!0,"update:modelValue":e=>!0},setup(e,a){let{slots:l}=a
const{themeClasses:o}=Ia(),{borderClasses:n}=Sl(e),{backgroundColorClasses:r,backgroundColorStyles:i}=vl((()=>e.bgColor)),{densityClasses:s}=Tl(e),{elevationClasses:u}=xl(e),{roundedClasses:c}=ml(e),{ssrBootStyles:d}=_l(),v=t.computed((()=>Number(e.height)-("comfortable"===e.density?8:0)-("compact"===e.density?16:0))),p=ra(e,"active",e.active),{layoutItemStyles:m}=la({id:e.name,order:t.computed((()=>parseInt(e.order,10))),position:t.toRef((()=>"bottom")),layoutSize:t.toRef((()=>p.value?v.value:0)),elementSize:v,active:p,absolute:t.toRef((()=>e.absolute))})
return Wl(e,Yl),xt({VBtn:{baseColor:t.toRef((()=>e.baseColor)),color:t.toRef((()=>e.color)),density:t.toRef((()=>e.density)),stacked:t.toRef((()=>"horizontal"!==e.mode)),variant:"text"}},{scoped:!0}),Ft((()=>t.createVNode(e.tag,{class:t.normalizeClass(["v-bottom-navigation",{"v-bottom-navigation--active":p.value,"v-bottom-navigation--grow":e.grow,"v-bottom-navigation--shift":"shift"===e.mode},o.value,r.value,n.value,s.value,u.value,c.value,e.class]),style:t.normalizeStyle([i.value,m.value,{height:f(v.value)},d.value,e.style])},{default:()=>[l.default&&t.createElementVNode("div",{class:"v-bottom-navigation__content"},[l.default()])]}))),{}}}),ts=yt({fullscreen:Boolean,retainFocus:{type:Boolean,default:!0},scrollable:Boolean,...vi({origin:"center center",scrollStrategy:"block",transition:{component:Oa},zIndex:2400})},"VDialog"),as=Et()({name:"VDialog",props:ts(),emits:{"update:modelValue":e=>!0,afterEnter:()=>!0,afterLeave:()=>!0},setup(e,l){let{emit:o,slots:n}=l
const r=ra(e,"modelValue"),{scopeId:i}=li(),s=t.ref()
function u(e){const t=e.relatedTarget,a=e.target
if(t!==a&&s.value?.contentEl&&s.value?.globalTop&&![document,s.value.contentEl].includes(a)&&!s.value.contentEl.contains(a)){const e=X(s.value.contentEl)
if(!e.length)return
const a=e[0],l=e[e.length-1]
t===a?l.focus():a.focus()}}function c(){o("afterEnter"),(e.scrim||e.retainFocus)&&s.value?.contentEl&&!s.value.contentEl.contains(document.activeElement)&&s.value.contentEl.focus({preventScroll:!0})}function d(){o("afterLeave")}return t.onBeforeUnmount((()=>{document.removeEventListener("focusin",u)})),a&&t.watch((()=>r.value&&e.retainFocus),(e=>{e?document.addEventListener("focusin",u):document.removeEventListener("focusin",u)}),{immediate:!0}),t.watch(r,(async e=>{e||(await t.nextTick(),s.value.activatorEl?.focus({preventScroll:!0}))})),Ft((()=>{const a=pi.filterProps(e),l=t.mergeProps({"aria-haspopup":"dialog"},e.activatorProps),o=t.mergeProps({tabindex:-1},e.contentProps)
return t.createVNode(pi,t.mergeProps({ref:s,class:["v-dialog",{"v-dialog--fullscreen":e.fullscreen,"v-dialog--scrollable":e.scrollable},e.class],style:e.style},a,{modelValue:r.value,"onUpdate:modelValue":e=>r.value=e,"aria-modal":"true",activatorProps:l,contentProps:o,height:e.fullscreen?void 0:e.height,width:e.fullscreen?void 0:e.width,maxHeight:e.fullscreen?void 0:e.maxHeight,maxWidth:e.fullscreen?void 0:e.maxWidth,role:"dialog",onAfterEnter:c,onAfterLeave:d},i),{activator:n.activator,default:function(){for(var e=arguments.length,a=new Array(e),l=0;l<e;l++)a[l]=arguments[l]
return t.createVNode(nl,{root:"VDialog"},{default:()=>[n.default?.(...a)]})}})})),gi({},s)}}),ls=yt({inset:Boolean,...ts({transition:"bottom-sheet-transition"})},"VBottomSheet"),os=Et()({name:"VBottomSheet",props:ls(),emits:{"update:modelValue":e=>!0},setup(e,a){let{slots:l}=a
const o=ra(e,"modelValue")
return Ft((()=>{const a=as.filterProps(e)
return t.createVNode(as,t.mergeProps(a,{contentClass:["v-bottom-sheet__content",e.contentClass],modelValue:o.value,"onUpdate:modelValue":e=>o.value=e,class:["v-bottom-sheet",{"v-bottom-sheet--inset":e.inset},e.class],style:e.style}),l)})),{}}}),ns=yt({divider:[Number,String],...bt()},"VBreadcrumbsDivider"),rs=Et()({name:"VBreadcrumbsDivider",props:ns(),setup(e,a){let{slots:l}=a
return Ft((()=>t.createElementVNode("li",{"aria-hidden":"true",class:t.normalizeClass(["v-breadcrumbs-divider",e.class]),style:t.normalizeStyle(e.style)},[l?.default?.()??e.divider]))),{}}}),is=yt({active:Boolean,activeClass:String,activeColor:String,color:String,disabled:Boolean,title:String,...bt(),...ho(),...Ba({tag:"li"})},"VBreadcrumbsItem"),ss=Et()({name:"VBreadcrumbsItem",props:is(),setup(e,a){let{slots:l,attrs:o}=a
const n=go(e,o),r=t.computed((()=>e.active||n.isActive?.value)),{textColorClasses:i,textColorStyles:s}=dl((()=>r.value?e.activeColor:e.color))
return Ft((()=>t.createVNode(e.tag,{class:t.normalizeClass(["v-breadcrumbs-item",{"v-breadcrumbs-item--active":r.value,"v-breadcrumbs-item--disabled":e.disabled,[`${e.activeClass}`]:r.value&&e.activeClass},i.value,e.class]),style:t.normalizeStyle([s.value,e.style]),"aria-current":r.value?"page":void 0},{default:()=>[n.isLink.value?t.createElementVNode("a",t.mergeProps({class:"v-breadcrumbs-item--link",onClick:n.navigate},n.linkProps),[l.default?.()??e.title]):l.default?.()??e.title]}))),{}}}),us=yt({activeClass:String,activeColor:String,bgColor:String,color:String,disabled:Boolean,divider:{type:String,default:"/"},icon:zt,items:{type:Array,default:()=>[]},...bt(),...Al(),...pl(),...Ba({tag:"ul"})},"VBreadcrumbs"),cs=Et()({name:"VBreadcrumbs",props:us(),setup(e,a){let{slots:l}=a
const{backgroundColorClasses:o,backgroundColorStyles:n}=vl((()=>e.bgColor)),{densityClasses:r}=Tl(e),{roundedClasses:i}=ml(e)
xt({VBreadcrumbsDivider:{divider:t.toRef((()=>e.divider))},VBreadcrumbsItem:{activeClass:t.toRef((()=>e.activeClass)),activeColor:t.toRef((()=>e.activeColor)),color:t.toRef((()=>e.color)),disabled:t.toRef((()=>e.disabled))}})
const s=t.computed((()=>e.items.map((e=>"string"==typeof e?{item:{title:e},raw:e}:{item:e,raw:e}))))
return Ft((()=>{const a=!(!l.prepend&&!e.icon)
return t.createVNode(e.tag,{class:t.normalizeClass(["v-breadcrumbs",o.value,r.value,i.value,e.class]),style:t.normalizeStyle([n.value,e.style])},{default:()=>[a&&t.createElementVNode("li",{key:"prepend",class:"v-breadcrumbs__prepend"},[l.prepend?t.createVNode(nl,{key:"prepend-defaults",disabled:!e.icon,defaults:{VIcon:{icon:e.icon,start:!0}}},l.prepend):t.createVNode(Jl,{key:"prepend-icon",start:!0,icon:e.icon},null)]),s.value.map(((e,a,o)=>{let{item:n,raw:r}=e
return t.createElementVNode(t.Fragment,null,[l.item?.({item:n,index:a})??t.createVNode(ss,t.mergeProps({key:a,disabled:a>=o.length-1},"string"==typeof n?{title:n}:n),{default:l.title?()=>l.title?.({item:n,index:a}):void 0}),a<o.length-1&&t.createVNode(rs,null,{default:l.divider?()=>l.divider?.({item:r,index:a}):void 0})])})),l.default?.()]})})),{}}}),ds=Et()({name:"VCardActions",props:bt(),setup(e,a){let{slots:l}=a
return xt({VBtn:{slim:!0,variant:"text"}}),Ft((()=>t.createElementVNode("div",{class:t.normalizeClass(["v-card-actions",e.class]),style:t.normalizeStyle(e.style)},[l.default?.()]))),{}}}),vs=yt({opacity:[Number,String],...bt(),...Ba()},"VCardSubtitle"),ps=Et()({name:"VCardSubtitle",props:vs(),setup(e,a){let{slots:l}=a
return Ft((()=>t.createVNode(e.tag,{class:t.normalizeClass(["v-card-subtitle",e.class]),style:t.normalizeStyle([{"--v-card-subtitle-opacity":e.opacity},e.style])},l))),{}}}),ms=It("v-card-title"),fs=yt({appendAvatar:String,appendIcon:zt,prependAvatar:String,prependIcon:zt,subtitle:{type:[String,Number,Boolean],default:void 0},title:{type:[String,Number,Boolean],default:void 0},...bt(),...Al()},"VCardItem"),gs=Et()({name:"VCardItem",props:fs(),setup(e,a){let{slots:l}=a
return Ft((()=>{const a=!(!e.prependAvatar&&!e.prependIcon),o=!(!a&&!l.prepend),n=!(!e.appendAvatar&&!e.appendIcon),r=!(!n&&!l.append),i=!(null==e.title&&!l.title),s=!(null==e.subtitle&&!l.subtitle)
return t.createElementVNode("div",{class:t.normalizeClass(["v-card-item",e.class]),style:t.normalizeStyle(e.style)},[o&&t.createElementVNode("div",{key:"prepend",class:"v-card-item__prepend"},[l.prepend?t.createVNode(nl,{key:"prepend-defaults",disabled:!a,defaults:{VAvatar:{density:e.density,image:e.prependAvatar},VIcon:{density:e.density,icon:e.prependIcon}}},l.prepend):t.createElementVNode(t.Fragment,null,[e.prependAvatar&&t.createVNode(Go,{key:"prepend-avatar",density:e.density,image:e.prependAvatar},null),e.prependIcon&&t.createVNode(Jl,{key:"prepend-icon",density:e.density,icon:e.prependIcon},null)])]),t.createElementVNode("div",{class:"v-card-item__content"},[i&&t.createVNode(ms,{key:"title"},{default:()=>[l.title?.()??t.toDisplayString(e.title)]}),s&&t.createVNode(ps,{key:"subtitle"},{default:()=>[l.subtitle?.()??t.toDisplayString(e.subtitle)]}),l.default?.()]),r&&t.createElementVNode("div",{key:"append",class:"v-card-item__append"},[l.append?t.createVNode(nl,{key:"append-defaults",disabled:!n,defaults:{VAvatar:{density:e.density,image:e.appendAvatar},VIcon:{density:e.density,icon:e.appendIcon}}},l.append):t.createElementVNode(t.Fragment,null,[e.appendIcon&&t.createVNode(Jl,{key:"append-icon",density:e.density,icon:e.appendIcon},null),e.appendAvatar&&t.createVNode(Go,{key:"append-avatar",density:e.density,image:e.appendAvatar},null)])])])})),{}}}),hs=yt({opacity:[Number,String],...bt(),...Ba()},"VCardText"),ys=Et()({name:"VCardText",props:hs(),setup(e,a){let{slots:l}=a
return Ft((()=>t.createVNode(e.tag,{class:t.normalizeClass(["v-card-text",e.class]),style:t.normalizeStyle([{"--v-card-text-opacity":e.opacity},e.style])},l))),{}}}),bs=yt({appendAvatar:String,appendIcon:zt,disabled:Boolean,flat:Boolean,hover:Boolean,image:String,link:{type:Boolean,default:void 0},prependAvatar:String,prependIcon:zt,ripple:{type:[Boolean,Object],default:!0},subtitle:{type:[String,Number,Boolean],default:void 0},text:{type:[String,Number,Boolean],default:void 0},title:{type:[String,Number,Boolean],default:void 0},...wl(),...bt(),...Al(),...rl(),...kl(),...so(),...oo(),...po(),...pl(),...ho(),...Ba(),...Va(),...zl({variant:"elevated"})},"VCard"),Vs=Et()({name:"VCard",directives:{vRipple:Fo},props:bs(),setup(e,a){let{attrs:l,slots:o}=a
const{themeClasses:n}=Ea(e),{borderClasses:r}=Sl(e),{colorClasses:i,colorStyles:s,variantClasses:u}=$l(e),{densityClasses:c}=Tl(e),{dimensionStyles:d}=il(e),{elevationClasses:v}=xl(e),{loaderClasses:p}=uo(e),{locationStyles:m}=no(e),{positionClasses:f}=mo(e),{roundedClasses:g}=ml(e),h=go(e,l)
return Ft((()=>{const a=!1!==e.link&&h.isLink.value,l=!e.disabled&&!1!==e.link&&(e.link||h.isClickable.value),y=a?"a":e.tag,b=!(!o.title&&null==e.title),V=!(!o.subtitle&&null==e.subtitle),w=b||V,S=!!(o.append||e.appendAvatar||e.appendIcon),k=!!(o.prepend||e.prependAvatar||e.prependIcon),x=!(!o.image&&!e.image),C=w||k||S,N=!(!o.text&&null==e.text)
return t.withDirectives(t.createVNode(y,t.mergeProps({class:["v-card",{"v-card--disabled":e.disabled,"v-card--flat":e.flat,"v-card--hover":e.hover&&!(e.disabled||e.flat),"v-card--link":l},n.value,r.value,i.value,c.value,v.value,p.value,f.value,g.value,u.value,e.class],style:[s.value,d.value,m.value,e.style],onClick:l&&h.navigate,tabindex:e.disabled?-1:void 0},h.linkProps),{default:()=>[x&&t.createElementVNode("div",{key:"image",class:"v-card__image"},[o.image?t.createVNode(nl,{key:"image-defaults",disabled:!e.image,defaults:{VImg:{cover:!0,src:e.image}}},o.image):t.createVNode(Vl,{key:"image-img",cover:!0,src:e.image},null)]),t.createVNode(co,{name:"v-card",active:!!e.loading,color:"boolean"==typeof e.loading?void 0:e.loading},{default:o.loader}),C&&t.createVNode(gs,{key:"item",prependAvatar:e.prependAvatar,prependIcon:e.prependIcon,title:e.title,subtitle:e.subtitle,appendAvatar:e.appendAvatar,appendIcon:e.appendIcon},{default:o.item,prepend:o.prepend,title:o.title,subtitle:o.subtitle,append:o.append}),N&&t.createVNode(ys,{key:"text"},{default:()=>[o.text?.()??e.text]}),o.default?.(),o.actions&&t.createVNode(ds,null,{default:o.actions}),Fl(l,"v-card")]}),[[Fo,l&&e.ripple]])})),{}}}),ws=e=>{const{touchstartX:t,touchendX:a,touchstartY:l,touchendY:o}=e
e.offsetX=a-t,e.offsetY=o-l,Math.abs(e.offsetY)<.5*Math.abs(e.offsetX)&&(e.left&&a<t-16&&e.left(e),e.right&&a>t+16&&e.right(e)),Math.abs(e.offsetX)<.5*Math.abs(e.offsetY)&&(e.up&&o<l-16&&e.up(e),e.down&&o>l+16&&e.down(e))}
function Ss(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}
const t={touchstartX:0,touchstartY:0,touchendX:0,touchendY:0,touchmoveX:0,touchmoveY:0,offsetX:0,offsetY:0,left:e.left,right:e.right,up:e.up,down:e.down,start:e.start,move:e.move,end:e.end}
return{touchstart:e=>function(e,t){const a=e.changedTouches[0]
t.touchstartX=a.clientX,t.touchstartY=a.clientY,t.start?.({originalEvent:e,...t})}(e,t),touchend:e=>function(e,t){const a=e.changedTouches[0]
t.touchendX=a.clientX,t.touchendY=a.clientY,t.end?.({originalEvent:e,...t}),ws(t)}(e,t),touchmove:e=>function(e,t){const a=e.changedTouches[0]
t.touchmoveX=a.clientX,t.touchmoveY=a.clientY,t.move?.({originalEvent:e,...t})}(e,t)}}const ks={mounted:function(e,t){const a=t.value,l=a?.parent?e.parentElement:e,o=a?.options??{passive:!0},n=t.instance?.$.uid
if(!l||!n)return
const r=Ss(t.value)
l._touchHandlers=l._touchHandlers??Object.create(null),l._touchHandlers[n]=r,w(r).forEach((e=>{l.addEventListener(e,r[e],o)}))},unmounted:function(e,t){const a=t.value?.parent?e.parentElement:e,l=t.instance?.$.uid
if(!a?._touchHandlers||!l)return
const o=a._touchHandlers[l]
w(o).forEach((e=>{a.removeEventListener(e,o[e])})),delete a._touchHandlers[l]}},xs=Symbol.for("vuetify:v-window"),Cs=Symbol.for("vuetify:v-window-group"),Ns=yt({continuous:Boolean,nextIcon:{type:[Boolean,String,Function,Object],default:"$next"},prevIcon:{type:[Boolean,String,Function,Object],default:"$prev"},reverse:Boolean,showArrows:{type:[Boolean,String],validator:e=>"boolean"==typeof e||"hover"===e},touch:{type:[Object,Boolean],default:void 0},direction:{type:String,default:"horizontal"},modelValue:null,disabled:Boolean,selectedClass:{type:String,default:"v-window-item--active"},mandatory:{type:[Boolean,String],default:"force"},...bt(),...Ba(),...Va()},"VWindow"),Es=Et()({name:"VWindow",directives:{vTouch:ks},props:Ns(),emits:{"update:modelValue":e=>!0},setup(e,a){let{slots:l}=a
const{themeClasses:o}=Ea(e),{isRtl:n}=ya(),{t:r}=ga(),i=Wl(e,Cs),s=t.ref(),u=t.computed((()=>n.value?!e.reverse:e.reverse)),c=t.shallowRef(!1),d=t.computed((()=>`v-window-${"vertical"===e.direction?"y":"x"}${(u.value?!c.value:c.value)?"-reverse":""}-transition`)),v=t.shallowRef(0),p=t.ref(void 0),m=t.computed((()=>i.items.value.findIndex((e=>i.selected.value.includes(e.id)))))
t.watch(m,((e,t)=>{const a=i.items.value.length,l=a-1
c.value=a<=2?e<t:e===l&&0===t||(0!==e||t!==l)&&e<t})),t.provide(xs,{transition:d,isReversed:c,transitionCount:v,transitionHeight:p,rootRef:s})
const f=t.toRef((()=>e.continuous||0!==m.value)),g=t.toRef((()=>e.continuous||m.value!==i.items.value.length-1))
function h(){f.value&&i.prev()}function y(){g.value&&i.next()}const b=t.computed((()=>{const a=[],o={icon:n.value?e.nextIcon:e.prevIcon,class:"v-window__"+(u.value?"right":"left"),onClick:i.prev,"aria-label":r("$vuetify.carousel.prev")}
a.push(f.value?l.prev?l.prev({props:o}):t.createVNode($o,o,null):t.createElementVNode("div",null,null))
const s={icon:n.value?e.prevIcon:e.nextIcon,class:"v-window__"+(u.value?"left":"right"),onClick:i.next,"aria-label":r("$vuetify.carousel.next")}
return a.push(g.value?l.next?l.next({props:s}):t.createVNode($o,s,null):t.createElementVNode("div",null,null)),a})),V=t.computed((()=>{if(!1===e.touch)return e.touch
return{...{left:()=>{u.value?h():y()},right:()=>{u.value?y():h()},start:e=>{let{originalEvent:t}=e
t.stopPropagation()}},...!0===e.touch?{}:e.touch}}))
return Ft((()=>t.withDirectives(t.createVNode(e.tag,{ref:s,class:t.normalizeClass(["v-window",{"v-window--show-arrows-on-hover":"hover"===e.showArrows},o.value,e.class]),style:t.normalizeStyle(e.style)},{default:()=>[t.createElementVNode("div",{class:"v-window__container",style:{height:p.value}},[l.default?.({group:i}),!1!==e.showArrows&&t.createElementVNode("div",{class:"v-window__controls"},[b.value])]),l.additional?.({group:i})]}),[[ks,V.value]]))),{group:i}}}),Is=yt({color:String,cycle:Boolean,delimiterIcon:{type:zt,default:"$delimiter"},height:{type:[Number,String],default:500},hideDelimiters:Boolean,hideDelimiterBackground:Boolean,interval:{type:[Number,String],default:6e3,validator:e=>Number(e)>0},progress:[Boolean,String],verticalDelimiters:[Boolean,String],...Ns({continuous:!0,mandatory:"force",showArrows:!0})},"VCarousel"),_s=Et()({name:"VCarousel",props:Is(),emits:{"update:modelValue":e=>!0},setup(e,a){let{slots:l}=a
const o=ra(e,"modelValue"),{t:n}=ga(),r=t.ref()
let i=-1
function s(){e.cycle&&r.value&&(i=window.setTimeout(r.value.group.next,Number(e.interval)>0?Number(e.interval):6e3))}function u(){window.clearTimeout(i),window.requestAnimationFrame(s)}return t.watch(o,u),t.watch((()=>e.interval),u),t.watch((()=>e.cycle),(e=>{e?u():window.clearTimeout(i)})),t.onMounted(s),Ft((()=>{const a=Es.filterProps(e)
return t.createVNode(Es,t.mergeProps({ref:r},a,{modelValue:o.value,"onUpdate:modelValue":e=>o.value=e,class:["v-carousel",{"v-carousel--hide-delimiter-background":e.hideDelimiterBackground,"v-carousel--vertical-delimiters":e.verticalDelimiters},e.class],style:[{height:f(e.height)},e.style]}),{default:l.default,additional:a=>{let{group:r}=a
return t.createElementVNode(t.Fragment,null,[!e.hideDelimiters&&t.createElementVNode("div",{class:"v-carousel__controls",style:{left:"left"===e.verticalDelimiters&&e.verticalDelimiters?0:"auto",right:"right"===e.verticalDelimiters?0:"auto"}},[r.items.value.length>0&&t.createVNode(nl,{defaults:{VBtn:{color:e.color,icon:e.delimiterIcon,size:"x-small",variant:"text"}},scoped:!0},{default:()=>[r.items.value.map(((e,a)=>{const o={id:`carousel-item-${e.id}`,"aria-label":n("$vuetify.carousel.ariaLabel.delimiter",a+1,r.items.value.length),class:["v-carousel__controls__item",r.isSelected(e.id)&&"v-btn--active"],onClick:()=>r.select(e.id,!0)}
return l.item?l.item({props:o,item:e}):t.createVNode($o,t.mergeProps(e,o),null)}))]})]),e.progress&&t.createVNode(io,{class:"v-carousel__progress",color:"string"==typeof e.progress?e.progress:void 0,modelValue:(r.getItemIndex(o.value)+1)/r.items.value.length*100},null)])},prev:l.prev,next:l.next})})),{}}}),Ps=yt({reverseTransition:{type:[Boolean,String],default:void 0},transition:{type:[Boolean,String],default:void 0},...bt(),...jl(),...ti()},"VWindowItem"),Bs=Et()({name:"VWindowItem",directives:{vTouch:ks},props:Ps(),emits:{"group:selected":e=>!0},setup(e,a){let{slots:l}=a
const o=t.inject(xs),n=Hl(e,Cs),{isBooted:r}=_l()
if(!o||!n)throw new Error("[Vuetify] VWindowItem must be used inside VWindow")
const i=t.shallowRef(!1),s=t.computed((()=>r.value&&(o.isReversed.value?!1!==e.reverseTransition:!1!==e.transition)))
function u(){i.value&&o&&(i.value=!1,o.transitionCount.value>0&&(o.transitionCount.value-=1,0===o.transitionCount.value&&(o.transitionHeight.value=void 0)))}function c(){!i.value&&o&&(i.value=!0,0===o.transitionCount.value&&(o.transitionHeight.value=f(o.rootRef.value?.clientHeight)),o.transitionCount.value+=1)}function d(){u()}function v(e){i.value&&t.nextTick((()=>{s.value&&i.value&&o&&(o.transitionHeight.value=f(e.clientHeight))}))}const p=t.computed((()=>{const t=o.isReversed.value?e.reverseTransition:e.transition
return!!s.value&&{name:"string"!=typeof t?o.transition.value:t,onBeforeEnter:c,onAfterEnter:u,onEnterCancelled:d,onBeforeLeave:c,onAfterLeave:u,onLeaveCancelled:d,onEnter:v}})),{hasContent:m}=ai(e,n.isSelected)
return Ft((()=>t.createVNode(gl,{transition:p.value,disabled:!r.value},{default:()=>[t.withDirectives(t.createElementVNode("div",{class:t.normalizeClass(["v-window-item",n.selectedClass.value,e.class]),style:t.normalizeStyle(e.style)},[m.value&&l.default?.()]),[[t.vShow,n.isSelected.value]])]}))),{groupItem:n}}}),Rs=yt({...bl(),...Ps()},"VCarouselItem"),As=Et()({name:"VCarouselItem",inheritAttrs:!1,props:Rs(),setup(e,a){let{slots:l,attrs:o}=a
Ft((()=>{const a=Vl.filterProps(e),n=Bs.filterProps(e)
return t.createVNode(Bs,t.mergeProps({class:["v-carousel-item",e.class]},n),{default:()=>[t.createVNode(Vl,t.mergeProps(o,a),l)]})}))}}),Ts=It("v-code","code"),Ds=Nt({name:"VColorPickerCanvas",props:yt({color:{type:Object},disabled:Boolean,dotSize:{type:[Number,String],default:10},height:{type:[Number,String],default:150},width:{type:[Number,String],default:300},...bt()},"VColorPickerCanvas")(),emits:{"update:color":e=>!0,"update:position":e=>!0},setup(e,a){let{emit:l}=a
const o=t.shallowRef(!1),n=t.ref(),r=t.shallowRef(parseFloat(e.width)),i=t.shallowRef(parseFloat(e.height)),s=t.ref({x:0,y:0}),u=t.computed({get:()=>s.value,set(t){if(!n.value)return
const{x:a,y:o}=t
s.value=t,l("update:color",{h:e.color?.h??0,s:R(a,0,r.value)/r.value,v:1-R(o,0,i.value)/i.value,a:e.color?.a??1})}}),c=t.computed((()=>{const{x:t,y:a}=u.value,l=parseInt(e.dotSize,10)/2
return{width:f(e.dotSize),height:f(e.dotSize),transform:`translate(${f(t-l)}, ${f(a-l)})`}})),{resizeRef:d}=Zt((e=>{if(!d.el?.offsetParent)return
const{width:t,height:a}=e[0].contentRect
r.value=t,i.value=a}))
function v(t){"mousedown"===t.type&&t.preventDefault(),e.disabled||(p(t),window.addEventListener("mousemove",p),window.addEventListener("mouseup",m),window.addEventListener("touchmove",p),window.addEventListener("touchend",m))}function p(t){if(e.disabled||!n.value)return
o.value=!0
const a=function(e){return"touches"in e?{clientX:e.touches[0].clientX,clientY:e.touches[0].clientY}:{clientX:e.clientX,clientY:e.clientY}}(t)
!function(e,t,a){const{left:l,top:o,width:n,height:r}=a
u.value={x:R(e-l,0,n),y:R(t-o,0,r)}}(a.clientX,a.clientY,n.value.getBoundingClientRect())}function m(){window.removeEventListener("mousemove",p),window.removeEventListener("mouseup",m),window.removeEventListener("touchmove",p),window.removeEventListener("touchend",m)}function g(){if(!n.value)return
const t=n.value,a=t.getContext("2d")
if(!a)return
const l=a.createLinearGradient(0,0,t.width,0)
l.addColorStop(0,"hsla(0, 0%, 100%, 1)"),l.addColorStop(1,`hsla(${e.color?.h??0}, 100%, 50%, 1)`),a.fillStyle=l,a.fillRect(0,0,t.width,t.height)
const o=a.createLinearGradient(0,0,0,t.height)
o.addColorStop(0,"hsla(0, 0%, 0%, 0)"),o.addColorStop(1,"hsla(0, 0%, 0%, 1)"),a.fillStyle=o,a.fillRect(0,0,t.width,t.height)}return t.watch((()=>e.color?.h),g,{immediate:!0}),t.watch((()=>[r.value,i.value]),((e,t)=>{g(),s.value={x:u.value.x*e[0]/t[0],y:u.value.y*e[1]/t[1]}}),{flush:"post"}),t.watch((()=>e.color),(()=>{o.value?o.value=!1:s.value=e.color?{x:e.color.s*r.value,y:(1-e.color.v)*i.value}:{x:0,y:0}}),{deep:!0,immediate:!0}),t.onMounted((()=>g())),Ft((()=>t.createElementVNode("div",{ref:d,class:t.normalizeClass(["v-color-picker-canvas",e.class]),style:t.normalizeStyle(e.style),onMousedown:v,onTouchstartPassive:v},[t.createElementVNode("canvas",{ref:n,width:r.value,height:i.value},null),e.color&&t.createElementVNode("div",{class:t.normalizeClass(["v-color-picker-canvas__dot",{"v-color-picker-canvas__dot--disabled":e.disabled}]),style:t.normalizeStyle(c.value)},null)]))),{}}})
const Fs={h:0,s:0,v:0,a:1},zs={inputProps:{type:"number",min:0},inputs:[{label:"R",max:255,step:1,getValue:e=>Math.round(e.r),getColor:(e,t)=>({...e,r:Number(t)})},{label:"G",max:255,step:1,getValue:e=>Math.round(e.g),getColor:(e,t)=>({...e,g:Number(t)})},{label:"B",max:255,step:1,getValue:e=>Math.round(e.b),getColor:(e,t)=>({...e,b:Number(t)})},{label:"A",max:1,step:.01,getValue:e=>{let{a:t}=e
return null!=t?Math.round(100*t)/100:1},getColor:(e,t)=>({...e,a:Number(t)})}],to:at,from:ot},$s={inputProps:{type:"number",min:0},inputs:[{label:"H",max:360,step:1,getValue:e=>Math.round(e.h),getColor:(e,t)=>({...e,h:Number(t)})},{label:"S",max:1,step:.01,getValue:e=>Math.round(100*e.s)/100,getColor:(e,t)=>({...e,s:Number(t)})},{label:"L",max:1,step:.01,getValue:e=>Math.round(100*e.l)/100,getColor:(e,t)=>({...e,l:Number(t)})},{label:"A",max:1,step:.01,getValue:e=>{let{a:t}=e
return null!=t?Math.round(100*t)/100:1},getColor:(e,t)=>({...e,a:Number(t)})}],to:nt,from:rt},Ms={inputProps:{type:"text"},inputs:[{label:"HEXA",getValue:e=>e,getColor:(e,t)=>t}],to:vt,from:function(e){return ot(dt(e))}},Os={rgb:{...zs,inputs:zs.inputs?.slice(0,3)},rgba:zs,hsl:{...$s,inputs:$s.inputs.slice(0,3)},hsla:$s,hex:{...Ms,inputs:[{label:"HEX",getValue:e=>e.slice(0,7),getColor:(e,t)=>t}]},hexa:Ms},Ls=e=>{let{label:a,...l}=e
return t.createElementVNode("div",{class:"v-color-picker-edit__input"},[t.createElementVNode("input",t.normalizeProps(t.guardReactiveProps(l)),null),t.createElementVNode("span",null,[a])])},js=Nt({name:"VColorPickerEdit",props:yt({color:Object,disabled:Boolean,mode:{type:String,default:"rgba",validator:e=>Object.keys(Os).includes(e)},modes:{type:Array,default:()=>Object.keys(Os),validator:e=>Array.isArray(e)&&e.every((e=>Object.keys(Os).includes(e)))},...bt()},"VColorPickerEdit")(),emits:{"update:color":e=>!0,"update:mode":e=>!0},setup(e,a){let{emit:l}=a
const o=t.computed((()=>e.modes.map((e=>({...Os[e],name:e}))))),n=t.computed((()=>{const t=o.value.find((t=>t.name===e.mode))
if(!t)return[]
const a=e.color?t.to(e.color):null
return t.inputs?.map((o=>{let{getValue:n,getColor:r,...i}=o
return{...t.inputProps,...i,disabled:e.disabled,value:a&&n(a),onChange:e=>{const o=e.target
o&&l("update:color",t.from(r(a??t.to(Fs),o.value)))}}}))}))
return Ft((()=>t.createElementVNode("div",{class:t.normalizeClass(["v-color-picker-edit",e.class]),style:t.normalizeStyle(e.style)},[n.value?.map((e=>t.createVNode(Ls,e,null))),o.value.length>1&&t.createVNode($o,{icon:"$unfold",size:"x-small",variant:"plain",onClick:()=>{const t=o.value.findIndex((t=>t.name===e.mode))
l("update:mode",o.value[(t+1)%o.value.length].name)}},null)]))),{}}}),Hs=Symbol.for("vuetify:v-slider")
function Ws(e,t,a){const l="vertical"===a,o=t.getBoundingClientRect(),n="touches"in e?e.touches[0]:e
return l?n.clientY-(o.top+o.height/2):n.clientX-(o.left+o.width/2)}const Us=yt({disabled:{type:Boolean,default:null},error:Boolean,readonly:{type:Boolean,default:null},max:{type:[Number,String],default:100},min:{type:[Number,String],default:0},step:{type:[Number,String],default:0},thumbColor:String,thumbLabel:{type:[Boolean,String],default:void 0,validator:e=>"boolean"==typeof e||"always"===e},thumbSize:{type:[Number,String],default:20},showTicks:{type:[Boolean,String],default:!1,validator:e=>"boolean"==typeof e||"always"===e},ticks:{type:[Array,Object]},tickSize:{type:[Number,String],default:2},color:String,trackColor:String,trackFillColor:String,trackSize:{type:[Number,String],default:4},direction:{type:String,default:"horizontal",validator:e=>["vertical","horizontal"].includes(e)},reverse:Boolean,...pl(),...kl({elevation:2}),ripple:{type:Boolean,default:!0}},"Slider"),Ys=e=>{const a=t.computed((()=>parseFloat(e.min))),l=t.computed((()=>parseFloat(e.max))),o=t.computed((()=>Number(e.step)>0?parseFloat(e.step):0)),n=t.computed((()=>Math.max(A(o.value),A(a.value))))
return{min:a,max:l,step:o,decimals:n,roundValue:function(e){if(e=parseFloat(e),o.value<=0)return e
const t=R(e,a.value,l.value),r=a.value%o.value
let i=Math.round((t-r)/o.value)*o.value+r
return t>i&&i+o.value>l.value&&(i=l.value),parseFloat(Math.min(i,l.value).toFixed(n.value))}}},Gs=e=>{let{props:a,steps:l,onSliderStart:o,onSliderMove:n,onSliderEnd:r,getActiveThumb:i}=e
const{isRtl:s}=ya(),u=t.toRef((()=>a.reverse)),c=t.computed((()=>"vertical"===a.direction)),d=t.computed((()=>c.value!==u.value)),{min:v,max:p,step:f,decimals:g,roundValue:h}=l,y=t.computed((()=>parseInt(a.thumbSize,10))),b=t.computed((()=>parseInt(a.tickSize,10))),V=t.computed((()=>parseInt(a.trackSize,10))),w=t.computed((()=>(p.value-v.value)/f.value)),S=t.toRef((()=>a.disabled)),k=t.computed((()=>a.error||a.disabled?void 0:a.thumbColor??a.color)),x=t.computed((()=>a.error||a.disabled?void 0:a.trackColor??a.color)),C=t.computed((()=>a.error||a.disabled?void 0:a.trackFillColor??a.color)),N=t.shallowRef(!1),E=t.shallowRef(0),I=t.ref(),_=t.ref()
function P(e){const t=I.value?.$el
if(!t)return
const l="vertical"===a.direction,o=l?"top":"left",n=l?"height":"width",r=l?"clientY":"clientX",{[o]:i,[n]:u}=t.getBoundingClientRect(),c=function(e,t){return"touches"in e&&e.touches.length?e.touches[0][t]:"changedTouches"in e&&e.changedTouches.length?e.changedTouches[0][t]:e[t]}(e,r)
let m=R((c-i-E.value)/u)||0
return(l?d.value:d.value!==s.value)&&(m=1-m),h(v.value+m*(p.value-v.value))}const B=e=>{const t=P(e)
null!=t&&r({value:t}),N.value=!1,E.value=0},A=e=>{const l=P(e)
_.value=i(e),_.value&&(N.value=!0,_.value.contains(e.target)?E.value=Ws(e,_.value,a.direction):(E.value=0,null!=l&&n({value:l})),null!=l&&o({value:l}),t.nextTick((()=>_.value?.focus())))},T={passive:!0,capture:!0}
function D(e){const t=P(e)
null!=t&&n({value:t})}function F(e){e.stopPropagation(),e.preventDefault(),B(e),window.removeEventListener("mousemove",D,T),window.removeEventListener("mouseup",F)}function z(e){B(e),window.removeEventListener("touchmove",D,T),e.target?.removeEventListener("touchend",z)}const $=e=>{const t=(e-v.value)/(p.value-v.value)*100
return R(isNaN(t)?0:t,0,100)},M=t.toRef((()=>a.showTicks)),O=t.computed((()=>M.value?a.ticks?Array.isArray(a.ticks)?a.ticks.map((e=>({value:e,position:$(e),label:e.toString()}))):Object.keys(a.ticks).map((e=>({value:parseFloat(e),position:$(parseFloat(e)),label:a.ticks[e]}))):w.value!==1/0?m(w.value+1).map((e=>{const t=v.value+e*f.value
return{value:t,position:$(t)}})):[]:[])),L=t.computed((()=>O.value.some((e=>{let{label:t}=e
return!!t})))),j={activeThumbRef:_,color:t.toRef((()=>a.color)),decimals:g,disabled:S,direction:t.toRef((()=>a.direction)),elevation:t.toRef((()=>a.elevation)),hasLabels:L,isReversed:u,indexFromEnd:d,min:v,max:p,mousePressed:N,numTicks:w,onSliderMousedown:function(e){0===e.button&&(e.preventDefault(),A(e),window.addEventListener("mousemove",D,T),window.addEventListener("mouseup",F,{passive:!1}))},onSliderTouchstart:function(e){A(e),window.addEventListener("touchmove",D,T),e.target?.addEventListener("touchend",z,{passive:!1})},parsedTicks:O,parseMouseMove:P,position:$,readonly:t.toRef((()=>a.readonly)),rounded:t.toRef((()=>a.rounded)),roundValue:h,showTicks:M,startOffset:E,step:f,thumbSize:y,thumbColor:k,thumbLabel:t.toRef((()=>a.thumbLabel)),ticks:t.toRef((()=>a.ticks)),tickSize:b,trackColor:x,trackContainerRef:I,trackFillColor:C,trackSize:V,vertical:c}
return t.provide(Hs,j),j},qs=yt({focused:Boolean,max:{type:Number,required:!0},min:{type:Number,required:!0},modelValue:{type:Number,required:!0},position:{type:Number,required:!0},ripple:{type:[Boolean,Object],default:!0},name:String,...bt()},"VSliderThumb"),Ks=Et()({name:"VSliderThumb",directives:{vRipple:Fo},props:qs(),emits:{"update:modelValue":e=>!0},setup(e,a){let{slots:l,emit:o}=a
const n=t.inject(Hs),{isRtl:r,rtlClasses:i}=ya()
if(!n)throw new Error("[Vuetify] v-slider-thumb must be used inside v-slider or v-range-slider")
const{min:s,max:u,thumbColor:c,step:d,disabled:v,thumbSize:p,thumbLabel:m,direction:g,isReversed:h,vertical:y,readonly:b,elevation:w,mousePressed:S,decimals:k,indexFromEnd:x}=n,C=t.computed((()=>v.value?void 0:w.value)),{elevationClasses:N}=xl(C),{textColorClasses:E,textColorStyles:I}=dl(c),{pageup:_,pagedown:P,end:B,home:R,left:A,right:T,down:D,up:F}=V,z=[_,P,B,R,A,T,D,F],$=t.computed((()=>d.value?[1,2,3]:[1,5,10]))
function M(t){const a=function(t,a){if(!z.includes(t.key))return
t.preventDefault()
const l=d.value||.1,o=(u.value-s.value)/l
if([A,T,D,F].includes(t.key)){const e=(y.value?[r.value?A:T,h.value?D:F]:x.value!==r.value?[A,F]:[T,F]).includes(t.key)?1:-1,n=t.shiftKey?2:t.ctrlKey?1:0;-1!==e||a!==u.value||n||Number.isInteger(o)?a+=e*l*$.value[n]:a-=o%1*l}else t.key===R?a=s.value:t.key===B?a=u.value:a-=(t.key===P?1:-1)*l*(o>100?o/10:10)
return Math.max(e.min,Math.min(e.max,a))}(t,e.modelValue)
null!=a&&o("update:modelValue",a)}return Ft((()=>{const a=f(x.value?100-e.position:e.position,"%")
return t.createElementVNode("div",{class:t.normalizeClass(["v-slider-thumb",{"v-slider-thumb--focused":e.focused,"v-slider-thumb--pressed":e.focused&&S.value},e.class,i.value]),style:t.normalizeStyle([{"--v-slider-thumb-position":a,"--v-slider-thumb-size":f(p.value)},e.style]),role:"slider",tabindex:v.value?-1:0,"aria-label":e.name,"aria-valuemin":s.value,"aria-valuemax":u.value,"aria-valuenow":e.modelValue,"aria-readonly":!!b.value,"aria-orientation":g.value,onKeydown:b.value?void 0:M},[t.createElementVNode("div",{class:t.normalizeClass(["v-slider-thumb__surface",E.value,N.value]),style:{...I.value}},null),t.withDirectives(t.createElementVNode("div",{class:t.normalizeClass(["v-slider-thumb__ripple",E.value]),style:t.normalizeStyle(I.value)},null),[[Fo,e.ripple,null,{circle:!0,center:!0}]]),t.createVNode(Ga,{origin:"bottom center"},{default:()=>[t.withDirectives(t.createElementVNode("div",{class:"v-slider-thumb__label-container"},[t.createElementVNode("div",{class:t.normalizeClass(["v-slider-thumb__label"])},[t.createElementVNode("div",null,[l["thumb-label"]?.({modelValue:e.modelValue})??e.modelValue.toFixed(d.value?k.value:1)])])]),[[t.vShow,m.value&&e.focused||"always"===m.value]])]})])})),{}}}),Xs=yt({start:{type:Number,required:!0},stop:{type:Number,required:!0},...bt()},"VSliderTrack"),Zs=Et()({name:"VSliderTrack",props:Xs(),emits:{},setup(e,a){let{slots:l}=a
const o=t.inject(Hs)
if(!o)throw new Error("[Vuetify] v-slider-track must be inside v-slider or v-range-slider")
const{color:n,parsedTicks:r,rounded:i,showTicks:s,tickSize:u,trackColor:c,trackFillColor:d,trackSize:v,vertical:p,min:m,max:g,indexFromEnd:h}=o,{roundedClasses:y}=ml(i),{backgroundColorClasses:b,backgroundColorStyles:V}=vl(d),{backgroundColorClasses:w,backgroundColorStyles:S}=vl(c),k=t.computed((()=>`inset-${p.value?"block":"inline"}-${h.value?"end":"start"}`)),x=t.computed((()=>p.value?"height":"width")),C=t.computed((()=>({[k.value]:"0%",[x.value]:"100%"}))),N=t.computed((()=>e.stop-e.start)),E=t.computed((()=>({[k.value]:f(e.start,"%"),[x.value]:f(N.value,"%")}))),I=t.computed((()=>{if(!s.value)return[]
return(p.value?r.value.slice().reverse():r.value).map(((a,o)=>{const n=a.value!==m.value&&a.value!==g.value?f(a.position,"%"):void 0
return t.createElementVNode("div",{key:a.value,class:t.normalizeClass(["v-slider-track__tick",{"v-slider-track__tick--filled":a.position>=e.start&&a.position<=e.stop,"v-slider-track__tick--first":a.value===m.value,"v-slider-track__tick--last":a.value===g.value}]),style:{[k.value]:n}},[(a.label||l["tick-label"])&&t.createElementVNode("div",{class:"v-slider-track__tick-label"},[l["tick-label"]?.({tick:a,index:o})??a.label])])}))}))
return Ft((()=>t.createElementVNode("div",{class:t.normalizeClass(["v-slider-track",y.value,e.class]),style:t.normalizeStyle([{"--v-slider-track-size":f(v.value),"--v-slider-tick-size":f(u.value)},e.style])},[t.createElementVNode("div",{class:t.normalizeClass(["v-slider-track__background",w.value,{"v-slider-track__background--opacity":!!n.value||!d.value}]),style:{...C.value,...S.value}},null),t.createElementVNode("div",{class:t.normalizeClass(["v-slider-track__fill",b.value]),style:{...E.value,...V.value}},null),s.value&&t.createElementVNode("div",{class:t.normalizeClass(["v-slider-track__ticks",{"v-slider-track__ticks--always-show":"always"===s.value}])},[I.value])]))),{}}}),Qs=yt({...sn(),...Us(),...gn(),modelValue:{type:[Number,String],default:0}},"VSlider"),Js=Et()({name:"VSlider",props:Qs(),emits:{"update:focused":e=>!0,"update:modelValue":e=>!0,start:e=>!0,end:e=>!0},setup(e,a){let{slots:l,emit:o}=a
const n=t.ref(),{rtlClasses:r}=ya(),i=Ys(e),s=ra(e,"modelValue",void 0,(e=>i.roundValue(null==e?i.min.value:e))),{min:u,max:c,mousePressed:d,roundValue:v,onSliderMousedown:p,onSliderTouchstart:m,trackContainerRef:f,position:g,hasLabels:h,readonly:y}=Gs({props:e,steps:i,onSliderStart:()=>{o("start",s.value)},onSliderEnd:e=>{let{value:t}=e
const a=v(t)
s.value=a,o("end",a)},onSliderMove:e=>{let{value:t}=e
return s.value=v(t)},getActiveThumb:()=>n.value?.$el}),{isFocused:b,focus:V,blur:w}=un(e),S=t.computed((()=>g(s.value)))
return Ft((()=>{const a=hn.filterProps(e),o=!!(e.label||l.label||l.prepend)
return t.createVNode(hn,t.mergeProps({class:["v-slider",{"v-slider--has-labels":!!l["tick-label"]||h.value,"v-slider--focused":b.value,"v-slider--pressed":d.value,"v-slider--disabled":e.disabled},r.value,e.class],style:e.style},a,{focused:b.value}),{...l,prepend:o?a=>t.createElementVNode(t.Fragment,null,[l.label?.(a)??(e.label?t.createVNode(Ko,{id:a.id.value,class:"v-slider__label",text:e.label},null):void 0),l.prepend?.(a)]):void 0,default:a=>{let{id:o,messagesId:r}=a
return t.createElementVNode("div",{class:"v-slider__container",onMousedown:y.value?void 0:p,onTouchstartPassive:y.value?void 0:m},[t.createElementVNode("input",{id:o.value,name:e.name||o.value,disabled:!!e.disabled,readonly:!!e.readonly,tabindex:"-1",value:s.value},null),t.createVNode(Zs,{ref:f,start:0,stop:S.value},{"tick-label":l["tick-label"]}),t.createVNode(Ks,{ref:n,"aria-describedby":r.value,focused:b.value,min:u.value,max:c.value,modelValue:s.value,"onUpdate:modelValue":e=>s.value=e,position:S.value,elevation:e.elevation,onFocus:V,onBlur:w,ripple:e.ripple,name:e.name},{"thumb-label":l["thumb-label"]})])}})})),{}}}),eu=Nt({name:"VColorPickerPreview",props:yt({color:{type:Object},disabled:Boolean,hideAlpha:Boolean,...bt()},"VColorPickerPreview")(),emits:{"update:color":e=>!0},setup(e,a){let{emit:l}=a
const o=new AbortController
async function r(){if(!n||e.disabled)return
const t=new window.EyeDropper
try{const a=ot(tt((await t.open({signal:o.signal})).sRGBHex))
l("update:color",{...e.color??Fs,...a})}catch(e){}}return t.onUnmounted((()=>o.abort())),Ft((()=>t.createElementVNode("div",{class:t.normalizeClass(["v-color-picker-preview",{"v-color-picker-preview--hide-alpha":e.hideAlpha},e.class]),style:t.normalizeStyle(e.style)},[n&&t.createElementVNode("div",{class:"v-color-picker-preview__eye-dropper",key:"eyeDropper"},[t.createVNode($o,{density:"comfortable",disabled:e.disabled,icon:"$eyeDropper",variant:"plain",onClick:r},null)]),t.createElementVNode("div",{class:"v-color-picker-preview__dot"},[t.createElementVNode("div",{style:{background:st(e.color??Fs)}},null)]),t.createElementVNode("div",{class:"v-color-picker-preview__sliders"},[t.createVNode(Js,{class:"v-color-picker-preview__track v-color-picker-preview__hue",modelValue:e.color?.h,"onUpdate:modelValue":t=>l("update:color",{...e.color??Fs,h:t}),step:0,min:0,max:360,disabled:e.disabled,thumbSize:14,trackSize:8,trackFillColor:"white",hideDetails:!0},null),!e.hideAlpha&&t.createVNode(Js,{class:"v-color-picker-preview__track v-color-picker-preview__alpha",modelValue:e.color?.a??1,"onUpdate:modelValue":t=>l("update:color",{...e.color??Fs,a:t}),step:1/256,min:0,max:1,disabled:e.disabled,thumbSize:14,trackSize:8,trackFillColor:"white",hideDetails:!0},null)])]))),{}}})
var tu={red:{base:"#f44336",lighten5:"#ffebee",lighten4:"#ffcdd2",lighten3:"#ef9a9a",lighten2:"#e57373",lighten1:"#ef5350",darken1:"#e53935",darken2:"#d32f2f",darken3:"#c62828",darken4:"#b71c1c",accent1:"#ff8a80",accent2:"#ff5252",accent3:"#ff1744",accent4:"#d50000"},pink:{base:"#e91e63",lighten5:"#fce4ec",lighten4:"#f8bbd0",lighten3:"#f48fb1",lighten2:"#f06292",lighten1:"#ec407a",darken1:"#d81b60",darken2:"#c2185b",darken3:"#ad1457",darken4:"#880e4f",accent1:"#ff80ab",accent2:"#ff4081",accent3:"#f50057",accent4:"#c51162"},purple:{base:"#9c27b0",lighten5:"#f3e5f5",lighten4:"#e1bee7",lighten3:"#ce93d8",lighten2:"#ba68c8",lighten1:"#ab47bc",darken1:"#8e24aa",darken2:"#7b1fa2",darken3:"#6a1b9a",darken4:"#4a148c",accent1:"#ea80fc",accent2:"#e040fb",accent3:"#d500f9",accent4:"#aa00ff"},deepPurple:{base:"#673ab7",lighten5:"#ede7f6",lighten4:"#d1c4e9",lighten3:"#b39ddb",lighten2:"#9575cd",lighten1:"#7e57c2",darken1:"#5e35b1",darken2:"#512da8",darken3:"#4527a0",darken4:"#311b92",accent1:"#b388ff",accent2:"#7c4dff",accent3:"#651fff",accent4:"#6200ea"},indigo:{base:"#3f51b5",lighten5:"#e8eaf6",lighten4:"#c5cae9",lighten3:"#9fa8da",lighten2:"#7986cb",lighten1:"#5c6bc0",darken1:"#3949ab",darken2:"#303f9f",darken3:"#283593",darken4:"#1a237e",accent1:"#8c9eff",accent2:"#536dfe",accent3:"#3d5afe",accent4:"#304ffe"},blue:{base:"#2196f3",lighten5:"#e3f2fd",lighten4:"#bbdefb",lighten3:"#90caf9",lighten2:"#64b5f6",lighten1:"#42a5f5",darken1:"#1e88e5",darken2:"#1976d2",darken3:"#1565c0",darken4:"#0d47a1",accent1:"#82b1ff",accent2:"#448aff",accent3:"#2979ff",accent4:"#2962ff"},lightBlue:{base:"#03a9f4",lighten5:"#e1f5fe",lighten4:"#b3e5fc",lighten3:"#81d4fa",lighten2:"#4fc3f7",lighten1:"#29b6f6",darken1:"#039be5",darken2:"#0288d1",darken3:"#0277bd",darken4:"#01579b",accent1:"#80d8ff",accent2:"#40c4ff",accent3:"#00b0ff",accent4:"#0091ea"},cyan:{base:"#00bcd4",lighten5:"#e0f7fa",lighten4:"#b2ebf2",lighten3:"#80deea",lighten2:"#4dd0e1",lighten1:"#26c6da",darken1:"#00acc1",darken2:"#0097a7",darken3:"#00838f",darken4:"#006064",accent1:"#84ffff",accent2:"#18ffff",accent3:"#00e5ff",accent4:"#00b8d4"},teal:{base:"#009688",lighten5:"#e0f2f1",lighten4:"#b2dfdb",lighten3:"#80cbc4",lighten2:"#4db6ac",lighten1:"#26a69a",darken1:"#00897b",darken2:"#00796b",darken3:"#00695c",darken4:"#004d40",accent1:"#a7ffeb",accent2:"#64ffda",accent3:"#1de9b6",accent4:"#00bfa5"},green:{base:"#4caf50",lighten5:"#e8f5e9",lighten4:"#c8e6c9",lighten3:"#a5d6a7",lighten2:"#81c784",lighten1:"#66bb6a",darken1:"#43a047",darken2:"#388e3c",darken3:"#2e7d32",darken4:"#1b5e20",accent1:"#b9f6ca",accent2:"#69f0ae",accent3:"#00e676",accent4:"#00c853"},lightGreen:{base:"#8bc34a",lighten5:"#f1f8e9",lighten4:"#dcedc8",lighten3:"#c5e1a5",lighten2:"#aed581",lighten1:"#9ccc65",darken1:"#7cb342",darken2:"#689f38",darken3:"#558b2f",darken4:"#33691e",accent1:"#ccff90",accent2:"#b2ff59",accent3:"#76ff03",accent4:"#64dd17"},lime:{base:"#cddc39",lighten5:"#f9fbe7",lighten4:"#f0f4c3",lighten3:"#e6ee9c",lighten2:"#dce775",lighten1:"#d4e157",darken1:"#c0ca33",darken2:"#afb42b",darken3:"#9e9d24",darken4:"#827717",accent1:"#f4ff81",accent2:"#eeff41",accent3:"#c6ff00",accent4:"#aeea00"},yellow:{base:"#ffeb3b",lighten5:"#fffde7",lighten4:"#fff9c4",lighten3:"#fff59d",lighten2:"#fff176",lighten1:"#ffee58",darken1:"#fdd835",darken2:"#fbc02d",darken3:"#f9a825",darken4:"#f57f17",accent1:"#ffff8d",accent2:"#ffff00",accent3:"#ffea00",accent4:"#ffd600"},amber:{base:"#ffc107",lighten5:"#fff8e1",lighten4:"#ffecb3",lighten3:"#ffe082",lighten2:"#ffd54f",lighten1:"#ffca28",darken1:"#ffb300",darken2:"#ffa000",darken3:"#ff8f00",darken4:"#ff6f00",accent1:"#ffe57f",accent2:"#ffd740",accent3:"#ffc400",accent4:"#ffab00"},orange:{base:"#ff9800",lighten5:"#fff3e0",lighten4:"#ffe0b2",lighten3:"#ffcc80",lighten2:"#ffb74d",lighten1:"#ffa726",darken1:"#fb8c00",darken2:"#f57c00",darken3:"#ef6c00",darken4:"#e65100",accent1:"#ffd180",accent2:"#ffab40",accent3:"#ff9100",accent4:"#ff6d00"},deepOrange:{base:"#ff5722",lighten5:"#fbe9e7",lighten4:"#ffccbc",lighten3:"#ffab91",lighten2:"#ff8a65",lighten1:"#ff7043",darken1:"#f4511e",darken2:"#e64a19",darken3:"#d84315",darken4:"#bf360c",accent1:"#ff9e80",accent2:"#ff6e40",accent3:"#ff3d00",accent4:"#dd2c00"},brown:{base:"#795548",lighten5:"#efebe9",lighten4:"#d7ccc8",lighten3:"#bcaaa4",lighten2:"#a1887f",lighten1:"#8d6e63",darken1:"#6d4c41",darken2:"#5d4037",darken3:"#4e342e",darken4:"#3e2723"},blueGrey:{base:"#607d8b",lighten5:"#eceff1",lighten4:"#cfd8dc",lighten3:"#b0bec5",lighten2:"#90a4ae",lighten1:"#78909c",darken1:"#546e7a",darken2:"#455a64",darken3:"#37474f",darken4:"#263238"},grey:{base:"#9e9e9e",lighten5:"#fafafa",lighten4:"#f5f5f5",lighten3:"#eeeeee",lighten2:"#e0e0e0",lighten1:"#bdbdbd",darken1:"#757575",darken2:"#616161",darken3:"#424242",darken4:"#212121"},shades:{black:"#000000",white:"#ffffff",transparent:"#ffffff00"}}
const au=yt({swatches:{type:Array,default:()=>function(e){return Object.keys(e).map((t=>{const a=e[t]
return a.base?[a.base,a.darken4,a.darken3,a.darken2,a.darken1,a.lighten1,a.lighten2,a.lighten3,a.lighten4,a.lighten5]:[a.black,a.white,a.transparent]}))}(tu)},disabled:Boolean,color:Object,maxHeight:[Number,String],...bt()},"VColorPickerSwatches")
const lu=Nt({name:"VColorPickerSwatches",props:au(),emits:{"update:color":e=>!0},setup(e,a){let{emit:l}=a
return Ft((()=>t.createElementVNode("div",{class:t.normalizeClass(["v-color-picker-swatches",e.class]),style:t.normalizeStyle([{maxHeight:f(e.maxHeight)},e.style])},[t.createElementVNode("div",null,[e.swatches.map((a=>t.createElementVNode("div",{class:"v-color-picker-swatches__swatch"},[a.map((a=>{const o=tt(a),n=ot(o),r=it(o)
return t.createElementVNode("div",{class:"v-color-picker-swatches__color",onClick:()=>n&&l("update:color",n)},[t.createElementVNode("div",{style:{background:r}},[e.color&&d(e.color,n)?t.createVNode(Jl,{size:"x-small",icon:"$success",color:gt(a,"#FFFFFF")>2?"white":"black"},null):void 0])])}))])))])]))),{}}}),ou=It("v-picker-title"),nu=yt({color:String,...wl(),...bt(),...rl(),...kl(),...oo(),...po(),...pl(),...Ba(),...Va()},"VSheet"),ru=Et()({name:"VSheet",props:nu(),setup(e,a){let{slots:l}=a
const{themeClasses:o}=Ea(e),{backgroundColorClasses:n,backgroundColorStyles:r}=vl((()=>e.color)),{borderClasses:i}=Sl(e),{dimensionStyles:s}=il(e),{elevationClasses:u}=xl(e),{locationStyles:c}=no(e),{positionClasses:d}=mo(e),{roundedClasses:v}=ml(e)
return Ft((()=>t.createVNode(e.tag,{class:t.normalizeClass(["v-sheet",o.value,n.value,i.value,u.value,d.value,v.value,e.class]),style:t.normalizeStyle([r.value,s.value,c.value,e.style])},l))),{}}}),iu=yt({bgColor:String,divided:Boolean,landscape:Boolean,title:String,hideHeader:Boolean,...nu()},"VPicker"),su=Et()({name:"VPicker",props:iu(),setup(e,a){let{slots:l}=a
const{backgroundColorClasses:o,backgroundColorStyles:n}=vl((()=>e.color))
return Ft((()=>{const a=ru.filterProps(e),r=!(!e.title&&!l.title)
return t.createVNode(ru,t.mergeProps(a,{color:e.bgColor,class:["v-picker",{"v-picker--divided":e.divided,"v-picker--landscape":e.landscape,"v-picker--with-actions":!!l.actions},e.class],style:e.style}),{default:()=>[!e.hideHeader&&t.createElementVNode("div",{key:"header",class:t.normalizeClass([o.value]),style:t.normalizeStyle([n.value])},[r&&t.createVNode(ou,{key:"picker-title"},{default:()=>[l.title?.()??e.title]}),l.header&&t.createElementVNode("div",{class:"v-picker__header"},[l.header()])]),t.createElementVNode("div",{class:"v-picker__body"},[l.default?.()]),l.actions&&t.createVNode(nl,{defaults:{VBtn:{slim:!0,variant:"text"}}},{default:()=>[t.createElementVNode("div",{class:"v-picker__actions"},[l.actions()])]})]})})),{}}})
function uu(e){const t=e.slice(-2).toUpperCase()
switch(!0){case"GB-alt-variant"===e:return{firstDay:0,firstWeekSize:4}
case"001"===e:return{firstDay:1,firstWeekSize:1}
case"AG AS BD BR BS BT BW BZ CA CO DM DO ET GT GU HK HN ID IL IN JM JP KE\n    KH KR LA MH MM MO MT MX MZ NI NP PA PE PH PK PR PY SA SG SV TH TT TW UM US\n    VE VI WS YE ZA ZW".includes(t):return{firstDay:0,firstWeekSize:1}
case"AI AL AM AR AU AZ BA BM BN BY CL CM CN CR CY EC GE HR KG KZ LB LK LV\n    MD ME MK MN MY NZ RO RS SI TJ TM TR UA UY UZ VN XK".includes(t):return{firstDay:1,firstWeekSize:1}
case"AD AN AT AX BE BG CH CZ DE DK EE ES FI FJ FO FR GB GF GP GR HU IE IS\n    IT LI LT LU MC MQ NL NO PL RE RU SE SK SM VA".includes(t):return{firstDay:1,firstWeekSize:4}
case"AE AF BH DJ DZ EG IQ IR JO KW LY OM QA SD SY".includes(t):return{firstDay:6,firstWeekSize:1}
case"MV"===t:return{firstDay:5,firstWeekSize:1}
case"PT"===t:return{firstDay:0,firstWeekSize:4}
default:return null}}function cu(e,t,a){const l=a??uu(t)?.firstDay??0,o=new Date(e)
for(;o.getDay()!==l;)o.setDate(o.getDate()-1)
return o}function du(e){return new Date(e.getFullYear(),e.getMonth(),1)}function vu(e){return new Date(e.getFullYear(),e.getMonth()+1,0)}const pu=/^([12]\d{3}-([1-9]|0[1-9]|1[0-2])-([1-9]|0[1-9]|[12]\d|3[01]))$/
function mu(e){if(null==e)return new Date
if(e instanceof Date)return e
if("string"==typeof e){let t
if(pu.test(e))return function(e){const t=e.split("-").map(Number)
return new Date(t[0],t[1]-1,t[2])}(e)
if(t=Date.parse(e),!isNaN(t))return new Date(t)}return null}const fu=new Date(2e3,0,2)
function gu(e,t,a,l){const o=mu(e)??new Date,n=l?.[t]
if("function"==typeof n)return n(o,t,a)
let r={}
switch(t){case"fullDate":r={year:"numeric",month:"long",day:"numeric"}
break
case"fullDateWithWeekday":r={weekday:"long",year:"numeric",month:"long",day:"numeric"}
break
case"normalDate":return`${o.getDate()} ${new Intl.DateTimeFormat(a,{month:"long"}).format(o)}`
case"normalDateWithWeekday":r={weekday:"short",day:"numeric",month:"short"}
break
case"shortDate":r={month:"short",day:"numeric"}
break
case"year":r={year:"numeric"}
break
case"month":r={month:"long"}
break
case"monthShort":r={month:"short"}
break
case"monthAndYear":r={month:"long",year:"numeric"}
break
case"monthAndDate":r={month:"long",day:"numeric"}
break
case"weekday":r={weekday:"long"}
break
case"weekdayShort":r={weekday:"short"}
break
case"dayOfMonth":return new Intl.NumberFormat(a).format(o.getDate())
case"hours12h":r={hour:"numeric",hour12:!0}
break
case"hours24h":r={hour:"numeric",hour12:!1}
break
case"minutes":r={minute:"numeric"}
break
case"seconds":r={second:"numeric"}
break
case"fullTime":r={hour:"numeric",minute:"numeric"}
break
case"fullTime12h":r={hour:"numeric",minute:"numeric",hour12:!0}
break
case"fullTime24h":r={hour:"numeric",minute:"numeric",hour12:!1}
break
case"fullDateTime":r={year:"numeric",month:"short",day:"numeric",hour:"numeric",minute:"numeric"}
break
case"fullDateTime12h":r={year:"numeric",month:"short",day:"numeric",hour:"numeric",minute:"numeric",hour12:!0}
break
case"fullDateTime24h":r={year:"numeric",month:"short",day:"numeric",hour:"numeric",minute:"numeric",hour12:!1}
break
case"keyboardDate":r={year:"numeric",month:"2-digit",day:"2-digit"}
break
case"keyboardDateTime":return r={year:"numeric",month:"2-digit",day:"2-digit",hour:"numeric",minute:"numeric"},new Intl.DateTimeFormat(a,r).format(o).replace(/, /g," ")
case"keyboardDateTime12h":return r={year:"numeric",month:"2-digit",day:"2-digit",hour:"numeric",minute:"numeric",hour12:!0},new Intl.DateTimeFormat(a,r).format(o).replace(/, /g," ")
case"keyboardDateTime24h":return r={year:"numeric",month:"2-digit",day:"2-digit",hour:"numeric",minute:"numeric",hour12:!1},new Intl.DateTimeFormat(a,r).format(o).replace(/, /g," ")
default:r=n??{timeZone:"UTC",timeZoneName:"short"}}return new Intl.DateTimeFormat(a,r).format(o)}function hu(e,t){const a=new Date(e)
return a.setDate(a.getDate()+t),a}function yu(e){return e.getFullYear()}function bu(e,t){return e.getTime()>t.getTime()}function Vu(e,t){return e.getTime()===t.getTime()}function wu(e,t,a){const l=new Date(e),o=new Date(t)
switch(a){case"years":return l.getFullYear()-o.getFullYear()
case"quarters":return Math.floor((l.getMonth()-o.getMonth()+12*(l.getFullYear()-o.getFullYear()))/4)
case"months":return l.getMonth()-o.getMonth()+12*(l.getFullYear()-o.getFullYear())
case"weeks":return Math.floor((l.getTime()-o.getTime())/6048e5)
case"days":return Math.floor((l.getTime()-o.getTime())/864e5)
case"hours":return Math.floor((l.getTime()-o.getTime())/36e5)
case"minutes":return Math.floor((l.getTime()-o.getTime())/6e4)
case"seconds":return Math.floor((l.getTime()-o.getTime())/1e3)
default:return l.getTime()-o.getTime()}}function Su(e){return new Date(e.getFullYear(),e.getMonth(),e.getDate(),0,0,0,0)}class ku{constructor(e){this.locale=e.locale,this.formats=e.formats}date(e){return mu(e)}toJsDate(e){return e}toISO(e){return function(e,t){const a=e.toJsDate(t)
return`${a.getFullYear()}-${D(String(a.getMonth()+1),2,"0")}-${D(String(a.getDate()),2,"0")}`}(this,e)}parseISO(e){return function(e){const[t,a,l]=e.split("-").map(Number)
return new Date(t,a-1,l)}(e)}addMinutes(e,t){return function(e,t){const a=new Date(e)
return a.setMinutes(a.getMinutes()+t),a}(e,t)}addHours(e,t){return function(e,t){const a=new Date(e)
return a.setHours(a.getHours()+t),a}(e,t)}addDays(e,t){return hu(e,t)}addWeeks(e,t){return function(e,t){const a=new Date(e)
return a.setDate(a.getDate()+7*t),a}(e,t)}addMonths(e,t){return function(e,t){const a=new Date(e)
return a.setDate(1),a.setMonth(a.getMonth()+t),a}(e,t)}getWeekArray(e,t){const a=void 0!==t?Number(t):void 0
return function(e,t,a){const l=[]
let o=[]
const n=du(e),r=vu(e),i=a??uu(t)?.firstDay??0,s=(n.getDay()-i+7)%7,u=(r.getDay()-i+7)%7
for(let e=0;e<s;e++){const t=new Date(n)
t.setDate(t.getDate()-(s-e)),o.push(t)}for(let t=1;t<=r.getDate();t++){const a=new Date(e.getFullYear(),e.getMonth(),t)
o.push(a),7===o.length&&(l.push(o),o=[])}for(let e=1;e<7-u;e++){const t=new Date(r)
t.setDate(t.getDate()+e),o.push(t)}return o.length>0&&l.push(o),l}(e,this.locale,a)}startOfWeek(e,t){const a=void 0!==t?Number(t):void 0
return cu(e,this.locale,a)}endOfWeek(e){return function(e,t){const a=new Date(e),l=((uu(t)?.firstDay??0)+6)%7
for(;a.getDay()!==l;)a.setDate(a.getDate()+1)
return a}(e,this.locale)}startOfMonth(e){return du(e)}endOfMonth(e){return vu(e)}format(e,t){return gu(e,t,this.locale,this.formats)}isEqual(e,t){return Vu(e,t)}isValid(e){return function(e){const t=new Date(e)
return t instanceof Date&&!isNaN(t.getTime())}(e)}isWithinRange(e,t){return function(e,t){return bu(e,t[0])&&function(e,t){return e.getTime()<t.getTime()}(e,t[1])}(e,t)}isAfter(e,t){return bu(e,t)}isAfterDay(e,t){return function(e,t){return bu(Su(e),Su(t))}(e,t)}isBefore(e,t){return!bu(e,t)&&!Vu(e,t)}isSameDay(e,t){return function(e,t){return e.getDate()===t.getDate()&&e.getMonth()===t.getMonth()&&e.getFullYear()===t.getFullYear()}(e,t)}isSameMonth(e,t){return function(e,t){return e.getMonth()===t.getMonth()&&e.getFullYear()===t.getFullYear()}(e,t)}isSameYear(e,t){return function(e,t){return e.getFullYear()===t.getFullYear()}(e,t)}setMinutes(e,t){return function(e,t){const a=new Date(e)
return a.setMinutes(t),a}(e,t)}setHours(e,t){return function(e,t){const a=new Date(e)
return a.setHours(t),a}(e,t)}setMonth(e,t){return function(e,t){const a=new Date(e)
return a.setMonth(t),a}(e,t)}setDate(e,t){return function(e,t){const a=new Date(e)
return a.setDate(t),a}(e,t)}setYear(e,t){return function(e,t){const a=new Date(e)
return a.setFullYear(t),a}(e,t)}getDiff(e,t,a){return wu(e,t,a)}getWeekdays(e){const t=void 0!==e?Number(e):void 0
return function(e,t){const a=t??uu(e)?.firstDay??0
return m(7).map((t=>{const l=new Date(fu)
return l.setDate(fu.getDate()+a+t),new Intl.DateTimeFormat(e,{weekday:"narrow"}).format(l)}))}(this.locale,t)}getYear(e){return yu(e)}getMonth(e){return function(e){return e.getMonth()}(e)}getWeek(e,t,a){const l=void 0!==t?Number(t):void 0
return function(e,t,a,l){const o=uu(t),n=a??o?.firstDay??0,r=l??o?.firstWeekSize??1
function i(e){const a=new Date(e,0,1)
return 7-wu(a,cu(a,t,n),"days")}let s=yu(e)
s<yu(hu(cu(e,t,n),6))&&i(s+1)>=r&&s++
const u=new Date(s,0,1),c=i(s)
return 1+wu(e,hu(u,c>=r?c-7:c),"weeks")}(e,this.locale,l,a)}getDate(e){return function(e){return e.getDate()}(e)}getNextMonth(e){return function(e){return new Date(e.getFullYear(),e.getMonth()+1,1)}(e)}getPreviousMonth(e){return function(e){return new Date(e.getFullYear(),e.getMonth()-1,1)}(e)}getHours(e){return function(e){return e.getHours()}(e)}getMinutes(e){return function(e){return e.getMinutes()}(e)}startOfDay(e){return Su(e)}endOfDay(e){return function(e){return new Date(e.getFullYear(),e.getMonth(),e.getDate(),23,59,59,999)}(e)}startOfYear(e){return function(e){return new Date(e.getFullYear(),0,1)}(e)}endOfYear(e){return function(e){return new Date(e.getFullYear(),11,31)}(e)}}const xu=Symbol.for("vuetify:date-options"),Cu=Symbol.for("vuetify:date-adapter")
function Nu(e,a){const l=t.reactive("function"==typeof e.adapter?new e.adapter({locale:e.locale[a.current.value]??a.current.value,formats:e.formats}):e.adapter)
return t.watch(a.current,(t=>{l.locale=e.locale[t]??t??l.locale})),Object.assign(l,{createDateRange(e,t){const a=l.getDiff(t??e,e,"days"),o=[e]
for(let t=1;t<a;t++){const a=l.addDays(e,t)
o.push(a)}return t&&o.push(l.endOfDay(t)),o}})}function Eu(){const e=t.inject(xu)
if(!e)throw new Error("[Vuetify] Could not find injected date options")
return Nu(e,ga())}const Iu=Nt({name:"VColorPicker",props:yt({canvasHeight:{type:[String,Number],default:150},disabled:Boolean,dotSize:{type:[Number,String],default:10},hideCanvas:Boolean,hideSliders:Boolean,hideInputs:Boolean,mode:{type:String,default:"rgba",validator:e=>Object.keys(Os).includes(e)},modes:{type:Array,default:()=>Object.keys(Os),validator:e=>Array.isArray(e)&&e.every((e=>Object.keys(Os).includes(e)))},showSwatches:Boolean,swatches:Array,swatchesMaxHeight:{type:[Number,String],default:150},modelValue:{type:[Object,String]},...iu({hideHeader:!0})},"VColorPicker")(),emits:{"update:modelValue":e=>!0,"update:mode":e=>!0},setup(e,a){let{slots:l}=a
const o=ra(e,"mode"),n=t.ref(null),r=ra(e,"modelValue",void 0,(e=>{if(null==e||""===e)return null
let t
try{t=ot(tt(e))}catch(e){return Me(e),null}return t}),(t=>t?function(e,t){if(null==t||"string"==typeof t){const a=1!==e.a
if(t?.startsWith("rgb(")){const{r:t,g:l,b:o,a:n}=at(e)
return`rgb(${t} ${l} ${o}`+(a?` / ${n})`:")")}if(t?.startsWith("hsl(")){const{h:t,s:l,l:o,a:n}=nt(e)
return`hsl(${t} ${Math.round(100*l)} ${Math.round(100*o)}`+(a?` / ${n})`:")")}const l=vt(e)
return 1===e.a?l.slice(0,7):l}if("object"==typeof t){let a
return S(t,["r","g","b"])?a=at(e):S(t,["h","s","l"])?a=nt(e):S(t,["h","s","v"])&&(a=e),function(e,t){if(t){const{a:t,...a}=e
return a}return e}(a,!S(t,["a"])&&1===e.a)}return e}(t,e.modelValue):null)),i=t.computed((()=>r.value?{...r.value,h:n.value??r.value.h}:null)),{rtlClasses:s}=ya()
let u=!0
t.watch(r,(e=>{u?e&&(n.value=e.h):u=!0}),{immediate:!0})
const c=e=>{u=!1,n.value=e.h,r.value=e}
return t.onBeforeMount((()=>{e.modes.includes(o.value)||(o.value=e.modes[0])})),xt({VSlider:{color:void 0,trackColor:void 0,trackFillColor:void 0}}),Ft((()=>{const a=su.filterProps(e)
return t.createVNode(su,t.mergeProps(a,{class:["v-color-picker",s.value,e.class],style:[{"--v-color-picker-color-hsv":st({...i.value??Fs,a:1})},e.style]}),{...l,default:()=>t.createElementVNode(t.Fragment,null,[!e.hideCanvas&&t.createVNode(Ds,{key:"canvas",color:i.value,"onUpdate:color":c,disabled:e.disabled,dotSize:e.dotSize,width:e.width,height:e.canvasHeight},null),(!e.hideSliders||!e.hideInputs)&&t.createElementVNode("div",{key:"controls",class:"v-color-picker__controls"},[!e.hideSliders&&t.createVNode(eu,{key:"preview",color:i.value,"onUpdate:color":c,hideAlpha:!o.value.endsWith("a"),disabled:e.disabled},null),!e.hideInputs&&t.createVNode(js,{key:"edit",modes:e.modes,mode:o.value,"onUpdate:mode":e=>o.value=e,color:i.value,"onUpdate:color":c,disabled:e.disabled},null)]),e.showSwatches&&t.createVNode(lu,{key:"swatches",color:i.value,"onUpdate:color":c,maxHeight:e.swatchesMaxHeight,swatches:e.swatches,disabled:e.disabled},null)])})})),{}}}),_u=yt({autoSelectFirst:{type:[Boolean,String]},clearOnSelect:{type:Boolean,default:!0},delimiters:Array,...Li({filterKeys:["title"]}),...Fi({hideNoData:!0,returnObject:!0}),...C(Ei({modelValue:null,role:"combobox"}),["validationValue","dirty","appendInnerIcon"]),...fl({transition:!1})},"VCombobox"),Pu=Et()({name:"VCombobox",props:_u(),emits:{"update:focused":e=>!0,"update:modelValue":e=>!0,"update:search":e=>!0,"update:menu":e=>!0},setup(e,l){let{emit:o,slots:n}=l
const{t:r}=ga(),i=t.ref(),s=t.shallowRef(!1),u=t.shallowRef(!0),c=t.shallowRef(!1),v=t.ref(),p=t.ref(),m=t.shallowRef(-1)
let f=!1
const{items:g,transformIn:h,transformOut:y}=Cr(e),{textColorClasses:b,textColorStyles:V}=dl((()=>i.value?.color)),w=ra(e,"modelValue",[],(e=>h(B(e))),(t=>{const a=y(t)
return e.multiple?a:a[0]??null})),S=vn(e),k=t.computed((()=>!(!e.chips&&!n.chip))),x=t.computed((()=>k.value||!!n.selection)),C=t.shallowRef(e.multiple||x.value?"":w.value[0]?.title??""),N=t.computed({get:()=>C.value,set:t=>{if(C.value=t??"",e.multiple||x.value||(w.value=[kr(e,t)]),t&&e.multiple&&e.delimiters?.length){const a=t.split(new RegExp(`(?:${e.delimiters.join("|")})+`))
a.length>1&&(a.forEach((t=>{(t=t.trim())&&q(kr(e,t))})),C.value="")}t||(m.value=-1),u.value=!t}}),E=t.computed((()=>"function"==typeof e.counterValue?e.counterValue(w.value):"number"==typeof e.counterValue?e.counterValue:e.multiple?w.value.length:N.value.length)),{filteredItems:I,getMatches:P}=ji(e,g,(()=>u.value?"":N.value)),R=t.computed((()=>e.hideSelected?I.value.filter((e=>!w.value.some((t=>t.value===e.value)))):I.value)),A=t.computed((()=>e.hideNoData&&!R.value.length||S.isReadonly.value||S.isDisabled.value)),T=ra(e,"menu"),D=t.computed({get:()=>T.value,set:e=>{T.value&&!e&&v.value?.ΨopenChildren.size||e&&A.value||(T.value=e)}}),F=t.toRef((()=>D.value?e.closeText:e.openText))
t.watch(C,(e=>{f?t.nextTick((()=>f=!1)):s.value&&!D.value&&(D.value=!0),o("update:search",e)})),t.watch(w,(t=>{e.multiple||x.value||(C.value=t[0]?.title??"")}))
const z=t.computed((()=>w.value.map((e=>e.value)))),$=t.computed((()=>(!0===e.autoSelectFirst||"exact"===e.autoSelectFirst&&N.value===R.value[0]?.title)&&R.value.length>0&&!u.value&&!c.value)),M=t.ref(),O=Di(M,i)
function L(t){f=!0,e.openOnClear&&(D.value=!0)}function j(){A.value||(D.value=!0)}function H(e){A.value||(s.value&&(e.preventDefault(),e.stopPropagation()),D.value=!D.value)}function W(e){" "!==e.key&&oe(e)&&i.value?.focus()}function U(t){if(function(e){return e.isComposing&&_.includes(e.key)}(t)||S.isReadonly.value)return
const a=i.value?.selectionStart,l=w.value.length
if(["Enter","ArrowDown","ArrowUp"].includes(t.key)&&t.preventDefault(),["Enter","ArrowDown"].includes(t.key)&&(D.value=!0),["Escape"].includes(t.key)&&(D.value=!1),["Enter","Escape","Tab"].includes(t.key)&&($.value&&["Enter","Tab"].includes(t.key)&&!w.value.some((e=>{let{value:t}=e
return t===R.value[0].value}))&&q(I.value[0]),u.value=!0),"ArrowDown"===t.key&&$.value&&M.value?.focus("next"),"Enter"===t.key&&N.value&&(q(kr(e,N.value)),x.value&&(C.value="")),["Backspace","Delete"].includes(t.key)){if(!e.multiple&&x.value&&w.value.length>0&&!N.value)return q(w.value[0],!1)
if(~m.value){t.preventDefault()
const e=m.value
q(w.value[m.value],!1),m.value=e>=l-1?l-2:e}else"Backspace"!==t.key||N.value||(m.value=l-1)}else if(e.multiple)if("ArrowLeft"===t.key){if(m.value<0&&a&&a>0)return
const e=m.value>-1?m.value-1:l-1
w.value[e]?m.value=e:(m.value=-1,i.value?.setSelectionRange(N.value.length,N.value.length))}else if("ArrowRight"===t.key){if(m.value<0)return
const e=m.value+1
w.value[e]?m.value=e:(m.value=-1,i.value?.setSelectionRange(0,0))}else~m.value&&oe(t)&&(m.value=-1)}function Y(){e.eager&&p.value?.calculateVisibleItems()}function G(){s.value&&(u.value=!0,i.value?.focus())}function q(a){let l=!(arguments.length>1&&void 0!==arguments[1])||arguments[1]
if(a&&!a.props.disabled)if(e.multiple){const t=w.value.findIndex((t=>(e.valueComparator||d)(t.value,a.value))),o=null==l?!~t:l
if(~t){const e=o?[...w.value,a]:[...w.value]
e.splice(t,1),w.value=e}else o&&(w.value=[...w.value,a])
e.clearOnSelect&&(N.value="")}else{const e=!1!==l
w.value=e?[a]:[],C.value=e&&!x.value?a.title:"",t.nextTick((()=>{D.value=!1,u.value=!0}))}}function K(e){s.value=!0,setTimeout((()=>{c.value=!0}))}function X(e){c.value=!1}function Z(t){null!=t&&(""!==t||e.multiple||x.value)||(w.value=[])}return t.watch(s,((t,a)=>{if(!t&&t!==a&&(m.value=-1,D.value=!1,N.value)){if(e.multiple)return void q(kr(e,N.value))
if(!x.value)return
w.value.some((e=>{let{title:t}=e
return t===N.value}))?C.value="":q(kr(e,N.value))}})),t.watch(D,(()=>{if(!e.hideSelected&&D.value&&w.value.length){const t=R.value.findIndex((t=>w.value.some((a=>(e.valueComparator||d)(a.value,t.value)))))
a&&window.requestAnimationFrame((()=>{t>=0&&p.value?.scrollToIndex(t)}))}})),t.watch((()=>e.items),((e,t)=>{D.value||s.value&&!t.length&&e.length&&(D.value=!0)})),Ft((()=>{const a=!!(!e.hideNoData||R.value.length||n["prepend-item"]||n["append-item"]||n["no-data"]),l=w.value.length>0,o=Ii.filterProps(e)
return t.createVNode(Ii,t.mergeProps({ref:i},o,{modelValue:N.value,"onUpdate:modelValue":[e=>N.value=e,Z],focused:s.value,"onUpdate:focused":e=>s.value=e,validationValue:w.externalValue,counterValue:E.value,dirty:l,class:["v-combobox",{"v-combobox--active-menu":D.value,"v-combobox--chips":!!e.chips,"v-combobox--selection-slot":!!x.value,"v-combobox--selecting-index":m.value>-1,["v-combobox--"+(e.multiple?"multiple":"single")]:!0},e.class],style:e.style,readonly:S.isReadonly.value,placeholder:l?void 0:e.placeholder,"onClick:clear":L,"onMousedown:control":j,onKeydown:U}),{...n,default:()=>t.createElementVNode(t.Fragment,null,[t.createVNode(yi,t.mergeProps({ref:v,modelValue:D.value,"onUpdate:modelValue":e=>D.value=e,activator:"parent",contentClass:"v-combobox__content",disabled:A.value,eager:e.eager,maxHeight:310,openOnClick:!1,closeOnContentClick:!1,transition:e.transition,onAfterEnter:Y,onAfterLeave:G},e.menuProps),{default:()=>[a&&t.createVNode(_r,t.mergeProps({ref:M,selected:z.value,selectStrategy:e.multiple?"independent":"single-independent",onMousedown:e=>e.preventDefault(),onKeydown:W,onFocusin:K,onFocusout:X,tabindex:"-1","aria-live":"polite",color:e.itemColor??e.color},O,e.listProps),{default:()=>[n["prepend-item"]?.(),!R.value.length&&!e.hideNoData&&(n["no-data"]?.()??t.createVNode(fr,{key:"no-data",title:r(e.noDataText)},null)),t.createVNode(Ti,{ref:p,renderless:!0,items:R.value,itemKey:"value"},{default:a=>{let{item:l,index:o,itemRef:r}=a
const i=t.mergeProps(l.props,{ref:r,key:l.value,active:!(!$.value||0!==o)||void 0,onClick:()=>q(l,null)})
return n.item?.({item:l,index:o,props:i})??t.createVNode(fr,t.mergeProps(i,{role:"option"}),{prepend:a=>{let{isSelected:o}=a
return t.createElementVNode(t.Fragment,null,[e.multiple&&!e.hideSelected?t.createVNode(ln,{key:l.value,modelValue:o,ripple:!1,tabindex:"-1"},null):void 0,l.props.prependAvatar&&t.createVNode(Go,{image:l.props.prependAvatar},null),l.props.prependIcon&&t.createVNode(Jl,{icon:l.props.prependIcon},null)])},title:()=>u.value?l.title:Hi("v-combobox",l.title,P(l)?.title)})}}),n["append-item"]?.()]})]}),w.value.map(((a,l)=>{function o(e){e.stopPropagation(),e.preventDefault(),q(a,!1)}const r={"onClick:close":o,onKeydown(e){"Enter"!==e.key&&" "!==e.key||(e.preventDefault(),e.stopPropagation(),o(e))},onMousedown(e){e.preventDefault(),e.stopPropagation()},modelValue:!0,"onUpdate:modelValue":void 0},i=k.value?!!n.chip:!!n.selection,s=i?ae(k.value?n.chip({item:a,index:l,props:r}):n.selection({item:a,index:l})):void 0
if(!i||s)return t.createElementVNode("div",{key:a.value,class:t.normalizeClass(["v-combobox__selection",l===m.value&&["v-combobox__selection--selected",b.value]]),style:t.normalizeStyle(l===m.value?V.value:{})},[k.value?n.chip?t.createVNode(nl,{key:"chip-defaults",defaults:{VChip:{closable:e.closableChips,size:"small",text:a.title}}},{default:()=>[s]}):t.createVNode(Un,t.mergeProps({key:"chip",closable:e.closableChips,size:"small",text:a.title,disabled:a.props.disabled},r),null):s??t.createElementVNode("span",{class:"v-combobox__selection-text"},[a.title,e.multiple&&l<w.value.length-1&&t.createElementVNode("span",{class:"v-combobox__selection-comma"},[t.createTextVNode(",")])])])}))]),"append-inner":function(){for(var a=arguments.length,l=new Array(a),o=0;o<a;o++)l[o]=arguments[o]
return t.createElementVNode(t.Fragment,null,[n["append-inner"]?.(...l),e.hideNoData&&!e.items.length||!e.menuIcon?void 0:t.createVNode(Jl,{class:"v-combobox__menu-icon",color:i.value?.fieldIconColor,icon:e.menuIcon,onMousedown:H,onClick:ee,"aria-label":r(F.value),title:r(F.value),tabindex:"-1"},null)])}})})),gi({isFocused:s,isPristine:u,menu:D,search:N,selectionIndex:m,filteredItems:I,select:q},i)}}),Bu=yt({modelValue:null,color:String,cancelText:{type:String,default:"$vuetify.confirmEdit.cancel"},okText:{type:String,default:"$vuetify.confirmEdit.ok"},disabled:{type:[Boolean,Array],default:void 0},hideActions:Boolean},"VConfirmEdit"),Ru=Et()({name:"VConfirmEdit",props:Bu(),emits:{cancel:()=>!0,save:e=>!0,"update:modelValue":e=>!0},setup(e,a){let{emit:l,slots:o}=a
const n=ra(e,"modelValue"),r=t.ref()
t.watchEffect((()=>{r.value=structuredClone(t.toRaw(n.value))}))
const{t:i}=ga(),s=t.computed((()=>d(n.value,r.value)))
function u(t){return"boolean"==typeof e.disabled?e.disabled:Array.isArray(e.disabled)?e.disabled.includes(t):s.value}const c=t.computed((()=>u("save"))),v=t.computed((()=>u("cancel")))
function p(){n.value=r.value,l("save",r.value)}function m(){r.value=structuredClone(t.toRaw(n.value)),l("cancel")}function f(a){return t.createElementVNode(t.Fragment,null,[t.createVNode($o,t.mergeProps({disabled:v.value,variant:"text",color:e.color,onClick:m,text:i(e.cancelText)},a),null),t.createVNode($o,t.mergeProps({disabled:c.value,variant:"text",color:e.color,onClick:p,text:i(e.okText)},a),null)])}let g=!1
return Ft((()=>t.createElementVNode(t.Fragment,null,[o.default?.({model:r,save:p,cancel:m,isPristine:s.value,get actions(){return g=!0,f}}),!e.hideActions&&!g&&f()]))),{save:p,cancel:m,isPristine:s}}}),Au=yt({expandOnClick:Boolean,showExpand:Boolean,expanded:{type:Array,default:()=>[]}},"DataTable-expand"),Tu=Symbol.for("vuetify:datatable:expanded")
function Du(e){const a=t.toRef((()=>e.expandOnClick)),l=ra(e,"expanded",e.expanded,(e=>new Set(e)),(e=>[...e.values()]))
function o(e,t){const a=new Set(l.value)
t?a.add(e.value):a.delete(e.value),l.value=a}function n(e){return l.value.has(e.value)}const r={expand:o,expanded:l,expandOnClick:a,isExpanded:n,toggleExpand:function(e){o(e,!n(e))}}
return t.provide(Tu,r),r}function Fu(){const e=t.inject(Tu)
if(!e)throw new Error("foo")
return e}const zu=yt({groupBy:{type:Array,default:()=>[]}},"DataTable-group"),$u=Symbol.for("vuetify:data-table-group")
function Mu(e){return{groupBy:ra(e,"groupBy")}}function Ou(e){const{disableSort:a,groupBy:l,sortBy:o}=e,n=t.ref(new Set)
function r(e){return n.value.has(e.id)}const i={sortByWithGroups:t.computed((()=>l.value.map((e=>({...e,order:e.order??!1}))).concat(a?.value?[]:o.value))),toggleGroup:function(e){const t=new Set(n.value)
r(e)?t.delete(e.id):t.add(e.id),n.value=t},opened:n,groupBy:l,extractRows:function(e){return function e(t){const a=[]
for(const l of t.items)"type"in l&&"group"===l.type?a.push(...e(l)):a.push(l)
return[...new Set(a)]}({items:e})},isGroupOpen:r}
return t.provide($u,i),i}function Lu(){const e=t.inject($u)
if(!e)throw new Error("Missing group!")
return e}function ju(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,l=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"root"
if(!t.length)return[]
const o=function(e,t){if(!e.length)return[]
const a=new Map
for(const l of e){const e=v(l.raw,t)
a.has(e)||a.set(e,[]),a.get(e).push(l)}return a}(e,t[0]),n=[],r=t.slice(1)
return o.forEach(((e,o)=>{const i=t[0],s=`${l}_${i}_${o}`
n.push({depth:a,id:s,key:i,value:o,items:r.length?ju(e,r,a+1,s):e,type:"group"})})),n}function Hu(e,t){const a=[]
for(const l of e)"type"in l&&"group"===l.type?(null!=l.value&&a.push(l),(t.has(l.id)||null==l.value)&&a.push(...Hu(l.items,t))):a.push(l)
return a}function Wu(e,a,l){return{flatItems:t.computed((()=>{if(!a.value.length)return e.value
return Hu(ju(e.value,a.value.map((e=>e.key))),l.value)}))}}function Uu(e){let{page:a,itemsPerPage:l,sortBy:o,groupBy:n,search:r}=e
const i=Vt("VDataTable")
let s=null
t.watch((()=>({page:a.value,itemsPerPage:l.value,sortBy:o.value,groupBy:n.value,search:r.value})),(e=>{d(s,e)||(s&&s.search!==e.search&&(a.value=1),i.emit("update:options",e),s=e)}),{deep:!0,immediate:!0})}const Yu=yt({page:{type:[Number,String],default:1},itemsPerPage:{type:[Number,String],default:10}},"DataTable-paginate"),Gu=Symbol.for("vuetify:data-table-pagination")
function qu(e){return{page:ra(e,"page",void 0,(e=>Number(e??1))),itemsPerPage:ra(e,"itemsPerPage",void 0,(e=>Number(e??10)))}}function Ku(e){const{page:a,itemsPerPage:l,itemsLength:o}=e,n=t.computed((()=>-1===l.value?0:l.value*(a.value-1))),r=t.computed((()=>-1===l.value?o.value:Math.min(o.value,n.value+l.value))),i=t.computed((()=>-1===l.value||0===o.value?1:Math.ceil(o.value/l.value)))
t.watch([a,i],(()=>{a.value>i.value&&(a.value=i.value)}))
const s={page:a,itemsPerPage:l,startIndex:n,stopIndex:r,pageCount:i,itemsLength:o,nextPage:function(){a.value=R(a.value+1,1,i.value)},prevPage:function(){a.value=R(a.value-1,1,i.value)},setPage:function(e){a.value=R(e,1,i.value)},setItemsPerPage:function(e){l.value=e,a.value=1}}
return t.provide(Gu,s),s}function Xu(e){const a=Vt("usePaginatedItems"),{items:l,startIndex:o,stopIndex:n,itemsPerPage:r}=e,i=t.computed((()=>r.value<=0?l.value:l.value.slice(o.value,n.value)))
return t.watch(i,(e=>{a.emit("update:currentItems",e)}),{immediate:!0}),{paginatedItems:i}}const Zu={showSelectAll:!1,allSelected:()=>[],select:e=>{let{items:t,value:a}=e
return new Set(a?[t[0]?.value]:[])},selectAll:e=>{let{selected:t}=e
return t}},Qu={showSelectAll:!0,allSelected:e=>{let{currentPage:t}=e
return t},select:e=>{let{items:t,value:a,selected:l}=e
for(const e of t)a?l.add(e.value):l.delete(e.value)
return l},selectAll:e=>{let{value:t,currentPage:a,selected:l}=e
return Qu.select({items:a,value:t,selected:l})}},Ju={showSelectAll:!0,allSelected:e=>{let{allItems:t}=e
return t},select:e=>{let{items:t,value:a,selected:l}=e
for(const e of t)a?l.add(e.value):l.delete(e.value)
return l},selectAll:e=>{let{value:t,allItems:a,selected:l}=e
return Ju.select({items:a,value:t,selected:l})}},ec=yt({showSelect:Boolean,selectStrategy:{type:[String,Object],default:"page"},modelValue:{type:Array,default:()=>[]},valueComparator:{type:Function,default:d}},"DataTable-select"),tc=Symbol.for("vuetify:data-table-selection")
function ac(e,a){let{allItems:l,currentPage:o}=a
const n=ra(e,"modelValue",e.modelValue,(t=>new Set(B(t).map((t=>l.value.find((a=>e.valueComparator(t,a.value)))?.value??t)))),(e=>[...e.values()])),r=t.computed((()=>l.value.filter((e=>e.selectable)))),i=t.computed((()=>o.value.filter((e=>e.selectable)))),s=t.computed((()=>{if("object"==typeof e.selectStrategy)return e.selectStrategy
switch(e.selectStrategy){case"single":return Zu
case"all":return Ju
default:return Qu}})),u=t.shallowRef(null)
function c(e){return B(e).every((e=>n.value.has(e.value)))}function d(e,t){const a=s.value.select({items:e,value:t,selected:new Set(n.value)})
n.value=a}const v=t.computed((()=>n.value.size>0)),p=t.computed((()=>{const e=s.value.allSelected({allItems:r.value,currentPage:i.value})
return!!e.length&&c(e)})),m={toggleSelect:function(t,a,l){const n=[]
if(a=a??o.value.findIndex((e=>e.value===t.value)),"single"!==e.selectStrategy&&l?.shiftKey&&null!==u.value){const[e,t]=[u.value,a].sort(((e,t)=>e-t))
n.push(...o.value.slice(e,t+1).filter((e=>e.selectable)))}else n.push(t),u.value=a
d(n,!c([t]))},select:d,selectAll:function(e){const t=s.value.selectAll({value:e,allItems:r.value,currentPage:i.value,selected:new Set(n.value)})
n.value=t},isSelected:c,isSomeSelected:function(e){return B(e).some((e=>n.value.has(e.value)))},someSelected:v,allSelected:p,showSelectAll:t.toRef((()=>s.value.showSelectAll)),lastSelectedIndex:u,selectStrategy:s}
return t.provide(tc,m),m}function lc(){const e=t.inject(tc)
if(!e)throw new Error("Missing selection!")
return e}const oc=yt({sortBy:{type:Array,default:()=>[]},customKeySort:Object,multiSort:Boolean,mustSort:Boolean},"DataTable-sort"),nc=Symbol.for("vuetify:data-table-sort")
function rc(e){return{sortBy:ra(e,"sortBy"),mustSort:t.toRef((()=>e.mustSort)),multiSort:t.toRef((()=>e.multiSort))}}function ic(e){const{sortBy:a,mustSort:l,multiSort:o,page:n}=e
const r={sortBy:a,toggleSort:e=>{if(null==e.key)return
let t=a.value.map((e=>({...e})))??[]
const r=t.find((t=>t.key===e.key))
r?"desc"===r.order?l.value&&1===t.length?r.order="asc":t=t.filter((t=>t.key!==e.key)):r.order="desc":o.value?t.push({key:e.key,order:"asc"}):t=[{key:e.key,order:"asc"}],a.value=t,n&&(n.value=1)},isSorted:function(e){return!!a.value.find((t=>t.key===e.key))}}
return t.provide(nc,r),r}function sc(){const e=t.inject(nc)
if(!e)throw new Error("Missing sort!")
return e}function uc(e,a,l,o){const n=ga(),r=t.computed((()=>l.value.length?function(e,t,a,l){const o=new Intl.Collator(a,{sensitivity:"accent",usage:"sort"}),n=e.map((e=>[e,l?.transform?l.transform(e):e]))
return n.sort(((e,a)=>{for(let n=0;n<t.length;n++){let r=!1
const i=t[n].key,s=t[n].order??"asc"
if(!1===s)continue
let u=v(e[1],i),c=v(a[1],i),d=e[0].raw,p=a[0].raw
if("desc"===s&&([u,c]=[c,u],[d,p]=[p,d]),l?.sortRawFunctions?.[i]){const e=l.sortRawFunctions[i](d,p)
if(null==e)continue
if(r=!0,e)return e}if(l?.sortFunctions?.[i]){const e=l.sortFunctions[i](u,c)
if(null==e)continue
if(r=!0,e)return e}if(!r){if(u instanceof Date&&c instanceof Date)return u.getTime()-c.getTime()
if([u,c]=[u,c].map((e=>null!=e?e.toString().toLocaleLowerCase():e)),u!==c)return J(u)&&J(c)?0:J(u)?-1:J(c)?1:isNaN(u)||isNaN(c)?o.compare(u,c):Number(u)-Number(c)}}return 0})).map((e=>{let[t]=e
return t}))}(a.value,l.value,n.current.value,{transform:o?.transform,sortFunctions:{...e.customKeySort,...o?.sortFunctions?.value},sortRawFunctions:o?.sortRawFunctions?.value}):a.value))
return{sortedItems:r}}const cc=yt({items:{type:Array,default:()=>[]},itemValue:{type:[String,Array,Function],default:"id"},itemSelectable:{type:[String,Array,Function],default:null},returnObject:Boolean},"DataIterator-items")
function dc(e,t){return{type:"item",value:e.returnObject?t:p(t,e.itemValue),selectable:p(t,e.itemSelectable,!0),raw:t}}function vc(e){const a=t.computed((()=>function(e,t){const a=[]
for(const l of t)a.push(dc(e,l))
return a}(e,e.items)))
return{items:a}}const pc=yt({search:String,loading:Boolean,...bt(),...cc(),...ec(),...oc(),...Yu({itemsPerPage:5}),...Au(),...zu(),...Li(),...Ba(),...fl({transition:{component:Ya,hideOnLeave:!0}})},"VDataIterator"),mc=Et()({name:"VDataIterator",props:pc(),emits:{"update:modelValue":e=>!0,"update:groupBy":e=>!0,"update:page":e=>!0,"update:itemsPerPage":e=>!0,"update:sortBy":e=>!0,"update:options":e=>!0,"update:expanded":e=>!0,"update:currentItems":e=>!0},setup(e,a){let{slots:l}=a
const o=ra(e,"groupBy"),n=t.toRef((()=>e.search)),{items:r}=vc(e),{filteredItems:i}=ji(e,r,n,{transform:e=>e.raw}),{sortBy:s,multiSort:u,mustSort:c}=rc(e),{page:d,itemsPerPage:v}=qu(e),{toggleSort:p}=ic({sortBy:s,multiSort:u,mustSort:c,page:d}),{sortByWithGroups:m,opened:f,extractRows:g,isGroupOpen:h,toggleGroup:y}=Ou({groupBy:o,sortBy:s}),{sortedItems:b}=uc(e,i,m,{transform:e=>e.raw}),{flatItems:V}=Wu(b,o,f),w=t.toRef((()=>V.value.length)),{startIndex:S,stopIndex:k,pageCount:x,prevPage:C,nextPage:N,setItemsPerPage:E,setPage:I}=Ku({page:d,itemsPerPage:v,itemsLength:w}),{paginatedItems:_}=Xu({items:V,startIndex:S,stopIndex:k,itemsPerPage:v}),P=t.computed((()=>g(_.value))),{isSelected:B,select:R,selectAll:A,toggleSelect:T}=ac(e,{allItems:r,currentPage:P}),{isExpanded:D,toggleExpand:F}=Du(e)
Uu({page:d,itemsPerPage:v,sortBy:s,groupBy:o,search:n})
const z=t.computed((()=>({page:d.value,itemsPerPage:v.value,sortBy:s.value,pageCount:x.value,toggleSort:p,prevPage:C,nextPage:N,setPage:I,setItemsPerPage:E,isSelected:B,select:R,selectAll:A,toggleSelect:T,isExpanded:D,toggleExpand:F,isGroupOpen:h,toggleGroup:y,items:P.value,groupedItems:_.value})))
return Ft((()=>t.createVNode(e.tag,{class:t.normalizeClass(["v-data-iterator",{"v-data-iterator--loading":e.loading},e.class]),style:t.normalizeStyle(e.style)},{default:()=>[l.header?.(z.value),t.createVNode(gl,{transition:e.transition},{default:()=>[e.loading?t.createVNode(co,{key:"loader",name:"v-data-iterator",active:!0},{default:e=>l.loader?.(e)}):t.createElementVNode("div",{key:"items"},[_.value.length?l.default?.(z.value):l["no-data"]?.()])]}),l.footer?.(z.value)]}))),{}}})
const fc=yt({activeColor:String,start:{type:[Number,String],default:1},modelValue:{type:Number,default:e=>e.start},disabled:Boolean,length:{type:[Number,String],default:1,validator:e=>e%1==0},totalVisible:[Number,String],firstIcon:{type:zt,default:"$first"},prevIcon:{type:zt,default:"$prev"},nextIcon:{type:zt,default:"$next"},lastIcon:{type:zt,default:"$last"},ariaLabel:{type:String,default:"$vuetify.pagination.ariaLabel.root"},pageAriaLabel:{type:String,default:"$vuetify.pagination.ariaLabel.page"},currentPageAriaLabel:{type:String,default:"$vuetify.pagination.ariaLabel.currentPage"},firstAriaLabel:{type:String,default:"$vuetify.pagination.ariaLabel.first"},previousAriaLabel:{type:String,default:"$vuetify.pagination.ariaLabel.previous"},nextAriaLabel:{type:String,default:"$vuetify.pagination.ariaLabel.next"},lastAriaLabel:{type:String,default:"$vuetify.pagination.ariaLabel.last"},ellipsis:{type:String,default:"..."},showFirstLastPage:Boolean,...wl(),...bt(),...Al(),...kl(),...pl(),...Xl(),...Ba({tag:"nav"}),...Va(),...zl({variant:"text"})},"VPagination"),gc=Et()({name:"VPagination",props:fc(),emits:{"update:modelValue":e=>!0,first:e=>!0,prev:e=>!0,next:e=>!0,last:e=>!0},setup(e,a){let{slots:l,emit:o}=a
const n=ra(e,"modelValue"),{t:r,n:i}=ga(),{isRtl:s}=ya(),{themeClasses:u}=Ea(e),{width:c}=In(),d=t.shallowRef(-1)
xt(void 0,{scoped:!0})
const{resizeRef:v}=Zt((e=>{if(!e.length)return
const{target:t,contentRect:a}=e[0],l=t.querySelector(".v-pagination__list > *")
if(!l)return
const o=a.width,n=l.offsetWidth+2*parseFloat(getComputedStyle(l).marginRight)
d.value=h(o,n)})),p=t.computed((()=>parseInt(e.length,10))),f=t.computed((()=>parseInt(e.start,10))),g=t.computed((()=>null!=e.totalVisible?parseInt(e.totalVisible,10):d.value>=0?d.value:h(c.value,58)))
function h(t,a){const l=e.showFirstLastPage?5:3
return Math.max(0,Math.floor(Number(((t-a*l)/a).toFixed(2))))}const y=t.computed((()=>{if(p.value<=0||isNaN(p.value)||p.value>Number.MAX_SAFE_INTEGER)return[]
if(g.value<=0)return[]
if(1===g.value)return[n.value]
if(p.value<=g.value)return m(p.value,f.value)
const t=g.value%2==0,a=t?g.value/2:Math.floor(g.value/2),l=t?a:a+1,o=p.value-a
if(l-n.value>=0)return[...m(Math.max(1,g.value-1),f.value),e.ellipsis,p.value]
if(n.value-o>=(t?1:0)){const t=g.value-1,a=p.value-t+f.value
return[f.value,e.ellipsis,...m(t,a)]}{const t=Math.max(1,g.value-2),a=1===t?n.value:n.value-Math.ceil(t/2)+f.value
return[f.value,e.ellipsis,...m(t,a),e.ellipsis,p.value]}}))
function b(e,t,a){e.preventDefault(),n.value=t,a&&o(a,t)}const{refs:w,updateRef:S}=function(){const e=t.ref([])
return t.onBeforeUpdate((()=>e.value=[])),{refs:e,updateRef:function(t,a){e.value[a]=t}}}()
xt({VPaginationBtn:{color:t.toRef((()=>e.color)),border:t.toRef((()=>e.border)),density:t.toRef((()=>e.density)),size:t.toRef((()=>e.size)),variant:t.toRef((()=>e.variant)),rounded:t.toRef((()=>e.rounded)),elevation:t.toRef((()=>e.elevation))}})
const k=t.computed((()=>y.value.map(((t,a)=>{const l=e=>S(e,a)
if("string"==typeof t)return{isActive:!1,key:`ellipsis-${a}`,page:t,props:{ref:l,ellipsis:!0,icon:!0,disabled:!0}}
{const a=t===n.value
return{isActive:a,key:t,page:i(t),props:{ref:l,ellipsis:!1,icon:!0,disabled:!!e.disabled||Number(e.length)<2,color:a?e.activeColor:e.color,"aria-current":a,"aria-label":r(a?e.currentPageAriaLabel:e.pageAriaLabel,t),onClick:e=>b(e,t)}}}})))),x=t.computed((()=>{const t=!!e.disabled||n.value<=f.value,a=!!e.disabled||n.value>=f.value+p.value-1
return{first:e.showFirstLastPage?{icon:s.value?e.lastIcon:e.firstIcon,onClick:e=>b(e,f.value,"first"),disabled:t,"aria-label":r(e.firstAriaLabel),"aria-disabled":t}:void 0,prev:{icon:s.value?e.nextIcon:e.prevIcon,onClick:e=>b(e,n.value-1,"prev"),disabled:t,"aria-label":r(e.previousAriaLabel),"aria-disabled":t},next:{icon:s.value?e.prevIcon:e.nextIcon,onClick:e=>b(e,n.value+1,"next"),disabled:a,"aria-label":r(e.nextAriaLabel),"aria-disabled":a},last:e.showFirstLastPage?{icon:s.value?e.firstIcon:e.lastIcon,onClick:e=>b(e,f.value+p.value-1,"last"),disabled:a,"aria-label":r(e.lastAriaLabel),"aria-disabled":a}:void 0}}))
function C(){const e=n.value-f.value
w.value[e]?.$el.focus()}function N(a){a.key===V.left&&!e.disabled&&n.value>Number(e.start)?(n.value=n.value-1,t.nextTick(C)):a.key===V.right&&!e.disabled&&n.value<f.value+p.value-1&&(n.value=n.value+1,t.nextTick(C))}return Ft((()=>t.createVNode(e.tag,{ref:v,class:t.normalizeClass(["v-pagination",u.value,e.class]),style:t.normalizeStyle(e.style),role:"navigation","aria-label":r(e.ariaLabel),onKeydown:N,"data-test":"v-pagination-root"},{default:()=>[t.createElementVNode("ul",{class:"v-pagination__list"},[e.showFirstLastPage&&t.createElementVNode("li",{key:"first",class:"v-pagination__first","data-test":"v-pagination-first"},[l.first?l.first(x.value.first):t.createVNode($o,t.mergeProps({_as:"VPaginationBtn"},x.value.first),null)]),t.createElementVNode("li",{key:"prev",class:"v-pagination__prev","data-test":"v-pagination-prev"},[l.prev?l.prev(x.value.prev):t.createVNode($o,t.mergeProps({_as:"VPaginationBtn"},x.value.prev),null)]),k.value.map(((e,a)=>t.createElementVNode("li",{key:e.key,class:t.normalizeClass(["v-pagination__item",{"v-pagination__item--is-active":e.isActive}]),"data-test":"v-pagination-item"},[l.item?l.item(e):t.createVNode($o,t.mergeProps({_as:"VPaginationBtn"},e.props),{default:()=>[e.page]})]))),t.createElementVNode("li",{key:"next",class:"v-pagination__next","data-test":"v-pagination-next"},[l.next?l.next(x.value.next):t.createVNode($o,t.mergeProps({_as:"VPaginationBtn"},x.value.next),null)]),e.showFirstLastPage&&t.createElementVNode("li",{key:"last",class:"v-pagination__last","data-test":"v-pagination-last"},[l.last?l.last(x.value.last):t.createVNode($o,t.mergeProps({_as:"VPaginationBtn"},x.value.last),null)])])]}))),{}}}),hc=yt({prevIcon:{type:zt,default:"$prev"},nextIcon:{type:zt,default:"$next"},firstIcon:{type:zt,default:"$first"},lastIcon:{type:zt,default:"$last"},itemsPerPageText:{type:String,default:"$vuetify.dataFooter.itemsPerPageText"},pageText:{type:String,default:"$vuetify.dataFooter.pageText"},firstPageLabel:{type:String,default:"$vuetify.dataFooter.firstPage"},prevPageLabel:{type:String,default:"$vuetify.dataFooter.prevPage"},nextPageLabel:{type:String,default:"$vuetify.dataFooter.nextPage"},lastPageLabel:{type:String,default:"$vuetify.dataFooter.lastPage"},itemsPerPageOptions:{type:Array,default:()=>[{value:10,title:"10"},{value:25,title:"25"},{value:50,title:"50"},{value:100,title:"100"},{value:-1,title:"$vuetify.dataFooter.itemsPerPageAll"}]},showCurrentPage:Boolean},"VDataTableFooter"),yc=Et()({name:"VDataTableFooter",props:hc(),setup(e,a){let{slots:l}=a
const{t:o}=ga(),{page:n,pageCount:r,startIndex:i,stopIndex:s,itemsLength:u,itemsPerPage:c,setItemsPerPage:d}=function(){const e=t.inject(Gu)
if(!e)throw new Error("Missing pagination!")
return e}(),v=t.computed((()=>e.itemsPerPageOptions.map((e=>"number"==typeof e?{value:e,title:-1===e?o("$vuetify.dataFooter.itemsPerPageAll"):String(e)}:{...e,title:isNaN(Number(e.title))?o(e.title):e.title}))))
return Ft((()=>{const a=gc.filterProps(e)
return t.createElementVNode("div",{class:"v-data-table-footer"},[l.prepend?.(),t.createElementVNode("div",{class:"v-data-table-footer__items-per-page"},[t.createElementVNode("span",null,[o(e.itemsPerPageText)]),t.createVNode($i,{items:v.value,modelValue:c.value,"onUpdate:modelValue":e=>d(Number(e)),density:"compact",variant:"outlined","hide-details":!0},null)]),t.createElementVNode("div",{class:"v-data-table-footer__info"},[t.createElementVNode("div",null,[o(e.pageText,u.value?i.value+1:0,s.value,u.value)])]),t.createElementVNode("div",{class:"v-data-table-footer__pagination"},[t.createVNode(gc,t.mergeProps({modelValue:n.value,"onUpdate:modelValue":e=>n.value=e,density:"comfortable","first-aria-label":e.firstPageLabel,"last-aria-label":e.lastPageLabel,length:r.value,"next-aria-label":e.nextPageLabel,"previous-aria-label":e.prevPageLabel,rounded:!0,"show-first-last-page":!0,"total-visible":e.showCurrentPage?1:0,variant:"plain"},a),null)])])})),{}}}),bc=(Vc={align:{type:String,default:"start"},fixed:Boolean,fixedOffset:[Number,String],height:[Number,String],lastFixed:Boolean,noPadding:Boolean,tag:String,width:[Number,String],maxWidth:[Number,String],nowrap:Boolean},wc=(e,a)=>{let{slots:l}=a
const o=e.tag??"td"
return t.createVNode(o,{class:t.normalizeClass(["v-data-table__td",{"v-data-table-column--fixed":e.fixed,"v-data-table-column--last-fixed":e.lastFixed,"v-data-table-column--no-padding":e.noPadding,"v-data-table-column--nowrap":e.nowrap},`v-data-table-column--align-${e.align}`]),style:{height:f(e.height),width:f(e.width),maxWidth:f(e.maxWidth),left:f(e.fixedOffset||null)}},{default:()=>[l.default?.()]})},wc.props=Vc,wc)
var Vc,wc
const Sc=yt({headers:Array},"DataTable-header"),kc=Symbol.for("vuetify:data-table-headers"),xc={title:"",sortable:!1},Cc={...xc,width:48}
function Nc(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[]
if(e.children)for(const a of e.children)Nc(a,t)
else t.push(e)
return t}function Ec(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:new Set
for(const a of e)a.key&&t.add(a.key),a.children&&Ec(a.children,t)
return t}function Ic(e){if(e.key)return"data-table-group"===e.key?xc:["data-table-expand","data-table-select"].includes(e.key)?Cc:void 0}function _c(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0
return e.children?Math.max(t,...e.children.map((e=>_c(e,t+1)))):t}function Pc(e,t){const a=[]
let l=0
const o=function(){const e=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:[]).map((e=>({element:e,priority:0})))
return{enqueue:(t,a)=>{let l=!1
for(let o=0;o<e.length;o++)if(e[o].priority>a){e.splice(o,0,{element:t,priority:a}),l=!0
break}l||e.push({element:t,priority:a})},size:()=>e.length,count:()=>{let t=0
if(!e.length)return 0
const a=Math.floor(e[0].priority)
for(let l=0;l<e.length;l++)Math.floor(e[l].priority)===a&&(t+=1)
return t},dequeue:()=>e.shift()}}(e)
for(;o.size()>0;){let e=o.count()
const n=[]
let r=1
for(;e>0;){const{element:a,priority:i}=o.dequeue(),s=t-l-_c(a)
if(n.push({...a,rowspan:s??1,colspan:a.children?Nc(a).length:1}),a.children)for(const e of a.children){const t=i%1+r/Math.pow(10,l+2)
o.enqueue(e,l+s+t)}r+=1,e-=1}l+=1,a.push(n)}return{columns:e.map((e=>Nc(e))).flat(),headers:a}}function Bc(e){const t=[]
for(const a of e){const e={...Ic(a),...a},l=e.key??("string"==typeof e.value?e.value:null),o=e.value??l??null,n={...e,key:l,value:o,sortable:e.sortable??(null!=e.key||!!e.sort),children:e.children?Bc(e.children):void 0}
t.push(n)}return t}function Rc(e,a){const l=t.ref([]),o=t.ref([]),n=t.ref({}),r=t.ref({}),i=t.ref({})
t.watchEffect((()=>{const s=(e.headers||Object.keys(e.items[0]??{}).map((e=>({key:e,title:t.capitalize(e)})))).slice(),u=Ec(s)
a?.groupBy?.value.length&&!u.has("data-table-group")&&s.unshift({key:"data-table-group",title:"Group"}),a?.showSelect?.value&&!u.has("data-table-select")&&s.unshift({key:"data-table-select"}),a?.showExpand?.value&&!u.has("data-table-expand")&&s.push({key:"data-table-expand"})
const c=Bc(s)
!function(e){let t=!1
function a(e){if(e)if(arguments.length>1&&void 0!==arguments[1]&&arguments[1]&&(e.fixed=!0),e.fixed)if(e.children)for(let t=e.children.length-1;t>=0;t--)a(e.children[t],!0)
else t?isNaN(Number(e.width))?Oe(`Multiple fixed columns should have a static width (key: ${e.key})`):e.minWidth=Math.max(Number(e.width)||0,Number(e.minWidth)||0):e.lastFixed=!0,t=!0
else if(e.children)for(let t=e.children.length-1;t>=0;t--)a(e.children[t])
else t=!1}for(let t=e.length-1;t>=0;t--)a(e[t])
function l(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0
if(!e)return t
if(e.children){e.fixedOffset=t
for(const a of e.children)t=l(a,t)}else e.fixed&&(e.fixedOffset=t,t+=parseFloat(e.width||"0")||0)
return t}let o=0
for(const t of e)o=l(t,o)}(c)
const d=Math.max(...c.map((e=>_c(e))))+1,v=Pc(c,d)
l.value=v.headers,o.value=v.columns
const p=v.headers.flat(1)
for(const e of p)e.key&&(e.sortable&&(e.sort&&(n.value[e.key]=e.sort),e.sortRaw&&(r.value[e.key]=e.sortRaw)),e.filter&&(i.value[e.key]=e.filter))}))
const s={headers:l,columns:o,sortFunctions:n,sortRawFunctions:r,filterFunctions:i}
return t.provide(kc,s),s}function Ac(){const e=t.inject(kc)
if(!e)throw new Error("Missing headers!")
return e}const Tc=yt({color:String,disableSort:Boolean,fixedHeader:Boolean,multiSort:Boolean,sortAscIcon:{type:zt,default:"$sortAsc"},sortDescIcon:{type:zt,default:"$sortDesc"},headerProps:{type:Object},sticky:Boolean,...En(),...so()},"VDataTableHeaders"),Dc=Et()({name:"VDataTableHeaders",props:Tc(),setup(e,a){let{slots:l}=a
const{t:o}=ga(),{toggleSort:n,sortBy:r,isSorted:i}=sc(),{someSelected:s,allSelected:u,selectAll:c,showSelectAll:d}=lc(),{columns:v,headers:p}=Ac(),{loaderClasses:m}=uo(e)
function g(t,a){if(e.sticky||e.fixedHeader||t.fixed)return{position:"sticky",left:t.fixed?f(t.fixedOffset):void 0,top:e.sticky||e.fixedHeader?`calc(var(--v-table-header-height) * ${a})`:void 0}}function h(t){const a=r.value.find((e=>e.key===t.key))
return a?"asc"===a.order?e.sortAscIcon:e.sortDescIcon:e.sortAscIcon}const{backgroundColorClasses:y,backgroundColorStyles:b}=vl((()=>e.color)),{displayClasses:V,mobile:w}=In(e),S=t.computed((()=>({headers:p.value,columns:v.value,toggleSort:n,isSorted:i,sortBy:r.value,someSelected:s.value,allSelected:u.value,selectAll:c,getSortIcon:h}))),k=t.computed((()=>["v-data-table__th",{"v-data-table__th--sticky":e.sticky||e.fixedHeader},V.value,m.value])),x=a=>{let{column:o,x:v,y:p}=a
const m="data-table-select"===o.key||"data-table-expand"===o.key,V=t.mergeProps(e.headerProps??{},o.headerProps??{})
return t.createVNode(bc,t.mergeProps({tag:"th",align:o.align,class:[{"v-data-table__th--sortable":o.sortable&&!e.disableSort,"v-data-table__th--sorted":i(o),"v-data-table__th--fixed":o.fixed},...k.value],style:{width:f(o.width),minWidth:f(o.minWidth),maxWidth:f(o.maxWidth),...g(o,p)},colspan:o.colspan,rowspan:o.rowspan,onClick:o.sortable?()=>n(o):void 0,fixed:o.fixed,nowrap:o.nowrap,lastFixed:o.lastFixed,noPadding:m},V),{default:()=>{const a=`header.${o.key}`,v={column:o,selectAll:c,isSorted:i,toggleSort:n,sortBy:r.value,someSelected:s.value,allSelected:u.value,getSortIcon:h}
return l[a]?l[a](v):"data-table-select"===o.key?l["header.data-table-select"]?.(v)??(d.value&&t.createVNode(ln,{modelValue:u.value,indeterminate:s.value&&!u.value,"onUpdate:modelValue":c},null)):t.createElementVNode("div",{class:"v-data-table-header__content"},[t.createElementVNode("span",null,[o.title]),o.sortable&&!e.disableSort&&t.createVNode(Jl,{key:"icon",class:"v-data-table-header__sort-icon",icon:h(o)},null),e.multiSort&&i(o)&&t.createElementVNode("div",{key:"badge",class:t.normalizeClass(["v-data-table-header__sort-badge",...y.value]),style:t.normalizeStyle(b.value)},[r.value.findIndex((e=>e.key===o.key))+1])])}})},C=()=>{const a=t.computed((()=>v.value.filter((t=>t?.sortable&&!e.disableSort)))),d=t.computed((()=>{if(null!=v.value.find((e=>"data-table-select"===e.key)))return u.value?"$checkboxOn":s.value?"$checkboxIndeterminate":"$checkboxOff"}))
return t.createVNode(bc,t.mergeProps({tag:"th",class:[...k.value],colspan:p.value.length+1},e.headerProps),{default:()=>[t.createElementVNode("div",{class:"v-data-table-header__content"},[t.createVNode($i,{chips:!0,class:"v-data-table__td-sort-select",clearable:!0,density:"default",items:a.value,label:o("$vuetify.dataTable.sortBy"),multiple:e.multiSort,variant:"underlined","onClick:clear":()=>r.value=[],appendIcon:d.value,"onClick:append":()=>c(!u.value)},{...l,chip:e=>t.createVNode(Un,{onClick:e.item.raw?.sortable?()=>n(e.item.raw):void 0,onMousedown:e=>{e.preventDefault(),e.stopPropagation()}},{default:()=>[e.item.title,t.createVNode(Jl,{class:t.normalizeClass(["v-data-table__td-sort-icon",i(e.item.raw)&&"v-data-table__td-sort-icon-active"]),icon:h(e.item.raw),size:"small"},null)]})})])]})}
Ft((()=>w.value?t.createElementVNode("tr",null,[t.createVNode(C,null,null)]):t.createElementVNode(t.Fragment,null,[l.headers?l.headers(S.value):p.value.map(((e,a)=>t.createElementVNode("tr",null,[e.map(((e,l)=>t.createVNode(x,{column:e,x:l,y:a},null)))]))),e.loading&&t.createElementVNode("tr",{class:"v-data-table-progress"},[t.createElementVNode("th",{colspan:v.value.length},[t.createVNode(co,{name:"v-data-table-progress",absolute:!0,active:!0,color:"boolean"==typeof e.loading?void 0:e.loading,indeterminate:!0},{default:l.loader})])])])))}}),Fc=yt({item:{type:Object,required:!0}},"VDataTableGroupHeaderRow"),zc=Et()({name:"VDataTableGroupHeaderRow",props:Fc(),setup(e,a){let{slots:l}=a
const{isGroupOpen:o,toggleGroup:n,extractRows:r}=Lu(),{isSelected:i,isSomeSelected:s,select:u}=lc(),{columns:c}=Ac(),d=t.computed((()=>r([e.item])))
return()=>t.createElementVNode("tr",{class:"v-data-table-group-header-row",style:{"--v-data-table-group-header-row-depth":e.item.depth}},[c.value.map((a=>{if("data-table-group"===a.key){const a=o(e.item)?"$expand":"$next",r=()=>n(e.item)
return l["data-table-group"]?.({item:e.item,count:d.value.length,props:{icon:a,onClick:r}})??t.createVNode(bc,{class:"v-data-table-group-header-row__column"},{default:()=>[t.createVNode($o,{size:"small",variant:"text",icon:a,onClick:r},null),t.createElementVNode("span",null,[e.item.value]),t.createElementVNode("span",null,[t.createTextVNode("("),d.value.length,t.createTextVNode(")")])]})}if("data-table-select"===a.key){const e=i(d.value),a=s(d.value)&&!e,o=e=>u(d.value,e)
return l["data-table-select"]?.({props:{modelValue:e,indeterminate:a,"onUpdate:modelValue":o}})??t.createElementVNode("td",null,[t.createVNode(ln,{modelValue:e,indeterminate:a,"onUpdate:modelValue":o},null)])}return t.createElementVNode("td",null,null)}))])}}),$c=yt({index:Number,item:Object,cellProps:[Object,Function],onClick:G(),onContextmenu:G(),onDblclick:G(),...En()},"VDataTableRow"),Mc=Et()({name:"VDataTableRow",props:$c(),setup(e,a){let{slots:l}=a
const{displayClasses:o,mobile:n}=In(e,"v-data-table__tr"),{isSelected:r,toggleSelect:i,someSelected:s,allSelected:u,selectAll:c}=lc(),{isExpanded:d,toggleExpand:p}=Fu(),{toggleSort:m,sortBy:f,isSorted:g}=sc(),{columns:h}=Ac()
Ft((()=>t.createElementVNode("tr",{class:t.normalizeClass(["v-data-table__tr",{"v-data-table__tr--clickable":!!(e.onClick||e.onContextmenu||e.onDblclick)},o.value]),onClick:e.onClick,onContextmenu:e.onContextmenu,onDblclick:e.onDblclick},[e.item&&h.value.map(((a,o)=>{const h=e.item,y=`item.${a.key}`,b=`header.${a.key}`,V={index:e.index,item:h.raw,internalItem:h,value:v(h.columns,a.key),column:a,isSelected:r,toggleSelect:i,isExpanded:d,toggleExpand:p},w={column:a,selectAll:c,isSorted:g,toggleSort:m,sortBy:f.value,someSelected:s.value,allSelected:u.value,getSortIcon:()=>""},S="function"==typeof e.cellProps?e.cellProps({index:V.index,item:V.item,internalItem:V.internalItem,value:V.value,column:a}):e.cellProps,k="function"==typeof a.cellProps?a.cellProps({index:V.index,item:V.item,internalItem:V.internalItem,value:V.value}):a.cellProps
return t.createVNode(bc,t.mergeProps({align:a.align,class:{"v-data-table__td--expanded-row":"data-table-expand"===a.key,"v-data-table__td--select-row":"data-table-select"===a.key},fixed:a.fixed,fixedOffset:a.fixedOffset,lastFixed:a.lastFixed,maxWidth:n.value?void 0:a.maxWidth,noPadding:"data-table-select"===a.key||"data-table-expand"===a.key,nowrap:a.nowrap,width:n.value?void 0:a.width},S,k),{default:()=>{if("data-table-select"===a.key)return l["item.data-table-select"]?.({...V,props:{disabled:!h.selectable,modelValue:r([h]),onClick:t.withModifiers((()=>i(h)),["stop"])}})??t.createVNode(ln,{disabled:!h.selectable,modelValue:r([h]),onClick:t.withModifiers((t=>i(h,e.index,t)),["stop"])},null)
if("data-table-expand"===a.key)return l["item.data-table-expand"]?.({...V,props:{icon:d(h)?"$collapse":"$expand",size:"small",variant:"text",onClick:t.withModifiers((()=>p(h)),["stop"])}})??t.createVNode($o,{icon:d(h)?"$collapse":"$expand",size:"small",variant:"text",onClick:t.withModifiers((()=>p(h)),["stop"])},null)
if(l[y]&&!n.value)return l[y](V)
const o=t.toDisplayString(V.value)
return n.value?t.createElementVNode(t.Fragment,null,[t.createElementVNode("div",{class:"v-data-table__td-title"},[l[b]?.(w)??a.title]),t.createElementVNode("div",{class:"v-data-table__td-value"},[l[y]?.(V)??o])]):o}})}))])))}}),Oc=yt({loading:[Boolean,String],loadingText:{type:String,default:"$vuetify.dataIterator.loadingText"},hideNoData:Boolean,items:{type:Array,default:()=>[]},noDataText:{type:String,default:"$vuetify.noDataText"},rowProps:[Object,Function],cellProps:[Object,Function],...En()},"VDataTableRows"),Lc=Et()({name:"VDataTableRows",inheritAttrs:!1,props:Oc(),setup(e,a){let{attrs:l,slots:o}=a
const{columns:n}=Ac(),{expandOnClick:r,toggleExpand:i,isExpanded:s}=Fu(),{isSelected:u,toggleSelect:c}=lc(),{toggleGroup:d,isGroupOpen:v}=Lu(),{t:p}=ga(),{mobile:m}=In(e)
return Ft((()=>!e.loading||e.items.length&&!o.loading?e.loading||e.items.length||e.hideNoData?t.createElementVNode(t.Fragment,null,[e.items.map(((a,p)=>{if("group"===a.type){const e={index:p,item:a,columns:n.value,isExpanded:s,toggleExpand:i,isSelected:u,toggleSelect:c,toggleGroup:d,isGroupOpen:v}
return o["group-header"]?o["group-header"](e):t.createVNode(zc,t.mergeProps({key:`group-header_${a.id}`,item:a},Bt(l,":group-header",(()=>e))),o)}const f={index:p,item:a.raw,internalItem:a,columns:n.value,isExpanded:s,toggleExpand:i,isSelected:u,toggleSelect:c},g={...f,props:t.mergeProps({key:`item_${a.key??a.index}`,onClick:r.value?()=>{i(a)}:void 0,index:p,item:a,cellProps:e.cellProps,mobile:m.value},Bt(l,":row",(()=>f)),"function"==typeof e.rowProps?e.rowProps({item:f.item,index:f.index,internalItem:f.internalItem}):e.rowProps)}
return t.createElementVNode(t.Fragment,{key:g.props.key},[o.item?o.item(g):t.createVNode(Mc,g.props,o),s(a)&&o["expanded-row"]?.(f)])}))]):t.createElementVNode("tr",{class:"v-data-table-rows-no-data",key:"no-data"},[t.createElementVNode("td",{colspan:n.value.length},[o["no-data"]?.()??p(e.noDataText)])]):t.createElementVNode("tr",{class:"v-data-table-rows-loading",key:"loading"},[t.createElementVNode("td",{colspan:n.value.length},[o.loading?.()??p(e.loadingText)])]))),{}}}),jc=yt({fixedHeader:Boolean,fixedFooter:Boolean,height:[Number,String],hover:Boolean,...bt(),...Al(),...Ba(),...Va()},"VTable"),Hc=Et()({name:"VTable",props:jc(),setup(e,a){let{slots:l,emit:o}=a
const{themeClasses:n}=Ea(e),{densityClasses:r}=Tl(e)
return Ft((()=>t.createVNode(e.tag,{class:t.normalizeClass(["v-table",{"v-table--fixed-height":!!e.height,"v-table--fixed-header":e.fixedHeader,"v-table--fixed-footer":e.fixedFooter,"v-table--has-top":!!l.top,"v-table--has-bottom":!!l.bottom,"v-table--hover":e.hover},n.value,r.value,e.class]),style:t.normalizeStyle(e.style)},{default:()=>[l.top?.(),l.default?t.createElementVNode("div",{class:"v-table__wrapper",style:{height:f(e.height)}},[t.createElementVNode("table",null,[l.default()])]):l.wrapper?.(),l.bottom?.()]}))),{}}}),Wc=yt({items:{type:Array,default:()=>[]},itemValue:{type:[String,Array,Function],default:"id"},itemSelectable:{type:[String,Array,Function],default:null},rowProps:[Object,Function],cellProps:[Object,Function],returnObject:Boolean},"DataTable-items")
function Uc(e,t,a){return t.map(((t,l)=>function(e,t,a,l){const o=e.returnObject?t:p(t,e.itemValue),n=p(t,e.itemSelectable,!0),r=l.reduce(((e,a)=>(null!=a.key&&(e[a.key]=p(t,a.value)),e)),{})
return{type:"item",key:e.returnObject?p(t,e.itemValue):o,index:a,value:o,selectable:n,columns:r,raw:t}}(e,t,l,a)))}function Yc(e,a){return{items:t.computed((()=>Uc(e,e.items,a.value)))}}const Gc=yt({...Oc(),hideDefaultBody:Boolean,hideDefaultFooter:Boolean,hideDefaultHeader:Boolean,width:[String,Number],search:String,...Au(),...zu(),...Sc(),...Wc(),...ec(),...oc(),...Tc(),...jc()},"DataTable"),qc=yt({...Yu(),...Gc(),...Li(),...hc()},"VDataTable"),Kc=Et()({name:"VDataTable",props:qc(),emits:{"update:modelValue":e=>!0,"update:page":e=>!0,"update:itemsPerPage":e=>!0,"update:sortBy":e=>!0,"update:options":e=>!0,"update:groupBy":e=>!0,"update:expanded":e=>!0,"update:currentItems":e=>!0},setup(e,a){let{attrs:l,slots:o}=a
const{groupBy:n}=Mu(e),{sortBy:r,multiSort:i,mustSort:s}=rc(e),{page:u,itemsPerPage:c}=qu(e),{disableSort:d}=t.toRefs(e),{columns:v,headers:p,sortFunctions:m,sortRawFunctions:f,filterFunctions:g}=Rc(e,{groupBy:n,showSelect:t.toRef((()=>e.showSelect)),showExpand:t.toRef((()=>e.showExpand))}),{items:h}=Yc(e,v),y=t.toRef((()=>e.search)),{filteredItems:b}=ji(e,h,y,{transform:e=>e.columns,customKeyFilter:g}),{toggleSort:V}=ic({sortBy:r,multiSort:i,mustSort:s,page:u}),{sortByWithGroups:w,opened:S,extractRows:k,isGroupOpen:x,toggleGroup:C}=Ou({groupBy:n,sortBy:r,disableSort:d}),{sortedItems:N}=uc(e,b,w,{transform:e=>({...e.raw,...e.columns}),sortFunctions:m,sortRawFunctions:f}),{flatItems:E}=Wu(N,n,S),I=t.computed((()=>E.value.length)),{startIndex:_,stopIndex:P,pageCount:B,setItemsPerPage:R}=Ku({page:u,itemsPerPage:c,itemsLength:I}),{paginatedItems:A}=Xu({items:E,startIndex:_,stopIndex:P,itemsPerPage:c}),T=t.computed((()=>k(A.value))),{isSelected:D,select:F,selectAll:z,toggleSelect:$,someSelected:M,allSelected:O}=ac(e,{allItems:h,currentPage:T}),{isExpanded:L,toggleExpand:j}=Du(e)
Uu({page:u,itemsPerPage:c,sortBy:r,groupBy:n,search:y}),xt({VDataTableRows:{hideNoData:t.toRef((()=>e.hideNoData)),noDataText:t.toRef((()=>e.noDataText)),loading:t.toRef((()=>e.loading)),loadingText:t.toRef((()=>e.loadingText))}})
const H=t.computed((()=>({page:u.value,itemsPerPage:c.value,sortBy:r.value,pageCount:B.value,toggleSort:V,setItemsPerPage:R,someSelected:M.value,allSelected:O.value,isSelected:D,select:F,selectAll:z,toggleSelect:$,isExpanded:L,toggleExpand:j,isGroupOpen:x,toggleGroup:C,items:T.value.map((e=>e.raw)),internalItems:T.value,groupedItems:A.value,columns:v.value,headers:p.value})))
return Ft((()=>{const a=yc.filterProps(e),n=Dc.filterProps(e),r=Lc.filterProps(e),i=Hc.filterProps(e)
return t.createVNode(Hc,t.mergeProps({class:["v-data-table",{"v-data-table--show-select":e.showSelect,"v-data-table--loading":e.loading},e.class],style:e.style},i,{fixedHeader:e.fixedHeader||e.sticky}),{top:()=>o.top?.(H.value),default:()=>o.default?o.default(H.value):t.createElementVNode(t.Fragment,null,[o.colgroup?.(H.value),!e.hideDefaultHeader&&t.createElementVNode("thead",{key:"thead"},[t.createVNode(Dc,n,o)]),o.thead?.(H.value),!e.hideDefaultBody&&t.createElementVNode("tbody",null,[o["body.prepend"]?.(H.value),o.body?o.body(H.value):t.createVNode(Lc,t.mergeProps(l,r,{items:A.value}),o),o["body.append"]?.(H.value)]),o.tbody?.(H.value),o.tfoot?.(H.value)]),bottom:()=>o.bottom?o.bottom(H.value):!e.hideDefaultFooter&&t.createElementVNode(t.Fragment,null,[t.createVNode(br,null,null),t.createVNode(yc,a,{prepend:o["footer.prepend"]})])})})),{}}}),Xc=yt({...C(Gc(),["hideDefaultFooter"]),...zu(),...Bi(),...Li()},"VDataTableVirtual"),Zc=Et()({name:"VDataTableVirtual",props:Xc(),emits:{"update:modelValue":e=>!0,"update:sortBy":e=>!0,"update:options":e=>!0,"update:groupBy":e=>!0,"update:expanded":e=>!0},setup(e,a){let{attrs:l,slots:o}=a
const{groupBy:n}=Mu(e),{sortBy:r,multiSort:i,mustSort:s}=rc(e),{disableSort:u}=t.toRefs(e),{columns:c,headers:d,filterFunctions:v,sortFunctions:p,sortRawFunctions:m}=Rc(e,{groupBy:n,showSelect:t.toRef((()=>e.showSelect)),showExpand:t.toRef((()=>e.showExpand))}),{items:g}=Yc(e,c),h=t.toRef((()=>e.search)),{filteredItems:y}=ji(e,g,h,{transform:e=>e.columns,customKeyFilter:v}),{toggleSort:b}=ic({sortBy:r,multiSort:i,mustSort:s}),{sortByWithGroups:V,opened:w,extractRows:S,isGroupOpen:k,toggleGroup:x}=Ou({groupBy:n,sortBy:r,disableSort:u}),{sortedItems:C}=uc(e,y,V,{transform:e=>({...e.raw,...e.columns}),sortFunctions:p,sortRawFunctions:m}),{flatItems:N}=Wu(C,n,w),E=t.computed((()=>S(N.value))),{isSelected:I,select:_,selectAll:P,toggleSelect:B,someSelected:R,allSelected:A}=ac(e,{allItems:E,currentPage:E}),{isExpanded:T,toggleExpand:D}=Du(e),{containerRef:F,markerRef:z,paddingTop:$,paddingBottom:M,computedItems:O,handleItemResize:L,handleScroll:j,handleScrollend:H,calculateVisibleItems:W,scrollToIndex:U}=Ri(e,N),Y=t.computed((()=>O.value.map((e=>e.raw))))
Uu({sortBy:r,page:t.shallowRef(1),itemsPerPage:t.shallowRef(-1),groupBy:n,search:h}),xt({VDataTableRows:{hideNoData:t.toRef((()=>e.hideNoData)),noDataText:t.toRef((()=>e.noDataText)),loading:t.toRef((()=>e.loading)),loadingText:t.toRef((()=>e.loadingText))}})
const G=t.computed((()=>({sortBy:r.value,toggleSort:b,someSelected:R.value,allSelected:A.value,isSelected:I,select:_,selectAll:P,toggleSelect:B,isExpanded:T,toggleExpand:D,isGroupOpen:k,toggleGroup:x,items:E.value.map((e=>e.raw)),internalItems:E.value,groupedItems:N.value,columns:c.value,headers:d.value})))
return Ft((()=>{const a=Dc.filterProps(e),n=Lc.filterProps(e),r=Hc.filterProps(e)
return t.createVNode(Hc,t.mergeProps({class:["v-data-table",{"v-data-table--loading":e.loading},e.class],style:e.style},r,{fixedHeader:e.fixedHeader||e.sticky}),{top:()=>o.top?.(G.value),wrapper:()=>t.createElementVNode("div",{ref:F,onScrollPassive:j,onScrollend:H,class:"v-table__wrapper",style:{height:f(e.height)}},[t.createElementVNode("table",null,[o.colgroup?.(G.value),!e.hideDefaultHeader&&t.createElementVNode("thead",{key:"thead"},[t.createVNode(Dc,a,o)]),o.thead?.(G.value),!e.hideDefaultBody&&t.createElementVNode("tbody",{key:"tbody"},[t.createElementVNode("tr",{ref:z,style:{height:f($.value),border:0}},[t.createElementVNode("td",{colspan:c.value.length,style:{height:0,border:0}},null)]),o["body.prepend"]?.(G.value),t.createVNode(Lc,t.mergeProps(l,n,{items:Y.value}),{...o,item:e=>t.createVNode(Pi,{key:e.internalItem.index,renderless:!0,"onUpdate:height":t=>L(e.internalItem.index,t)},{default:a=>{let{itemRef:l}=a
return o.item?.({...e,itemRef:l})??t.createVNode(Mc,t.mergeProps(e.props,{ref:l,key:e.internalItem.index,index:e.internalItem.index}),o)}})}),o["body.append"]?.(G.value),t.createElementVNode("tr",{style:{height:f(M.value),border:0}},[t.createElementVNode("td",{colspan:c.value.length,style:{height:0,border:0}},null)])]),o.tbody?.(G.value),o.tfoot?.(G.value)])]),bottom:()=>o.bottom?.(G.value)})})),{calculateVisibleItems:W,scrollToIndex:U}}}),Qc=yt({itemsLength:{type:[Number,String],required:!0},...Yu(),...Gc(),...hc()},"VDataTableServer"),Jc=Et()({name:"VDataTableServer",props:Qc(),emits:{"update:modelValue":e=>!0,"update:page":e=>!0,"update:itemsPerPage":e=>!0,"update:sortBy":e=>!0,"update:options":e=>!0,"update:expanded":e=>!0,"update:groupBy":e=>!0},setup(e,a){let{attrs:l,slots:o}=a
const{groupBy:n}=Mu(e),{sortBy:r,multiSort:i,mustSort:s}=rc(e),{page:u,itemsPerPage:c}=qu(e),{disableSort:d}=t.toRefs(e),v=t.computed((()=>parseInt(e.itemsLength,10))),{columns:p,headers:m}=Rc(e,{groupBy:n,showSelect:t.toRef((()=>e.showSelect)),showExpand:t.toRef((()=>e.showExpand))}),{items:f}=Yc(e,p),{toggleSort:g}=ic({sortBy:r,multiSort:i,mustSort:s,page:u}),{opened:h,isGroupOpen:y,toggleGroup:b,extractRows:V}=Ou({groupBy:n,sortBy:r,disableSort:d}),{pageCount:w,setItemsPerPage:S}=Ku({page:u,itemsPerPage:c,itemsLength:v}),{flatItems:k}=Wu(f,n,h),{isSelected:x,select:C,selectAll:N,toggleSelect:E,someSelected:I,allSelected:_}=ac(e,{allItems:f,currentPage:f}),{isExpanded:P,toggleExpand:B}=Du(e),R=t.computed((()=>V(f.value)))
Uu({page:u,itemsPerPage:c,sortBy:r,groupBy:n,search:t.toRef((()=>e.search))}),t.provide("v-data-table",{toggleSort:g,sortBy:r}),xt({VDataTableRows:{hideNoData:t.toRef((()=>e.hideNoData)),noDataText:t.toRef((()=>e.noDataText)),loading:t.toRef((()=>e.loading)),loadingText:t.toRef((()=>e.loadingText))}})
const A=t.computed((()=>({page:u.value,itemsPerPage:c.value,sortBy:r.value,pageCount:w.value,toggleSort:g,setItemsPerPage:S,someSelected:I.value,allSelected:_.value,isSelected:x,select:C,selectAll:N,toggleSelect:E,isExpanded:P,toggleExpand:B,isGroupOpen:y,toggleGroup:b,items:R.value.map((e=>e.raw)),internalItems:R.value,groupedItems:k.value,columns:p.value,headers:m.value})))
Ft((()=>{const a=yc.filterProps(e),n=Dc.filterProps(e),r=Lc.filterProps(e),i=Hc.filterProps(e)
return t.createVNode(Hc,t.mergeProps({class:["v-data-table",{"v-data-table--loading":e.loading},e.class],style:e.style},i,{fixedHeader:e.fixedHeader||e.sticky}),{top:()=>o.top?.(A.value),default:()=>o.default?o.default(A.value):t.createElementVNode(t.Fragment,null,[o.colgroup?.(A.value),!e.hideDefaultHeader&&t.createElementVNode("thead",{key:"thead",class:"v-data-table__thead",role:"rowgroup"},[t.createVNode(Dc,n,o)]),o.thead?.(A.value),!e.hideDefaultBody&&t.createElementVNode("tbody",{class:"v-data-table__tbody",role:"rowgroup"},[o["body.prepend"]?.(A.value),o.body?o.body(A.value):t.createVNode(Lc,t.mergeProps(l,r,{items:k.value}),o),o["body.append"]?.(A.value)]),o.tbody?.(A.value),o.tfoot?.(A.value)]),bottom:()=>o.bottom?o.bottom(A.value):!e.hideDefaultFooter&&t.createElementVNode(t.Fragment,null,[t.createVNode(br,null,null),t.createVNode(yc,a,{prepend:o["footer.prepend"]})])})}))}}),ed=yt({fluid:{type:Boolean,default:!1},...bt(),...rl(),...Ba()},"VContainer"),td=Et()({name:"VContainer",props:ed(),setup(e,a){let{slots:l}=a
const{rtlClasses:o}=ya(),{dimensionStyles:n}=il(e)
return Ft((()=>t.createVNode(e.tag,{class:t.normalizeClass(["v-container",{"v-container--fluid":e.fluid},o.value,e.class]),style:t.normalizeStyle([n.value,e.style])},l))),{}}}),ad=Vn.reduce(((e,t)=>(e[t]={type:[Boolean,String,Number],default:!1},e)),{}),ld=Vn.reduce(((e,a)=>(e["offset"+t.capitalize(a)]={type:[String,Number],default:null},e)),{}),od=Vn.reduce(((e,a)=>(e["order"+t.capitalize(a)]={type:[String,Number],default:null},e)),{}),nd={col:Object.keys(ad),offset:Object.keys(ld),order:Object.keys(od)}
function rd(e,t,a){let l=e
if(null!=a&&!1!==a){if(t){l+=`-${t.replace(e,"")}`}return"col"===e&&(l="v-"+l),"col"!==e||""!==a&&!0!==a?(l+=`-${a}`,l.toLowerCase()):l.toLowerCase()}}const id=["auto","start","end","center","baseline","stretch"],sd=yt({cols:{type:[Boolean,String,Number],default:!1},...ad,offset:{type:[String,Number],default:null},...ld,order:{type:[String,Number],default:null},...od,alignSelf:{type:String,default:null,validator:e=>id.includes(e)},...bt(),...Ba()},"VCol"),ud=Et()({name:"VCol",props:sd(),setup(e,a){let{slots:l}=a
const o=t.computed((()=>{const t=[]
let a
for(a in nd)nd[a].forEach((l=>{const o=e[l],n=rd(a,l,o)
n&&t.push(n)}))
const l=t.some((e=>e.startsWith("v-col-")))
return t.push({"v-col":!l||!e.cols,[`v-col-${e.cols}`]:e.cols,[`offset-${e.offset}`]:e.offset,[`order-${e.order}`]:e.order,[`align-self-${e.alignSelf}`]:e.alignSelf}),t}))
return()=>t.h(e.tag,{class:[o.value,e.class],style:e.style},l.default?.())}}),cd=["start","end","center"],dd=["space-between","space-around","space-evenly"]
function vd(e,a){return Vn.reduce(((l,o)=>(l[e+t.capitalize(o)]=a(),l)),{})}const pd=[...cd,"baseline","stretch"],md=e=>pd.includes(e),fd=vd("align",(()=>({type:String,default:null,validator:md}))),gd=[...cd,...dd],hd=e=>gd.includes(e),yd=vd("justify",(()=>({type:String,default:null,validator:hd}))),bd=[...cd,...dd,"stretch"],Vd=e=>bd.includes(e),wd=vd("alignContent",(()=>({type:String,default:null,validator:Vd}))),Sd={align:Object.keys(fd),justify:Object.keys(yd),alignContent:Object.keys(wd)},kd={align:"align",justify:"justify",alignContent:"align-content"}
function xd(e,t,a){let l=kd[e]
if(null!=a){if(t){l+=`-${t.replace(e,"")}`}return l+=`-${a}`,l.toLowerCase()}}const Cd=yt({dense:Boolean,noGutters:Boolean,align:{type:String,default:null,validator:md},...fd,justify:{type:String,default:null,validator:hd},...yd,alignContent:{type:String,default:null,validator:Vd},...wd,...bt(),...Ba()},"VRow"),Nd=Et()({name:"VRow",props:Cd(),setup(e,a){let{slots:l}=a
const o=t.computed((()=>{const t=[]
let a
for(a in Sd)Sd[a].forEach((l=>{const o=e[l],n=xd(a,l,o)
n&&t.push(n)}))
return t.push({"v-row--no-gutters":e.noGutters,"v-row--dense":e.dense,[`align-${e.align}`]:e.align,[`justify-${e.justify}`]:e.justify,[`align-content-${e.alignContent}`]:e.alignContent}),t}))
return()=>t.h(e.tag,{class:["v-row",o.value,e.class],style:e.style},l.default?.())}}),Ed=It("v-spacer","div","VSpacer"),Id=yt({active:{type:[String,Array],default:void 0},controlHeight:[Number,String],disabled:{type:[Boolean,String,Array],default:null},nextIcon:{type:zt,default:"$next"},prevIcon:{type:zt,default:"$prev"},modeIcon:{type:zt,default:"$subgroup"},text:String,viewMode:{type:String,default:"month"}},"VDatePickerControls"),_d=Et()({name:"VDatePickerControls",props:Id(),emits:{"click:year":()=>!0,"click:month":()=>!0,"click:prev":()=>!0,"click:next":()=>!0,"click:text":()=>!0},setup(e,a){let{emit:l}=a
const o=t.computed((()=>Array.isArray(e.disabled)?e.disabled.includes("text"):!!e.disabled)),n=t.computed((()=>Array.isArray(e.disabled)?e.disabled.includes("mode"):!!e.disabled)),r=t.computed((()=>Array.isArray(e.disabled)?e.disabled.includes("prev"):!!e.disabled)),i=t.computed((()=>Array.isArray(e.disabled)?e.disabled.includes("next"):!!e.disabled))
function s(){l("click:prev")}function u(){l("click:next")}function c(){l("click:year")}function d(){l("click:month")}return Ft((()=>t.createElementVNode("div",{class:t.normalizeClass(["v-date-picker-controls"]),style:{"--v-date-picker-controls-height":f(e.controlHeight)}},[t.createVNode($o,{class:"v-date-picker-controls__month-btn","data-testid":"month-btn",disabled:o.value,text:e.text,variant:"text",rounded:!0,onClick:d},null),t.createVNode($o,{class:"v-date-picker-controls__mode-btn","data-testid":"year-btn",disabled:n.value,density:"comfortable",icon:e.modeIcon,variant:"text",onClick:c},null),t.createVNode(Ed,null,null),t.createElementVNode("div",{class:"v-date-picker-controls__month"},[t.createVNode($o,{"data-testid":"prev-month",disabled:r.value,density:"comfortable",icon:e.prevIcon,variant:"text",onClick:s},null),t.createVNode($o,{"data-testid":"next-month",disabled:i.value,icon:e.nextIcon,density:"comfortable",variant:"text",onClick:u},null)])]))),{}}}),Pd=yt({appendIcon:zt,color:String,header:String,transition:String,onClick:G()},"VDatePickerHeader"),Bd=Et()({name:"VDatePickerHeader",props:Pd(),emits:{click:()=>!0,"click:append":()=>!0},setup(e,a){let{emit:l,slots:o}=a
const{backgroundColorClasses:n,backgroundColorStyles:r}=vl((()=>e.color))
function i(){l("click")}function s(){l("click:append")}return Ft((()=>{const a=!(!o.default&&!e.header),l=!(!o.append&&!e.appendIcon)
return t.createElementVNode("div",{class:t.normalizeClass(["v-date-picker-header",{"v-date-picker-header--clickable":!!e.onClick},n.value]),style:t.normalizeStyle(r.value),onClick:i},[o.prepend&&t.createElementVNode("div",{key:"prepend",class:"v-date-picker-header__prepend"},[o.prepend()]),a&&t.createVNode(gl,{key:"content",name:e.transition},{default:()=>[t.createElementVNode("div",{key:e.header,class:"v-date-picker-header__content"},[o.default?.()??e.header])]}),l&&t.createElementVNode("div",{class:"v-date-picker-header__append"},[o.append?t.createVNode(nl,{key:"append-defaults",disabled:!e.appendIcon,defaults:{VBtn:{icon:e.appendIcon,variant:"text"}}},{default:()=>[o.append?.()]}):t.createVNode($o,{key:"append-btn",icon:e.appendIcon,variant:"text",onClick:s},null)])])})),{}}}),Rd=yt({allowedDates:[Array,Function],disabled:{type:Boolean,default:null},displayValue:null,modelValue:Array,month:[Number,String],max:null,min:null,showAdjacentMonths:Boolean,year:[Number,String],weekdays:{type:Array,default:()=>[0,1,2,3,4,5,6]},weeksInMonth:{type:String,default:"dynamic"},firstDayOfWeek:{type:[Number,String],default:void 0}},"calendar")
const Ad=yt({color:String,hideWeekdays:Boolean,multiple:[Boolean,Number,String],showWeek:Boolean,transition:{type:String,default:"picker-transition"},reverseTransition:{type:String,default:"picker-reverse-transition"},...C(Rd(),["displayValue"])},"VDatePickerMonth"),Td=Et()({name:"VDatePickerMonth",props:Ad(),emits:{"update:modelValue":e=>!0,"update:month":e=>!0,"update:year":e=>!0},setup(e,a){let{emit:l,slots:o}=a
const n=t.ref(),{daysInMonth:r,model:i,weekNumbers:s}=function(e){const a=Eu(),l=ra(e,"modelValue",[],(e=>B(e).map((e=>a.date(e))))),o=t.computed((()=>e.displayValue?a.date(e.displayValue):l.value.length>0?a.date(l.value[0]):e.min?a.date(e.min):Array.isArray(e.allowedDates)?a.date(e.allowedDates[0]):a.date())),n=ra(e,"year",void 0,(e=>{const t=null!=e?Number(e):a.getYear(o.value)
return a.startOfYear(a.setYear(a.date(),t))}),(e=>a.getYear(e))),r=ra(e,"month",void 0,(e=>{const t=null!=e?Number(e):a.getMonth(o.value),l=a.setYear(a.startOfMonth(a.date()),a.getYear(n.value))
return a.setMonth(l,t)}),(e=>a.getMonth(e))),i=t.computed((()=>{const t=a.toJsDate(a.startOfWeek(a.date(),e.firstDayOfWeek)).getDay()
return[0,1,2,3,4,5,6].map((e=>(e+t)%7))})),s=t.computed((()=>{const t=a.getWeekArray(r.value,e.firstDayOfWeek),l=t.flat()
if("static"===e.weeksInMonth&&l.length<42){const e=l[l.length-1]
let o=[]
for(let n=1;n<=42-l.length;n++)o.push(a.addDays(e,n)),n%7==0&&(t.push(o),o=[])}return t}))
function u(t,o){return t.filter((e=>i.value.includes(a.toJsDate(e).getDay()))).map(((t,n)=>{const i=a.toISO(t),s=!a.isSameMonth(t,r.value),u=a.isSameDay(t,a.startOfMonth(r.value)),c=a.isSameDay(t,a.endOfMonth(r.value)),d=a.isSameDay(t,r.value)
return{date:t,formatted:a.format(t,"keyboardDate"),isAdjacent:s,isDisabled:p(t),isEnd:c,isHidden:s&&!e.showAdjacentMonths,isSame:d,isSelected:l.value.some((e=>a.isSameDay(t,e))),isStart:u,isToday:a.isSameDay(t,o),isWeekEnd:n%7==6,isWeekStart:n%7==0,isoDate:i,localized:a.format(t,"dayOfMonth"),month:a.getMonth(t),year:a.getYear(t)}}))}const c=t.computed((()=>{const t=a.startOfWeek(o.value,e.firstDayOfWeek),l=[]
for(let e=0;e<=6;e++)l.push(a.addDays(t,e))
return u(l,a.date())})),d=t.computed((()=>u(s.value.flat(),a.date()))),v=t.computed((()=>s.value.map((t=>t.length?a.getWeek(t[0],e.firstDayOfWeek):null))))
function p(t){if(e.disabled)return!0
const l=a.date(t)
return!((!e.min||!a.isAfter(a.date(e.min),l))&&(!e.max||!a.isAfter(l,a.date(e.max)))&&(Array.isArray(e.allowedDates)&&e.allowedDates.length>0?e.allowedDates.some((e=>a.isSameDay(a.date(e),l))):"function"==typeof e.allowedDates?e.allowedDates(l):e.weekdays.includes(a.toJsDate(l).getDay())))}return{displayValue:o,daysInMonth:d,daysInWeek:c,genDays:u,model:l,weeksInMonth:s,weekDays:i,weekNumbers:v}}(e),u=Eu(),c=t.shallowRef(),d=t.shallowRef(),v=t.shallowRef(!1),p=t.toRef((()=>v.value?e.reverseTransition:e.transition))
"range"===e.multiple&&i.value.length>0&&(c.value=i.value[0],i.value.length>1&&(d.value=i.value[i.value.length-1]))
const m=t.computed((()=>{const t=["number","string"].includes(typeof e.multiple)?Number(e.multiple):1/0
return i.value.length>=t}))
function f(t){"range"===e.multiple?function(e){const t=u.startOfDay(e)
if(0===i.value.length?c.value=void 0:1===i.value.length&&(c.value=i.value[0],d.value=void 0),c.value)if(d.value)c.value=e,d.value=void 0,i.value=[c.value]
else{if(u.isSameDay(t,c.value))return c.value=void 0,void(i.value=[])
u.isBefore(t,c.value)?(d.value=u.endOfDay(c.value),c.value=t):d.value=u.endOfDay(t),i.value=u.createDateRange(c.value,d.value)}else c.value=t,i.value=[c.value]}(t):e.multiple?function(e){const t=i.value.findIndex((t=>u.isSameDay(t,e)))
if(-1===t)i.value=[...i.value,e]
else{const e=[...i.value]
e.splice(t,1),i.value=e}}(t):i.value=[t]}t.watch(r,((e,t)=>{t&&(v.value=u.isBefore(e[0].date,t[0].date))})),Ft((()=>t.createElementVNode("div",{class:"v-date-picker-month"},[e.showWeek&&t.createElementVNode("div",{key:"weeks",class:"v-date-picker-month__weeks"},[!e.hideWeekdays&&t.createElementVNode("div",{key:"hide-week-days",class:"v-date-picker-month__day"},[t.createTextVNode(" ")]),s.value.map((e=>t.createElementVNode("div",{class:t.normalizeClass(["v-date-picker-month__day","v-date-picker-month__day--adjacent"])},[e])))]),t.createVNode(gl,{name:p.value},{default:()=>[t.createElementVNode("div",{ref:n,key:r.value[0].date?.toString(),class:"v-date-picker-month__days"},[!e.hideWeekdays&&u.getWeekdays(e.firstDayOfWeek).map((e=>t.createElementVNode("div",{class:t.normalizeClass(["v-date-picker-month__day","v-date-picker-month__weekday"])},[e]))),r.value.map(((a,l)=>{const n={props:{class:"v-date-picker-month__day-btn",color:a.isSelected||a.isToday?e.color:void 0,disabled:a.isDisabled,icon:!0,ripple:!1,text:a.localized,variant:a.isSelected?"flat":a.isToday?"outlined":"text",onClick:()=>f(a.date)},item:a,i:l}
return m.value&&!a.isSelected&&(a.isDisabled=!0),t.createElementVNode("div",{class:t.normalizeClass(["v-date-picker-month__day",{"v-date-picker-month__day--adjacent":a.isAdjacent,"v-date-picker-month__day--hide-adjacent":a.isHidden,"v-date-picker-month__day--selected":a.isSelected,"v-date-picker-month__day--week-end":a.isWeekEnd,"v-date-picker-month__day--week-start":a.isWeekStart}]),"data-v-date":a.isDisabled?void 0:a.isoDate},[(e.showAdjacentMonths||!a.isAdjacent)&&(o.day?.(n)??t.createVNode($o,n.props,null))])}))])]})])))}}),Dd=yt({color:String,height:[String,Number],min:null,max:null,modelValue:Number,year:Number,allowedMonths:[Array,Function]},"VDatePickerMonths"),Fd=Et()({name:"VDatePickerMonths",props:Dd(),emits:{"update:modelValue":e=>!0},setup(e,a){let{emit:l,slots:o}=a
const n=Eu(),r=ra(e,"modelValue"),i=t.computed((()=>{let t=n.startOfYear(n.date())
return e.year&&(t=n.setYear(t,e.year)),m(12).map((a=>{const l=n.format(t,"monthShort"),o=!!(!function(t){if(Array.isArray(e.allowedMonths)&&e.allowedMonths.length)return e.allowedMonths.includes(t)
if("function"==typeof e.allowedMonths)return e.allowedMonths(t)
return!0}(a)||e.min&&n.isAfter(n.startOfMonth(n.date(e.min)),t)||e.max&&n.isAfter(t,n.startOfMonth(n.date(e.max))))
return t=n.getNextMonth(t),{isDisabled:o,text:l,value:a}}))}))
return t.watchEffect((()=>{r.value=r.value??n.getMonth(n.date())})),Ft((()=>t.createElementVNode("div",{class:"v-date-picker-months",style:{height:f(e.height)}},[t.createElementVNode("div",{class:"v-date-picker-months__content"},[i.value.map(((a,n)=>{const i={active:r.value===n,color:r.value===n?e.color:void 0,disabled:a.isDisabled,rounded:!0,text:a.text,variant:r.value===a.value?"flat":"text",onClick:()=>function(e){if(r.value===e)return void l("update:modelValue",r.value)
r.value=e}(n)}
return o.month?.({month:a,i:n,props:i})??t.createVNode($o,t.mergeProps({key:"month"},i),null)}))])]))),{}}}),zd=yt({color:String,height:[String,Number],min:null,max:null,modelValue:Number,allowedYears:[Array,Function]},"VDatePickerYears"),$d=Et()({name:"VDatePickerYears",props:zd(),emits:{"update:modelValue":e=>!0},setup(e,a){let{emit:l,slots:o}=a
const n=Eu(),r=ra(e,"modelValue"),i=t.computed((()=>{const t=n.getYear(n.date())
let a=t-100,l=t+52
e.min&&(a=n.getYear(n.date(e.min))),e.max&&(l=n.getYear(n.date(e.max)))
let o=n.startOfYear(n.date())
return o=n.setYear(o,a),m(l-a+1,a).map((e=>{const t=n.format(o,"year")
return o=n.setYear(o,n.getYear(o)+1),{text:t,value:e,isDisabled:!u(e)}}))}))
t.watchEffect((()=>{r.value=r.value??n.getYear(n.date())}))
const s=le()
function u(t){return Array.isArray(e.allowedYears)&&e.allowedYears.length?e.allowedYears.includes(t):"function"!=typeof e.allowedYears||e.allowedYears(t)}return t.onMounted((async()=>{await t.nextTick(),s.el?.scrollIntoView({block:"center"})})),Ft((()=>t.createElementVNode("div",{class:"v-date-picker-years",style:{height:f(e.height)}},[t.createElementVNode("div",{class:"v-date-picker-years__content"},[i.value.map(((a,n)=>{const i={ref:r.value===a.value?s:void 0,active:r.value===a.value,color:r.value===a.value?e.color:void 0,rounded:!0,text:a.text,disabled:a.isDisabled,variant:r.value===a.value?"flat":"text",onClick:()=>{r.value!==a.value?r.value=a.value:l("update:modelValue",r.value)}}
return o.year?.({year:a,i:n,props:i})??t.createVNode($o,t.mergeProps({key:"month"},i),null)}))])]))),{}}}),Md=yt({header:{type:String,default:"$vuetify.datePicker.header"},headerColor:String,...Id(),...Ad({weeksInMonth:"static"}),...C(Dd(),["modelValue"]),...C(zd(),["modelValue"]),...iu({title:"$vuetify.datePicker.title"}),modelValue:null},"VDatePicker"),Od=Et()({name:"VDatePicker",props:Md(),emits:{"update:modelValue":e=>!0,"update:month":e=>!0,"update:year":e=>!0,"update:viewMode":e=>!0},setup(e,a){let{emit:l,slots:o}=a
const n=Eu(),{t:r}=ga(),{rtlClasses:i}=ya(),s=ra(e,"modelValue",void 0,(e=>B(e).map((e=>n.date(e)))),(t=>e.multiple?t:t[0])),u=ra(e,"viewMode"),c=t.computed((()=>{const t=n.date(e.min)
return e.min&&n.isValid(t)?t:null})),d=t.computed((()=>{const t=n.date(e.max)
return e.max&&n.isValid(t)?t:null})),v=t.computed((()=>{const e=n.date()
let t=e
return s.value?.[0]?t=n.date(s.value[0]):c.value&&n.isBefore(e,c.value)?t=c.value:d.value&&n.isAfter(e,d.value)&&(t=d.value),t&&n.isValid(t)?t:e})),p=t.toRef((()=>e.headerColor??e.color)),m=t.ref(Number(e.month??n.getMonth(n.startOfMonth(v.value)))),f=t.ref(Number(e.year??n.getYear(n.startOfYear(n.setMonth(v.value,m.value))))),g=t.shallowRef(!1),h=t.computed((()=>e.multiple&&s.value.length>1?r("$vuetify.datePicker.itemsSelected",s.value.length):s.value[0]&&n.isValid(s.value[0])?n.format(n.date(s.value[0]),"normalDateWithWeekday"):r(e.header))),y=t.computed((()=>{let e=n.date()
return e=n.setDate(e,1),e=n.setMonth(e,m.value),e=n.setYear(e,f.value),n.format(e,"monthAndYear")})),b=t.toRef((()=>`date-picker-header${g.value?"-reverse":""}-transition`)),V=t.computed((()=>{if(e.disabled)return!0
const t=[]
if("month"!==u.value)t.push("prev","next")
else{let e=n.date()
if(e=n.startOfMonth(e),e=n.setMonth(e,m.value),e=n.setYear(e,f.value),c.value){const a=n.addDays(n.startOfMonth(e),-1)
n.isAfter(c.value,a)&&t.push("prev")}if(d.value){const a=n.addDays(n.endOfMonth(e),1)
n.isAfter(a,d.value)&&t.push("next")}}return t}))
function w(t,a){const l=e.allowedDates
if("function"!=typeof l)return!0
const o=n.getDiff(a,t,"days")
for(let e=0;e<o;e++)if(l(n.addDays(t,e)))return!0
return!1}function S(t){if("function"==typeof e.allowedDates){const e=n.parseISO(`${t}-01-01`)
return w(e,n.endOfYear(e))}if(Array.isArray(e.allowedDates)&&e.allowedDates.length){for(const a of e.allowedDates)if(n.getYear(n.date(a))===t)return!0
return!1}return!0}function k(t){if("function"==typeof e.allowedDates){const e=n.parseISO(`${f.value}-${t+1}-01`)
return w(e,n.endOfMonth(e))}if(Array.isArray(e.allowedDates)&&e.allowedDates.length){for(const a of e.allowedDates)if(n.getYear(n.date(a))===f.value&&n.getMonth(n.date(a))===t)return!0
return!1}return!0}function x(){m.value<11?m.value++:(f.value++,m.value=0,R(f.value)),P(m.value)}function N(){m.value>0?m.value--:(f.value--,m.value=11,R(f.value)),P(m.value)}function E(){u.value="month"}function I(){u.value="months"===u.value?"month":"months"}function _(){u.value="year"===u.value?"month":"year"}function P(e){"months"===u.value&&I(),l("update:month",e)}function R(e){"year"===u.value&&_(),l("update:year",e)}return t.watch(s,((e,t)=>{const a=B(t),l=B(e)
if(!l.length)return
const o=n.date(a[a.length-1]),r=n.date(l[l.length-1]),i=n.getMonth(r),s=n.getYear(r)
i!==m.value&&(m.value=i,P(m.value)),s!==f.value&&(f.value=s,R(f.value)),g.value=n.isBefore(o,r)})),Ft((()=>{const a=su.filterProps(e),l=_d.filterProps(e),n=Bd.filterProps(e),v=Td.filterProps(e),g=C(Fd.filterProps(e),["modelValue"]),w=C($d.filterProps(e),["modelValue"]),B={color:p.value,header:h.value,transition:b.value}
return t.createVNode(su,t.mergeProps(a,{color:p.value,class:["v-date-picker",`v-date-picker--${u.value}`,{"v-date-picker--show-week":e.showWeek},i.value,e.class],style:e.style}),{title:()=>o.title?.()??t.createElementVNode("div",{class:"v-date-picker__title"},[r(e.title)]),header:()=>o.header?t.createVNode(nl,{defaults:{VDatePickerHeader:{...B}}},{default:()=>[o.header?.(B)]}):t.createVNode(Bd,t.mergeProps({key:"header"},n,B,{onClick:"month"!==u.value?E:void 0}),{...o,default:void 0}),default:()=>t.createElementVNode(t.Fragment,null,[t.createVNode(_d,t.mergeProps(l,{disabled:V.value,text:y.value,"onClick:next":x,"onClick:prev":N,"onClick:month":I,"onClick:year":_}),null),t.createVNode(Ya,{hideOnLeave:!0},{default:()=>["months"===u.value?t.createVNode(Fd,t.mergeProps({key:"date-picker-months"},g,{modelValue:m.value,"onUpdate:modelValue":[e=>m.value=e,P],min:c.value,max:d.value,year:f.value,allowedMonths:k}),null):"year"===u.value?t.createVNode($d,t.mergeProps({key:"date-picker-years"},w,{modelValue:f.value,"onUpdate:modelValue":[e=>f.value=e,R],min:c.value,max:d.value,allowedYears:S}),null):t.createVNode(Td,t.mergeProps({key:"date-picker-month"},v,{modelValue:s.value,"onUpdate:modelValue":e=>s.value=e,month:m.value,"onUpdate:month":[e=>m.value=e,P],year:f.value,"onUpdate:year":[e=>f.value=e,R],min:c.value,max:d.value}),null)]})]),actions:o.actions})})),{}}}),Ld=yt({actionText:String,bgColor:String,color:String,icon:zt,image:String,justify:{type:String,default:"center"},headline:String,title:String,text:String,textWidth:{type:[Number,String],default:500},href:String,to:String,...bt(),...rl(),...Xl({size:void 0}),...Va()},"VEmptyState"),jd=Et()({name:"VEmptyState",props:Ld(),emits:{"click:action":e=>!0},setup(e,a){let{emit:l,slots:o}=a
const{themeClasses:n}=Ea(e),{backgroundColorClasses:r,backgroundColorStyles:i}=vl((()=>e.bgColor)),{dimensionStyles:s}=il(e),{displayClasses:u}=In()
function c(e){l("click:action",e)}return Ft((()=>{const a=!(!o.actions&&!e.actionText),l=!(!o.headline&&!e.headline),d=!(!o.title&&!e.title),v=!(!o.text&&!e.text),p=!!(o.media||e.image||e.icon),m=e.size||(e.image?200:96)
return t.createElementVNode("div",{class:t.normalizeClass(["v-empty-state",{[`v-empty-state--${e.justify}`]:!0},n.value,r.value,u.value,e.class]),style:t.normalizeStyle([i.value,s.value,e.style])},[p&&t.createElementVNode("div",{key:"media",class:"v-empty-state__media"},[o.media?t.createVNode(nl,{key:"media-defaults",defaults:{VImg:{src:e.image,height:m},VIcon:{size:m,icon:e.icon}}},{default:()=>[o.media()]}):t.createElementVNode(t.Fragment,null,[e.image?t.createVNode(Vl,{key:"image",src:e.image,height:m},null):e.icon?t.createVNode(Jl,{key:"icon",color:e.color,size:m,icon:e.icon},null):void 0])]),l&&t.createElementVNode("div",{key:"headline",class:"v-empty-state__headline"},[o.headline?.()??e.headline]),d&&t.createElementVNode("div",{key:"title",class:"v-empty-state__title"},[o.title?.()??e.title]),v&&t.createElementVNode("div",{key:"text",class:"v-empty-state__text",style:{maxWidth:f(e.textWidth)}},[o.text?.()??e.text]),o.default&&t.createElementVNode("div",{key:"content",class:"v-empty-state__content"},[o.default()]),a&&t.createElementVNode("div",{key:"actions",class:"v-empty-state__actions"},[t.createVNode(nl,{defaults:{VBtn:{class:"v-empty-state__action-btn",color:e.color??"surface-variant",href:e.href,text:e.actionText,to:e.to}}},{default:()=>[o.actions?.({props:{onClick:c}})??t.createVNode($o,{onClick:c},null)]})])])})),{}}}),Hd=Symbol.for("vuetify:v-expansion-panel"),Wd=yt({...bt(),...ti()},"VExpansionPanelText"),Ud=Et()({name:"VExpansionPanelText",props:Wd(),setup(e,a){let{slots:l}=a
const o=t.inject(Hd)
if(!o)throw new Error("[Vuetify] v-expansion-panel-text needs to be placed inside v-expansion-panel")
const{hasContent:n,onAfterLeave:r}=ai(e,o.isSelected)
return Ft((()=>t.createVNode(al,{onAfterLeave:r},{default:()=>[t.withDirectives(t.createElementVNode("div",{class:t.normalizeClass(["v-expansion-panel-text",e.class]),style:t.normalizeStyle(e.style)},[l.default&&n.value&&t.createElementVNode("div",{class:"v-expansion-panel-text__wrapper"},[l.default?.()])]),[[t.vShow,o.isSelected.value]])]}))),{}}}),Yd=yt({color:String,expandIcon:{type:zt,default:"$expand"},collapseIcon:{type:zt,default:"$collapse"},hideActions:Boolean,focusable:Boolean,static:Boolean,ripple:{type:[Boolean,Object],default:!1},readonly:Boolean,...bt(),...rl()},"VExpansionPanelTitle"),Gd=Et()({name:"VExpansionPanelTitle",directives:{vRipple:Fo},props:Yd(),setup(e,a){let{slots:l}=a
const o=t.inject(Hd)
if(!o)throw new Error("[Vuetify] v-expansion-panel-title needs to be placed inside v-expansion-panel")
const{backgroundColorClasses:n,backgroundColorStyles:r}=vl((()=>e.color)),{dimensionStyles:i}=il(e),s=t.computed((()=>({collapseIcon:e.collapseIcon,disabled:o.disabled.value,expanded:o.isSelected.value,expandIcon:e.expandIcon,readonly:e.readonly}))),u=t.toRef((()=>o.isSelected.value?e.collapseIcon:e.expandIcon))
return Ft((()=>t.withDirectives(t.createElementVNode("button",{class:t.normalizeClass(["v-expansion-panel-title",{"v-expansion-panel-title--active":o.isSelected.value,"v-expansion-panel-title--focusable":e.focusable,"v-expansion-panel-title--static":e.static},n.value,e.class]),style:t.normalizeStyle([r.value,i.value,e.style]),type:"button",tabindex:o.disabled.value?-1:void 0,disabled:o.disabled.value,"aria-expanded":o.isSelected.value,onClick:e.readonly?void 0:o.toggle},[t.createElementVNode("span",{class:"v-expansion-panel-title__overlay"},null),l.default?.(s.value),!e.hideActions&&t.createVNode(nl,{defaults:{VIcon:{icon:u.value}}},{default:()=>[t.createElementVNode("span",{class:"v-expansion-panel-title__icon"},[l.actions?.(s.value)??t.createVNode(Jl,null,null)])]})]),[[Fo,e.ripple]]))),{}}}),qd=yt({title:String,text:String,bgColor:String,...kl(),...jl(),...pl(),...Ba(),...Yd(),...Wd()},"VExpansionPanel"),Kd=Et()({name:"VExpansionPanel",props:qd(),emits:{"group:selected":e=>!0},setup(e,a){let{slots:l}=a
const o=Hl(e,Hd),{backgroundColorClasses:n,backgroundColorStyles:r}=vl((()=>e.bgColor)),{elevationClasses:i}=xl(e),{roundedClasses:s}=ml(e),u=t.toRef((()=>o?.disabled.value||e.disabled)),c=t.computed((()=>o.group.items.value.reduce(((e,t,a)=>(o.group.selected.value.includes(t.id)&&e.push(a),e)),[]))),d=t.computed((()=>{const e=o.group.items.value.findIndex((e=>e.id===o.id))
return!o.isSelected.value&&c.value.some((t=>t-e==1))})),v=t.computed((()=>{const e=o.group.items.value.findIndex((e=>e.id===o.id))
return!o.isSelected.value&&c.value.some((t=>t-e==-1))}))
return t.provide(Hd,o),Ft((()=>{const a=!(!l.text&&!e.text),c=!(!l.title&&!e.title),p=Gd.filterProps(e),m=Ud.filterProps(e)
return t.createVNode(e.tag,{class:t.normalizeClass(["v-expansion-panel",{"v-expansion-panel--active":o.isSelected.value,"v-expansion-panel--before-active":d.value,"v-expansion-panel--after-active":v.value,"v-expansion-panel--disabled":u.value},s.value,n.value,e.class]),style:t.normalizeStyle([r.value,e.style])},{default:()=>[t.createElementVNode("div",{class:t.normalizeClass(["v-expansion-panel__shadow",...i.value])},null),t.createVNode(nl,{defaults:{VExpansionPanelTitle:{...p},VExpansionPanelText:{...m}}},{default:()=>[c&&t.createVNode(Gd,{key:"title"},{default:()=>[l.title?l.title():e.title]}),a&&t.createVNode(Ud,{key:"text"},{default:()=>[l.text?l.text():e.text]}),l.default?.()]})]})})),{groupItem:o}}}),Xd=["default","accordion","inset","popout"],Zd=yt({flat:Boolean,...Ll(),...k(qd(),["bgColor","collapseIcon","color","eager","elevation","expandIcon","focusable","hideActions","readonly","ripple","rounded","tile","static"]),...Va(),...bt(),...Ba(),variant:{type:String,default:"default",validator:e=>Xd.includes(e)}},"VExpansionPanels"),Qd=Et()({name:"VExpansionPanels",props:Zd(),emits:{"update:modelValue":e=>!0},setup(e,a){let{slots:l}=a
const{next:o,prev:n}=Wl(e,Hd),{themeClasses:r}=Ea(e),i=t.toRef((()=>e.variant&&`v-expansion-panels--variant-${e.variant}`))
return xt({VExpansionPanel:{bgColor:t.toRef((()=>e.bgColor)),collapseIcon:t.toRef((()=>e.collapseIcon)),color:t.toRef((()=>e.color)),eager:t.toRef((()=>e.eager)),elevation:t.toRef((()=>e.elevation)),expandIcon:t.toRef((()=>e.expandIcon)),focusable:t.toRef((()=>e.focusable)),hideActions:t.toRef((()=>e.hideActions)),readonly:t.toRef((()=>e.readonly)),ripple:t.toRef((()=>e.ripple)),rounded:t.toRef((()=>e.rounded)),static:t.toRef((()=>e.static))}}),Ft((()=>t.createVNode(e.tag,{class:t.normalizeClass(["v-expansion-panels",{"v-expansion-panels--flat":e.flat,"v-expansion-panels--tile":e.tile},r.value,i.value,e.class]),style:t.normalizeStyle(e.style)},{default:()=>[l.default?.({prev:n,next:o})]}))),{next:o,prev:n}}}),Jd=yt({app:Boolean,appear:Boolean,extended:Boolean,layout:Boolean,offset:Boolean,modelValue:{type:Boolean,default:!0},...C(zo({active:!0}),["location"]),...ta(),...oo(),...fl({transition:"fab-transition"})},"VFab"),ev=Et()({name:"VFab",props:Jd(),emits:{"update:modelValue":e=>!0},setup(e,a){let{slots:l}=a
const o=ra(e,"modelValue"),n=t.shallowRef(56),r=t.ref(),{resizeRef:i}=Zt((e=>{e.length&&(n.value=e[0].target.clientHeight)})),s=t.toRef((()=>e.app||e.absolute)),u=t.computed((()=>!!s.value&&(e.location?.split(" ").shift()??"bottom"))),c=t.computed((()=>!!s.value&&(e.location?.split(" ")[1]??"end")))
na((()=>e.app),(()=>{const a=la({id:e.name,order:t.computed((()=>parseInt(e.order,10))),position:u,layoutSize:t.computed((()=>e.layout?n.value+24:0)),elementSize:t.computed((()=>n.value+24)),active:t.computed((()=>e.app&&o.value)),absolute:t.toRef((()=>e.absolute))})
t.watchEffect((()=>{r.value=a.layoutItemStyles.value}))}))
const d=t.ref()
return Ft((()=>{const a=$o.filterProps(e)
return t.createElementVNode("div",{ref:d,class:t.normalizeClass(["v-fab",{"v-fab--absolute":e.absolute,"v-fab--app":!!e.app,"v-fab--extended":e.extended,"v-fab--offset":e.offset,[`v-fab--${u.value}`]:s.value,[`v-fab--${c.value}`]:s.value},e.class]),style:t.normalizeStyle([e.app?{...r.value}:{height:e.absolute?"100%":"inherit"},e.style])},[t.createElementVNode("div",{class:"v-fab__container"},[t.createVNode(gl,{appear:e.appear,transition:e.transition},{default:()=>[t.withDirectives(t.createVNode($o,t.mergeProps({ref:i},a,{active:void 0,location:void 0}),l),[[t.vShow,e.active]])]})])])})),{}}}),tv=yt({chips:Boolean,counter:Boolean,counterSizeString:{type:String,default:"$vuetify.fileInput.counterSize"},counterString:{type:String,default:"$vuetify.fileInput.counter"},hideInput:Boolean,multiple:Boolean,showSize:{type:[Boolean,Number,String],default:!1,validator:e=>"boolean"==typeof e||[1e3,1024].includes(Number(e))},...gn({prependIcon:"$file"}),modelValue:{type:[Array,Object],default:e=>e.multiple?[]:null,validator:e=>B(e).every((e=>null!=e&&"object"==typeof e))},...xi({clearable:!0})},"VFileInput"),av=Et()({name:"VFileInput",inheritAttrs:!1,props:tv(),emits:{"click:control":e=>!0,"mousedown:control":e=>!0,"update:focused":e=>!0,"update:modelValue":e=>!0},setup(e,a){let{attrs:l,emit:o,slots:n}=a
const{t:r}=ga(),i=ra(e,"modelValue",e.modelValue,(e=>B(e)),(t=>!e.multiple&&Array.isArray(t)?t[0]:t)),{isFocused:s,focus:u,blur:c}=un(e),d=t.computed((()=>"boolean"!=typeof e.showSize?e.showSize:void 0)),v=t.computed((()=>(i.value??[]).reduce(((e,t)=>{let{size:a=0}=t
return e+a}),0))),p=t.computed((()=>F(v.value,d.value))),m=t.computed((()=>(i.value??[]).map((t=>{const{name:a="",size:l=0}=t
return e.showSize?`${a} (${F(l,d.value)})`:a})))),f=t.computed((()=>{const t=i.value?.length??0
return e.showSize?r(e.counterSizeString,t,p.value):r(e.counterString,t)})),g=t.ref(),h=t.ref(),y=t.ref(),b=t.toRef((()=>s.value||e.active)),V=t.computed((()=>["plain","underlined"].includes(e.variant))),w=t.shallowRef(!1)
function S(){y.value!==document.activeElement&&y.value?.focus(),s.value||u()}function k(e){y.value?.click()}function x(e){o("mousedown:control",e)}function C(e){y.value?.click(),o("click:control",e)}function N(a){a.stopPropagation(),S(),t.nextTick((()=>{i.value=[],K(e["onClick:clear"],a)}))}function E(e){e.preventDefault(),e.stopImmediatePropagation(),w.value=!0}function I(e){e.preventDefault(),w.value=!1}function _(e){if(e.preventDefault(),e.stopImmediatePropagation(),w.value=!1,!e.dataTransfer?.files?.length||!y.value)return
const t=new DataTransfer
for(const a of e.dataTransfer.files)t.items.add(a)
y.value.files=t.files,y.value.dispatchEvent(new Event("change",{bubbles:!0}))}return t.watch(i,(e=>{(!Array.isArray(e)||!e.length)&&y.value&&(y.value.value="")})),Ft((()=>{const a=!(!n.counter&&!e.counter),o=!(!a&&!n.details),[r,u]=P(l),{modelValue:d,...B}=hn.filterProps(e),R=Ci.filterProps(e)
return t.createVNode(hn,t.mergeProps({ref:g,modelValue:e.multiple?i.value:i.value[0],class:["v-file-input",{"v-file-input--chips":!!e.chips,"v-file-input--dragging":w.value,"v-file-input--hide":e.hideInput,"v-input--plain-underlined":V.value},e.class],style:e.style,"onClick:prepend":k},r,B,{centerAffix:!V.value,focused:s.value}),{...n,default:a=>{let{id:l,isDisabled:o,isDirty:r,isReadonly:d,isValid:f}=a
return t.createVNode(Ci,t.mergeProps({ref:h,"prepend-icon":e.prependIcon,onMousedown:x,onClick:C,"onClick:clear":N,"onClick:prependInner":e["onClick:prependInner"],"onClick:appendInner":e["onClick:appendInner"]},R,{id:l.value,active:b.value||r.value,dirty:r.value||e.dirty,disabled:o.value,focused:s.value,error:!1===f.value,onDragover:E,onDrop:_}),{...n,default:a=>{let{props:{class:l,...r}}=a
return t.createElementVNode(t.Fragment,null,[t.createElementVNode("input",t.mergeProps({ref:y,type:"file",readonly:d.value,disabled:o.value,multiple:e.multiple,name:e.name,onClick:e=>{e.stopPropagation(),d.value&&e.preventDefault(),S()},onChange:e=>{if(!e.target)return
const t=e.target
i.value=[...t.files??[]]},onDragleave:I,onFocus:S,onBlur:c},r,u),null),t.createElementVNode("div",{class:t.normalizeClass(l)},[!!i.value?.length&&!e.hideInput&&(n.selection?n.selection({fileNames:m.value,totalBytes:v.value,totalBytesReadable:p.value}):e.chips?m.value.map((e=>t.createVNode(Un,{key:e,size:"small",text:e},null))):m.value.join(", "))])])}})},details:o?l=>t.createElementVNode(t.Fragment,null,[n.details?.(l),a&&t.createElementVNode(t.Fragment,null,[t.createElementVNode("span",null,null),t.createVNode(Vi,{active:!!i.value?.length,value:f.value,disabled:e.disabled},n.counter)])]):void 0})})),gi({},g,h,y)}}),lv=yt({app:Boolean,color:String,height:{type:[Number,String],default:"auto"},...wl(),...bt(),...kl(),...ta(),...pl(),...Ba({tag:"footer"}),...Va()},"VFooter"),ov=Et()({name:"VFooter",props:lv(),setup(e,a){let{slots:l}=a
const o=t.ref(),{themeClasses:n}=Ea(e),{backgroundColorClasses:r,backgroundColorStyles:i}=vl((()=>e.color)),{borderClasses:s}=Sl(e),{elevationClasses:u}=xl(e),{roundedClasses:c}=ml(e),d=t.shallowRef(32),{resizeRef:v}=Zt((e=>{e.length&&(d.value=e[0].target.clientHeight)})),p=t.computed((()=>"auto"===e.height?d.value:parseInt(e.height,10)))
return na((()=>e.app),(()=>{const a=la({id:e.name,order:t.computed((()=>parseInt(e.order,10))),position:t.toRef((()=>"bottom")),layoutSize:p,elementSize:t.computed((()=>"auto"===e.height?void 0:p.value)),active:t.toRef((()=>e.app)),absolute:t.toRef((()=>e.absolute))})
t.watchEffect((()=>{o.value=a.layoutItemStyles.value}))})),Ft((()=>t.createVNode(e.tag,{ref:v,class:t.normalizeClass(["v-footer",n.value,r.value,s.value,u.value,c.value,e.class]),style:t.normalizeStyle([i.value,e.app?o.value:{height:f(e.height)},e.style])},l))),{}}}),nv=yt({...bt(),...dn()},"VForm"),rv=Et()({name:"VForm",props:nv(),emits:{"update:modelValue":e=>!0,submit:e=>!0},setup(e,a){let{slots:l,emit:o}=a
const n=function(e){const a=ra(e,"modelValue"),l=t.toRef((()=>e.disabled)),o=t.toRef((()=>e.readonly)),n=t.shallowRef(!1),r=t.ref([]),i=t.ref([])
return t.watch(r,(()=>{let e=0,t=0
const l=[]
for(const a of r.value)!1===a.isValid?(t++,l.push({id:a.id,errorMessages:a.errorMessages})):!0===a.isValid&&e++
i.value=l,a.value=!(t>0)&&(e===r.value.length||null)}),{deep:!0,flush:"post"}),t.provide(cn,{register:e=>{let{id:a,vm:l,validate:o,reset:n,resetValidation:i}=e
r.value.some((e=>e.id===a))&&Me(`Duplicate input name "${a}"`),r.value.push({id:a,validate:o,reset:n,resetValidation:i,vm:t.markRaw(l),isValid:null,errorMessages:[]})},unregister:e=>{r.value=r.value.filter((t=>t.id!==e))},update:(e,t,a)=>{const l=r.value.find((t=>t.id===e))
l&&(l.isValid=t,l.errorMessages=a)},isDisabled:l,isReadonly:o,isValidating:n,isValid:a,items:r,validateOn:t.toRef((()=>e.validateOn))}),{errors:i,isDisabled:l,isReadonly:o,isValidating:n,isValid:a,items:r,validate:async function(){const t=[]
let a=!0
i.value=[],n.value=!0
for(const l of r.value){const o=await l.validate()
if(o.length>0&&(a=!1,t.push({id:l.id,errorMessages:o})),!a&&e.fastFail)break}return i.value=t,n.value=!1,{valid:a,errors:i.value}},reset:function(){r.value.forEach((e=>e.reset()))},resetValidation:function(){r.value.forEach((e=>e.resetValidation()))}}}(e),r=t.ref()
function i(e){e.preventDefault(),n.reset()}function s(e){const t=e,a=n.validate()
t.then=a.then.bind(a),t.catch=a.catch.bind(a),t.finally=a.finally.bind(a),o("submit",t),t.defaultPrevented||a.then((e=>{let{valid:t}=e
t&&r.value?.submit()})),t.preventDefault()}return Ft((()=>t.createElementVNode("form",{ref:r,class:t.normalizeClass(["v-form",e.class]),style:t.normalizeStyle(e.style),novalidate:!0,onReset:i,onSubmit:s},[l.default?.(n)]))),gi(n,r)}}),iv=yt({disabled:Boolean,modelValue:{type:Boolean,default:null},...Kr()},"VHover"),sv=Et()({name:"VHover",props:iv(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:a}=t
const l=ra(e,"modelValue"),{runOpenDelay:o,runCloseDelay:n}=Xr(e,(t=>!e.disabled&&(l.value=t)))
return()=>a.default?.({isHovering:l.value,props:{onMouseenter:o,onMouseleave:n}})}}),uv=yt({color:String,direction:{type:String,default:"vertical",validator:e=>["vertical","horizontal"].includes(e)},side:{type:String,default:"end",validator:e=>["start","end","both"].includes(e)},mode:{type:String,default:"intersect",validator:e=>["intersect","manual"].includes(e)},margin:[Number,String],loadMoreText:{type:String,default:"$vuetify.infiniteScroll.loadMore"},emptyText:{type:String,default:"$vuetify.infiniteScroll.empty"},...rl(),...Ba()},"VInfiniteScroll"),cv=Nt({name:"VInfiniteScrollIntersect",props:{side:{type:String,required:!0},rootMargin:String},emits:{intersect:(e,t)=>!0},setup(e,a){let{emit:l}=a
const{intersectionRef:o,isIntersecting:n}=eo()
return t.watch(n,(async t=>{l("intersect",e.side,t)})),Ft((()=>t.createElementVNode("div",{class:"v-infinite-scroll-intersect",style:{"--v-infinite-margin-size":e.rootMargin},ref:o},[t.createTextVNode(" ")]))),{}}}),dv=Et()({name:"VInfiniteScroll",props:uv(),emits:{load:e=>!0},setup(e,a){let{slots:l,emit:o}=a
const n=t.ref(),r=t.shallowRef("ok"),i=t.shallowRef("ok"),s=t.computed((()=>f(e.margin))),u=t.shallowRef(!1)
function c(t){if(!n.value)return
const a="vertical"===e.direction?"scrollTop":"scrollLeft"
n.value[a]=t}function d(){if(!n.value)return 0
const t="vertical"===e.direction?"scrollHeight":"scrollWidth"
return n.value[t]}function v(e,t){"start"===e?r.value=t:"end"===e&&(i.value=t)}t.onMounted((()=>{n.value&&("start"===e.side?c(d()):"both"===e.side&&c(d()/2-function(){if(!n.value)return 0
const t="vertical"===e.direction?"clientHeight":"clientWidth"
return n.value[t]}()/2))}))
let p=0
function m(e,t){u.value=t,u.value&&g(e)}function g(a){if("manual"!==e.mode&&!u.value)return
const l=function(e){return"start"===e?r.value:i.value}(a)
n.value&&!["empty","loading"].includes(l)&&(p=d(),v(a,"loading"),o("load",{side:a,done:function(l){v(a,l),t.nextTick((()=>{"empty"!==l&&"error"!==l&&("ok"===l&&"start"===a&&c(d()-p+function(){if(!n.value)return 0
const t="vertical"===e.direction?"scrollTop":"scrollLeft"
return n.value[t]}()),"manual"!==e.mode&&t.nextTick((()=>{window.requestAnimationFrame((()=>{window.requestAnimationFrame((()=>{window.requestAnimationFrame((()=>{g(a)}))}))}))})))}))}}))}const{t:h}=ga()
function y(a,o){if(e.side!==a&&"both"!==e.side)return
const n=()=>g(a),r={side:a,props:{onClick:n,color:e.color}}
return"error"===o?l.error?.(r):"empty"===o?l.empty?.(r)??t.createElementVNode("div",null,[h(e.emptyText)]):"manual"===e.mode?"loading"===o?l.loading?.(r)??t.createVNode(ao,{indeterminate:!0,color:e.color},null):l["load-more"]?.(r)??t.createVNode($o,{variant:"outlined",color:e.color,onClick:n},{default:()=>[h(e.loadMoreText)]}):l.loading?.(r)??t.createVNode(ao,{indeterminate:!0,color:e.color},null)}const{dimensionStyles:b}=il(e)
Ft((()=>{const a=e.tag,o="start"===e.side||"both"===e.side,u="end"===e.side||"both"===e.side,c="intersect"===e.mode
return t.createVNode(a,{ref:n,class:t.normalizeClass(["v-infinite-scroll",`v-infinite-scroll--${e.direction}`,{"v-infinite-scroll--start":o,"v-infinite-scroll--end":u}]),style:t.normalizeStyle(b.value)},{default:()=>[t.createElementVNode("div",{class:"v-infinite-scroll__side"},[y("start",r.value)]),o&&c&&t.createVNode(cv,{key:"start",side:"start",onIntersect:m,rootMargin:s.value},null),l.default?.(),u&&c&&t.createVNode(cv,{key:"end",side:"end",onIntersect:m,rootMargin:s.value},null),t.createElementVNode("div",{class:"v-infinite-scroll__side"},[y("end",i.value)])]})}))}}),vv=Symbol.for("vuetify:v-item-group"),pv=yt({...bt(),...Ll({selectedClass:"v-item--selected"}),...Ba(),...Va()},"VItemGroup"),mv=Et()({name:"VItemGroup",props:pv(),emits:{"update:modelValue":e=>!0},setup(e,a){let{slots:l}=a
const{themeClasses:o}=Ea(e),{isSelected:n,select:r,next:i,prev:s,selected:u}=Wl(e,vv)
return()=>t.createVNode(e.tag,{class:t.normalizeClass(["v-item-group",o.value,e.class]),style:t.normalizeStyle(e.style)},{default:()=>[l.default?.({isSelected:n,select:r,next:i,prev:s,selected:u.value})]})}}),fv=Et()({name:"VItem",props:jl(),emits:{"group:selected":e=>!0},setup(e,t){let{slots:a}=t
const{isSelected:l,select:o,toggle:n,selectedClass:r,value:i,disabled:s}=Hl(e,vv)
return()=>a.default?.({isSelected:l.value,selectedClass:r.value,select:o,toggle:n,value:i.value,disabled:s.value})}}),gv=It("v-kbd","kbd"),hv=yt({...bt(),...rl(),...ea()},"VLayout"),yv=Et()({name:"VLayout",props:hv(),setup(e,a){let{slots:l}=a
const{layoutClasses:o,layoutStyles:n,getLayoutItem:r,items:i,layoutRef:s}=oa(e),{dimensionStyles:u}=il(e)
return Ft((()=>t.createElementVNode("div",{ref:s,class:t.normalizeClass([o.value,e.class]),style:t.normalizeStyle([u.value,n.value,e.style])},[l.default?.()]))),{getLayoutItem:r,items:i}}}),bv=yt({position:{type:String,required:!0},size:{type:[Number,String],default:300},modelValue:Boolean,...bt(),...ta()},"VLayoutItem"),Vv=Et()({name:"VLayoutItem",props:bv(),setup(e,a){let{slots:l}=a
const{layoutItemStyles:o}=la({id:e.name,order:t.computed((()=>parseInt(e.order,10))),position:t.toRef((()=>e.position)),elementSize:t.toRef((()=>e.size)),layoutSize:t.toRef((()=>e.size)),active:t.toRef((()=>e.modelValue)),absolute:t.toRef((()=>e.absolute))})
return()=>t.createElementVNode("div",{class:t.normalizeClass(["v-layout-item",e.class]),style:t.normalizeStyle([o.value,e.style])},[l.default?.()])}}),wv=yt({modelValue:Boolean,options:{type:Object,default:()=>({root:void 0,rootMargin:void 0,threshold:void 0})},...bt(),...rl(),...Ba(),...fl({transition:"fade-transition"})},"VLazy"),Sv=Et()({name:"VLazy",directives:{vIntersect:yl},props:wv(),emits:{"update:modelValue":e=>!0},setup(e,a){let{slots:l}=a
const{dimensionStyles:o}=il(e),n=ra(e,"modelValue")
function r(e){n.value||(n.value=e)}return Ft((()=>t.withDirectives(t.createVNode(e.tag,{class:t.normalizeClass(["v-lazy",e.class]),style:t.normalizeStyle([o.value,e.style])},{default:()=>[n.value&&t.createVNode(gl,{transition:e.transition,appear:!0},{default:()=>[l.default?.()]})]}),[[yl,{handler:r,options:e.options},null]]))),{}}}),kv=yt({locale:String,fallbackLocale:String,messages:Object,rtl:{type:Boolean,default:void 0},...bt()},"VLocaleProvider"),xv=Et()({name:"VLocaleProvider",props:kv(),setup(e,a){let{slots:l}=a
const{rtlClasses:o}=ha(e)
return Ft((()=>t.createElementVNode("div",{class:t.normalizeClass(["v-locale-provider",o.value,e.class]),style:t.normalizeStyle(e.style)},[l.default?.()]))),{}}}),Cv=yt({scrollable:Boolean,...bt(),...rl(),...Ba({tag:"main"})},"VMain"),Nv=Et()({name:"VMain",props:Cv(),setup(e,a){let{slots:l}=a
const{dimensionStyles:o}=il(e),{mainStyles:n}=aa(),{ssrBootStyles:r}=_l()
return Ft((()=>t.createVNode(e.tag,{class:t.normalizeClass(["v-main",{"v-main--scrollable":e.scrollable},e.class]),style:t.normalizeStyle([n.value,r.value,o.value,e.style])},{default:()=>[e.scrollable?t.createElementVNode("div",{class:"v-main__scroller"},[l.default?.()]):l.default?.()]}))),{}}})
const Ev=100,Iv=20
function _v(e){return(e<0?-1:1)*Math.sqrt(Math.abs(e))*1.41421356237}function Pv(e){if(e.length<2)return 0
if(2===e.length)return e[1].t===e[0].t?0:(e[1].d-e[0].d)/(e[1].t-e[0].t)
let t=0
for(let a=e.length-1;a>0;a--){if(e[a].t===e[a-1].t)continue
const l=_v(t),o=(e[a].d-e[a-1].d)/(e[a].t-e[a-1].t)
t+=(o-l)*Math.abs(o),a===e.length-1&&(t*=.5)}return 1e3*_v(t)}function Bv(){const e={}
return{addMovement:function(t){Array.from(t.changedTouches).forEach((a=>{(e[a.identifier]??(e[a.identifier]=new H(Iv))).push([t.timeStamp,a])}))},endTouch:function(t){Array.from(t.changedTouches).forEach((t=>{delete e[t.identifier]}))},getVelocity:function(t){const a=e[t]?.values().reverse()
if(!a)throw new Error(`No samples for touch id ${t}`)
const l=a[0],o=[],n=[]
for(const e of a){if(l[0]-e[0]>Ev)break
o.push({t:e[0],d:e[1].clientX}),n.push({t:e[0],d:e[1].clientY})}return{x:Pv(o),y:Pv(n),get direction(){const{x:e,y:t}=this,[a,l]=[Math.abs(e),Math.abs(t)]
return a>l&&e>=0?"right":a>l&&e<=0?"left":l>a&&t>=0?"down":l>a&&t<=0?"up":function(){throw new Error}()}}}}}function Rv(){throw new Error}const Av=["start","end","left","right","top","bottom"],Tv=yt({color:String,disableResizeWatcher:Boolean,disableRouteWatcher:Boolean,expandOnHover:Boolean,floating:Boolean,modelValue:{type:Boolean,default:null},permanent:Boolean,rail:{type:Boolean,default:null},railWidth:{type:[Number,String],default:56},scrim:{type:[Boolean,String],default:!0},image:String,temporary:Boolean,persistent:Boolean,touchless:Boolean,width:{type:[Number,String],default:256},location:{type:String,default:"start",validator:e=>Av.includes(e)},sticky:Boolean,...wl(),...bt(),...Kr(),...En({mobile:null}),...kl(),...ta(),...pl(),...Ba({tag:"nav"}),...Va()},"VNavigationDrawer"),Dv=Et()({name:"VNavigationDrawer",props:Tv(),emits:{"update:modelValue":e=>!0,"update:rail":e=>!0},setup(e,a){let{attrs:l,emit:o,slots:n}=a
const{isRtl:r}=ya(),{themeClasses:i}=Ea(e),{borderClasses:s}=Sl(e),{backgroundColorClasses:u,backgroundColorStyles:c}=vl((()=>e.color)),{elevationClasses:d}=xl(e),{displayClasses:v,mobile:p}=In(e),{roundedClasses:m}=ml(e),g=fo(),h=ra(e,"modelValue",null,(e=>!!e)),{ssrBootStyles:y}=_l(),{scopeId:b}=li(),V=t.ref(),w=t.shallowRef(!1),{runOpenDelay:S,runCloseDelay:k}=Xr(e,(e=>{w.value=e})),x=t.computed((()=>e.rail&&e.expandOnHover&&w.value?Number(e.width):Number(e.rail?e.railWidth:e.width))),C=t.computed((()=>ce(e.location,r.value))),N=t.toRef((()=>e.persistent)),E=t.computed((()=>!e.permanent&&(p.value||e.temporary))),I=t.computed((()=>e.sticky&&!E.value&&"bottom"!==C.value))
na((()=>e.expandOnHover&&null!=e.rail),(()=>{t.watch(w,(e=>o("update:rail",!e)))})),na((()=>!e.disableResizeWatcher),(()=>{t.watch(E,(a=>!e.permanent&&t.nextTick((()=>h.value=!a))))})),na((()=>!e.disableRouteWatcher&&!!g),(()=>{t.watch(g.currentRoute,(()=>E.value&&(h.value=!1)))})),t.watch((()=>e.permanent),(e=>{e&&(h.value=!0)})),null!=e.modelValue||E.value||(h.value=e.permanent||!p.value)
const{isDragging:_,dragProgress:P}=function(e){let{el:a,isActive:l,isTemporary:o,width:n,touchless:r,position:i}=e
t.onMounted((()=>{window.addEventListener("touchstart",b,{passive:!0}),window.addEventListener("touchmove",V,{passive:!1}),window.addEventListener("touchend",w,{passive:!0})})),t.onBeforeUnmount((()=>{window.removeEventListener("touchstart",b),window.removeEventListener("touchmove",V),window.removeEventListener("touchend",w)}))
const s=t.computed((()=>["left","right"].includes(i.value))),{addMovement:u,endTouch:c,getVelocity:d}=Bv()
let v=!1
const p=t.shallowRef(!1),m=t.shallowRef(0),f=t.shallowRef(0)
let g
function h(e,t){return("left"===i.value?e:"right"===i.value?document.documentElement.clientWidth-e:"top"===i.value?e:"bottom"===i.value?document.documentElement.clientHeight-e:Rv())-(t?n.value:0)}function y(e){let t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1]
const a="left"===i.value?(e-f.value)/n.value:"right"===i.value?(document.documentElement.clientWidth-e-f.value)/n.value:"top"===i.value?(e-f.value)/n.value:"bottom"===i.value?(document.documentElement.clientHeight-e-f.value)/n.value:Rv()
return t?R(a):a}function b(e){if(r.value)return
const t=e.changedTouches[0].clientX,a=e.changedTouches[0].clientY,d="left"===i.value?t<25:"right"===i.value?t>document.documentElement.clientWidth-25:"top"===i.value?a<25:"bottom"===i.value?a>document.documentElement.clientHeight-25:Rv(),p=l.value&&("left"===i.value?t<n.value:"right"===i.value?t>document.documentElement.clientWidth-n.value:"top"===i.value?a<n.value:"bottom"===i.value?a>document.documentElement.clientHeight-n.value:Rv());(d||p||l.value&&o.value)&&(g=[t,a],f.value=h(s.value?t:a,l.value),m.value=y(s.value?t:a),v=f.value>-20&&f.value<80,c(e),u(e))}function V(e){const t=e.changedTouches[0].clientX,a=e.changedTouches[0].clientY
if(v){if(!e.cancelable)return void(v=!1)
const l=Math.abs(t-g[0]),o=Math.abs(a-g[1]);(s.value?l>o&&l>3:o>l&&o>3)?(p.value=!0,v=!1):(s.value?o:l)>3&&(v=!1)}if(!p.value)return
e.preventDefault(),u(e)
const l=y(s.value?t:a,!1)
m.value=Math.max(0,Math.min(1,l)),l>1?f.value=h(s.value?t:a,!0):l<0&&(f.value=h(s.value?t:a,!1))}function w(e){if(v=!1,!p.value)return
u(e),p.value=!1
const t=d(e.changedTouches[0].identifier),a=Math.abs(t.x),o=Math.abs(t.y),n=s.value?a>o&&a>400:o>a&&o>3
l.value=n?t.direction===({left:"right",right:"left",top:"down",bottom:"up"}[i.value]||Rv()):m.value>.5}const S=t.computed((()=>p.value?{transform:"left"===i.value?`translateX(calc(-100% + ${m.value*n.value}px))`:"right"===i.value?`translateX(calc(100% - ${m.value*n.value}px))`:"top"===i.value?`translateY(calc(-100% + ${m.value*n.value}px))`:"bottom"===i.value?`translateY(calc(100% - ${m.value*n.value}px))`:Rv(),transition:"none"}:void 0))
return na(p,(()=>{const e=a.value?.style.transform??null,l=a.value?.style.transition??null
t.watchEffect((()=>{a.value?.style.setProperty("transform",S.value?.transform||"none"),a.value?.style.setProperty("transition",S.value?.transition||null)})),t.onScopeDispose((()=>{a.value?.style.setProperty("transform",e),a.value?.style.setProperty("transition",l)}))})),{isDragging:p,dragProgress:m,dragStyles:S}}({el:V,isActive:h,isTemporary:E,width:x,touchless:t.toRef((()=>e.touchless)),position:C}),B=t.computed((()=>{const t=E.value?0:e.rail&&e.expandOnHover?Number(e.railWidth):x.value
return _.value?t*P.value:t})),{layoutItemStyles:A,layoutItemScrimStyles:T}=la({id:e.name,order:t.computed((()=>parseInt(e.order,10))),position:C,layoutSize:B,elementSize:x,active:t.readonly(h),disableTransitions:t.toRef((()=>_.value)),absolute:t.computed((()=>e.absolute||I.value&&"string"!=typeof D.value))}),{isStuck:D,stickyStyles:F}=function(e){let{rootEl:a,isSticky:l,layoutItemStyles:o}=e
const n=t.shallowRef(!1),r=t.shallowRef(0),i=t.computed((()=>{const e="boolean"==typeof n.value?"top":n.value
return[l.value?{top:"auto",bottom:"auto",height:void 0}:void 0,n.value?{[e]:f(r.value)}:{top:o.value.top}]}))
t.onMounted((()=>{t.watch(l,(e=>{e?window.addEventListener("scroll",u,{passive:!0}):window.removeEventListener("scroll",u)}),{immediate:!0})})),t.onBeforeUnmount((()=>{window.removeEventListener("scroll",u)}))
let s=0
function u(){const e=s>window.scrollY?"up":"down",t=a.value.getBoundingClientRect(),l=parseFloat(o.value.top??0),i=window.scrollY-Math.max(0,r.value-l),u=t.height+Math.max(r.value,l)-window.scrollY-window.innerHeight,c=parseFloat(getComputedStyle(a.value).getPropertyValue("--v-body-scroll-y"))||0
t.height<window.innerHeight-l?(n.value="top",r.value=l):"up"===e&&"bottom"===n.value||"down"===e&&"top"===n.value?(r.value=window.scrollY+t.top-c,n.value=!0):"down"===e&&u<=0?(r.value=0,n.value="bottom"):"up"===e&&i<=0&&(c?"top"!==n.value&&(r.value=-i+c+l,n.value="top"):(r.value=t.top+i,n.value="top")),s=window.scrollY}return{isStuck:n,stickyStyles:i}}({rootEl:V,isSticky:I,layoutItemStyles:A}),z=vl((()=>"string"==typeof e.scrim?e.scrim:null)),$=t.computed((()=>({..._.value?{opacity:.2*P.value,transition:"none"}:void 0,...T.value})))
return xt({VList:{bgColor:"transparent"}}),Ft((()=>{const a=n.image||e.image
return t.createElementVNode(t.Fragment,null,[t.createVNode(e.tag,t.mergeProps({ref:V,onMouseenter:S,onMouseleave:k,class:["v-navigation-drawer",`v-navigation-drawer--${C.value}`,{"v-navigation-drawer--expand-on-hover":e.expandOnHover,"v-navigation-drawer--floating":e.floating,"v-navigation-drawer--is-hovering":w.value,"v-navigation-drawer--rail":e.rail,"v-navigation-drawer--temporary":E.value,"v-navigation-drawer--persistent":N.value,"v-navigation-drawer--active":h.value,"v-navigation-drawer--sticky":I.value},i.value,u.value,s.value,v.value,d.value,m.value,e.class],style:[c.value,A.value,y.value,F.value,e.style]},b,l),{default:()=>[a&&t.createElementVNode("div",{key:"image",class:"v-navigation-drawer__img"},[n.image?t.createVNode(nl,{key:"image-defaults",disabled:!e.image,defaults:{VImg:{alt:"",cover:!0,height:"inherit",src:e.image}}},n.image):t.createVNode(Vl,{key:"image-img",alt:"",cover:!0,height:"inherit",src:e.image},null)]),n.prepend&&t.createElementVNode("div",{class:"v-navigation-drawer__prepend"},[n.prepend?.()]),t.createElementVNode("div",{class:"v-navigation-drawer__content"},[n.default?.()]),n.append&&t.createElementVNode("div",{class:"v-navigation-drawer__append"},[n.append?.()])]}),t.createVNode(t.Transition,{name:"fade-transition"},{default:()=>[E.value&&(_.value||h.value)&&!!e.scrim&&t.createElementVNode("div",t.mergeProps({class:["v-navigation-drawer__scrim",z.backgroundColorClasses.value],style:[$.value,z.backgroundColorStyles.value],onClick:()=>{N.value||(h.value=!1)}},b),null)]})])})),{isStuck:D}}}),Fv=Nt({name:"VNoSsr",setup(e,t){let{slots:a}=t
const l=ei()
return()=>l.value&&a.default?.()}})
const zv=yt({controlVariant:{type:String,default:"default"},inset:Boolean,hideInput:Boolean,modelValue:{type:Number,default:null},min:{type:Number,default:Number.MIN_SAFE_INTEGER},max:{type:Number,default:Number.MAX_SAFE_INTEGER},step:{type:Number,default:1},precision:{type:Number,default:0},...C(Ei(),["modelValue","validationValue"])},"VNumberInput"),$v=Et()({name:"VNumberInput",props:{...zv()},emits:{"update:modelValue":e=>!0},setup(e,a){let{slots:l}=a
const o=t.ref(),{holdStart:n,holdStop:r}=function(e){let{toggleUpDown:a}=e,l=-1,o=-1
function n(){window.clearTimeout(l),window.clearInterval(o)}function r(e){a("up"===e)}return t.onScopeDispose(n),{holdStart:function(e){n(),r(e),l=window.setTimeout((()=>{o=window.setInterval((()=>r(e)),50)}),500)},holdStop:n}}({toggleUpDown:N}),i=vn(e),s=t.computed((()=>i.isDisabled.value||i.isReadonly.value)),{isFocused:u,focus:c,blur:d}=un(e)
function v(t){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e.precision
const l=null==a?String(t):t.toFixed(a)
return u.value?Number(l).toString():l}const p=ra(e,"modelValue",null,(e=>e??null),(t=>null==t?t??null:R(Number(t),e.min,e.max))),m=t.shallowRef(null)
t.watchEffect((()=>{u.value&&!s.value||(null==p.value?m.value=null:isNaN(p.value)||(m.value=v(p.value)))}))
const f=t.computed({get:()=>m.value,set(t){null===t||""===t?(p.value=null,m.value=null):!isNaN(Number(t))&&Number(t)<=e.max&&Number(t)>=e.min&&(p.value=Number(t),m.value=t)}}),g=t.computed((()=>!s.value&&(p.value??0)+e.step<=e.max)),h=t.computed((()=>!s.value&&(p.value??0)-e.step>=e.min)),y=t.computed((()=>e.hideInput?"stacked":e.controlVariant)),b=t.toRef((()=>"split"===y.value?"$plus":"$collapse")),V=t.toRef((()=>"split"===y.value?"$minus":"$expand")),w=t.toRef((()=>"split"===y.value?"default":"small")),S=t.toRef((()=>"stacked"===y.value?"auto":"100%")),k={props:{onClick:_,onPointerup:P,onPointerdown:B,onPointercancel:T}},x={props:{onClick:_,onPointerup:P,onPointerdown:A,onPointercancel:T}}
function C(e){if(null==e)return 0
const t=e.toString(),a=t.indexOf(".")
return~a?t.length-a:0}function N(){let t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0]
if(s.value)return
if(null==p.value)return void(f.value=v(R(0,e.min,e.max)))
let a=Math.max(C(p.value),C(e.step))
null!=e.precision&&(a=Math.max(a,e.precision)),t?g.value&&(f.value=v(p.value+e.step,a)):h.value&&(f.value=v(p.value-e.step,a))}function E(t){if(!t.data)return
const a=t.target,{value:l,selectionStart:o,selectionEnd:n}=a??{},r=l?l.slice(0,o)+t.data+l.slice(n):t.data,i=function(e,t){const a=e.split("").filter((e=>/[\d\-.]/.test(e))).filter(((e,t,a)=>0===t&&/[-]/.test(e)||"."===e&&t===a.indexOf(".")||/\d/.test(e))).join("")
if(0===t)return a.split(".")[0]
if(null!==t&&/\.\d/.test(a)){const e=a.split(".")
return[e[0],e[1].substring(0,t)].join(".")}return a}(r,e.precision);/^-?(\d+(\.\d*)?|(\.\d+)|\d*|\.)$/.test(r)||(t.preventDefault(),a.value=i),null!=e.precision&&(r.split(".")[1]?.length>e.precision&&(t.preventDefault(),a.value=i),0===e.precision&&r.includes(".")&&(t.preventDefault(),a.value=i))}async function I(e){["Enter","ArrowLeft","ArrowRight","Backspace","Delete","Tab"].includes(e.key)||e.ctrlKey||["ArrowDown","ArrowUp"].includes(e.key)&&(e.preventDefault(),D(),await t.nextTick(),"ArrowDown"===e.key?N(!1):N())}function _(e){e.stopPropagation()}function P(e){const t=e.currentTarget
t?.releasePointerCapture(e.pointerId),e.preventDefault(),e.stopPropagation(),r()}function B(e){const t=e.currentTarget
t?.setPointerCapture(e.pointerId),e.preventDefault(),e.stopPropagation(),n("up")}function A(e){const t=e.currentTarget
t?.setPointerCapture(e.pointerId),e.preventDefault(),e.stopPropagation(),n("down")}function T(e){const t=e.currentTarget
t?.releasePointerCapture(e.pointerId),r()}function D(){if(s.value)return
if(!o.value)return
const t=o.value.value
t&&!isNaN(Number(t))?f.value=v(R(Number(t),e.min,e.max)):f.value=null}function F(){c(),s.value||(null===p.value||isNaN(p.value)?f.value=null:f.value=p.value.toString())}function z(){d(),D()}return t.watch((()=>e.precision),(()=>function(){if(s.value)return
if(null===p.value||isNaN(p.value))return void(f.value=null)
f.value=null==e.precision?String(p.value):p.value.toFixed(e.precision)}())),t.onMounted((()=>{D()})),Ft((()=>{const{modelValue:a,...n}=Ii.filterProps(e)
function r(){return l.increment?t.createVNode(nl,{key:"increment-defaults",defaults:{VBtn:{disabled:!g.value,flat:!0,height:S.value,size:w.value,icon:b.value}}},{default:()=>[l.increment(k)]}):t.createVNode($o,{"aria-hidden":"true","data-testid":"increment",disabled:!g.value,flat:!0,height:S.value,icon:b.value,key:"increment-btn",onClick:_,onPointerdown:B,onPointerup:P,onPointercancel:T,size:w.value,tabindex:"-1"},null)}function i(){return l.decrement?t.createVNode(nl,{key:"decrement-defaults",defaults:{VBtn:{disabled:!h.value,flat:!0,height:S.value,size:w.value,icon:V.value}}},{default:()=>[l.decrement(x)]}):t.createVNode($o,{"aria-hidden":"true","data-testid":"decrement",disabled:!h.value,flat:!0,height:S.value,icon:V.value,key:"decrement-btn",onClick:_,onPointerdown:A,onPointerup:P,onPointercancel:T,size:w.value,tabindex:"-1"},null)}function s(){return t.createElementVNode("div",{class:"v-number-input__control"},[i(),t.createVNode(br,{vertical:"stacked"!==y.value},null),r()])}function u(){return e.hideInput||e.inset?void 0:t.createVNode(br,{vertical:!0},null)}const c="split"===y.value?t.createElementVNode("div",{class:"v-number-input__control"},[t.createVNode(br,{vertical:!0},null),r()]):e.reverse||"hidden"===y.value?void 0:t.createElementVNode(t.Fragment,null,[u(),s()]),d=l["append-inner"]||c,v="split"===y.value?t.createElementVNode("div",{class:"v-number-input__control"},[i(),t.createVNode(br,{vertical:!0},null)]):e.reverse&&"hidden"!==y.value?t.createElementVNode(t.Fragment,null,[s(),u()]):void 0,m=l["prepend-inner"]||v
return t.createVNode(Ii,t.mergeProps({ref:o,modelValue:f.value,"onUpdate:modelValue":e=>f.value=e,validationValue:p.value,onBeforeinput:E,onFocus:F,onBlur:z,onKeydown:I,class:["v-number-input",{"v-number-input--default":"default"===y.value,"v-number-input--hide-input":e.hideInput,"v-number-input--inset":e.inset,"v-number-input--reverse":e.reverse,"v-number-input--split":"split"===y.value,"v-number-input--stacked":"stacked"===y.value},e.class]},n,{style:e.style,inputmode:"decimal"}),{...l,"append-inner":d?function(){for(var e=arguments.length,a=new Array(e),o=0;o<e;o++)a[o]=arguments[o]
return t.createElementVNode(t.Fragment,null,[l["append-inner"]?.(...a),c])}:void 0,"prepend-inner":m?function(){for(var e=arguments.length,a=new Array(e),o=0;o<e;o++)a[o]=arguments[o]
return t.createElementVNode(t.Fragment,null,[v,l["prepend-inner"]?.(...a)])}:void 0})})),gi({},o)}}),Mv=yt({autofocus:Boolean,divider:String,focusAll:Boolean,label:{type:String,default:"$vuetify.input.otp"},length:{type:[Number,String],default:6},modelValue:{type:[Number,String],default:void 0},placeholder:String,type:{type:String,default:"number"},...rl(),...sn(),...k(xi({variant:"outlined"}),["baseColor","bgColor","class","color","disabled","error","loading","rounded","style","theme","variant"])},"VOtpInput"),Ov=Et()({name:"VOtpInput",props:Mv(),emits:{finish:e=>!0,"update:focused":e=>!0,"update:modelValue":e=>!0},setup(e,a){let{attrs:l,emit:o,slots:n}=a
const{dimensionStyles:r}=il(e),{isFocused:i,focus:s,blur:u}=un(e),c=ra(e,"modelValue","",(e=>null==e?[]:String(e).split("")),(e=>e.join(""))),{t:d}=ga(),v=t.computed((()=>Number(e.length))),p=t.computed((()=>Array(v.value).fill(0))),m=t.ref(-1),f=t.ref(),g=t.ref([]),h=t.computed((()=>g.value[m.value]))
function y(){if(w(h.value.value))return void(h.value.value="")
const e=c.value.slice(),t=h.value.value
e[m.value]=t
let a=null
m.value>c.value.length?a=c.value.length+1:m.value+1!==v.value&&(a="next"),c.value=e,a&&Q(f.value,a)}function b(e){const t=c.value.slice(),a=m.value
let l=null;["ArrowLeft","ArrowRight","Backspace","Delete"].includes(e.key)&&(e.preventDefault(),"ArrowLeft"===e.key?l="prev":"ArrowRight"===e.key?l="next":["Backspace","Delete"].includes(e.key)&&(t[m.value]="",c.value=t,m.value>0&&"Backspace"===e.key?l="prev":requestAnimationFrame((()=>{g.value[a]?.select()}))),requestAnimationFrame((()=>{null!=l&&Q(f.value,l)})))}function V(){u(),m.value=-1}function w(t){return"number"===e.type&&/[^0-9]/g.test(t)}return xt({VField:{color:t.toRef((()=>e.color)),bgColor:t.toRef((()=>e.color)),baseColor:t.toRef((()=>e.baseColor)),disabled:t.toRef((()=>e.disabled)),error:t.toRef((()=>e.error)),variant:t.toRef((()=>e.variant))}},{scoped:!0}),t.watch(c,(e=>{e.length===v.value&&o("finish",e.join(""))}),{deep:!0}),t.watch(m,(e=>{e<0||t.nextTick((()=>{g.value[e]?.select()}))})),Ft((()=>{const[a,o]=P(l)
return t.createElementVNode("div",t.mergeProps({class:["v-otp-input",{"v-otp-input--divided":!!e.divider},e.class],style:[e.style]},a),[t.createElementVNode("div",{ref:f,class:"v-otp-input__content",style:t.normalizeStyle([r.value])},[p.value.map(((a,l)=>t.createElementVNode(t.Fragment,null,[e.divider&&0!==l&&t.createElementVNode("span",{class:"v-otp-input__divider"},[e.divider]),t.createVNode(Ci,{focused:i.value&&e.focusAll||m.value===l,key:l},{...n,loader:void 0,default:()=>t.createElementVNode("input",{ref:e=>g.value[l]=e,"aria-label":d(e.label,l+1),autofocus:0===l&&e.autofocus,autocomplete:"one-time-code",class:t.normalizeClass(["v-otp-input__field"]),disabled:e.disabled,inputmode:"number"===e.type?"numeric":"text",min:"number"===e.type?0:void 0,maxlength:0===l?v.value:"1",placeholder:e.placeholder,type:"number"===e.type?"text":e.type,value:c.value[l],onInput:y,onFocus:e=>function(e,t){s(),m.value=t}(0,l),onBlur:V,onKeydown:b,onPaste:e=>function(e,t){t.preventDefault(),t.stopPropagation()
const a=t?.clipboardData?.getData("Text").slice(0,v.value)??""
w(a)||(c.value=a.split(""),g.value?.[e].blur())}(l,e)},null)})]))),t.createElementVNode("input",t.mergeProps({class:"v-otp-input-input",type:"hidden"},o,{value:c.value.join("")}),null),t.createVNode(pi,{contained:!0,"content-class":"v-otp-input__loader","model-value":!!e.loading,persistent:!0},{default:()=>[n.loader?.()??t.createVNode(ao,{color:"boolean"==typeof e.loading?void 0:e.loading,indeterminate:!0,size:"24",width:"2"},null)]}),n.default?.()])])})),{blur:()=>{g.value?.some((e=>e.blur()))},focus:()=>{g.value?.[0].focus()},reset:function(){c.value=[]},isFocused:i}}})
const Lv=yt({scale:{type:[Number,String],default:.5},...bt()},"VParallax"),jv=Et()({name:"VParallax",props:Lv(),setup(e,a){let{slots:l}=a
const{intersectionRef:o,isIntersecting:n}=eo(),{resizeRef:r,contentRect:i}=Zt(),{height:s}=In(),u=t.ref()
let c
t.watchEffect((()=>{o.value=r.value=u.value?.$el})),t.watch(n,(e=>{e?(c=Rt(o.value),c=c===document.scrollingElement?document:c,c.addEventListener("scroll",p,{passive:!0}),p()):c.removeEventListener("scroll",p)})),t.onBeforeUnmount((()=>{c?.removeEventListener("scroll",p)})),t.watch(s,p),t.watch((()=>i.value?.height),p)
const d=t.computed((()=>1-R(Number(e.scale))))
let v=-1
function p(){n.value&&(cancelAnimationFrame(v),v=requestAnimationFrame((()=>{const e=(u.value?.$el).querySelector(".v-img__img")
if(!e)return
const t=c instanceof Document?document.documentElement.clientHeight:c.clientHeight,a=c instanceof Document?window.scrollY:c.scrollTop,l=o.value.getBoundingClientRect().top+a,n=i.value.height,r=(s=(a-(l+(n-t)/2))*d.value,Math.floor(Math.abs(s))*Math.sign(s))
var s
const v=Math.max(1,(d.value*(t-n)+n)/n)
e.style.setProperty("transform",`translateY(${r}px) scale(${v})`)})))}return Ft((()=>t.createVNode(Vl,{class:t.normalizeClass(["v-parallax",{"v-parallax--active":n.value},e.class]),style:t.normalizeStyle(e.style),ref:u,cover:!0,onLoadstart:p,onLoad:p},l))),{}}}),Hv=yt({...en({falseIcon:"$radioOff",trueIcon:"$radioOn"})},"VRadio"),Wv=Et()({name:"VRadio",props:Hv(),setup(e,a){let{slots:l}=a
return Ft((()=>{const a=tn.filterProps(e)
return t.createVNode(tn,t.mergeProps(a,{class:["v-radio",e.class],style:e.style,type:"radio"}),l)})),{}}}),Uv=yt({height:{type:[Number,String],default:"auto"},...gn(),...C(Zo(),["multiple"]),trueIcon:{type:zt,default:"$radioOn"},falseIcon:{type:zt,default:"$radioOff"},type:{type:String,default:"radio"}},"VRadioGroup"),Yv=Et()({name:"VRadioGroup",inheritAttrs:!1,props:Uv(),emits:{"update:modelValue":e=>!0},setup(e,a){let{attrs:l,slots:o}=a
const n=t.useId(),r=t.computed((()=>e.id||`radio-group-${n}`)),i=ra(e,"modelValue")
return Ft((()=>{const[a,n]=P(l),s=hn.filterProps(e),u=tn.filterProps(e),c=o.label?o.label({label:e.label,props:{for:r.value}}):e.label
return t.createVNode(hn,t.mergeProps({class:["v-radio-group",e.class],style:e.style},a,s,{modelValue:i.value,"onUpdate:modelValue":e=>i.value=e,id:r.value}),{...o,default:a=>{let{id:l,messagesId:r,isDisabled:s,isReadonly:d}=a
return t.createElementVNode(t.Fragment,null,[c&&t.createVNode(Ko,{id:l.value},{default:()=>[c]}),t.createVNode(Jo,t.mergeProps(u,{id:l.value,"aria-describedby":r.value,defaultsTarget:"VRadio",trueIcon:e.trueIcon,falseIcon:e.falseIcon,type:e.type,disabled:s.value,readonly:d.value,"aria-labelledby":c?l.value:void 0,multiple:!1},n,{modelValue:i.value,"onUpdate:modelValue":e=>i.value=e}),o)])}})})),{}}}),Gv=yt({...sn(),...gn(),...Us(),strict:Boolean,modelValue:{type:Array,default:()=>[0,0]}},"VRangeSlider"),qv=Et()({name:"VRangeSlider",props:Gv(),emits:{"update:focused":e=>!0,"update:modelValue":e=>!0,end:e=>!0,start:e=>!0},setup(e,a){let{slots:l,emit:o}=a
const n=t.ref(),r=t.ref(),i=t.ref(),{rtlClasses:s}=ya()
const u=Ys(e),c=ra(e,"modelValue",void 0,(e=>e?.length?e.map((e=>u.roundValue(e))):[0,0])),{activeThumbRef:d,hasLabels:v,max:p,min:m,mousePressed:f,onSliderMousedown:g,onSliderTouchstart:h,position:y,trackContainerRef:b,readonly:V}=Gs({props:e,steps:u,onSliderStart:()=>{o("start",c.value)},onSliderEnd:t=>{let{value:a}=t
const l=d.value===n.value?.$el?[a,c.value[1]]:[c.value[0],a]
!e.strict&&l[0]<l[1]&&(c.value=l),o("end",c.value)},onSliderMove:t=>{let{value:a}=t
const[l,o]=c.value
e.strict||l!==o||l===m.value||(d.value=a>l?r.value?.$el:n.value?.$el,d.value?.focus()),d.value===n.value?.$el?c.value=[Math.min(a,o),o]:c.value=[l,Math.max(l,a)]},getActiveThumb:function(t){if(!n.value||!r.value)return
const a=Ws(t,n.value.$el,e.direction),l=Ws(t,r.value.$el,e.direction),o=Math.abs(a),i=Math.abs(l)
return o<i||o===i&&a<0?n.value.$el:r.value.$el}}),{isFocused:w,focus:S,blur:k}=un(e),x=t.computed((()=>y(c.value[0]))),C=t.computed((()=>y(c.value[1])))
return Ft((()=>{const a=hn.filterProps(e),o=!!(e.label||l.label||l.prepend)
return t.createVNode(hn,t.mergeProps({class:["v-slider","v-range-slider",{"v-slider--has-labels":!!l["tick-label"]||v.value,"v-slider--focused":w.value,"v-slider--pressed":f.value,"v-slider--disabled":e.disabled},s.value,e.class],style:e.style,ref:i},a,{focused:w.value}),{...l,prepend:o?a=>t.createElementVNode(t.Fragment,null,[l.label?.(a)??(e.label?t.createVNode(Ko,{class:"v-slider__label",text:e.label},null):void 0),l.prepend?.(a)]):void 0,default:a=>{let{id:o,messagesId:i}=a
return t.createElementVNode("div",{class:"v-slider__container",onMousedown:V.value?void 0:g,onTouchstartPassive:V.value?void 0:h},[t.createElementVNode("input",{id:`${o.value}_start`,name:e.name||o.value,disabled:!!e.disabled,readonly:!!e.readonly,tabindex:"-1",value:c.value[0]},null),t.createElementVNode("input",{id:`${o.value}_stop`,name:e.name||o.value,disabled:!!e.disabled,readonly:!!e.readonly,tabindex:"-1",value:c.value[1]},null),t.createVNode(Zs,{ref:b,start:x.value,stop:C.value},{"tick-label":l["tick-label"]}),t.createVNode(Ks,{ref:n,"aria-describedby":i.value,focused:w&&d.value===n.value?.$el,modelValue:c.value[0],"onUpdate:modelValue":e=>c.value=[e,c.value[1]],onFocus:e=>{S(),d.value=n.value?.$el,p.value!==m.value&&c.value[0]===c.value[1]&&c.value[1]===m.value&&e.relatedTarget!==r.value?.$el&&(n.value?.$el.blur(),r.value?.$el.focus())},onBlur:()=>{k(),d.value=void 0},min:m.value,max:c.value[1],position:x.value,ripple:e.ripple},{"thumb-label":l["thumb-label"]}),t.createVNode(Ks,{ref:r,"aria-describedby":i.value,focused:w&&d.value===r.value?.$el,modelValue:c.value[1],"onUpdate:modelValue":e=>c.value=[c.value[0],e],onFocus:e=>{S(),d.value=r.value?.$el,p.value!==m.value&&c.value[0]===c.value[1]&&c.value[0]===p.value&&e.relatedTarget!==n.value?.$el&&(r.value?.$el.blur(),n.value?.$el.focus())},onBlur:()=>{k(),d.value=void 0},min:c.value[0],max:p.value,position:C.value,ripple:e.ripple},{"thumb-label":l["thumb-label"]})])}})})),{}}}),Kv=yt({name:String,itemAriaLabel:{type:String,default:"$vuetify.rating.ariaLabel.item"},activeColor:String,color:String,clearable:Boolean,disabled:Boolean,emptyIcon:{type:zt,default:"$ratingEmpty"},fullIcon:{type:zt,default:"$ratingFull"},halfIncrements:Boolean,hover:Boolean,length:{type:[Number,String],default:5},readonly:Boolean,modelValue:{type:[Number,String],default:0},itemLabels:Array,itemLabelPosition:{type:String,default:"top",validator:e=>["top","bottom"].includes(e)},ripple:Boolean,...bt(),...Al(),...Xl(),...Ba(),...Va()},"VRating"),Xv=Et()({name:"VRating",props:Kv(),emits:{"update:modelValue":e=>!0},setup(e,a){let{slots:l}=a
const{t:o}=ga(),{themeClasses:n}=Ea(e),r=ra(e,"modelValue"),i=t.computed((()=>R(parseFloat(r.value),0,Number(e.length)))),s=t.computed((()=>m(Number(e.length),1))),u=t.computed((()=>s.value.flatMap((t=>e.halfIncrements?[t-.5,t]:[t])))),c=t.shallowRef(-1),d=t.computed((()=>u.value.map((t=>{const a=e.hover&&c.value>-1,l=i.value>=t,o=c.value>=t,n=(a?o:l)?e.fullIcon:e.emptyIcon,r=e.activeColor??e.color
return{isFilled:l,isHovered:o,icon:n,color:l||o?r:e.color}})))),v=t.computed((()=>[0,...u.value].map((t=>({onMouseenter:e.hover?function(){c.value=t}:void 0,onMouseleave:e.hover?function(){c.value=-1}:void 0,onClick:function(){e.disabled||e.readonly||(r.value=i.value===t&&e.clearable?0:t)}}))))),p=t.useId(),f=t.computed((()=>e.name??`v-rating-${p}`))
function g(a){let{value:n,index:r,showStar:s=!0}=a
const{onMouseenter:u,onMouseleave:c,onClick:p}=v.value[r+1],m=`${f.value}-${String(n).replace(".","-")}`,g={color:d.value[r]?.color,density:e.density,disabled:e.disabled,icon:d.value[r]?.icon,ripple:e.ripple,size:e.size,variant:"plain"}
return t.createElementVNode(t.Fragment,null,[t.createElementVNode("label",{for:m,class:t.normalizeClass({"v-rating__item--half":e.halfIncrements&&n%1>0,"v-rating__item--full":e.halfIncrements&&n%1==0}),onMouseenter:u,onMouseleave:c,onClick:p},[t.createElementVNode("span",{class:"v-rating__hidden"},[o(e.itemAriaLabel,n,e.length)]),s?l.item?l.item({...d.value[r],props:g,value:n,index:r,rating:i.value}):t.createVNode($o,t.mergeProps({"aria-label":o(e.itemAriaLabel,n,e.length)},g),null):void 0]),t.createElementVNode("input",{class:"v-rating__hidden",name:f.value,id:m,type:"radio",value:n,checked:i.value===n,tabindex:-1,readonly:e.readonly,disabled:e.disabled},null)])}function h(e){return l["item-label"]?l["item-label"](e):e.label?t.createElementVNode("span",null,[e.label]):t.createElementVNode("span",null,[t.createTextVNode(" ")])}return Ft((()=>{const a=!!e.itemLabels?.length||l["item-label"]
return t.createVNode(e.tag,{class:t.normalizeClass(["v-rating",{"v-rating--hover":e.hover,"v-rating--readonly":e.readonly},n.value,e.class]),style:t.normalizeStyle(e.style)},{default:()=>[t.createVNode(g,{value:0,index:-1,showStar:!1},null),s.value.map(((l,o)=>t.createElementVNode("div",{class:"v-rating__wrapper"},[a&&"top"===e.itemLabelPosition?h({value:l,index:o,label:e.itemLabels?.[o]}):void 0,t.createElementVNode("div",{class:"v-rating__item"},[e.halfIncrements?t.createElementVNode(t.Fragment,null,[t.createVNode(g,{value:l-.5,index:2*o},null),t.createVNode(g,{value:l,index:2*o+1},null)]):t.createVNode(g,{value:l,index:o},null)]),a&&"bottom"===e.itemLabelPosition?h({value:l,index:o,label:e.itemLabels?.[o]}):void 0])))]})})),{}}}),Zv={actions:"button@2",article:"heading, paragraph",avatar:"avatar",button:"button",card:"image, heading","card-avatar":"image, list-item-avatar",chip:"chip","date-picker":"list-item, heading, divider, date-picker-options, date-picker-days, actions","date-picker-options":"text, avatar@2","date-picker-days":"avatar@28",divider:"divider",heading:"heading",image:"image","list-item":"text","list-item-avatar":"avatar, text","list-item-two-line":"sentences","list-item-avatar-two-line":"avatar, sentences","list-item-three-line":"paragraph","list-item-avatar-three-line":"avatar, paragraph",ossein:"ossein",paragraph:"text@3",sentences:"text@2",subtitle:"text",table:"table-heading, table-thead, table-tbody, table-tfoot","table-heading":"chip, text","table-thead":"heading@6","table-tbody":"table-row-divider@6","table-row-divider":"table-row, divider","table-row":"text@6","table-tfoot":"text@2, avatar@2",text:"text"}
function Qv(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[]
return t.createElementVNode("div",{class:t.normalizeClass(["v-skeleton-loader__bone",`v-skeleton-loader__${e}`])},[a])}function Jv(e){const[t,a]=e.split("@")
return Array.from({length:a}).map((()=>ep(t)))}function ep(e){let t=[]
if(!e)return t
const a=Zv[e]
if(e===a);else{if(e.includes(","))return tp(e)
if(e.includes("@"))return Jv(e)
a.includes(",")?t=tp(a):a.includes("@")?t=Jv(a):a&&t.push(ep(a))}return[Qv(e,t)]}function tp(e){return e.replace(/\s/g,"").split(",").map(ep)}const ap=yt({boilerplate:Boolean,color:String,loading:Boolean,loadingText:{type:String,default:"$vuetify.loading"},type:{type:[String,Array],default:"ossein"},...rl(),...kl(),...Va()},"VSkeletonLoader"),lp=Et()({name:"VSkeletonLoader",props:ap(),setup(e,a){let{slots:l}=a
const{backgroundColorClasses:o,backgroundColorStyles:n}=vl((()=>e.color)),{dimensionStyles:r}=il(e),{elevationClasses:i}=xl(e),{themeClasses:s}=Ea(e),{t:u}=ga(),c=t.computed((()=>ep(B(e.type).join(","))))
return Ft((()=>{const a=!l.default||e.loading,d=e.boilerplate||!a?{}:{ariaLive:"polite",ariaLabel:u(e.loadingText),role:"alert"}
return t.createElementVNode("div",t.mergeProps({class:["v-skeleton-loader",{"v-skeleton-loader--boilerplate":e.boilerplate},s.value,o.value,i.value],style:[n.value,a?r.value:{}]},d),[a?c.value:l.default?.()])})),{}}}),op=Et()({name:"VSlideGroupItem",props:jl(),emits:{"group:selected":e=>!0},setup(e,t){let{slots:a}=t
const l=Hl(e,$n)
return()=>a.default?.({isSelected:l.isSelected.value,select:l.select,toggle:l.toggle,selectedClass:l.selectedClass.value})}})
const np=yt({multiLine:Boolean,text:String,timer:[Boolean,String],timeout:{type:[Number,String],default:5e3},vertical:Boolean,...oo({location:"bottom"}),...po(),...pl(),...zl(),...Va(),...C(vi({transition:"v-snackbar-transition"}),["persistent","noClickAnimation","scrim","scrollStrategy"])},"VSnackbar"),rp=Et()({name:"VSnackbar",props:np(),emits:{"update:modelValue":e=>!0},setup(e,a){let{slots:l}=a
const o=ra(e,"modelValue"),{positionClasses:n}=mo(e),{scopeId:r}=li(),{themeClasses:i}=Ea(e),{colorClasses:s,colorStyles:u,variantClasses:c}=$l(e),{roundedClasses:d}=ml(e),v=function(e){const a=t.shallowRef(e())
let l=-1
function o(){clearInterval(l)}return t.onScopeDispose(o),{clear:o,time:a,start:function(t){const n=t?getComputedStyle(t):{transitionDuration:.2},r=1e3*parseFloat(n.transitionDuration)||200
if(o(),a.value<=0)return
const i=performance.now()
l=window.setInterval((()=>{const t=performance.now()-i+r
a.value=Math.max(e()-t,0),a.value<=0&&o()}),r)},reset:function(){o(),t.nextTick((()=>a.value=e()))}}}((()=>Number(e.timeout))),p=t.ref(),m=t.ref(),f=t.shallowRef(!1),g=t.shallowRef(0),h=t.ref(),b=t.inject(Qt,void 0)
na((()=>!!b),(()=>{const e=aa()
t.watchEffect((()=>{h.value=e.mainStyles.value}))})),t.watch(o,w),t.watch((()=>e.timeout),w),t.onMounted((()=>{o.value&&w()}))
let V=-1
function w(){v.reset(),window.clearTimeout(V)
const t=Number(e.timeout)
if(!o.value||-1===t)return
const a=y(m.value)
v.start(a),V=window.setTimeout((()=>{o.value=!1}),t)}function S(){f.value=!0,v.reset(),window.clearTimeout(V)}function k(){f.value=!1,w()}function x(e){g.value=e.touches[0].clientY}function C(e){Math.abs(g.value-e.changedTouches[0].clientY)>50&&(o.value=!1)}function N(){f.value&&k()}const E=t.computed((()=>e.location.split(" ").reduce(((e,t)=>(e[`v-snackbar--${t}`]=!0,e)),{})))
return Ft((()=>{const a=pi.filterProps(e),g=!!(l.default||l.text||e.text)
return t.createVNode(pi,t.mergeProps({ref:p,class:["v-snackbar",{"v-snackbar--active":o.value,"v-snackbar--multi-line":e.multiLine&&!e.vertical,"v-snackbar--timer":!!e.timer,"v-snackbar--vertical":e.vertical},E.value,n.value,e.class],style:[h.value,e.style]},a,{modelValue:o.value,"onUpdate:modelValue":e=>o.value=e,contentProps:t.mergeProps({class:["v-snackbar__wrapper",i.value,s.value,d.value,c.value],style:[u.value],onPointerenter:S,onPointerleave:k},a.contentProps),persistent:!0,noClickAnimation:!0,scrim:!1,scrollStrategy:"none",_disableGlobalStack:!0,onTouchstartPassive:x,onTouchend:C,onAfterLeave:N},r),{default:()=>[Fl(!1,"v-snackbar"),e.timer&&!f.value&&t.createElementVNode("div",{key:"timer",class:"v-snackbar__timer"},[t.createVNode(io,{ref:m,color:"string"==typeof e.timer?e.timer:"info",max:e.timeout,"model-value":v.time.value},null)]),g&&t.createElementVNode("div",{key:"content",class:"v-snackbar__content",role:"status","aria-live":"polite"},[l.text?.()??e.text,l.default?.()]),l.actions&&t.createVNode(nl,{defaults:{VBtn:{variant:"text",ripple:!1,slim:!0}}},{default:()=>[t.createElementVNode("div",{class:"v-snackbar__actions"},[l.actions({isActive:o})])]})],activator:l.activator})})),gi({},p)}}),ip=yt({closable:[Boolean,String],closeText:{type:String,default:"$vuetify.dismiss"},modelValue:{type:Array,default:()=>[]},...C(np(),["modelValue"])},"VSnackbarQueue"),sp=Et()({name:"VSnackbarQueue",props:ip(),emits:{"update:modelValue":e=>!0},setup(e,a){let{emit:l,slots:o}=a
const{t:n}=ga(),r=t.shallowRef(!1),i=t.shallowRef(!1),s=t.shallowRef()
function u(){e.modelValue.length?c():(s.value=void 0,i.value=!1)}function c(){const[a,...o]=e.modelValue
l("update:modelValue",o),s.value="string"==typeof a?{text:a}:a,t.nextTick((()=>{r.value=!0}))}function d(){r.value=!1}t.watch((()=>e.modelValue.length),((e,t)=>{!i.value&&e>t&&c()})),t.watch(r,(e=>{e&&(i.value=!0)}))
const v=t.computed((()=>({color:"string"==typeof e.closable?e.closable:void 0,text:n(e.closeText)})))
Ft((()=>{const a=!(!e.closable&&!o.actions),{modelValue:l,...n}=rp.filterProps(e)
return t.createElementVNode(t.Fragment,null,[i.value&&!!s.value&&(o.default?t.createVNode(nl,{defaults:{VSnackbar:s.value}},{default:()=>[o.default({item:s.value})]}):t.createVNode(rp,t.mergeProps(n,s.value,{modelValue:r.value,"onUpdate:modelValue":e=>r.value=e,onAfterLeave:u}),{text:o.text?()=>o.text?.({item:s.value}):void 0,actions:a?()=>t.createElementVNode(t.Fragment,null,[o.actions?t.createVNode(nl,{defaults:{VBtn:v.value}},{default:()=>[o.actions({item:s.value,props:{onClick:d}})]}):t.createVNode($o,t.mergeProps(v.value,{onClick:d}),null)]):void 0}))])}))}}),up=yt({autoDraw:Boolean,autoDrawDuration:[Number,String],autoDrawEasing:{type:String,default:"ease"},color:String,gradient:{type:Array,default:()=>[]},gradientDirection:{type:String,validator:e=>["top","bottom","left","right"].includes(e),default:"top"},height:{type:[String,Number],default:75},labels:{type:Array,default:()=>[]},labelSize:{type:[Number,String],default:7},lineWidth:{type:[String,Number],default:4},id:String,itemValue:{type:String,default:"value"},modelValue:{type:Array,default:()=>[]},min:[String,Number],max:[String,Number],padding:{type:[String,Number],default:8},showLabels:Boolean,smooth:[Boolean,String,Number],width:{type:[Number,String],default:300}},"Line"),cp=yt({autoLineWidth:Boolean,...up()},"VBarline"),dp=Et()({name:"VBarline",props:cp(),setup(e,a){let{slots:l}=a
const o=t.useId(),n=t.computed((()=>e.id||`barline-${o}`)),r=t.computed((()=>Number(e.autoDrawDuration)||500)),i=t.computed((()=>Boolean(e.showLabels||e.labels.length>0||!!l?.label))),s=t.computed((()=>parseFloat(e.lineWidth)||4)),u=t.computed((()=>Math.max(e.modelValue.length*s.value,Number(e.width)))),c=t.computed((()=>({minX:0,maxX:u.value,minY:0,maxY:parseInt(e.height,10)}))),d=t.computed((()=>e.modelValue.map((t=>p(t,e.itemValue,t)))))
function v(t,a){const{minX:l,maxX:o,minY:n,maxY:r}=a,i=t.length
let s=null!=e.max?Number(e.max):Math.max(...t),u=null!=e.min?Number(e.min):Math.min(...t)
u>0&&null==e.min&&(u=0),s<0&&null==e.max&&(s=0)
const c=o/i,d=(r-n)/(s-u||1),v=r-Math.abs(u*d)
return t.map(((e,t)=>{const a=Math.abs(d*e)
return{x:l+t*c,y:v-a+Number(e<0)*a,height:a,value:e}}))}const m=t.computed((()=>{const t=[],a=v(d.value,c.value),l=a.length
for(let o=0;t.length<l;o++){const l=a[o]
let n=e.labels[o]
n||(n="object"==typeof l?l.value:l),t.push({x:l.x,value:String(n)})}return t})),f=t.computed((()=>v(d.value,c.value))),g=t.computed((()=>(Math.abs(f.value[0].x-f.value[1].x)-s.value)/2)),h=t.computed((()=>"boolean"==typeof e.smooth?e.smooth?2:0:Number(e.smooth)))
Ft((()=>{const a=e.gradient.slice().length?e.gradient.slice().reverse():[""]
return t.createElementVNode("svg",{display:"block"},[t.createElementVNode("defs",null,[t.createElementVNode("linearGradient",{id:n.value,gradientUnits:"userSpaceOnUse",x1:"left"===e.gradientDirection?"100%":"0",y1:"top"===e.gradientDirection?"100%":"0",x2:"right"===e.gradientDirection?"100%":"0",y2:"bottom"===e.gradientDirection?"100%":"0"},[a.map(((e,l)=>t.createElementVNode("stop",{offset:l/Math.max(a.length-1,1),"stop-color":e||"currentColor"},null)))])]),t.createElementVNode("clipPath",{id:`${n.value}-clip`},[f.value.map((a=>t.createElementVNode("rect",{x:a.x+g.value,y:a.y,width:s.value,height:a.height,rx:h.value,ry:h.value},[e.autoDraw&&t.createElementVNode(t.Fragment,null,[t.createElementVNode("animate",{attributeName:"y",from:a.y+a.height,to:a.y,dur:`${r.value}ms`,fill:"freeze"},null),t.createElementVNode("animate",{attributeName:"height",from:"0",to:a.height,dur:`${r.value}ms`,fill:"freeze"},null)])])))]),i.value&&t.createElementVNode("g",{key:"labels",style:{textAnchor:"middle",dominantBaseline:"mathematical",fill:"currentColor"}},[m.value.map(((a,o)=>t.createElementVNode("text",{x:a.x+g.value+s.value/2,y:parseInt(e.height,10)-2+(parseInt(e.labelSize,10)||5.25),"font-size":Number(e.labelSize)||7},[l.label?.({index:o,value:a.value})??a.value])))]),t.createElementVNode("g",{"clip-path":`url(#${n.value}-clip)`,fill:`url(#${n.value})`},[t.createElementVNode("rect",{x:0,y:0,width:Math.max(e.modelValue.length*s.value,Number(e.width)),height:e.height},null)])])}))}})
function vp(e,t){let a=arguments.length>2&&void 0!==arguments[2]&&arguments[2],l=arguments.length>3&&void 0!==arguments[3]?arguments[3]:75
if(0===e.length)return""
const o=e.shift(),n=e[e.length-1]
return(a?`M${o.x} ${l-o.x+2} L${o.x} ${o.y}`:`M${o.x} ${o.y}`)+e.map(((a,l)=>{const n=e[l+1],r=e[l-1]||o,i=n&&(u=a,c=r,pp((s=n).x+c.x)===pp(2*u.x)&&pp(s.y+c.y)===pp(2*u.y))
var s,u,c
if(!n||i)return`L${a.x} ${a.y}`
const d=Math.min(mp(r,a),mp(n,a)),v=d/2<t?d/2:t,p=fp(r,a,v),m=fp(n,a,v)
return`L${p.x} ${p.y}S${a.x} ${a.y} ${m.x} ${m.y}`})).join("")+(a?`L${n.x} ${l-o.x+2} Z`:"")}function pp(e){return parseInt(e,10)}function mp(e,t){return Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2))}function fp(e,t,a){const l=e.x-t.x,o=e.y-t.y,n=Math.sqrt(l*l+o*o),r=l/n,i=o/n
return{x:t.x+r*a,y:t.y+i*a}}const gp=yt({fill:Boolean,...up()},"VTrendline"),hp=Et()({name:"VTrendline",props:gp(),setup(e,a){let{slots:l}=a
const o=t.useId(),n=t.computed((()=>e.id||`trendline-${o}`)),r=t.computed((()=>Number(e.autoDrawDuration)||(e.fill?500:2e3))),i=t.ref(0),s=t.ref(null)
function u(t,a){const{minX:l,maxX:o,minY:n,maxY:r}=a,i=t.length,s=null!=e.max?Number(e.max):Math.max(...t),u=null!=e.min?Number(e.min):Math.min(...t),c=(o-l)/(i-1),d=(r-n)/(s-u||1)
return t.map(((e,t)=>({x:l+t*c,y:r-(e-u)*d,value:e})))}const c=t.computed((()=>Boolean(e.showLabels||e.labels.length>0||!!l?.label))),d=t.computed((()=>parseFloat(e.lineWidth)||4)),v=t.computed((()=>Number(e.width))),m=t.computed((()=>{const t=Number(e.padding)
return{minX:t,maxX:v.value-t,minY:t,maxY:parseInt(e.height,10)-t}})),f=t.computed((()=>e.modelValue.map((t=>p(t,e.itemValue,t))))),g=t.computed((()=>{const t=[],a=u(f.value,m.value),l=a.length
for(let o=0;t.length<l;o++){const l=a[o]
let n=e.labels[o]
n||(n="object"==typeof l?l.value:l),t.push({x:l.x,value:String(n)})}return t}))
function h(t){const a="boolean"==typeof e.smooth?e.smooth?8:0:Number(e.smooth)
return vp(u(f.value,m.value),a,t,parseInt(e.height,10))}t.watch((()=>e.modelValue),(async()=>{if(await t.nextTick(),!e.autoDraw||!s.value)return
const a=s.value,l=a.getTotalLength()
e.fill?(a.style.transformOrigin="bottom center",a.style.transition="none",a.style.transform="scaleY(0)",a.getBoundingClientRect(),a.style.transition=`transform ${r.value}ms ${e.autoDrawEasing}`,a.style.transform="scaleY(1)"):(a.style.strokeDasharray=`${l}`,a.style.strokeDashoffset=`${l}`,a.getBoundingClientRect(),a.style.transition=`stroke-dashoffset ${r.value}ms ${e.autoDrawEasing}`,a.style.strokeDashoffset="0"),i.value=l}),{immediate:!0}),Ft((()=>{const a=e.gradient.slice().length?e.gradient.slice().reverse():[""]
return t.createElementVNode("svg",{display:"block","stroke-width":parseFloat(e.lineWidth)??4},[t.createElementVNode("defs",null,[t.createElementVNode("linearGradient",{id:n.value,gradientUnits:"userSpaceOnUse",x1:"left"===e.gradientDirection?"100%":"0",y1:"top"===e.gradientDirection?"100%":"0",x2:"right"===e.gradientDirection?"100%":"0",y2:"bottom"===e.gradientDirection?"100%":"0"},[a.map(((e,l)=>t.createElementVNode("stop",{offset:l/Math.max(a.length-1,1),"stop-color":e||"currentColor"},null)))])]),c.value&&t.createElementVNode("g",{key:"labels",style:{textAnchor:"middle",dominantBaseline:"mathematical",fill:"currentColor"}},[g.value.map(((a,o)=>t.createElementVNode("text",{x:a.x+d.value/2+d.value/2,y:parseInt(e.height,10)-4+(parseInt(e.labelSize,10)||5.25),"font-size":Number(e.labelSize)||7},[l.label?.({index:o,value:a.value})??a.value])))]),t.createElementVNode("path",{ref:s,d:h(e.fill),fill:e.fill?`url(#${n.value})`:"none",stroke:e.fill?"none":`url(#${n.value})`},null),e.fill&&t.createElementVNode("path",{d:h(!1),fill:"none",stroke:e.color??e.gradient?.[0]},null)])}))}}),yp=yt({type:{type:String,default:"trend"},...cp(),...gp()},"VSparkline"),bp=Et()({name:"VSparkline",props:yp(),setup(e,a){let{slots:l}=a
const{textColorClasses:o,textColorStyles:n}=dl((()=>e.color)),r=t.computed((()=>Boolean(e.showLabels||e.labels.length>0||!!l?.label))),i=t.computed((()=>{let t=parseInt(e.height,10)
return r.value&&(t+=1.5*parseInt(e.labelSize,10)),t}))
Ft((()=>{const a="trend"===e.type?hp:dp,r="trend"===e.type?hp.filterProps(e):dp.filterProps(e)
return t.createVNode(a,t.mergeProps({key:e.type,class:o.value,style:n.value,viewBox:`0 0 ${e.width} ${parseInt(i.value,10)}`},r),l)}))}}),Vp=yt({...bt(),...hi({offset:8,minWidth:0,openDelay:0,closeDelay:100,location:"top center",transition:"scale-transition"})},"VSpeedDial"),wp=Et()({name:"VSpeedDial",props:Vp(),emits:{"update:modelValue":e=>!0},setup(e,a){let{slots:l}=a
const o=ra(e,"modelValue"),n=t.ref(),r=t.computed((()=>{const[t,a="center"]=e.location?.split(" ")??[]
return`${t} ${a}`})),i=t.computed((()=>({[`v-speed-dial__content--${r.value.replace(" ","-")}`]:!0})))
return Ft((()=>{const a=yi.filterProps(e)
return t.createVNode(yi,t.mergeProps(a,{modelValue:o.value,"onUpdate:modelValue":e=>o.value=e,class:e.class,style:e.style,contentClass:["v-speed-dial__content",i.value,e.contentClass],location:r.value,ref:n,transition:"fade-transition"}),{...l,default:a=>t.createVNode(nl,{defaults:{VBtn:{size:"small"}}},{default:()=>[t.createVNode(gl,{appear:!0,group:!0,transition:e.transition},{default:()=>[l.default?.(a)]})]})})})),{}}}),Sp=Symbol.for("vuetify:v-stepper"),kp=yt({color:String,disabled:{type:[Boolean,String],default:!1},prevText:{type:String,default:"$vuetify.stepper.prev"},nextText:{type:String,default:"$vuetify.stepper.next"}},"VStepperActions"),xp=Et()({name:"VStepperActions",props:kp(),emits:{"click:prev":()=>!0,"click:next":()=>!0},setup(e,a){let{emit:l,slots:o}=a
const{t:n}=ga()
function r(){l("click:prev")}function i(){l("click:next")}return Ft((()=>{const a={onClick:r},l={onClick:i}
return t.createElementVNode("div",{class:"v-stepper-actions"},[t.createVNode(nl,{defaults:{VBtn:{disabled:["prev",!0].includes(e.disabled),text:n(e.prevText),variant:"text"}}},{default:()=>[o.prev?.({props:a})??t.createVNode($o,a,null)]}),t.createVNode(nl,{defaults:{VBtn:{color:e.color,disabled:["next",!0].includes(e.disabled),text:n(e.nextText),variant:"tonal"}}},{default:()=>[o.next?.({props:l})??t.createVNode($o,l,null)]})])})),{}}}),Cp=It("v-stepper-header"),Np=yt({color:String,title:String,subtitle:String,complete:Boolean,completeIcon:{type:zt,default:"$complete"},editable:Boolean,editIcon:{type:zt,default:"$edit"},error:Boolean,errorIcon:{type:zt,default:"$error"},icon:zt,ripple:{type:[Boolean,Object],default:!0},rules:{type:Array,default:()=>[]}},"StepperItem"),Ep=yt({...Np(),...jl()},"VStepperItem"),Ip=Et()({name:"VStepperItem",directives:{vRipple:Fo},props:Ep(),emits:{"group:selected":e=>!0},setup(e,a){let{slots:l}=a
const o=Hl(e,Sp,!0),n=t.computed((()=>o?.value.value??e.value)),r=t.computed((()=>e.rules.every((e=>!0===e())))),i=t.computed((()=>!e.disabled&&e.editable)),s=t.computed((()=>!e.disabled&&e.editable)),u=t.computed((()=>e.error||!r.value)),c=t.computed((()=>e.complete||e.rules.length>0&&r.value)),d=t.computed((()=>u.value?e.errorIcon:c.value?e.completeIcon:o.isSelected.value&&e.editable?e.editIcon:e.icon)),v=t.computed((()=>({canEdit:s.value,hasError:u.value,hasCompleted:c.value,title:e.title,subtitle:e.subtitle,step:n.value,value:e.value})))
return Ft((()=>{const a=(!o||o.isSelected.value||c.value||s.value)&&!u.value&&!e.disabled,r=!(null==e.title&&!l.title),p=!(null==e.subtitle&&!l.subtitle)
return t.withDirectives(t.createElementVNode("button",{class:t.normalizeClass(["v-stepper-item",{"v-stepper-item--complete":c.value,"v-stepper-item--disabled":e.disabled,"v-stepper-item--error":u.value},o?.selectedClass.value]),disabled:!e.editable,type:"button",onClick:function(){o?.toggle()}},[i.value&&Fl(!0,"v-stepper-item"),t.createVNode(Go,{key:"stepper-avatar",class:"v-stepper-item__avatar",color:a?e.color:void 0,size:24},{default:()=>[l.icon?.(v.value)??(d.value?t.createVNode(Jl,{icon:d.value},null):n.value)]}),t.createElementVNode("div",{class:"v-stepper-item__content"},[r&&t.createElementVNode("div",{key:"title",class:"v-stepper-item__title"},[l.title?.(v.value)??e.title]),p&&t.createElementVNode("div",{key:"subtitle",class:"v-stepper-item__subtitle"},[l.subtitle?.(v.value)??e.subtitle]),l.default?.(v.value)])]),[[Fo,e.ripple&&e.editable,null]])})),{}}}),_p=yt({...C(Ns(),["continuous","nextIcon","prevIcon","showArrows","touch","mandatory"])},"VStepperWindow"),Pp=Et()({name:"VStepperWindow",props:_p(),emits:{"update:modelValue":e=>!0},setup(e,a){let{slots:l}=a
const o=t.inject(Sp,null),n=ra(e,"modelValue"),r=t.computed({get:()=>null==n.value&&o?o.items.value.find((e=>o.selected.value.includes(e.id)))?.value:n.value,set(e){n.value=e}})
return Ft((()=>{const a=Es.filterProps(e)
return t.createVNode(Es,t.mergeProps({_as:"VStepperWindow"},a,{modelValue:r.value,"onUpdate:modelValue":e=>r.value=e,class:["v-stepper-window",e.class],style:e.style,mandatory:!1,touch:!1}),l)})),{}}}),Bp=yt({...Ps()},"VStepperWindowItem"),Rp=Et()({name:"VStepperWindowItem",props:Bp(),setup(e,a){let{slots:l}=a
return Ft((()=>{const a=Bs.filterProps(e)
return t.createVNode(Bs,t.mergeProps({_as:"VStepperWindowItem"},a,{class:["v-stepper-window-item",e.class],style:e.style}),l)})),{}}}),Ap=yt({altLabels:Boolean,bgColor:String,completeIcon:zt,editIcon:zt,editable:Boolean,errorIcon:zt,hideActions:Boolean,items:{type:Array,default:()=>[]},itemTitle:{type:String,default:"title"},itemValue:{type:String,default:"value"},nonLinear:Boolean,flat:Boolean,...En()},"Stepper"),Tp=yt({...Ap(),...Ll({mandatory:"force",selectedClass:"v-stepper-item--selected"}),...nu(),...k(kp(),["prevText","nextText"])},"VStepper"),Dp=Et()({name:"VStepper",props:Tp(),emits:{"update:modelValue":e=>!0},setup(e,a){let{slots:l}=a
const{items:o,next:n,prev:r,selected:i}=Wl(e,Sp),{displayClasses:s,mobile:u}=In(e),{completeIcon:c,editIcon:d,errorIcon:v,color:m,editable:f,prevText:g,nextText:h}=t.toRefs(e),y=t.computed((()=>e.items.map(((t,a)=>({title:p(t,e.itemTitle,t),value:p(t,e.itemValue,a+1),raw:t}))))),b=t.computed((()=>o.value.findIndex((e=>i.value.includes(e.id)))))
return xt({VStepperItem:{editable:f,errorIcon:v,completeIcon:c,editIcon:d,prevText:g,nextText:h},VStepperActions:{color:m,disabled:t.computed((()=>e.disabled?e.disabled:0===b.value?"prev":b.value===o.value.length-1&&"next")),prevText:g,nextText:h}}),Ft((()=>{const a=ru.filterProps(e),o=!(!l.header&&!e.items.length),i=e.items.length>0,c=!(e.hideActions||!i&&!l.actions)
return t.createVNode(ru,t.mergeProps(a,{color:e.bgColor,class:["v-stepper",{"v-stepper--alt-labels":e.altLabels,"v-stepper--flat":e.flat,"v-stepper--non-linear":e.nonLinear,"v-stepper--mobile":u.value},s.value,e.class],style:e.style}),{default:()=>[o&&t.createVNode(Cp,{key:"stepper-header"},{default:()=>[y.value.map(((e,a)=>{let{raw:o,...n}=e
return t.createElementVNode(t.Fragment,null,[!!a&&t.createVNode(br,null,null),t.createVNode(Ip,n,{default:l[`header-item.${n.value}`]??l.header,icon:l.icon,title:l.title,subtitle:l.subtitle})])}))]}),i&&t.createVNode(Pp,{key:"stepper-window"},{default:()=>[y.value.map((e=>t.createVNode(Rp,{value:e.value},{default:()=>l[`item.${e.value}`]?.(e)??l.item?.(e)})))]}),l.default?.({prev:r,next:n}),c&&(l.actions?.({next:n,prev:r})??t.createVNode(xp,{key:"stepper-actions","onClick:prev":r,"onClick:next":n},l))]})})),{prev:r,next:n}}}),Fp=yt({indeterminate:Boolean,inset:Boolean,flat:Boolean,loading:{type:[Boolean,String],default:!1},...gn(),...en()},"VSwitch"),zp=Et()({name:"VSwitch",inheritAttrs:!1,props:Fp(),emits:{"update:focused":e=>!0,"update:modelValue":e=>!0,"update:indeterminate":e=>!0},setup(e,l){let{attrs:o,slots:n}=l
const r=ra(e,"indeterminate"),i=ra(e,"modelValue"),{loaderClasses:s}=uo(e),{isFocused:u,focus:c,blur:d}=un(e),v=t.ref(),p=a&&window.matchMedia("(forced-colors: active)").matches,m=t.toRef((()=>"string"==typeof e.loading&&""!==e.loading?e.loading:e.color)),f=t.useId(),g=t.toRef((()=>e.id||`switch-${f}`))
function h(){r.value&&(r.value=!1)}function y(e){e.stopPropagation(),e.preventDefault(),v.value?.input?.click()}return Ft((()=>{const[a,l]=P(o),f=hn.filterProps(e),b=tn.filterProps(e)
return t.createVNode(hn,t.mergeProps({class:["v-switch",{"v-switch--flat":e.flat},{"v-switch--inset":e.inset},{"v-switch--indeterminate":r.value},s.value,e.class]},a,f,{modelValue:i.value,"onUpdate:modelValue":e=>i.value=e,id:g.value,focused:u.value,style:e.style}),{...n,default:a=>{let{id:o,messagesId:s,isDisabled:u,isReadonly:f,isValid:g}=a
const V={model:i,isValid:g}
return t.createVNode(tn,t.mergeProps({ref:v},b,{modelValue:i.value,"onUpdate:modelValue":[e=>i.value=e,h],id:o.value,"aria-describedby":s.value,type:"checkbox","aria-checked":r.value?"mixed":void 0,disabled:u.value,readonly:f.value,onFocus:c,onBlur:d},l),{...n,default:e=>{let{backgroundColorClasses:a,backgroundColorStyles:l}=e
return t.createElementVNode("div",{class:t.normalizeClass(["v-switch__track",p?void 0:a.value]),style:t.normalizeStyle(l.value),onClick:y},[n["track-true"]&&t.createElementVNode("div",{key:"prepend",class:"v-switch__track-true"},[n["track-true"](V)]),n["track-false"]&&t.createElementVNode("div",{key:"append",class:"v-switch__track-false"},[n["track-false"](V)])])},input:a=>{let{inputNode:l,icon:o,backgroundColorClasses:r,backgroundColorStyles:i}=a
return t.createElementVNode(t.Fragment,null,[l,t.createElementVNode("div",{class:t.normalizeClass(["v-switch__thumb",{"v-switch__thumb--filled":o||e.loading},e.inset||p?void 0:r.value]),style:t.normalizeStyle(e.inset?void 0:i.value)},[n.thumb?t.createVNode(nl,{defaults:{VIcon:{icon:o,size:"x-small"}}},{default:()=>[n.thumb({...V,icon:o})]}):t.createVNode(Ga,null,{default:()=>[e.loading?t.createVNode(co,{name:"v-switch",active:!0,color:!1===g.value?void 0:m.value},{default:e=>n.loader?n.loader(e):t.createVNode(ao,{active:e.isActive,color:e.color,indeterminate:!0,size:"16",width:"2"},null)}):o&&t.createVNode(Jl,{key:String(o),icon:o,size:"x-small"},null)]})])])}})}})})),{}}}),$p=yt({color:String,height:[Number,String],window:Boolean,...bt(),...kl(),...ta(),...pl(),...Ba(),...Va()},"VSystemBar"),Mp=Et()({name:"VSystemBar",props:$p(),setup(e,a){let{slots:l}=a
const{themeClasses:o}=Ea(e),{backgroundColorClasses:n,backgroundColorStyles:r}=vl((()=>e.color)),{elevationClasses:i}=xl(e),{roundedClasses:s}=ml(e),{ssrBootStyles:u}=_l(),c=t.computed((()=>e.height??(e.window?32:24))),{layoutItemStyles:d}=la({id:e.name,order:t.computed((()=>parseInt(e.order,10))),position:t.shallowRef("top"),layoutSize:c,elementSize:c,active:t.computed((()=>!0)),absolute:t.toRef((()=>e.absolute))})
return Ft((()=>t.createVNode(e.tag,{class:t.normalizeClass(["v-system-bar",{"v-system-bar--window":e.window},o.value,n.value,i.value,s.value,e.class]),style:t.normalizeStyle([r.value,d.value,u.value,e.style])},l))),{}}}),Op=Symbol.for("vuetify:v-tabs"),Lp=yt({fixed:Boolean,sliderColor:String,hideSlider:Boolean,direction:{type:String,default:"horizontal"},...C(zo({selectedClass:"v-tab--selected",variant:"text"}),["active","block","flat","location","position","symbol"])},"VTab"),jp=Et()({name:"VTab",props:Lp(),setup(e,a){let{slots:l,attrs:o}=a
const{textColorClasses:n,textColorStyles:r}=dl((()=>e.sliderColor)),i=t.ref(),s=t.ref(),u=t.computed((()=>"horizontal"===e.direction)),c=t.computed((()=>i.value?.group?.isSelected.value??!1))
function d(e){let{value:t}=e
if(t){const e=i.value?.$el.parentElement?.querySelector(".v-tab--selected .v-tab__slider"),t=s.value
if(!e||!t)return
const a=getComputedStyle(e).color,l=e.getBoundingClientRect(),o=t.getBoundingClientRect(),n=u.value?"x":"y",r=u.value?"X":"Y",c=u.value?"right":"bottom",d=u.value?"width":"height",v=l[n]>o[n]?l[c]-o[c]:l[n]-o[n],p=Math.sign(v)>0?u.value?"right":"bottom":Math.sign(v)<0?u.value?"left":"top":"center",m=(Math.abs(v)+(Math.sign(v)<0?l[d]:o[d]))/Math.max(l[d],o[d])||0,f=1.5
be(t,{backgroundColor:[a,"currentcolor"],transform:[`translate${r}(${v}px) scale${r}(${l[d]/o[d]||0})`,`translate${r}(${v/f}px) scale${r}(${(m-1)/f+1})`,"none"],transformOrigin:Array(3).fill(p)},{duration:225,easing:Pt})}}return Ft((()=>{const a=$o.filterProps(e)
return t.createVNode($o,t.mergeProps({symbol:Op,ref:i,class:["v-tab",e.class],style:e.style,tabindex:c.value?0:-1,role:"tab","aria-selected":String(c.value),active:!1},a,o,{block:e.fixed,maxWidth:e.fixed?300:void 0,"onGroup:selected":d}),{...l,default:()=>t.createElementVNode(t.Fragment,null,[l.default?.()??e.text,!e.hideSlider&&t.createElementVNode("div",{ref:s,class:t.normalizeClass(["v-tab__slider",n.value]),style:t.normalizeStyle(r.value)},null)])})})),gi({},i)}}),Hp=yt({...C(Ns(),["continuous","nextIcon","prevIcon","showArrows","touch","mandatory"])},"VTabsWindow"),Wp=Et()({name:"VTabsWindow",props:Hp(),emits:{"update:modelValue":e=>!0},setup(e,a){let{slots:l}=a
const o=t.inject(Op,null),n=ra(e,"modelValue"),r=t.computed({get:()=>null==n.value&&o?o.items.value.find((e=>o.selected.value.includes(e.id)))?.value:n.value,set(e){n.value=e}})
return Ft((()=>{const a=Es.filterProps(e)
return t.createVNode(Es,t.mergeProps({_as:"VTabsWindow"},a,{modelValue:r.value,"onUpdate:modelValue":e=>r.value=e,class:["v-tabs-window",e.class],style:e.style,mandatory:!1,touch:!1}),l)})),{}}}),Up=yt({...Ps()},"VTabsWindowItem"),Yp=Et()({name:"VTabsWindowItem",props:Up(),setup(e,a){let{slots:l}=a
return Ft((()=>{const a=Bs.filterProps(e)
return t.createVNode(Bs,t.mergeProps({_as:"VTabsWindowItem"},a,{class:["v-tabs-window-item",e.class],style:e.style}),l)})),{}}})
const Gp=yt({alignTabs:{type:String,default:"start"},color:String,fixedTabs:Boolean,items:{type:Array,default:()=>[]},stacked:Boolean,bgColor:String,grow:Boolean,height:{type:[Number,String],default:void 0},hideSlider:Boolean,sliderColor:String,...Mn({mandatory:"force",selectedClass:"v-tab-item--selected"}),...Al(),...Ba()},"VTabs"),qp=Et()({name:"VTabs",props:Gp(),emits:{"update:modelValue":e=>!0},setup(e,a){let{attrs:l,slots:o}=a
const n=ra(e,"modelValue"),r=t.computed((()=>function(e){return e?e.map((e=>g(e)?e:{text:e,value:e})):[]}(e.items))),{densityClasses:i}=Tl(e),{backgroundColorClasses:s,backgroundColorStyles:u}=vl((()=>e.bgColor)),{scopeId:c}=li()
return xt({VTab:{color:t.toRef((()=>e.color)),direction:t.toRef((()=>e.direction)),stacked:t.toRef((()=>e.stacked)),fixed:t.toRef((()=>e.fixedTabs)),sliderColor:t.toRef((()=>e.sliderColor)),hideSlider:t.toRef((()=>e.hideSlider))}}),Ft((()=>{const a=On.filterProps(e),d=!!(o.window||e.items.length>0)
return t.createElementVNode(t.Fragment,null,[t.createVNode(On,t.mergeProps(a,{modelValue:n.value,"onUpdate:modelValue":e=>n.value=e,class:["v-tabs",`v-tabs--${e.direction}`,`v-tabs--align-tabs-${e.alignTabs}`,{"v-tabs--fixed-tabs":e.fixedTabs,"v-tabs--grow":e.grow,"v-tabs--stacked":e.stacked},i.value,s.value,e.class],style:[{"--v-tabs-height":f(e.height)},u.value,e.style],role:"tablist",symbol:Op},c,l),{default:()=>[o.default?.()??r.value.map((e=>o.tab?.({item:e})??t.createVNode(jp,t.mergeProps(e,{key:e.text,value:e.value}),{default:o[`tab.${e.value}`]?()=>o[`tab.${e.value}`]?.({item:e}):void 0})))]}),d&&t.createVNode(Wp,t.mergeProps({modelValue:n.value,"onUpdate:modelValue":e=>n.value=e,key:"tabs-window"},c),{default:()=>[r.value.map((e=>o.item?.({item:e})??t.createVNode(Yp,{value:e.value},{default:()=>o[`item.${e.value}`]?.({item:e})}))),o.window?.()]})])})),{}}}),Kp=yt({autoGrow:Boolean,autofocus:Boolean,counter:[Boolean,Number,String],counterValue:Function,prefix:String,placeholder:String,persistentPlaceholder:Boolean,persistentCounter:Boolean,noResize:Boolean,rows:{type:[Number,String],default:5,validator:e=>!isNaN(parseFloat(e))},maxRows:{type:[Number,String],validator:e=>!isNaN(parseFloat(e))},suffix:String,modelModifiers:Object,...gn(),...xi()},"VTextarea"),Xp=Et()({name:"VTextarea",directives:{vIntersect:yl},inheritAttrs:!1,props:Kp(),emits:{"click:control":e=>!0,"mousedown:control":e=>!0,"update:focused":e=>!0,"update:modelValue":e=>!0},setup(e,a){let{attrs:l,emit:o,slots:n}=a
const r=ra(e,"modelValue"),{isFocused:i,focus:s,blur:u}=un(e),c=t.computed((()=>"function"==typeof e.counterValue?e.counterValue(r.value):(r.value||"").toString().length)),d=t.computed((()=>l.maxlength?l.maxlength:!e.counter||"number"!=typeof e.counter&&"string"!=typeof e.counter?void 0:e.counter))
function v(t,a){e.autofocus&&t&&a[0].target?.focus?.()}const p=t.ref(),m=t.ref(),g=t.shallowRef(""),h=t.ref(),y=t.computed((()=>e.persistentPlaceholder||i.value||e.active))
function b(){h.value!==document.activeElement&&h.value?.focus(),i.value||s()}function V(e){b(),o("click:control",e)}function w(e){o("mousedown:control",e)}function S(a){a.stopPropagation(),b(),t.nextTick((()=>{r.value="",K(e["onClick:clear"],a)}))}function k(a){const l=a.target
if(r.value=l.value,e.modelModifiers?.trim){const e=[l.selectionStart,l.selectionEnd]
t.nextTick((()=>{l.selectionStart=e[0],l.selectionEnd=e[1]}))}}const x=t.ref(),C=t.ref(Number(e.rows)),N=t.computed((()=>["plain","underlined"].includes(e.variant)))
function E(){e.autoGrow&&t.nextTick((()=>{if(!x.value||!m.value)return
const t=getComputedStyle(x.value),a=getComputedStyle(m.value.$el),l=parseFloat(t.getPropertyValue("--v-field-padding-top"))+parseFloat(t.getPropertyValue("--v-input-padding-top"))+parseFloat(t.getPropertyValue("--v-field-padding-bottom")),o=x.value.scrollHeight,n=parseFloat(t.lineHeight),r=R(o??0,Math.max(parseFloat(e.rows)*n+l,parseFloat(a.getPropertyValue("--v-input-control-height"))),parseFloat(e.maxRows)*n+l||1/0)
C.value=Math.floor((r-l)/n),g.value=f(r)}))}let I
return t.watchEffect((()=>{e.autoGrow||(C.value=Number(e.rows))})),t.onMounted(E),t.watch(r,E),t.watch((()=>e.rows),E),t.watch((()=>e.maxRows),E),t.watch((()=>e.density),E),t.watch(x,(e=>{e?(I=new ResizeObserver(E),I.observe(x.value)):I?.disconnect()})),t.onBeforeUnmount((()=>{I?.disconnect()})),Ft((()=>{const a=!!(n.counter||e.counter||e.counterValue),o=!(!a&&!n.details),[s,f]=P(l),{modelValue:E,...I}=hn.filterProps(e),_=Ci.filterProps(e)
return t.createVNode(hn,t.mergeProps({ref:p,modelValue:r.value,"onUpdate:modelValue":e=>r.value=e,class:["v-textarea v-text-field",{"v-textarea--prefixed":e.prefix,"v-textarea--suffixed":e.suffix,"v-text-field--prefixed":e.prefix,"v-text-field--suffixed":e.suffix,"v-textarea--auto-grow":e.autoGrow,"v-textarea--no-resize":e.noResize||e.autoGrow,"v-input--plain-underlined":N.value},e.class],style:e.style},s,I,{centerAffix:1===C.value&&!N.value,focused:i.value}),{...n,default:a=>{let{id:l,isDisabled:o,isDirty:s,isReadonly:c,isValid:d}=a
return t.createVNode(Ci,t.mergeProps({ref:m,style:{"--v-textarea-control-height":g.value},onClick:V,onMousedown:w,"onClick:clear":S,"onClick:prependInner":e["onClick:prependInner"],"onClick:appendInner":e["onClick:appendInner"]},_,{id:l.value,active:y.value||s.value,centerAffix:1===C.value&&!N.value,dirty:s.value||e.dirty,disabled:o.value,focused:i.value,error:!1===d.value}),{...n,default:a=>{let{props:{class:l,...n}}=a
return t.createElementVNode(t.Fragment,null,[e.prefix&&t.createElementVNode("span",{class:"v-text-field__prefix"},[e.prefix]),t.withDirectives(t.createElementVNode("textarea",t.mergeProps({ref:h,class:l,value:r.value,onInput:k,autofocus:e.autofocus,readonly:c.value,disabled:o.value,placeholder:e.placeholder,rows:e.rows,name:e.name,onFocus:b,onBlur:u},n,f),null),[[yl,{handler:v},null,{once:!0}]]),e.autoGrow&&t.withDirectives(t.createElementVNode("textarea",{class:t.normalizeClass([l,"v-textarea__sizer"]),id:`${n.id}-sizer`,"onUpdate:modelValue":e=>r.value=e,ref:x,readonly:!0,"aria-hidden":"true"},null),[[t.vModelText,r.value]]),e.suffix&&t.createElementVNode("span",{class:"v-text-field__suffix"},[e.suffix])])}})},details:o?l=>t.createElementVNode(t.Fragment,null,[n.details?.(l),a&&t.createElementVNode(t.Fragment,null,[t.createElementVNode("span",null,null),t.createVNode(Vi,{active:e.persistentCounter||i.value,value:c.value,max:d.value,disabled:e.disabled},n.counter)])]):void 0})})),gi({},p,m,h)}}),Zp=yt({withBackground:Boolean,...bt(),...Va(),...Ba()},"VThemeProvider"),Qp=Et()({name:"VThemeProvider",props:Zp(),setup(e,a){let{slots:l}=a
const{themeClasses:o}=Ea(e)
return()=>e.withBackground?t.createVNode(e.tag,{class:t.normalizeClass(["v-theme-provider",o.value,e.class]),style:t.normalizeStyle(e.style)},{default:()=>[l.default?.()]}):l.default?.()}}),Jp=yt({dotColor:String,fillDot:Boolean,hideDot:Boolean,icon:zt,iconColor:String,lineColor:String,...bt(),...pl(),...Xl(),...kl()},"VTimelineDivider"),em=Et()({name:"VTimelineDivider",props:Jp(),setup(e,a){let{slots:l}=a
const{sizeClasses:o,sizeStyles:n}=Zl(e,"v-timeline-divider__dot"),{backgroundColorStyles:r,backgroundColorClasses:i}=vl((()=>e.dotColor)),{roundedClasses:s}=ml(e,"v-timeline-divider__dot"),{elevationClasses:u}=xl(e),{backgroundColorClasses:c,backgroundColorStyles:d}=vl((()=>e.lineColor))
return Ft((()=>t.createElementVNode("div",{class:t.normalizeClass(["v-timeline-divider",{"v-timeline-divider--fill-dot":e.fillDot},e.class]),style:t.normalizeStyle(e.style)},[t.createElementVNode("div",{class:t.normalizeClass(["v-timeline-divider__before",c.value]),style:t.normalizeStyle(d.value)},null),!e.hideDot&&t.createElementVNode("div",{key:"dot",class:t.normalizeClass(["v-timeline-divider__dot",u.value,s.value,o.value]),style:t.normalizeStyle(n.value)},[t.createElementVNode("div",{class:t.normalizeClass(["v-timeline-divider__inner-dot",i.value,s.value]),style:t.normalizeStyle(r.value)},[l.default?t.createVNode(nl,{key:"icon-defaults",disabled:!e.icon,defaults:{VIcon:{color:e.iconColor,icon:e.icon,size:e.size}}},l.default):t.createVNode(Jl,{key:"icon",color:e.iconColor,icon:e.icon,size:e.size},null)])]),t.createElementVNode("div",{class:t.normalizeClass(["v-timeline-divider__after",c.value]),style:t.normalizeStyle(d.value)},null)]))),{}}}),tm=yt({density:String,dotColor:String,fillDot:Boolean,hideDot:Boolean,hideOpposite:{type:Boolean,default:void 0},icon:zt,iconColor:String,lineInset:[Number,String],side:{type:String,validator:e=>null==e||["start","end"].includes(e)},...bt(),...rl(),...kl(),...pl(),...Xl(),...Ba()},"VTimelineItem"),am=Et()({name:"VTimelineItem",props:tm(),setup(e,a){let{slots:l}=a
const{dimensionStyles:o}=il(e),n=t.shallowRef(0),r=t.ref()
return t.watch(r,(e=>{e&&(n.value=e.$el.querySelector(".v-timeline-divider__dot")?.getBoundingClientRect().width??0)}),{flush:"post"}),Ft((()=>t.createElementVNode("div",{class:t.normalizeClass(["v-timeline-item",{"v-timeline-item--fill-dot":e.fillDot,"v-timeline-item--side-start":"start"===e.side,"v-timeline-item--side-end":"end"===e.side},e.class]),style:t.normalizeStyle([{"--v-timeline-dot-size":f(n.value),"--v-timeline-line-inset":e.lineInset?`calc(var(--v-timeline-dot-size) / 2 + ${f(e.lineInset)})`:f(0)},e.style])},[t.createElementVNode("div",{class:"v-timeline-item__body",style:t.normalizeStyle(o.value)},[l.default?.()]),t.createVNode(em,{ref:r,hideDot:e.hideDot,icon:e.icon,iconColor:e.iconColor,size:e.size,elevation:e.elevation,dotColor:e.dotColor,fillDot:e.fillDot,rounded:e.rounded},{default:l.icon}),"compact"!==e.density&&t.createElementVNode("div",{class:"v-timeline-item__opposite"},[!e.hideOpposite&&l.opposite?.()])]))),{}}}),lm=yt({align:{type:String,default:"center",validator:e=>["center","start"].includes(e)},direction:{type:String,default:"vertical",validator:e=>["vertical","horizontal"].includes(e)},justify:{type:String,default:"auto",validator:e=>["auto","center"].includes(e)},side:{type:String,validator:e=>null==e||["start","end"].includes(e)},lineThickness:{type:[String,Number],default:2},lineColor:String,truncateLine:{type:String,validator:e=>["start","end","both"].includes(e)},...k(tm({lineInset:0}),["dotColor","fillDot","hideOpposite","iconColor","lineInset","size"]),...bt(),...Al(),...Ba(),...Va()},"VTimeline"),om=Et()({name:"VTimeline",props:lm(),setup(e,a){let{slots:l}=a
const{themeClasses:o}=Ea(e),{densityClasses:n}=Tl(e),{rtlClasses:r}=ya()
xt({VTimelineDivider:{lineColor:t.toRef((()=>e.lineColor))},VTimelineItem:{density:t.toRef((()=>e.density)),dotColor:t.toRef((()=>e.dotColor)),fillDot:t.toRef((()=>e.fillDot)),hideOpposite:t.toRef((()=>e.hideOpposite)),iconColor:t.toRef((()=>e.iconColor)),lineColor:t.toRef((()=>e.lineColor)),lineInset:t.toRef((()=>e.lineInset)),size:t.toRef((()=>e.size))}})
const i=t.computed((()=>{const t=e.side?e.side:"default"!==e.density?"end":null
return t&&`v-timeline--side-${t}`})),s=t.computed((()=>{const t=["v-timeline--truncate-line-start","v-timeline--truncate-line-end"]
switch(e.truncateLine){case"both":return t
case"start":return t[0]
case"end":return t[1]
default:return null}}))
return Ft((()=>t.createVNode(e.tag,{class:t.normalizeClass(["v-timeline",`v-timeline--${e.direction}`,`v-timeline--align-${e.align}`,`v-timeline--justify-${e.justify}`,s.value,{"v-timeline--inset-line":!!e.lineInset},o.value,n.value,i.value,r.value,e.class]),style:t.normalizeStyle([{"--v-timeline-line-thickness":f(e.lineThickness)},e.style])},l))),{}}}),nm=yt({...bt(),...zl({variant:"text"})},"VToolbarItems"),rm=Et()({name:"VToolbarItems",props:nm(),setup(e,a){let{slots:l}=a
return xt({VBtn:{color:t.toRef((()=>e.color)),height:"inherit",variant:t.toRef((()=>e.variant))}}),Ft((()=>t.createElementVNode("div",{class:t.normalizeClass(["v-toolbar-items",e.class]),style:t.normalizeStyle(e.style)},[l.default?.()]))),{}}}),im=yt({id:String,interactive:Boolean,text:String,...C(vi({closeOnBack:!1,location:"end",locationStrategy:"connected",eager:!0,minWidth:0,offset:10,openOnClick:!1,openOnHover:!0,origin:"auto",scrim:!1,scrollStrategy:"reposition",transition:null}),["absolute","persistent"])},"VTooltip"),sm=Et()({name:"VTooltip",props:im(),emits:{"update:modelValue":e=>!0},setup(e,a){let{slots:l}=a
const o=ra(e,"modelValue"),{scopeId:n}=li(),r=t.useId(),i=t.toRef((()=>e.id||`v-tooltip-${r}`)),s=t.ref(),u=t.computed((()=>e.location.split(" ").length>1?e.location:e.location+" center")),c=t.computed((()=>"auto"===e.origin||"overlap"===e.origin||e.origin.split(" ").length>1||e.location.split(" ").length>1?e.origin:e.origin+" center")),d=t.toRef((()=>null!=e.transition?e.transition:o.value?"scale-transition":"fade-transition")),v=t.computed((()=>t.mergeProps({"aria-describedby":i.value},e.activatorProps)))
return Ft((()=>{const a=pi.filterProps(e)
return t.createVNode(pi,t.mergeProps({ref:s,class:["v-tooltip",{"v-tooltip--interactive":e.interactive},e.class],style:e.style,id:i.value},a,{modelValue:o.value,"onUpdate:modelValue":e=>o.value=e,transition:d.value,absolute:!0,location:u.value,origin:c.value,persistent:!0,role:"tooltip",activatorProps:v.value,_disableGlobalStack:!0},n),{activator:l.activator,default:function(){for(var t=arguments.length,a=new Array(t),o=0;o<t;o++)a[o]=arguments[o]
return l.default?.(...a)??e.text}})})),gi({},s)}}),um=Et()({name:"VValidation",props:mn(),emits:{"update:modelValue":e=>!0},setup(e,t){let{slots:a}=t
const l=fn(e,"validation")
return()=>a.default?.(l)}})
var cm=Object.freeze({__proto__:null,VAlert:Uo,VAlertTitle:jo,VApp:Pa,VAppBar:Bl,VAppBarNavIcon:Oo,VAppBarTitle:Lo,VAutocomplete:Ui,VAvatar:Go,VBadge:Gi,VBanner:Qi,VBannerActions:Ki,VBannerText:Xi,VBottomNavigation:es,VBottomSheet:os,VBreadcrumbs:cs,VBreadcrumbsDivider:rs,VBreadcrumbsItem:ss,VBtn:$o,VBtnGroup:Ol,VBtnToggle:ql,VCard:Vs,VCardActions:ds,VCardItem:gs,VCardSubtitle:ps,VCardText:ys,VCardTitle:ms,VCarousel:_s,VCarouselItem:As,VCheckbox:bn,VCheckboxBtn:ln,VChip:Un,VChipGroup:Hn,VClassIcon:Ht,VCode:Ts,VCol:ud,VColorPicker:Iu,VCombobox:Pu,VComponentIcon:Ot,VConfirmEdit:Ru,VContainer:td,VCounter:Vi,VDataIterator:mc,VDataTable:Kc,VDataTableFooter:yc,VDataTableHeaders:Dc,VDataTableRow:Mc,VDataTableRows:Lc,VDataTableServer:Jc,VDataTableVirtual:Zc,VDatePicker:Od,VDatePickerControls:_d,VDatePickerHeader:Bd,VDatePickerMonth:Td,VDatePickerMonths:Fd,VDatePickerYears:$d,VDefaultsProvider:nl,VDialog:as,VDialogBottomTransition:Wa,VDialogTopTransition:Ua,VDialogTransition:Oa,VDivider:br,VEmptyState:jd,VExpandTransition:al,VExpandXTransition:ll,VExpansionPanel:Kd,VExpansionPanelText:Ud,VExpansionPanelTitle:Gd,VExpansionPanels:Qd,VFab:ev,VFabTransition:Ha,VFadeTransition:Ya,VField:Ci,VFieldLabel:Si,VFileInput:av,VFooter:ov,VForm:rv,VHover:sv,VIcon:Jl,VImg:Vl,VInfiniteScroll:dv,VInput:hn,VItem:fv,VItemGroup:mv,VKbd:gv,VLabel:Ko,VLayout:yv,VLayoutItem:Vv,VLazy:Sv,VLigatureIcon:jt,VList:_r,VListGroup:cr,VListImg:Pr,VListItem:fr,VListItemAction:Rr,VListItemMedia:Tr,VListItemSubtitle:vr,VListItemTitle:pr,VListSubheader:hr,VLocaleProvider:xv,VMain:Nv,VMenu:yi,VMessages:rn,VNavigationDrawer:Dv,VNoSsr:Fv,VNumberInput:$v,VOtpInput:Ov,VOverlay:pi,VPagination:gc,VParallax:jv,VProgressCircular:ao,VProgressLinear:io,VRadio:Wv,VRadioGroup:Yv,VRangeSlider:qv,VRating:Xv,VResponsive:ul,VRow:Nd,VScaleTransition:Ga,VScrollXReverseTransition:Ka,VScrollXTransition:qa,VScrollYReverseTransition:Za,VScrollYTransition:Xa,VSelect:$i,VSelectionControl:tn,VSelectionControlGroup:Jo,VSheet:ru,VSkeletonLoader:lp,VSlideGroup:On,VSlideGroupItem:op,VSlideXReverseTransition:Ja,VSlideXTransition:Qa,VSlideYReverseTransition:tl,VSlideYTransition:el,VSlider:Js,VSnackbar:rp,VSnackbarQueue:sp,VSpacer:Ed,VSparkline:bp,VSpeedDial:wp,VStepper:Dp,VStepperActions:xp,VStepperHeader:Cp,VStepperItem:Ip,VStepperWindow:Pp,VStepperWindowItem:Rp,VSvgIcon:Lt,VSwitch:zp,VSystemBar:Mp,VTab:jp,VTable:Hc,VTabs:qp,VTabsWindow:Wp,VTabsWindowItem:Yp,VTextField:Ii,VTextarea:Xp,VThemeProvider:Qp,VTimeline:om,VTimelineItem:am,VToolbar:El,VToolbarItems:rm,VToolbarTitle:Aa,VTooltip:sm,VValidation:um,VVirtualScroll:Ti,VWindow:Es,VWindowItem:Bs})
function dm(e,t){e._mutate?.[t.instance.$.uid]&&(e._mutate[t.instance.$.uid].observer.disconnect(),delete e._mutate[t.instance.$.uid])}const vm={mounted:function(e,t){const a=t.modifiers||{},l=t.value,{once:o,immediate:n,...r}=a,i=!Object.keys(r).length,{handler:s,options:u}="object"==typeof l?l:{handler:l,options:{attributes:r?.attr??i,characterData:r?.char??i,childList:r?.child??i,subtree:r?.sub??i}},c=new MutationObserver((function(){let a=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],l=arguments.length>1?arguments[1]:void 0
s?.(a,l),o&&dm(e,t)}))
n&&s?.([],c),e._mutate=Object(e._mutate),e._mutate[t.instance.$.uid]={observer:c},c.observe(e,u)},unmounted:dm}
const pm={mounted:function(e,t){const a=t.value,l={passive:!t.modifiers?.active}
window.addEventListener("resize",a,l),e._onResize=Object(e._onResize),e._onResize[t.instance.$.uid]={handler:a,options:l},t.modifiers?.quiet||a()},unmounted:function(e,t){if(!e._onResize?.[t.instance.$.uid])return
const{handler:a,options:l}=e._onResize[t.instance.$.uid]
window.removeEventListener("resize",a,l),delete e._onResize[t.instance.$.uid]}}
function mm(e,t){const{self:a=!1}=t.modifiers??{},l=t.value,o="object"==typeof l&&l.options||{passive:!0},n="function"==typeof l||"handleEvent"in l?l:l.handler,r=a?e:t.arg?document.querySelector(t.arg):window
r&&(r.addEventListener("scroll",n,o),e._onScroll=Object(e._onScroll),e._onScroll[t.instance.$.uid]={handler:n,options:o,target:a?void 0:r})}function fm(e,t){if(!e._onScroll?.[t.instance.$.uid])return
const{handler:a,options:l,target:o=e}=e._onScroll[t.instance.$.uid]
o.removeEventListener("scroll",a,l),delete e._onScroll[t.instance.$.uid]}const gm={mounted:mm,unmounted:fm,updated:function(e,t){t.value!==t.oldValue&&(fm(e,t),mm(e,t))}}
const hm=function(e,a){const l=function(e,a){return function(l,o,n){const r="function"==typeof a?a(o):a,i=o.value?.text??o.value??r?.text,s=g(o.value)?o.value:{},u=()=>i??l.textContent,c=(n.ctx===o.instance.$?function(e,t){const a=new Set,l=t=>{for(const o of t){if(!o)continue
if(o===e||o.el&&e.el&&o.el===e.el)return!0
let t
if(a.add(o),o.suspense?t=l([o.ssContent]):Array.isArray(o.children)?t=l(o.children):o.component?.vnode&&(t=l([o.component?.subTree])),t)return t
a.delete(o)}return!1}
if(!l([t.subTree]))return Oe("Could not find original vnode, component will not inherit provides"),t
const o=Array.from(a).reverse()
for(const e of o)if(e.component)return e.component
return t}(n,o.instance.$)?.provides:n.ctx?.provides)??o.instance.$.provides,d=t.h(e,t.mergeProps(r,s),u)
d.appContext=Object.assign(Object.create(null),o.instance.$.appContext,{provides:c}),t.render(d,l)}}("string"==typeof e?t.resolveComponent(e):e,a)
return{mounted:l,updated:l,unmounted(e){t.render(null,e)}}}(sm,(e=>({activator:"parent",location:e.arg?.replace("-"," "),text:"boolean"==typeof e.value?void 0:e.value})))
var ym=Object.freeze({__proto__:null,ClickOutside:ci,Intersect:yl,Mutate:vm,Resize:pm,Ripple:Fo,Scroll:gm,Tooltip:hm,Touch:ks})
function bm(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}
const{blueprint:l,...o}=e,n=z(l,o),{aliases:r={},components:i={},directives:s={}}=n,u=t.effectScope()
return u.run((()=>{const e=function(e){return t.ref(e)}(n.defaults),l=function(e,l){const{thresholds:o,mobileBreakpoint:n}=kn(e),r=t.shallowRef(Cn(l)),i=t.shallowRef(Nn(l)),s=t.reactive({}),u=t.shallowRef(xn(l))
function c(){r.value=Cn(),u.value=xn()}return t.watchEffect((()=>{const e=u.value<o.sm,t=u.value<o.md&&!e,a=u.value<o.lg&&!(t||e),l=u.value<o.xl&&!(a||t||e),c=u.value<o.xxl&&!(l||a||t||e),d=u.value>=o.xxl,v=e?"xs":t?"sm":a?"md":l?"lg":c?"xl":"xxl",p="number"==typeof n?n:o[n],m=u.value<p
s.xs=e,s.sm=t,s.md=a,s.lg=l,s.xl=c,s.xxl=d,s.smAndUp=!e,s.mdAndUp=!(e||t),s.lgAndUp=!(e||t||a),s.xlAndUp=!(e||t||a||l),s.smAndDown=!(a||l||c||d),s.mdAndDown=!(l||c||d),s.lgAndDown=!(c||d),s.xlAndDown=!d,s.name=v,s.height=r.value,s.width=u.value,s.mobile=m,s.mobileBreakpoint=n,s.platform=i.value,s.thresholds=o})),a&&(window.addEventListener("resize",c,{passive:!0}),t.onScopeDispose((()=>{window.removeEventListener("resize",c)}),!0)),{...t.toRefs(s),update:function(){c(),i.value=Nn()},ssr:!!l}}(n.display,n.ssr),o=Na(n.theme),c=Wt(n.icons),d=fa(n.locale),v=function(e,t){const a=z({adapter:ku,locale:{af:"af-ZA",bg:"bg-BG",ca:"ca-ES",ckb:"",cs:"cs-CZ",de:"de-DE",el:"el-GR",en:"en-US",et:"et-EE",fa:"fa-IR",fi:"fi-FI",hr:"hr-HR",hu:"hu-HU",he:"he-IL",id:"id-ID",it:"it-IT",ja:"ja-JP",ko:"ko-KR",lv:"lv-LV",lt:"lt-LT",nl:"nl-NL",no:"no-NO",pl:"pl-PL",pt:"pt-PT",ro:"ro-RO",ru:"ru-RU",sk:"sk-SK",sl:"sl-SI",srCyrl:"sr-SP",srLatn:"sr-SP",sv:"sv-SE",th:"th-TH",tr:"tr-TR",az:"az-AZ",uk:"uk-UA",vi:"vi-VN",zhHans:"zh-CN",zhHant:"zh-TW"}},e)
return{options:a,instance:Nu(a,t)}}(n.date,d),p=function(e,t){return{rtl:t.isRtl,options:z({container:void 0,duration:300,layout:!1,offset:0,easing:"easeInOutCubic",patterns:{linear:e=>e,easeInQuad:e=>e**2,easeOutQuad:e=>e*(2-e),easeInOutQuad:e=>e<.5?2*e**2:(4-2*e)*e-1,easeInCubic:e=>e**3,easeOutCubic:e=>--e**3+1,easeInOutCubic:e=>e<.5?4*e**3:(e-1)*(2*e-2)*(2*e-2)+1,easeInQuart:e=>e**4,easeOutQuart:e=>1- --e**4,easeInOutQuart:e=>e<.5?8*e**4:1-8*--e**4,easeInQuint:e=>e**5,easeOutQuint:e=>1+--e**5,easeInOutQuint:e=>e<.5?16*e**5:1+16*--e**5}},e)}}(n.goTo,d)
return{install:function(u){for(const e in s)u.directive(e,s[e])
for(const e in i)u.component(e,i[e])
for(const e in r)u.component(e,Nt({...r[e],name:e,aliasName:r[e].name}))
const m=t.effectScope()
if(m.run((()=>{o.install(u)})),u.onUnmount((()=>m.stop())),u.provide(St,e),u.provide(wn,l),u.provide(ba,o),u.provide($t,c),u.provide(ma,d),u.provide(xu,v.options),u.provide(Cu,v.instance),u.provide(_n,p),a&&n.ssr)if(u.$nuxt)u.$nuxt.hook("app:suspense:resolve",(()=>{l.update()}))
else{const{mount:e}=u
u.mount=function(){const a=e(...arguments)
return t.nextTick((()=>l.update())),u.mount=e,a}}("boolean"!=typeof __VUE_OPTIONS_API__||__VUE_OPTIONS_API__)&&u.mixin({computed:{$vuetify(){return t.reactive({defaults:Vm.call(this,St),display:Vm.call(this,wn),theme:Vm.call(this,ba),icons:Vm.call(this,$t),locale:Vm.call(this,ma),date:Vm.call(this,Cu)})}}})},unmount:function(){u.stop()},defaults:e,display:l,theme:o,icons:c,locale:d,date:v,goTo:p}}))}function Vm(e){const t=this.$,a=t.parent?.provides??t.vnode.appContext?.provides
if(a&&e in a)return a[e]}bm.version="3.8.8"
const wm=function(){return bm({components:cm,directives:ym,...arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}})},Sm="3.8.8"
wm.version=Sm,e.blueprints=Xt,e.components=cm,e.createVuetify=wm,e.directives=ym,e.useDate=Eu,e.useDefaults=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0
const{props:a,provideSubDefaults:l}=Ct(e,t)
return l(),a},e.useDisplay=In,e.useGoTo=An,e.useLayout=aa,e.useLocale=ga,e.useRtl=ya,e.useTheme=Ia,e.version=Sm}))
//# sourceMappingURL=vuetify.min.js.map
