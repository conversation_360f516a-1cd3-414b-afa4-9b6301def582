!function(n,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((n=n||self).workbox={})}(this,(function(n){"use strict";try{self["workbox:window:5.1.4"]&&_()}catch(n){}function t(n,t){return new Promise((function(r){var e=new MessageChannel;e.port1.onmessage=function(n){r(n.data)},n.postMessage(t,[e.port2])}))}function r(n,t){for(var r=0;r<t.length;r++){var e=t[r];e.enumerable=e.enumerable||!1,e.configurable=!0,"value"in e&&(e.writable=!0),Object.defineProperty(n,e.key,e)}}function e(n,t){(null==t||t>n.length)&&(t=n.length);for(var r=0,e=new Array(t);r<t;r++)e[r]=n[r];return e}function i(n,t){var r;if("undefined"==typeof Symbol||null==n[Symbol.iterator]){if(Array.isArray(n)||(r=function(n,t){if(n){if("string"==typeof n)return e(n,t);var r=Object.prototype.toString.call(n).slice(8,-1);return"Object"===r&&n.constructor&&(r=n.constructor.name),"Map"===r||"Set"===r?Array.from(n):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?e(n,t):void 0}}(n))||t&&n&&"number"==typeof n.length){r&&(n=r);var i=0;return function(){return i>=n.length?{done:!0}:{done:!1,value:n[i++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}return(r=n[Symbol.iterator]()).next.bind(r)}try{self["workbox:core:5.1.4"]&&_()}catch(n){}var o=function(){var n=this;this.promise=new Promise((function(t,r){n.resolve=t,n.reject=r}))};function u(n,t){var r=location.href;return new URL(n,r).href===new URL(t,r).href}var a=function(n,t){this.type=n,Object.assign(this,t)};function c(n,t,r){return r?t?t(n):n:(n&&n.then||(n=Promise.resolve(n)),t?n.then(t):n)}function f(){}var s=function(n){var e,i;function f(t,r){var e,i;return void 0===r&&(r={}),(e=n.call(this)||this).t={},e.i=0,e.o=new o,e.u=new o,e.s=new o,e.v=0,e.h=new Set,e.l=function(){var n=e.m,t=n.installing;e.i>0||!u(t.scriptURL,e.g)||performance.now()>e.v+6e4?(e.p=t,n.removeEventListener("updatefound",e.l)):(e.P=t,e.h.add(t),e.o.resolve(t)),++e.i,t.addEventListener("statechange",e.j)},e.j=function(n){var t=e.m,r=n.target,i=r.state,o=r===e.p,u=o?"external":"",c={sw:r,originalEvent:n};!o&&e.S&&(c.isUpdate=!0),e.dispatchEvent(new a(u+i,c)),"installed"===i?e.O=self.setTimeout((function(){"installed"===i&&t.waiting===r&&e.dispatchEvent(new a(u+"waiting",c))}),200):"activating"===i&&(clearTimeout(e.O),o||e.u.resolve(r))},e.A=function(n){var t=e.P;t===navigator.serviceWorker.controller&&(e.dispatchEvent(new a("controlling",{sw:t,originalEvent:n,isUpdate:e.S})),e.s.resolve(t))},e.M=(i=function(n){var t=n.data,r=n.source;return c(e.getSW(),(function(){e.h.has(r)&&e.dispatchEvent(new a("message",{data:t,sw:r,originalEvent:n}))}))},function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];try{return Promise.resolve(i.apply(this,n))}catch(n){return Promise.reject(n)}}),e.g=t,e.t=r,navigator.serviceWorker.addEventListener("message",e.M),e}i=n,(e=f).prototype=Object.create(i.prototype),e.prototype.constructor=e,e.__proto__=i;var s,h,l,d=f.prototype;return d.register=function(n){var t=(void 0===n?{}:n).immediate,r=void 0!==t&&t;try{var e=this;return function(n,t){var r=n();if(r&&r.then)return r.then(t);return t(r)}((function(){if(!r&&"complete"!==document.readyState)return v(new Promise((function(n){return window.addEventListener("load",n)})))}),(function(){return e.S=Boolean(navigator.serviceWorker.controller),e.U=e._(),c(e.I(),(function(n){e.m=n,e.U&&(e.P=e.U,e.u.resolve(e.U),e.s.resolve(e.U),e.U.addEventListener("statechange",e.j,{once:!0}));var t=e.m.waiting;return t&&u(t.scriptURL,e.g)&&(e.P=t,Promise.resolve().then((function(){e.dispatchEvent(new a("waiting",{sw:t,wasWaitingBeforeRegister:!0}))})).then((function(){}))),e.P&&(e.o.resolve(e.P),e.h.add(e.P)),e.m.addEventListener("updatefound",e.l),navigator.serviceWorker.addEventListener("controllerchange",e.A,{once:!0}),e.m}))}))}catch(n){return Promise.reject(n)}},d.update=function(){try{return this.m?v(this.m.update()):void 0}catch(n){return Promise.reject(n)}},d.getSW=function(){try{return void 0!==this.P?this.P:this.o.promise}catch(n){return Promise.reject(n)}},d.messageSW=function(n){try{return c(this.getSW(),(function(r){return t(r,n)}))}catch(n){return Promise.reject(n)}},d._=function(){var n=navigator.serviceWorker.controller;return n&&u(n.scriptURL,this.g)?n:void 0},d.I=function(){try{var n=this;return function(n,t){try{var r=n()}catch(n){return t(n)}if(r&&r.then)return r.then(void 0,t);return r}((function(){return c(navigator.serviceWorker.register(n.g,n.t),(function(t){return n.v=performance.now(),t}))}),(function(n){throw n}))}catch(n){return Promise.reject(n)}},s=f,(h=[{key:"active",get:function(){return this.u.promise}},{key:"controlling",get:function(){return this.s.promise}}])&&r(s.prototype,h),l&&r(s,l),f}(function(){function n(){this.R=new Map}var t=n.prototype;return t.addEventListener=function(n,t){this.k(n).add(t)},t.removeEventListener=function(n,t){this.k(n).delete(t)},t.dispatchEvent=function(n){n.target=this;for(var t,r=i(this.k(n.type));!(t=r()).done;){(0,t.value)(n)}},t.k=function(n){return this.R.has(n)||this.R.set(n,new Set),this.R.get(n)},n}());function v(n,t){if(!t)return n&&n.then?n.then(f):Promise.resolve()}n.Workbox=s,n.messageSW=t,Object.defineProperty(n,"__esModule",{value:!0})}));

