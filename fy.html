<html>

<head>
	<title>翻译</title>
	<meta charset="utf-8">
	<meta name="apple-mobile-web-app-capable" content="yes" />
	<meta name="mobile-web-app-capable" content="yes" />
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<meta name="viewport" content="width=650,  user-scalable=no">
	<link rel="shortcut icon" href="#" />

	<link href="static/mdi_font/css/materialdesignicons.min.css" rel="stylesheet">
	<link href="static/vuetify.min.css" rel="stylesheet">
	<script src="static/vue.js"></script>
	<script src="static/vuetify.min.js"></script>
	<style>
		div {
			transition: all 0.3s;
		}

		.v-sheet.v-card {
			margin: 20px;
			padding: 10px;
		}

		[v-cloak] {
			display: none;
		}

		.v-application--wrap {
			display: unset;
		}
	</style>
</head>

<body>
	<div id="app" v-cloak>
		<v-app>
			
			<v-toolbar :elevation="2"
				style="position: sticky;top: 0;z-index: 1;min-width: 100vw;padding-right: 10px;">
				<b style="font-size: xx-large;padding-left: 20px;">翻译</b>
				<v-spacer></v-spacer>
				<v-btn class="ma-0" size="x-large" @click="f终止()" v-if="loading" variant="tonal">终止</v-btn>
						<v-checkbox v-model="b逐段模式" label="逐段翻译" hide-details
							style="display: inline-block;margin-top: 0;"></v-checkbox>
				<v-checkbox v-model="noCOT" class="ma-2" label="跳过思考" hide-details="auto"></v-checkbox>
			</v-toolbar>



			<v-card elevation="2">
				<v-row style="padding: 9px;">
					<v-col cols="6">
						<!-- <v-btn color="primary" dark size="x-large" @click="f自动格式化()" :loading="loading">
						格式化粘贴的PDF
					</v-btn> -->

						<v-btn color="primary" outlined size="x-large" style="display: inline-block;margin-top: 0;"
							@click="导入()">
							导入
						</v-btn>
					</v-col>
					<v-col cols="6" style="text-align: right;">To
						<v-btn color="primary" dark size="x-large" @click="f翻译('中文')" :loading="loading">
							中文
						</v-btn>
						<v-btn color="primary" dark size="x-large" @click="f翻译('English')" :loading="loading">
							English
						</v-btn>
						<v-btn color="primary" dark size="x-large" @click="f翻译('日本語')" :loading="loading">
							日本語
						</v-btn>
						<v-btn color="primary" dark size="x-large" @click="f翻译('Русский')" :loading="loading">
							Русский
						</v-btn>
					</v-col>
				</v-row>
				<v-divider></v-divider>
				<v-card-text>

					<v-row>
						<v-col cols="6">
							<v-textarea autofocus v-model="s输入" label="输入" rows="10" hide-details="auto"></v-textarea>
						</v-col>
						<v-col cols="6">
							<v-textarea v-model="s输出" label="输出" rows="10" hide-details="auto"></v-textarea>
						</v-col>
					</v-row>
				</v-card-text>
				<v-card-action style="text-align: right;display: block;">
					<v-btn color="primary" outlined size="x-large" @click="导出()">
						导出
					</v-btn>
					<v-btn color="primary" outlined size="x-large" @click="f复制正文()">
						复制正文
					</v-btn>
					<v-btn color="primary" outlined size="x-large" @click="f终止()" v-if="loading">
						终止
					</v-btn>
				</v-card-action>
			</v-card>
			<v-card elevation="2">
				翻译规则
				<v-divider></v-divider>
				<v-card-text>

					<v-data-table :headers=" [{ text: '术语', value: '术语', sortable: true, },
					{ text: '译文', value: '译文', sortable: true, },
					{ text: '删除', value: '功能', sortable: false, }
					]" :items="l翻译规则" :items-per-page="2000" hide-default-footer style="margin: 10px;">
						<template v-slot:item.术语="{ item }">
							<input v-model="item.术语" @change="保存()"></input>
						</template>
						</template>
						<template v-slot:item.译文="{ item }">
							<input v-model="item.译文" @change="保存()"></input>
						</template>
						</template>
						<template v-slot:item.功能="{ item }">
							<v-icon large text @click="删除 (item)">mdi-delete</v-icon>
						</template>
					</v-data-table>
				</v-card-text>
			</v-card>
			<v-snackbar v-model="b显示提示文本" :timeout="3000" style="white-space: pre-line">{{s提示文本}}</v-snackbar>
		</v-app>
	</div>
	<script>

		保存 = () => {
			if (app.l翻译规则.length == 0) {

				app.l翻译规则.push({ 术语: '', 译文: '' })
			} else if (app.l翻译规则[app.l翻译规则.length - 1].术语 != '')
				app.l翻译规则.push({ 术语: '', 译文: '' })
			localStorage["l翻译规则"] = JSON.stringify(app.l翻译规则)
		}
		删除 = (item) => {
			if (confirm("确认删除[" + item.术语 + "]?")) {
				app.l翻译规则.splice(app.l翻译规则.indexOf(item), 1)
				保存()
			}
		}
		app = new Vue({
			el: '#app',
			vuetify: new Vuetify(),
			data: () => ({
				noCOT: true,
				s输入: "",
				s输出: "",
				loading: false,
				// 是否显示snackbar
				b显示提示文本: false,
				// snackbar的文本
				s提示文本: "",
				temperature: 0.1,
				top_p: 0.1,
				max_length: 4000,
				llm_type: "",
				//显示对话框
				show_dialog: false,
				//对话框标题
				dialog_title: "",
				b逐段模式: true,
				l翻译规则: JSON.parse(localStorage["l翻译规则"] || "[{}]")
			}),
			methods: {
			},
			watch: {
			}
		})

		f自动格式化 = async () => {
			app.s输入 = app.s输入.replace(/ +/g, ' ').replace(/[\r\n]+/g, '\n').replace(/^[\n\s\t]+/, '').replace(/[\n\s\t]+$/, '')
			s = app.s输入.split('\n')
			let sum = 0
			s.forEach(i => {
				sum += i.byteLength()
			})
			let avg = sum / s.length
			console.log('全文平均行长度', avg)
			sum = 0
			let long_text_count = 0
			s.forEach(i => {
				let length = i.byteLength()
				if (length > avg - 5) {
					long_text_count += 1
					sum += length
				}
			})
			avg = sum / long_text_count
			console.log('平均截断行长度', avg)
			let result = ''
			s.forEach(i => {
				if (i.byteLength() < avg - 5)
					result += i + '\n'
				else
					result += i

			})
			app.s输入 = result.replace(/[\n\s\t]+$/, '')
		}

		导出 = () => {
			const blob = new Blob([app.s输出], {
				type: "text/plain;charset=utf-8"
			})
			const objectURL = URL.createObjectURL(blob)
			const aTag = document.createElement('a')
			aTag.href = objectURL
			aTag.download = window.prefix + Date.now() + "-翻译.md"
			aTag.click()
		}
		window.prefix = ''
		导入 = async () => {
			let contents = ''
			await new Promise(resolve => {
				let input = document.createElement('input')
				input.type = 'file'
				input.accept = '.md'
				input.onchange = function () {
					var file = input.files[0];
					window.prefix = file.name
					var reader = new FileReader();
					reader.onload = function (e) {
						contents = e.target.result;
						resolve()
					};
					reader.readAsText(file);
				}
				input.click()
			})

			app.s输入 = contents
		}
		f翻译 = async (lang) => {
			let input = app.s输入
			let rules = app.l翻译规则.filter(i => i.术语 && i.译文)
			rules.forEach(i => {
				input = input.replaceAll(i.术语, i.译文)
			})
			app.loading = true
			old = `Translate the following text to ${lang}. Keep the formula notation unchanged. Output translation directly without any additional text.\nSource Text:\n`
			if (app.b逐段模式) {
				inputs = input.split('\n')
				let sum = inputs.length
				let index = 0
				app.s输出 = ''
				for (input of inputs) {
					index += 1
					document.title = `${index}/${sum}`
					input = input.replaceAll('\r', '')
					if (input.trim() == '') {
						app.s输出 += '\n'
						continue
					}
					let prompt = make_prompt(old + `${input}\nTranslated Text ( ${lang} ):`, app.noCOT ? templates.skip_think_prompt : "")

					s旧输出 = app.s输出
					s新输出 = ''
					await send_prompt(prompt,
						[templates.eos, '\nNote: '], (s) => {

							s新输出 = s.replaceAll('\n', '')
							if (s新输出.indexOf("</think>") > -1)
								s新输出 = s新输出.slice(s新输出.indexOf("</think>") + 8)
							app.s输出 = s旧输出 + s新输出
						})
					// old = prompt + s新输出 + '</output>' + templates.eos
					app.s输出 += '\n'
				}
			} else {
				let prompt = make_prompt(old + `${input}\nTranslated Text ( ${lang} ):`, app.noCOT ? templates.skip_think_prompt : "")
				await send_prompt(prompt,
					[templates.eos], (s) => {
						s新输出 = s
						if (s新输出.indexOf("</think>") > -1)
							s新输出 = s新输出.slice(s新输出.indexOf("</think>") + 8)
						app.s输出 = s新输出
					})

			}
			app.loading = false
		}
		f复制正文 = async () => {
			copy(app.s输出)
		}
		f终止 = async () => {
			app.loading = false
			controller.abort()
		}
		alert = (text) => {
			app.s提示文本 = text; //.replace(/\n/g,"<br>")
			app.b显示提示文本 = true;
		}
	</script>
	<script src="wd_sdk.js"></script>
</body>

</html>